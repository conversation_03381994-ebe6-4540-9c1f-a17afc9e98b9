package com.kuaishou.serveree.themis.runner.kafka;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.Uninterruptibles;
import com.kuaishou.infra.framework.kafka.KsKafkaConsumer;
import com.kuaishou.infra.framework.kafka.MessageContext;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterSimpleFactory;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarOperations;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckComplexity;
import com.kuaishou.serveree.themis.component.common.entity.CheckDuplication;
import com.kuaishou.serveree.themis.component.common.entity.CheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.CheckFile;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileMeasures;
import com.kuaishou.serveree.themis.component.common.entity.CheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.CheckMeasures;
import com.kuaishou.serveree.themis.component.constant.platform.Index;
import com.kuaishou.serveree.themis.component.constant.sonar.SonarMetricsEnum;
import com.kuaishou.serveree.themis.component.entity.kafka.ScanFileMeasuresNotify;
import com.kuaishou.serveree.themis.component.entity.platform.DuplicationDetail;
import com.kuaishou.serveree.themis.component.entity.platform.DuplicationFileDetail;
import com.kuaishou.serveree.themis.component.entity.platform.FileDetail;
import com.kuaishou.serveree.themis.component.entity.platform.FileDuplication;
import com.kuaishou.serveree.themis.component.entity.platform.FileInfo;
import com.kuaishou.serveree.themis.component.entity.platform.FileIssue;
import com.kuaishou.serveree.themis.component.entity.platform.FileMeasure;
import com.kuaishou.serveree.themis.component.entity.platform.FileState;
import com.kuaishou.serveree.themis.component.entity.platform.FileState.ComplexityState;
import com.kuaishou.serveree.themis.component.entity.platform.FileState.DuplicationState;
import com.kuaishou.serveree.themis.component.entity.platform.FileState.IssueState;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarComponent;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarMeasures;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.DuplicationListResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.DuplicationListResp.Block;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.DuplicationListResp.BlockItem;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.DuplicationListResp.FileItem;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckComplexityService;
import com.kuaishou.serveree.themis.component.service.CheckDuplicationService;
import com.kuaishou.serveree.themis.component.service.CheckExecutionService;
import com.kuaishou.serveree.themis.component.service.CheckIssueService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresSnapshotService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformFileMeasureService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-28
 */
@Service
@Slf4j
public class PlatformScanResultSaveFileDataConsumer implements KsKafkaConsumer<String> {

    @Value("${kafka.serveree_quality_scan_result_file_data_topic}")
    private String topic;

    @Value("${kafka.platform_sonar_interactive_group}")
    private String group;

    @Resource
    private CheckBaseService checkBaseService;

    @Resource
    private CheckIssueService checkIssueService;

    @Resource
    private PlatformFileMeasureService platformFileMeasureService;

    @Resource
    private CheckComplexityService checkComplexityService;

    @Resource
    private CheckDuplicationService checkDuplicationService;

    @Autowired
    private SonarClusterSimpleFactory sonarClusterSimpleFactory;

    @Autowired
    @Qualifier("saveScanResultOfFileDataExecutor")
    private ThreadPoolExecutor saveScanResultOfFileDataExecutor;

    @Autowired
    private CheckMeasuresService checkMeasuresService;

    @Autowired
    private CheckMeasuresSnapshotService checkMeasuresSnapshotService;

    @Autowired
    private CheckExecutionService checkExecutionService;

    private static final int SLEEP_MS = 500;

    @Override
    public void consume(String message, MessageContext context) {
        Uninterruptibles.sleepUninterruptibly(SLEEP_MS, TimeUnit.MILLISECONDS);
        saveScanResultOfFileDataExecutor.execute(() -> {
            log.info("PlatformScanResultSaveFileDataConsumer, start consume, message: {}", message);
            try {
                process(message);
            } catch (Exception e) {
                log.error("PlatformScanResultSaveFileDataConsumer, consume error, message: {}", message, e);
            }
            log.info("PlatformScanResultSaveFileDataConsumer, consume completed, message: {}", message);
        });
    }

    @NotNull
    @Override
    public String topic() {
        return topic;
    }

    @NotNull
    @Override
    public String consumerGroup() {
        return group;
    }

    public void process(String message) {
        ScanFileMeasuresNotify notify = JSONUtils.deserialize(message, ScanFileMeasuresNotify.class);
        Long checkBaseId = notify.getCheckBaseId();
        CheckBase checkBase = checkBaseService.getById(checkBaseId);
        // 离线hook失败
        if (Objects.isNull(checkBase) || !checkBase.getIsLast()) {
            log.error("PlatformScanResultSaveFileDataConsumer, invalid checkBaseId, message is {}", message);
            return;
        }
        String projectKey = notify.getProjectKey();
        SonarOperations clusterOperation = sonarClusterSimpleFactory.getClusterOperations(notify.getGitProjectId());
        String sonarBranch = notify.getSonarBranch();
        List<CheckIssue> checkIssueList = checkIssueService.listByCheckBaseId(checkBaseId);
        // 文件维度的issue、measure、duplication数据
        FileInfo fileInfo = buildFileInfo(checkIssueList, clusterOperation, projectKey,
                sonarBranch);
        FileDuplication fileDuplication =
                buildFileDuplication(clusterOperation, projectKey, fileInfo.getFileList(),
                        sonarBranch);
        Pair<Map<String, CheckFile>, List<CheckFileMeasures>> pair =
                platformFileMeasureService.saveScanResultsForFiles(checkBase, fileInfo, fileDuplication);
        Map<String, CheckFile> path2CheckFileMap = pair.getLeft();
        // 更新相关表中fileId字段
        LocalDateTime now = LocalDateTime.now();
        List<CheckComplexity> checkComplexities = checkComplexityService.listByCheckBaseId(checkBaseId);
        if (CollectionUtils.isEmpty(checkComplexities)) {
            return;
        }
        for (CheckComplexity checkComplexity : checkComplexities) {
            checkComplexity.setFileId(
                    Optional.ofNullable(path2CheckFileMap.get(checkComplexity.getLocation()))
                            .map(CheckFile::getId).orElse(0L));
            checkComplexity.setGmtModified(now);
        }
        List<CheckDuplication> checkDuplications = checkDuplicationService.listByCheckBaseId(checkBaseId);
        if (CollectionUtils.isEmpty(checkDuplications)) {
            return;
        }
        for (CheckDuplication checkDuplication : checkDuplications) {
            checkDuplication.setFileId(
                    Optional.ofNullable(path2CheckFileMap.get(checkDuplication.getLocation()))
                            .map(CheckFile::getId).orElse(0L));
            checkDuplication.setGmtModified(now);
        }
        // 生成 FileState
        FileState fileState = buildFileState(fileInfo, path2CheckFileMap, pair.getRight());
        // 保存 FileState
        CheckMeasures checkMeasures =
                CheckMeasures.builder()
                        .checkRepoId(checkBase.getCheckRepoId())
                        .checkRepoBranchId(checkBase.getCheckRepoBranchId())
                        .baseId(checkBaseId)
                        .executionId(Optional.ofNullable(checkExecutionService.getByBaseId(checkBaseId))
                                .map(CheckExecution::getId).orElse(0L))
                        .metricKey(Index.FILE_STATE.getKey())
                        .metricValue(JSONUtils.serialize(fileState))
                        .gmtCreate(now)
                        .gmtModified(now).build();
        checkMeasuresService.save(checkMeasures);
        checkMeasuresSnapshotService.saveSnapshotMeasures(List.of(checkMeasures), 0L);
        CommonUtils.processInBatches(checkComplexities, 1000, checkComplexityService::updateBatchById);
        CommonUtils.processInBatches(checkDuplications, 1000, checkDuplicationService::updateBatchById);
    }

    public FileState buildFileState(FileInfo fileInfo, Map<String, CheckFile> path2FileMap,
            List<CheckFileMeasures> checkFileMeasuresList) {
        // 计算 IssueState
        int total = fileInfo.getFileList().size();
        int healthFile =
                (int) fileInfo.getFileList().stream().filter(f -> CollectionUtils.isEmpty(f.getIssueList())).count();
        IssueState issueState = IssueState.builder()
                .healthFile(healthFile)
                .unHealthFile(total - healthFile)
                .build();
        // 计算 ComplexityState
        Map<Long, String> fileId2ComplexityMap = checkFileMeasuresList.stream()
                .filter(f -> SonarMetricsEnum.COMPLEXITY.getKey().equals(f.getMetricKey()))
                .collect(Collectors.toMap(CheckFileMeasures::getFileId, this::getCheckFileMetricValueAsString, (a, b) -> a));
        int excellentFile = 0, goodFile = 0, unqualifiedFile = 0;
        for (FileDetail fileDetail : fileInfo.getFileList()) {
            // 获取圈复杂度
            int complexity = Optional.ofNullable(
                            fileId2ComplexityMap.get(path2FileMap.get(fileDetail.getPath()).getId()))
                    .map(Integer::parseInt).orElse(0);
            if (complexity <= 5) {
                excellentFile++;
            } else if (complexity <= 10) {
                goodFile++;
            } else {
                unqualifiedFile++;
            }
        }
        ComplexityState complexityState = ComplexityState.builder()
                .excellentFile(excellentFile)
                .goodFile(goodFile)
                .unqualifiedFile(unqualifiedFile)
                .build();
        // 计算 DuplicationState
        Integer duplicateFiles = checkFileMeasuresList.stream()
                .filter(f -> SonarMetricsEnum.DUPLICATED_FILES.getKey().equals(f.getMetricKey()))
                .map(cf -> Integer.parseInt(getCheckFileMetricValueAsString(cf)))
                .reduce(Integer::sum).orElse(0);
        DuplicationState duplicationState = DuplicationState.builder()
                .totalFile(total)
                .duplicationFile(duplicateFiles)
                .build();

        return FileState.builder().issueState(issueState)
                .complexityState(complexityState)
                .duplicationState(duplicationState)
                .build();
    }

    private String getCheckFileMetricValueAsString(CheckFileMeasures checkFileMeasures) {
        try {
            String deserialize = JSONUtils.deserialize(checkFileMeasures.getMetricValue(), String.class);
            return Objects.isNull(deserialize) ? "0" : deserialize;
        } catch (Exception e) {
            return "0";
        }
    }

    public FileInfo buildFileInfo(List<CheckIssue> checkIssues, SonarOperations clusterOperations,
            String projectKey, String sonarBranchName) {
        // List<CheckIssue> 根据 file path 分组 <path, List<CheckIssue>> --> <path, List<FileIssue>>
        Map<String, List<FileIssue>> path2FileIssueMap = checkIssues
                .stream()
                .collect(Collectors.groupingBy(CheckIssue::getLocation))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(
                        e -> e.getKey(),
                        e -> e.getValue().stream().map(FileIssue::convertFromCheckIssue).collect(Collectors.toList()),
                        (existing, replacement) -> existing));
        // 得到各个文件各项指标
        List<String> allMetricKeys = SonarMetricsEnum.getAllMetricKeys();
        Map<String, SonarComponent> path2MeasureMap =
                clusterOperations.getAllComponentsMeasures(projectKey, sonarBranchName, allMetricKeys);
        // 组合FileDetail
        List<FileDetail> fileDetailList = path2MeasureMap.entrySet().stream().map(e -> {
            // 文件路径
            String path = e.getKey();
            // 文件 issue列表
            List<FileIssue> issues = path2FileIssueMap.getOrDefault(path, Collections.emptyList());
            // 文件sonar measures
            List<SonarMeasures> sonarMeasures = e.getValue().getMeasures();
            // sonar measures -> file measures
            List<FileMeasure> fileMeasures = sonarMeasures.stream().map(sm -> {
                return FileMeasure.builder()
                        .metricKey(sm.getMetric()).metricValue(sm.getValue())
                        .build();
            }).collect(Collectors.toList());
            // 组合，先不保存 funtions 数据 TODO
            return FileDetail.builder()
                    .path(path)
                    .issueList(issues)
                    .fileMeasures(fileMeasures)
                    .functions(Collections.emptyList())
                    .build();
        }).collect(Collectors.toList());

        return FileInfo.builder().fileList(fileDetailList).build();
    }

    public FileDuplication buildFileDuplication(SonarOperations clusterOperations, String projectKey,
            List<FileDetail> fileList, String sonarBranchName) {
        Set<DuplicationFileDetail> fileBlockSet = Sets.newHashSet();
        List<DuplicationDetail> duplicationDetailList = Lists.newArrayList();
        // 遍历文件组件
        for (FileDetail file : fileList) {
            // 跳过没有重复块的
            if (!containsDuplicateBlock(file)) {
                continue;
            }
            String key = projectKey + ":" + file.getPath();
            // 此文件存在重复的所有块
            DuplicationListResp resp = clusterOperations.sonarApi().getFileDuplication(key, sonarBranchName);
            List<Block> fileDuplications = resp.getDuplications();
            if (CollectionUtils.isEmpty(fileDuplications)) {
                continue;
            }
            Map<String, FileItem> ref2FileMap = resp.getFiles();
            // 遍历
            for (Block block : fileDuplications) {
                // 这些block互相重复
                List<BlockItem> blockItems = block.getBlocks();
                // BlockItem --> DuplicationFileDetail
                List<DuplicationFileDetail> fileBlockList = blockItems.stream().map(bi -> {
                    DuplicationFileDetail fileBlock = new DuplicationFileDetail();
                    fileBlock.setStartLine(bi.getFrom());
                    fileBlock.setEndLine(bi.getFrom() + bi.getSize() - 1);
                    fileBlock.setPath(ref2FileMap.get(bi.get_ref()).getName());
                    fileBlock.setStartOffset(0);
                    fileBlock.setEndOffset(0);
                    return fileBlock;
                }).collect(Collectors.toList());
                // 已经统计过
                if (fileBlockSet.contains(fileBlockList.get(0))) {
                    continue;
                }
                // 此次重复块内容
                DuplicationDetail duplicationDetail = DuplicationDetail.builder().fileList(fileBlockList).build();
                duplicationDetailList.add(duplicationDetail);
                // 标记这些块已统计过
                fileBlockSet.addAll(fileBlockList);
            }
        }
        return FileDuplication.builder().duplicationList(duplicationDetailList).build();
    }

    private boolean containsDuplicateBlock(FileDetail file) {
        if (CollectionUtils.isEmpty(file.getFileMeasures())) {
            return false;
        }
        Map<String, FileMeasure> fileMeasureMap = file.getFileMeasures().stream()
                .collect(Collectors.toMap(FileMeasure::getMetricKey, Function.identity(), (existing, replacement) -> existing));
        if (!fileMeasureMap.containsKey(SonarMetricsEnum.DUPLICATED_BLOCKS.getKey())) {
            return false;
        }
        String value = (String) fileMeasureMap.get(SonarMetricsEnum.DUPLICATED_BLOCKS.getKey()).getMetricValue();
        return Integer.parseInt(value) > 0;
    }
}
