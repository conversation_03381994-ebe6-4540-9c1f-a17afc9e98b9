package com.kuaishou.serveree.themis.runner.task;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.annotation.Nonnull;
import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.infra.scheduler.client.ShortFixDelayTask;
import com.kuaishou.infra.scheduler.client.TaskContext;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.service.MarkLabelService;
import com.kuaishou.serveree.themis.component.service.TaskService;

import kuaishou.common.BizDef;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/05/21 10:40 上午
 */
@Component
@Slf4j
public class KspProductAddLabelTask implements ShortFixDelayTask {

    @Autowired
    private TaskService taskService;

    @Resource
    private ThreadPoolExecutor kspAddLabelExecutor;

    @Autowired
    private KsRedisLock ksRedisLock;

    @Autowired
    private MarkLabelService markLabelService;


    @Nonnull
    @Override
    public String name() {
        return "serveree_ksp_product_add_label_task";
    }

    @Override
    public long run(@Nonnull TaskContext context) {
        execute(context);
        return DEFAULT_DELAY;
    }

    /**
     * 产品打标定时任务
     *
     * @param context TaskContext
     */
    @Override
    public void execute(@Nonnull TaskContext context) {
        boolean lock = ksRedisLock.lock(KsRedisPrefixConstant.KSP_PIPELINE_LABEL_SYNC);
        if (!lock) {
            return;
        }
        List<com.kuaishou.serveree.themis.component.common.entity.Task> unLabelTasks = taskService.listUnLabelTasks();
        if (CollectionUtils.isEmpty(unLabelTasks)) {
            return;
        }
        CountDownLatch countDownLatch = new CountDownLatch(unLabelTasks.size());
        unLabelTasks.forEach(task -> kspAddLabelExecutor.execute(() -> {
            try {
                markLabelService.addLabels(task);
            } catch (Exception e) {
                log.error("productAddLabel taskMarks forEach error,task is {}", task, e);
            } finally {
                countDownLatch.countDown();
            }
        }));
        try {
            countDownLatch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("ProductAddLabelCompensateTask productAddLabel countDownLatch await exception", e);
            Thread.currentThread().interrupt();
        } finally {
            ksRedisLock.unlock(KsRedisPrefixConstant.KSP_PIPELINE_LABEL_SYNC);
        }
    }

    @Nonnull
    @Override
    public BizDef bizDef() {
        return BizDef.SERVER_EE;
    }


}
