package com.kuaishou.serveree.themis.runner.task;

import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.infra.scheduler.client.Task;
import com.kuaishou.infra.scheduler.client.TaskContext;
import com.kuaishou.serveree.themis.component.service.platform.generate.CommonUniqIdGenerator;

import kuaishou.common.BizDef;

/**
 * <AUTHOR>
 * @since 2023/7/3 5:11 PM
 */

@Component
public class GenerateCommonUniqIdTask implements Task {

    @Autowired
    private CommonUniqIdGenerator  commonUniqIdGenerator;

    @NotNull
    @Override
    public String name() {
        return "GenerateCommonUniqIdTask";
    }

    @Override
    public void execute(@NotNull TaskContext context) {
        commonUniqIdGenerator.generateCommonUniqId();
    }

    @NotNull
    @Override
    public BizDef bizDef() {
        return BizDef.SERVER_EE;
    }

}
