package com.kuaishou.serveree.themis.runner.task;

import javax.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.infra.scheduler.client.ShortFixDelayTask;
import com.kuaishou.infra.scheduler.client.TaskContext;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.service.check.QualityTaskKspSyncService;

import kuaishou.common.BizDef;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/05/21 10:40 上午
 */
@Component
@Slf4j
public class QualityTaskKspSyncTask implements ShortFixDelayTask {

    public static final Kconf<Boolean> COMPENSATE_TAG =
            Kconfs.ofBoolean("qa.themis.kspPipelinecompensateTag", true).build();

    @Autowired
    private QualityTaskKspSyncService qualityTaskKspSyncService;

    @Autowired
    private KsRedisLock ksRedisLock;


    @Nonnull
    @Override
    public String name() {
        return "serveree_quality_task_ksp_sync_task";
    }

    @Override
    public long run(@Nonnull TaskContext context) {
        execute(context);
        return DEFAULT_DELAY;
    }

    /**
     * KSP流水线执行结果同步定时任务
     *
     * @param context TaskContext
     */
    @Override
    public void execute(@Nonnull TaskContext context) {
        if (!COMPENSATE_TAG.get()) {
            return;
        }
        log.info("kspPipelineSync start status sync");
        boolean lock = ksRedisLock.lock(KsRedisPrefixConstant.KSP_PIPELINE_JOB_SYNC);
        if (!lock) {
            log.info("kspPipelineSync obtain lock fail,skip");
            return;
        }
        try {
            qualityTaskKspSyncService.kspPipelineSync();
        } catch (Exception e) {
            log.error("kspPipelineSync job error", e);
        } finally {
            ksRedisLock.unlock(KsRedisPrefixConstant.KSP_PIPELINE_JOB_SYNC);
        }
    }

    @Nonnull
    @Override
    public BizDef bizDef() {
        return BizDef.SERVER_EE;
    }


}
