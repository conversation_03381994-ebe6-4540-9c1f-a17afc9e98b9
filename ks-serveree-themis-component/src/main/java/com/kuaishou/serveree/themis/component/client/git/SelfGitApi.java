package com.kuaishou.serveree.themis.component.client.git;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.gitlab.api.GitlabAPI;
import org.gitlab.api.models.GitlabBranch;
import org.gitlab.api.models.GitlabGroup;
import org.gitlab.api.models.GitlabProject;
import org.gitlab.api.models.GitlabUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.annotation.Kconfig;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.entity.git.BlameInfo;
import com.kuaishou.serveree.themis.component.entity.git.BlameInfoRequest;
import com.kuaishou.serveree.themis.component.entity.git.GetGitBranchRequest;
import com.kuaishou.serveree.themis.component.entity.git.GetUserProjectsRequest;
import com.kuaishou.serveree.themis.component.entity.git.ProjectFilePathListInfo;
import com.kuaishou.serveree.themis.component.entity.git.SelfGitDetailRequest;
import com.kuaishou.serveree.themis.component.entity.git.SelfGitProject;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/10/30 6:52 下午
 */
@Component
@Slf4j
public class SelfGitApi {

    @Kconfig("qa.themis.gitlabToken")
    private Kconf<String> gitlabToken;

    @Value("${gitlab.host-url}")
    private String gitUrl;

    @Value("${gitlab.timeout}")
    private Integer timeout;

    @Autowired
    private GitlabAPI gitlabApi;

    public Map<String, Double> getLanguagesPercentage(Integer projectId) {
        String resultResp = HttpRequest
                .get(gitUrl + "/api/v4/projects/" + projectId + "/languages")
                .header("private-token", gitlabToken.get())
                .timeout(timeout)// 超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            throw new RuntimeException("调用git服务查询项目列表返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, new TypeReference<>() {
        });
    }

    public List<GitlabProject> listAllProjectsByGroupIdWithSubGroups(Integer groupId) {
        List<Integer> allSubGroupIds = this.listAllSubGroupIds(groupId);
        allSubGroupIds.add(groupId);
        List<GitlabProject> allProjects = com.google.common.collect.Lists.newArrayList();
        allSubGroupIds.forEach(subGroupId -> {
            List<GitlabProject> groupProjects = gitlabApi.getGroupProjects(subGroupId);
            if (CollectionUtils.isNotEmpty(groupProjects)) {
                allProjects.addAll(groupProjects);
            }
        });
        return allProjects;
    }

    public List<GitlabProject> listAllProjectsByGroupIdWithSubGroups(Integer groupId,
            List<Integer> excludeSubGroupIds) {
        List<Integer> allSubGroupIds = this.listAllSubGroupIds(groupId, excludeSubGroupIds);
        allSubGroupIds.add(groupId);
        List<GitlabProject> allProjects = Lists.newArrayList();
        for (Integer subGroupId : allSubGroupIds) {
            List<GitlabProject> groupProjects = gitlabApi.getGroupProjects(subGroupId);
            if (CollectionUtils.isNotEmpty(groupProjects)) {
                allProjects.addAll(groupProjects);
            }
        }
        return allProjects;
    }

    public List<Integer> listAllSubGroupIds(Integer parentGroupId) {
        int page = 1;
        int pageSize = 100;
        List<Integer> allGroupIds = Lists.newArrayList();
        List<Integer> groupIds;
        do {
            groupIds = this.listAllSubGroups(parentGroupId, page++, pageSize);
            allGroupIds.addAll(groupIds);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                for (Integer groupId : groupIds) {
                    allGroupIds.addAll(this.listAllSubGroupIds(groupId));
                }
            }
        } while (CollectionUtils.isNotEmpty(groupIds));
        return allGroupIds;
    }

    public List<Integer> listAllSubGroupIds(Integer parentGroupId, List<Integer> excludeGroupIds) {
        int page = 1;
        int pageSize = 100;
        List<Integer> allGroupIds = Lists.newArrayList();
        List<Integer> groupIds;
        do {
            groupIds = this.listAllSubGroups(parentGroupId, page++, pageSize);
            if (CollectionUtils.isNotEmpty(excludeGroupIds)) {
                groupIds.removeIf(excludeGroupIds::contains);
            }
            allGroupIds.addAll(groupIds);
            if (CollectionUtils.isNotEmpty(groupIds)) {
                for (Integer groupId : groupIds) {
                    allGroupIds.addAll(this.listAllSubGroupIds(groupId, excludeGroupIds));
                }
            }
        } while (CollectionUtils.isNotEmpty(groupIds));
        return allGroupIds;
    }

    public List<Integer> listAllSubGroups(Integer parentGroupId, Integer page, Integer pageSize) {
        String resultResp = HttpRequest
                .get(gitUrl + "/api/v4/groups/" + parentGroupId + "/subgroups?page=" + page + "&per_page="
                        + pageSize)
                .header("private-token", gitlabToken.get())
                .timeout(timeout)// 超时，毫秒
                .execute()
                .body();
        List<GitlabGroup> gitlabGroups = JSONUtils.deserializeList(resultResp, GitlabGroup.class);
        if (CollectionUtils.isEmpty(gitlabGroups)) {
            return Collections.emptyList();
        }
        return gitlabGroups.stream().map(GitlabGroup::getId).collect(Collectors.toList());
    }

    /**
     * 获取gitlab用户
     *
     * @param page Integer
     * @param pageSize Integer
     * @return List<GitlabUser>
     */
    public List<GitlabUser> getUsers(Integer page, Integer pageSize) {
        String resultResp = HttpRequest
                .get(gitUrl + "/api/v4/users?page=" + page + "&per_page=" + pageSize)
                .header("private-token", gitlabToken.get())
                .timeout(timeout)// 超时，毫秒
                .execute()
                .body();
        List<GitlabUser> gitlabUsers = JSONUtils.deserializeList(resultResp, GitlabUser.class);
        if (CollectionUtils.isEmpty(gitlabUsers)) {
            return Collections.emptyList();
        }
        return gitlabUsers;
    }

    public List<GitlabProject> getUserProjects(GetUserProjectsRequest request) {
        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("page", request.getPage());
        reqMap.put("per_page", request.getPageSize());
        if (request.getSimple() != null) {
            reqMap.put("simple", request.getSimple());
        }
        if (StringUtils.isNotEmpty(request.getSearch())) {
            reqMap.put("search", request.getSearch());
        }
        HttpRequest httpRequest = HttpRequest
                .get(gitUrl + "/api/v4/projects")
                .form(reqMap)
                .header("private-token", gitlabToken.get())
                .timeout(timeout);
        if (StringUtils.isNotEmpty(request.getUsername())) {
            httpRequest.header("Sudo", request.getUsername());
        }
        String resultResp = httpRequest.execute().body();
        List<GitlabProject> gitlabProjects = JSONUtils.deserializeList(resultResp, GitlabProject.class);
        if (CollectionUtils.isEmpty(gitlabProjects)) {
            return Collections.emptyList();
        }
        return gitlabProjects;
    }

    public List<GitlabBranch> getGitBranches(GetGitBranchRequest request) {
        Map<String, Object> reqMap = Maps.newHashMap();
        if (StringUtils.isNotEmpty(request.getSearch())) {
            reqMap.put("search", request.getSearch());
        }
        reqMap.put("page", request.getPage());
        reqMap.put("per_page", request.getPageSize());
        String resultResp = HttpRequest
                .get(gitUrl + "/api/v4/projects/" + request.getGitProjectId() + "/repository/branches")
                .form(reqMap)
                .header("private-token", gitlabToken.get())
                .timeout(timeout)
                .execute()
                .body();
        List<GitlabBranch> gitlabBranches = JSONUtils.deserializeList(resultResp, GitlabBranch.class);
        if (CollectionUtils.isEmpty(gitlabBranches)) {
            return Collections.emptyList();
        }
        return gitlabBranches;
    }

    public String getHighlightContent(String fileName, String content) {
        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("file_name", fileName);
        reqMap.put("content", content);
        String resultResp = HttpRequest
                .post(gitUrl + "/api/v4/highlight_code")
                .body(JSONUtils.serialize(reqMap))
                .header("private-token", gitlabToken.get())
                .timeout(timeout)
                .execute()
                .body();
        Map<String, String> highlightMap = JSONUtils.deserializeMap(resultResp, String.class, String.class);
        return highlightMap.get("rich_text");
    }

    public List<BlameInfo> getFileBlameInfo(BlameInfoRequest request) {
        Map<String, Object> reqMap = Maps.newHashMap();
        if (StringUtils.isNotEmpty(request.getRef())) {
            reqMap.put("ref", request.getRef());
        }
        String formatUrl = String.format("%s/api/v4/projects/%s/repository/files/%s/blame", gitUrl,
                request.getGitProjectId(),
                CommonUtils.encode(request.getFilePath()));
        String resultResp = HttpRequest
                .get(formatUrl)
                .form(reqMap)
                .header("private-token", gitlabToken.get())
                .timeout(timeout)
                .execute()
                .body();
        return JSONUtils.deserializeList(resultResp, BlameInfo.class);
    }

    public SelfGitProject getProjectDetail(SelfGitDetailRequest request) {
        Map<String, Object> reqMap = Maps.newHashMap();
        if (request.getStatistics()) {
            reqMap.put("statistics", true);
        }
        String resultResp = HttpRequest
                .get(gitUrl + "/api/v4/projects/" + request.getGitProjectId())
                .form(reqMap)
                .header("private-token", gitlabToken.get())
                .timeout(timeout)
                .execute()
                .body();
        return JSONUtils.deserialize(resultResp, SelfGitProject.class);
    }

    /**
     * 从gitlab获取文件列表
     */
    public List<String> getProjectFilePathList(Integer gitProjectId, String commitId) {
        Map<String, Object> reqMap = Map.of("ref", commitId);
        String url = String.format("%s/api/v4/projects/%s/repository/file_list", gitUrl, gitProjectId);
        HttpRequest httpRequest = HttpRequest
                .get(url)
                .form(reqMap)
                .header("private-token", gitlabToken.get())
                .timeout(timeout);
        try (HttpResponse response = httpRequest.execute()) {
            if (!response.isOk()) {
                log.error("获取项目文件列表失败：url: {}, req: {}, resp: {}", url, reqMap, response);
                throw new ThemisException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取项目文件列表失败");
            }
            ProjectFilePathListInfo deserialize = JSONUtils.deserialize(response.body(), ProjectFilePathListInfo.class);
            if (Objects.isNull(deserialize)) {
                log.error("获取项目文件列表失败：url: {}, req: {}, resp: {}", url, reqMap, response);
                throw new ThemisException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "获取项目文件列表失败");
            }
            return Objects.nonNull(deserialize.getPaths()) ? deserialize.getPaths() : Collections.emptyList();
        } catch (HttpException e) {
            log.error("获取项目文件列表失败：url: {}, req: {}", url, reqMap, e);
            throw new RuntimeException(e);
        }
    }
}
