package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MrStuckRecord对象", description = "")
public class MrStuckRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "仓库id")
    private Integer gitProjectId;

    @ApiModelProperty(value = "源分支")
    private String sourceBranch;

    @ApiModelProperty(value = "目标分支")
    private String targetBranch;

    @ApiModelProperty(value = "mrid")
    private Integer mrId;

    @ApiModelProperty(value = "kdev流水线id")
    private Long kdevPipelineId;

    @ApiModelProperty(value = "kdev流水线构建id")
    private Long kdevBuildId;

    @ApiModelProperty(value = "kdev流水线记录")
    private String kdevBuildUrl;

    @ApiModelProperty(value = "天琴流水线构建id")
    private Long kspBuildId;

    @ApiModelProperty(value = "触发时的commitId")
    private String commitId;

    @ApiModelProperty(value = "触发时设置的卡点等级")
    private String stuckSeverity;

    @ApiModelProperty(value = "是否只看diff内的issue")
    private Boolean onlyDiff;

    @ApiModelProperty(value = "kdev流水线执行状态")
    private Integer status;

    @ApiModelProperty(value = "kdev流水线执行状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "mr标题")
    private String mrTitle;

    @ApiModelProperty(value = "mr创建者")
    private String mrCreator;

    @ApiModelProperty(value = "mr地址")
    private String mrUrl;
}
