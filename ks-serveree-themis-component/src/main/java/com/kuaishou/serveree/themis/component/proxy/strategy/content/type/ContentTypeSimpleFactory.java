package com.kuaishou.serveree.themis.component.proxy.strategy.content.type;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.hutool.http.ContentType;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-17
 */
@Component
public class ContentTypeSimpleFactory {
    @Autowired
    private FormURLEncodedStrategy formURLEncodedStrategy;
    @Autowired
    private JsonStrategy jsonStrategy;
    @Autowired
    private MultiPartStrategy multiPartStrategy;

    private static Map<String, ContentTypeStrategy> contentTypeMap;

    @PostConstruct
    public void init() {
        contentTypeMap = new HashMap<>();
        contentTypeMap.put(ContentType.FORM_URLENCODED.getValue(), formURLEncodedStrategy);
        contentTypeMap.put(ContentType.JSON.getValue(), jsonStrategy);
        contentTypeMap.put(ContentType.MULTIPART.getValue(), multiPartStrategy);
    }

    public ContentTypeStrategy getInstance(String contentType) {
        ContentTypeStrategy strategy = contentTypeMap.get(contentType);
        if (strategy != null) {
            return strategy;
        }
        return contentTypeMap.entrySet().stream()
                .filter(entry -> contentType.contains(entry.getKey()))
                .findFirst()
                .map(Map.Entry::getValue)
                .orElse(null);
    }
}
