package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Facet;
import com.kuaishou.serveree.themis.component.entity.sonar.Issue;
import com.kuaishou.serveree.themis.component.entity.sonar.Paging;
import com.kuaishou.serveree.themis.component.entity.sonar.Rule;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/21 3:27 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IssuesResp {
    private Integer p;

    private Integer ps;

    private Integer total;

    private Paging paging;

    private List<Issue> issues;

    private List<Facet> facets;

    private List<Rule> rules;
}
