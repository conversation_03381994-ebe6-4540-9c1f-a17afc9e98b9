package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Profiles;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProfileCreateResp {

    private Profiles profile;

    private List<String> warnings;
}
