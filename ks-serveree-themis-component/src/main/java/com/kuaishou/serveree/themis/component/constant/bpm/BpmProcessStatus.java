package com.kuaishou.serveree.themis.component.constant.bpm;

import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-08
 */
@Getter
public enum BpmProcessStatus {

    AUDIT("Audit", "审批中"),
    END("End", "审批结束"),
    TASK_CREATE("Rejected", "拒绝"),
    TASK_COMPLETE("Termination", "终止"),
    ;

    private final String key;
    private final String desc;

    BpmProcessStatus(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
