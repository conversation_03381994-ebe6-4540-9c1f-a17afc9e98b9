package com.kuaishou.serveree.themis.component.entity.plugin;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/1/18 6:15 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginWhitelistSettings {

    private Map<String, List<Integer>> ruleIdProjectListMap;

    private Map<String, Map<Integer, List<String>>> ruleIdProjectIdArtifactIdsMap;

}
