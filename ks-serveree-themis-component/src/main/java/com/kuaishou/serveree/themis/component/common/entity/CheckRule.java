package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import org.apache.commons.lang3.StringUtils;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckRule对象", description = "")
public class CheckRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "规则key")
    private String ruleKey;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "规则描述")
    private String description;

    @ApiModelProperty(value = "类别")
    private String ruleType;

    @ApiModelProperty(value = "严重级别")
    private String severity;

    @ApiModelProperty(value = "是否删除")
    private Boolean deleted;

    private String ruleLink;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    private String language;

    private String scanner;

    private String name;

    private String htmlDesc;

    private String htmlDescZh;

    private String nameZh;

    private Boolean canSkip;

    public String getName() {
        return StringUtils.isNotBlank(nameZh) ? nameZh : name;
    }

    public String getHtmlDesc() {
        return StringUtils.isNotBlank(htmlDescZh) ? htmlDescZh : htmlDesc;
    }

    public String getOriginName() {
        return name;
    }

    public String getOriginHtmlDesc() {
        return htmlDesc;
    }

}
