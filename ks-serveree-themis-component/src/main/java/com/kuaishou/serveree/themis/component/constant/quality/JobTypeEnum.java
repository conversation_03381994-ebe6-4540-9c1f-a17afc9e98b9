package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2020/11/13 8:19 下午
 */
@AllArgsConstructor
public enum JobTypeEnum {

    LOCAL("本地调用"),
    KSP_PIPELINE("ksp流水线调用"),
    ;

    @Getter
    @Setter
    private String display;

    private static final Map<String, JobTypeEnum> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(JobTypeEnum.values()).forEach(jobTypeEnum ->
                ENUM_MAP.put(jobTypeEnum.name(), jobTypeEnum)
        );
    }

    public static JobTypeEnum getEnumByEnumName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return ENUM_MAP.get(name);
    }

}
