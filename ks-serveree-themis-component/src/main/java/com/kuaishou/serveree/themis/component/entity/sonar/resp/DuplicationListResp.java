package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
// CHECKSTYLE:OFF
public class DuplicationListResp {

    private List<Block> duplications;

    private Map<String, FileItem> files;

    @Data
    public static class Block {
        private List<BlockItem> blocks;
    }

    @Data
    public static class BlockItem {
        private int from;
        private int size;
        private String _ref;
    }

    @Data
    public static class FileItem {
        private String key;
        private String name;
        private String uuid;
        private String project;
        private String projectUuid;
        private String projectName;
    }
}
