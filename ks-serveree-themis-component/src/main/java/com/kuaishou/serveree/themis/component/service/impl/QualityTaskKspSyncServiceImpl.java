package com.kuaishou.serveree.themis.component.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.service.QualityCheckService;
import com.kuaishou.serveree.themis.component.service.QualityTaskKspSyncService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-05-18
 */

@Service
@Slf4j
public class QualityTaskKspSyncServiceImpl implements QualityTaskKspSyncService {
    @Autowired
    private QualityCheckService qualityCheckService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private KsRedisLock ksRedisLock;

    @Autowired
    private TaskConfigService taskConfigService;

    @Override
    public ThemisResponse<?> syncKspResult(Long pipelineId, Long buildId) {
        log.info("syncKspResult start");
        String lockString = String.format("%s:%s:%s", KsRedisPrefixConstant.KSP_PIPELINE_JOB_SYNC, pipelineId, buildId);
        boolean lock = ksRedisLock.spinLock(lockString);
        if (!lock) {
            log.info("syncKspResult obtain lock fail");
            return ThemisResponse.fail(ResultCodeConstant.SYNC_FAIL);
        }
        try {
            Map<Long, TaskConfig> taskConfMap = taskConfigService.getTasksBySponsorPipelineInfo(pipelineId, buildId)
                    .stream().collect(Collectors.toMap(TaskConfig::getTaskId, Function.identity(), (existing, replacement) -> existing));
            if (MapUtils.isEmpty(taskConfMap)) {
                return ThemisResponse.fail(ResultCodeConstant.TASK_NOT_EXIST);
            }

            List<Long> taskIdList = new ArrayList<>(taskConfMap.keySet());

            Map<Long, Task> taskMap = taskService.listByIds(taskIdList).stream()
                    .collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> existing));
            taskIdList.forEach(taskId -> {
                        TaskConfig taskConf = taskConfMap.get(taskId);
                        Task task = taskMap.get(taskId);
                        String taskStatus = task.getTaskStatus();
                        if (!TaskStatusEnum.SUCCESS.name().equals(taskStatus)
                                && !TaskStatusEnum.FAIL.name().equals(taskStatus)) {
                            qualityCheckService.syncKspPipelineResult(task, taskConf);
                        }
                    }
            );
            return ThemisResponse.success(taskIdList.toString());
        } catch (Exception e) {
            log.error("syncKspResult error", e);
            return ThemisResponse.fail(ResultCodeConstant.SYNC_FAIL);
        } finally {
            ksRedisLock.unlock(lockString);
        }
    }
}
