package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/5/25 10:34 AM
 */
@AllArgsConstructor
public enum ScannerIssueTypeMeasureKeyMapping {

    BUG_MSN(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner(), "BUG", "bugs"),
    VULNERABILITY_MSN(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner(), "VULNERABILITY", "vulnerabilities"),
    CODE_SMELL_MSN(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner(), "CODE_SMELL", "code_smells"),
    BUG_SKYEYE(PlatformScannerEnum.SKY_EYE.getScanner(), "BUG", "bugs"),
    VULNERABILITY_SKYEYE(PlatformScannerEnum.SKY_EYE.getScanner(), "VULNERABILITY", "vulnerabilities"),
    CODE_SMELL_SKYEYE(PlatformScannerEnum.SKY_EYE.getScanner(), "CODE_SMELL", "code_smells"),
    BUG_SSN(PlatformScannerEnum.SONAR_SCANNER_NEW.getScanner(), "BUG", "bugs"),
    VULNERABILITY_SSN(PlatformScannerEnum.SONAR_SCANNER_NEW.getScanner(), "VULNERABILITY", "vulnerabilities"),
    CODE_SMELL_SSN(PlatformScannerEnum.SONAR_SCANNER_NEW.getScanner(), "CODE_SMELL", "code_smells"),
    BUG_COVERITY(PlatformScannerEnum.COVERITY_SCANNER.getScanner(), "BUG", "bugs"),
    VULNERABILITY_COVERITY(PlatformScannerEnum.COVERITY_SCANNER.getScanner(), "VULNERABILITY", "vulnerabilities"),
    CODE_SMELL_COVERITY(PlatformScannerEnum.COVERITY_SCANNER.getScanner(), "CODE_SMELL", "code_smells"),
    ;

    @Getter
    private final String scanner;
    @Getter
    private final String type;
    @Getter
    private final String metricKey;

    private static final Map<String, Map<String, ScannerIssueTypeMeasureKeyMapping>> TYPE_MAP = Maps.newHashMap();

    static {
        for (ScannerIssueTypeMeasureKeyMapping value : ScannerIssueTypeMeasureKeyMapping.values()) {
            Map<String, ScannerIssueTypeMeasureKeyMapping> typeMappingMap =
                    CommonUtils.getOrCreate(value.getScanner(), TYPE_MAP, Maps::newHashMap);
            typeMappingMap.put(value.getType(), value);
        }
    }

    public static String getMetricKeyByScannerAndType(String scanner, String type) {
        Map<String, ScannerIssueTypeMeasureKeyMapping> typeMeasureKeyMappingMap = TYPE_MAP.get(scanner);
        if (MapUtils.isEmpty(typeMeasureKeyMappingMap)) {
            return "";
        }
        ScannerIssueTypeMeasureKeyMapping keyMapping = typeMeasureKeyMappingMap.get(type);
        if (keyMapping == null) {
            return "";
        }
        return keyMapping.getMetricKey();
    }
}
