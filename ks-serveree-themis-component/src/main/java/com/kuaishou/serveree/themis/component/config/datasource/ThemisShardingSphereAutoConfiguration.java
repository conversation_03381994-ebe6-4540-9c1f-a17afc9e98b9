package com.kuaishou.serveree.themis.component.config.datasource;

import java.sql.SQLException;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.sql.DataSource;

import org.apache.shardingsphere.driver.api.ShardingSphereDataSourceFactory;
import org.apache.shardingsphere.infra.config.RuleConfiguration;
import org.apache.shardingsphere.infra.config.mode.ModeConfiguration;
import org.apache.shardingsphere.infra.yaml.config.swapper.mode.ModeConfigurationYamlSwapper;
import org.apache.shardingsphere.spring.boot.prop.SpringBootPropertiesConfiguration;
import org.apache.shardingsphere.spring.boot.rule.LocalRulesCondition;
import org.apache.shardingsphere.spring.boot.schema.SchemaNameSetter;
import org.apache.shardingsphere.spring.transaction.TransactionTypeScanner;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import lombok.Generated;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-27
 *
 * Themis ShardingSphere auto configuration.
 * 拷贝自 org.apache.shardingsphere.spring.boot.autoconfigure.ShardingSphereAutoConfiguration
 */
@Configuration
@ComponentScan({"org.apache.shardingsphere.spring.boot.converter"})
@EnableConfigurationProperties({SpringBootPropertiesConfiguration.class})
@ConditionalOnProperty(
        prefix = "spring.shardingsphere",
        name = {"enabled"},
        havingValue = "true",
        matchIfMissing = true
)
@AutoConfigureBefore({DataSourceAutoConfiguration.class})
public class ThemisShardingSphereAutoConfiguration implements EnvironmentAware {
    private String schemaName;
    private final SpringBootPropertiesConfiguration props;
    private final Map<String, DataSource> dataSourceMap = new LinkedHashMap();

    @Bean
    public ModeConfiguration modeConfiguration() {
        return null == this.props.getMode() ? null
                                            : (new ModeConfigurationYamlSwapper()).swapToObject(this.props.getMode());
    }

    @Bean
    @Conditional({LocalRulesCondition.class})
    @Autowired(
            required = false
    )
    public DataSource shardingSphereDataSource(ObjectProvider<List<RuleConfiguration>> rules,
            ObjectProvider<ModeConfiguration> modeConfig) throws
            SQLException {
        Collection<RuleConfiguration>
                ruleConfigs = (Collection) Optional.ofNullable(rules.getIfAvailable()).orElse(Collections.emptyList());
        return ShardingSphereDataSourceFactory.createDataSource(this.schemaName,
                (ModeConfiguration) modeConfig.getIfAvailable(), this.dataSourceMap, ruleConfigs,
                this.props.getProps());
    }

    @Bean
    @ConditionalOnMissingBean({DataSource.class})
    public DataSource dataSource(ModeConfiguration modeConfig) throws SQLException {
        return ShardingSphereDataSourceFactory.createDataSource(this.schemaName, modeConfig);
    }

    @Bean
    public TransactionTypeScanner transactionTypeScanner() {
        return new TransactionTypeScanner();
    }

    public final void setEnvironment(Environment environment) {
        // 替换为 ThemisDataSourceMapSetter
        this.dataSourceMap.putAll(ThemisDataSourceMapSetter.getDataSourceMap(environment));
        this.schemaName = SchemaNameSetter.getSchemaName(environment);
    }

    @Generated
    public ThemisShardingSphereAutoConfiguration(SpringBootPropertiesConfiguration props) {
        this.props = props;
    }
}
