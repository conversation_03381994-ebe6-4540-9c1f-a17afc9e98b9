package com.kuaishou.serveree.themis.component.entity.git;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/8/8 2:17 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitCommit {

    private String id;
    @JsonProperty("parent_ids")
    private List<String> parentIds;
    private String message;
    @JsonProperty("authored_date")
    private Date authoredDate;
    @JsonProperty("author_name")
    private String authorName;
    @JsonProperty("author_email")
    private String authorEmail;
    @JsonProperty("committed_date")
    private Date committedDate;
    @JsonProperty("committer_name")
    private String committerName;
    @JsonProperty("committer_email")
    private String committerEmail;

}
