package com.kuaishou.serveree.themis.component.service.platform.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckFile;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileDuplication;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileDuplicationIndex;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileFunction;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileIssue;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileMeasures;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckFile;
import com.kuaishou.serveree.themis.component.common.entity.PCheckFileMeasures;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.CheckFileTypeEnum;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.entity.platform.DuplicationDetail;
import com.kuaishou.serveree.themis.component.entity.platform.DuplicationFileDetail;
import com.kuaishou.serveree.themis.component.entity.platform.FileDetail;
import com.kuaishou.serveree.themis.component.entity.platform.FileDuplication;
import com.kuaishou.serveree.themis.component.entity.platform.FileFunction;
import com.kuaishou.serveree.themis.component.entity.platform.FileInfo;
import com.kuaishou.serveree.themis.component.entity.platform.FileIssue;
import com.kuaishou.serveree.themis.component.entity.platform.FileMeasure;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckFileDuplicationIndexService;
import com.kuaishou.serveree.themis.component.service.CheckFileDuplicationService;
import com.kuaishou.serveree.themis.component.service.CheckFileFunctionService;
import com.kuaishou.serveree.themis.component.service.CheckFileIssueService;
import com.kuaishou.serveree.themis.component.service.CheckFileMeasuresService;
import com.kuaishou.serveree.themis.component.service.CheckFileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PCheckFileMeasuresService;
import com.kuaishou.serveree.themis.component.service.PCheckFileService;
import com.kuaishou.serveree.themis.component.service.id.tables.TablesIdGeneratorManager;
import com.kuaishou.serveree.themis.component.service.platform.PlatformFileMeasureService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.FileDuplicationRequest;
import com.kuaishou.serveree.themis.component.vo.request.PipelineFileMeasureRequest;
import com.kuaishou.serveree.themis.component.vo.response.FileContentDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.FileContentDetailResponse.DuplicationBlock;
import com.kuaishou.serveree.themis.component.vo.response.FileContentDetailResponse.FileBlock;
import com.kuaishou.serveree.themis.component.vo.response.IssueCycleComplexityResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueCycleComplexityResponse.CycleComplexityMeta;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-26
 */
@Slf4j
@Service
public class PlatformFileMeasureServiceImpl implements PlatformFileMeasureService {

    @Autowired
    private CheckFileService checkFileService;

    @Autowired
    private CheckFileDuplicationService checkFileDuplicationService;

    @Autowired
    private CheckFileDuplicationIndexService checkFileDuplicationIndexService;

    @Autowired
    private CheckFileIssueService checkFileIssueService;

    @Autowired
    private CheckFileFunctionService checkFileFunctionService;

    @Autowired
    private CheckFileMeasuresService checkFileMeasuresService;

    @Autowired
    private TablesIdGeneratorManager idGeneratorManager;

    @Autowired
    private CheckBaseService checkBaseService;

    @Autowired
    private GitOperations gitOperations;

    @Autowired
    private CheckRepoService checkRepoService;

    @Autowired
    private PCheckBaseService pCheckBaseService;
    @Autowired
    private PCheckFileService pCheckFileService;
    @Autowired
    private PCheckFileMeasuresService pCheckFileMeasuresService;

    @Transactional
    @Override
    public Pair<Map<String, CheckFile>, List<CheckFileMeasures>> saveScanResultsForFiles(
            CheckBase checkBase, FileInfo fileInfo, FileDuplication duplication) {
        long startTime = System.currentTimeMillis();
        // 生成文件层级关系
        Map<String, CheckFile> pathCheckFileMap = generatePathFileMap(fileInfo, checkBase);
        // 生成issue数据
        List<CheckFileIssue> checkFileIssues = generateFileIssueList(fileInfo, pathCheckFileMap);
        // 生成度量数据
        List<CheckFileMeasures> checkFileMeasures = generateFileMeasures(fileInfo, pathCheckFileMap);
        // 生成方法数据
        List<CheckFileFunction> checkFileFunctions = generateFileFunctions(fileInfo, pathCheckFileMap);
        // 生成duplication数据
        Pair<List<CheckFileDuplicationIndex>, List<CheckFileDuplication>> duplicationListPair =
                generateDuplicationListPair(duplication, pathCheckFileMap, checkBase);
        log.info("[PlatformFileMeasureService] generateDataToSave cost time : {}s",
                (System.currentTimeMillis() - startTime) / 1000);
        if (MapUtils.isNotEmpty(pathCheckFileMap)) {
            Collection<CheckFile> values = pathCheckFileMap.values();
            CommonUtils.processInBatches(values, 1000, checkFileService::insertBatch);
        }
        if (CollectionUtils.isNotEmpty(checkFileIssues)) {
            CommonUtils.processInBatches(checkFileIssues, 1000, checkFileIssueService::insertBatch);
        }
        if (CollectionUtils.isNotEmpty(checkFileMeasures)) {
            CommonUtils.processInBatches(checkFileMeasures, 1000, checkFileMeasuresService::insertBatch);
        }
        if (CollectionUtils.isNotEmpty(checkFileFunctions)) {
            CommonUtils.processInBatches(checkFileFunctions, 1000, checkFileFunctionService::insertBatch);
        }
        List<CheckFileDuplicationIndex> duplicationIndices = duplicationListPair.getLeft();
        if (CollectionUtils.isNotEmpty(duplicationIndices)) {
            CommonUtils.processInBatches(duplicationIndices, 1000, checkFileDuplicationIndexService::insertBatch);
        }
        List<CheckFileDuplication> fileDuplications = duplicationListPair.getRight();
        if (CollectionUtils.isNotEmpty(fileDuplications)) {
            CommonUtils.processInBatches(fileDuplications, 1000, checkFileDuplicationService::insertBatch);
        }
        log.info("[PlatformFileMeasureService] generateAndSaveData cost time : {}s",
                (System.currentTimeMillis() - startTime) / 1000);
        return Pair.of(pathCheckFileMap, checkFileMeasures);
    }

    @Override
    public void cleanFileData(CheckBase checkBase) {
        Long checkRepoId = checkBase.getCheckRepoId();
        Long checkRepoBranchId = checkBase.getCheckRepoBranchId();
        Long baseId = checkBase.getId();
        deleteByCheckRepoIdAndBranchIdAndCheckBaseId(checkRepoId, checkRepoBranchId, baseId);
    }

    @Override
    public IssueCycleComplexityResponse pageByMetricKey(PipelineFileMeasureRequest request) {
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(request.getKspBuildId());
        if (Objects.isNull(pCheckBase)) {
            throw new ThemisException(ResultCodeConstant.INVALID_PARAMS.getCode(), "kspBuildId无效");
        }
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(pCheckBase.getProjectId());
        // 查询文件指标值
        List<PCheckFileMeasures> pCheckFileMeasuresList = pCheckFileMeasuresService.listByMetricKey(
                checkRepo.getId(), pCheckBase.getBranch(), pCheckBase.getId(), request.getMetricKey()
        );
        // 无数据
        if (CollectionUtils.isEmpty(pCheckFileMeasuresList)) {
            return IssueCycleComplexityResponse
                    .builder()
                    .total(0)
                    .scanMode(ScanModeEnum.PROCESS.getCode())
                    .displayValue("0")
                    .metricValues(Collections.emptyList())
                    .build();
        }
        // 查询对应文件
        Map<Long, PCheckFile> fileIdMap =
                pCheckFileService.listByFileIds(checkRepo.getId(), pCheckBase.getBranch(), pCheckBase.getId(),
                                pCheckFileMeasuresList.stream().map(PCheckFileMeasures::getPFileId)
                                        .collect(Collectors.toList()))
                        .stream()
                        .collect(Collectors.toMap(PCheckFile::getId, Function.identity()));
        // 转换vo
        List<CycleComplexityMeta> complexities =
                convert2CycleComplexityMetas(pCheckFileMeasuresList, fileIdMap, checkRepo.getGitProjectId());
        // 计算指标值总和
        long displayValue = complexities.stream().mapToLong(CycleComplexityMeta::getScore).sum();
        // 对指标值倒序排序
        complexities.sort((o1, o2) -> (int) (o2.getScore() - o1.getScore()));
        // 内存分页
        complexities = CommonUtils.pageInMemory(complexities, request.getPage(), request.getPageSize());
        return IssueCycleComplexityResponse
                .builder()
                .total(pCheckFileMeasuresList.size())
                .scanMode(ScanModeEnum.PROCESS.getCode())
                .displayValue(String.valueOf(displayValue))
                .metricValues(complexities)
                .build();
    }

    private List<CycleComplexityMeta> convert2CycleComplexityMetas(List<PCheckFileMeasures> pCheckFileMeasuresList,
            Map<Long, PCheckFile> fileIdMap, Integer gitProjectId) {
        List<CycleComplexityMeta> complexityMetas = Lists.newArrayList();
        for (PCheckFileMeasures pCheckFileMeasures : pCheckFileMeasuresList) {
            CycleComplexityMeta cycleComplexityMeta = new CycleComplexityMeta();
            cycleComplexityMeta.setScore(CommonUtils.parseIntValue(pCheckFileMeasures.getMetricValue()));
            cycleComplexityMeta.setFileId(pCheckFileMeasures.getPFileId());
            cycleComplexityMeta.setPath(fileIdMap.get(pCheckFileMeasures.getPFileId()).getLocation());
            cycleComplexityMeta.setBaseId(pCheckFileMeasures.getPBaseId());
            cycleComplexityMeta.setGitProjectId(gitProjectId);
            cycleComplexityMeta.setBranch(pCheckFileMeasures.getBranch());
            complexityMetas.add(cycleComplexityMeta);
        }
        return complexityMetas;
    }


    @Transactional
    @Override
    public void deleteByCheckRepoIdAndBranchIdAndCheckBaseId(long checkRepoId, long checkRepoBranchId, long baseId) {
        // 删除文件数据
        checkFileService.remove(new QueryWrapper<CheckFile>().lambda()
                .eq(CheckFile::getCheckRepoId, checkRepoId)
                .eq(CheckFile::getCheckRepoBranchId, checkRepoBranchId)
                .eq(CheckFile::getBaseId, baseId)
        );
        // 删除文件issue数据
        checkFileIssueService.remove(new QueryWrapper<CheckFileIssue>().lambda()
                .eq(CheckFileIssue::getCheckRepoId, checkRepoId)
                .eq(CheckFileIssue::getCheckRepoBranchId, checkRepoBranchId)
                .eq(CheckFileIssue::getBaseId, baseId)
        );
        // 删除文件指标数据
        checkFileMeasuresService.remove(new QueryWrapper<CheckFileMeasures>().lambda()
                .eq(CheckFileMeasures::getCheckRepoId, checkRepoId)
                .eq(CheckFileMeasures::getCheckRepoBranchId, checkRepoBranchId)
                .eq(CheckFileMeasures::getBaseId, baseId)
        );
        // 删除文件方法数据
        checkFileFunctionService.remove(new QueryWrapper<CheckFileFunction>().lambda()
                .eq(CheckFileFunction::getCheckRepoId, checkRepoId)
                .eq(CheckFileFunction::getCheckRepoBranchId, checkRepoBranchId)
                .eq(CheckFileFunction::getBaseId, baseId)
        );
        // 删除文件重复数据
        checkFileDuplicationIndexService.remove(new QueryWrapper<CheckFileDuplicationIndex>().lambda()
                .eq(CheckFileDuplicationIndex::getCheckRepoId, checkRepoId)
                .eq(CheckFileDuplicationIndex::getCheckRepoBranchId, checkRepoBranchId)
                .eq(CheckFileDuplicationIndex::getBaseId, baseId)
        );
        checkFileDuplicationService.remove(new QueryWrapper<CheckFileDuplication>().lambda()
                .eq(CheckFileDuplication::getCheckRepoId, checkRepoId)
                .eq(CheckFileDuplication::getCheckRepoBranchId, checkRepoBranchId)
                .eq(CheckFileDuplication::getBaseId, baseId)
        );
    }

    private List<CheckFileFunction> generateFileFunctions(FileInfo fileInfo, Map<String, CheckFile> pathCheckFileMap) {
        List<CheckFileFunction> fileFunctions = Lists.newArrayList();
        List<FileDetail> fileList = fileInfo.getFileList();
        for (FileDetail fileDetail : fileList) {
            CheckFile checkFile = pathCheckFileMap.get(fileDetail.getPath());
            List<FileFunction> functions = fileDetail.getFunctions();
            for (FileFunction function : functions) {
                CheckFileFunction checkFileFunction = new CheckFileFunction();
                BeanUtil.copyProperties(function, checkFileFunction);
                BeanUtil.copyProperties(checkFile, checkFileFunction);
                checkFileFunction.setFileId(checkFile.getId());
                Long tableId = idGeneratorManager.generateDistributionId(CheckFileFunction.class);
                checkFileFunction.setId(tableId);
                fileFunctions.add(checkFileFunction);
            }
        }
        return fileFunctions;
    }

    private List<CheckFileMeasures> generateFileMeasures(FileInfo fileInfo,
            Map<String, CheckFile> pathCheckFileMap) {
        List<CheckFileMeasures> checkFileMeasuresList = Lists.newArrayList();
        List<FileDetail> fileList = fileInfo.getFileList();
        for (FileDetail fileDetail : fileList) {
            CheckFile checkFile = pathCheckFileMap.get(fileDetail.getPath());
            List<FileMeasure> fileMeasures = fileDetail.getFileMeasures();
            for (FileMeasure fileMeasure : fileMeasures) {
                CheckFileMeasures checkFileMeasures = new CheckFileMeasures();
                BeanUtil.copyProperties(checkFile, checkFileMeasures);
                BeanUtil.copyProperties(fileMeasure, checkFileMeasures);
                checkFileMeasures.setMetricValue(JSONUtils.serialize(fileMeasure.getMetricValue()));
                checkFileMeasures.setFileId(checkFile.getId());
                Long tableId = idGeneratorManager.generateDistributionId(CheckFileMeasures.class);
                checkFileMeasures.setId(tableId);
                checkFileMeasuresList.add(checkFileMeasures);
            }
        }
        return checkFileMeasuresList;
    }

    private List<CheckFileIssue> generateFileIssueList(FileInfo fileInfo, Map<String, CheckFile> pathCheckFileMap) {
        List<CheckFileIssue> fileIssues = Lists.newArrayList();
        for (FileDetail fileDetail : fileInfo.getFileList()) {
            String path = fileDetail.getPath();
            CheckFile checkFile = pathCheckFileMap.get(path);
            for (FileIssue fileIssue : fileDetail.getIssueList()) {
                CheckFileIssue checkFileIssue = new CheckFileIssue();
                BeanUtil.copyProperties(checkFile, checkFileIssue);
                BeanUtil.copyProperties(fileIssue, checkFileIssue);
                checkFileIssue.setFileId(checkFile.getId());
                Long tableId = idGeneratorManager.generateDistributionId(CheckFileIssue.class);
                checkFileIssue.setId(tableId);
                fileIssues.add(checkFileIssue);
            }
        }
        return fileIssues;
    }

    private Map<String, CheckFile> generatePathFileMap(FileInfo fileInfo, CheckBase checkBase) {
        Map<String, CheckFile> pathCheckFileMap = Maps.newHashMap();
        LocalDateTime now = LocalDateTime.now();
        for (FileDetail fileDetail : fileInfo.getFileList()) {
            String path = fileDetail.getPath();
            String[] pathSplitArray = path.split("/");
            int arrayLength = pathSplitArray.length;
            for (int i = 0; i < arrayLength; i++) {
                // 先获取顶级路径
                String location = getLevelPath(i + 1, pathSplitArray);
                if (pathCheckFileMap.containsKey(location)) {
                    continue;
                }
                CheckFile checkFile = new CheckFile();
                checkFile.setGmtCreate(now);
                checkFile.setGmtModified(now);
                checkFile.setLocation(location);
                checkFile.setBaseId(checkBase.getId());
                checkFile.setCheckRepoId(checkBase.getCheckRepoId());
                checkFile.setCheckRepoBranchId(checkBase.getCheckRepoBranchId());
                // 第一层级
                if (i == 0) {
                    checkFile.setParentFileId(0L);
                } else {
                    // 获取上一层级的数据
                    String upperLevelPath = getLevelPath(i, pathSplitArray);
                    CheckFile upperLevelCheckFile = pathCheckFileMap.get(upperLevelPath);
                    Long parentFileId = upperLevelCheckFile.getId();
                    checkFile.setParentFileId(parentFileId);
                }
                // 不是最后一层 那就是目录
                if (i != arrayLength - 1) {
                    checkFile.setFileType(CheckFileTypeEnum.DIRECTOR.getType());
                    checkFile.setFileSuffix("");
                } else {
                    checkFile.setFileType(CheckFileTypeEnum.FILE.getType());
                    String[] dotSplitArray = pathSplitArray[i].split("\\.");
                    String suffix = getFileSuffix(dotSplitArray);
                    checkFile.setFileSuffix(suffix);
                }
                checkFile.setFileLevel(i + 1);
                Long tableId = idGeneratorManager.generateDistributionId(CheckFile.class);
                checkFile.setId(tableId);
                pathCheckFileMap.put(location, checkFile);
            }
        }
        return pathCheckFileMap;
    }

    private Pair<List<CheckFileDuplicationIndex>, List<CheckFileDuplication>> generateDuplicationListPair(
            FileDuplication duplicationInfo, Map<String, CheckFile> pathCheckFileMap, CheckBase checkBase) {
        List<CheckFileDuplicationIndex> fileDuplicationIndices = Lists.newArrayList();
        List<CheckFileDuplication> fileDuplications = Lists.newArrayList();
        if (duplicationInfo == null) {
            return Pair.of(fileDuplicationIndices, fileDuplications);
        }
        List<DuplicationDetail> duplicationList = duplicationInfo.getDuplicationList();
        for (DuplicationDetail duplicationDetail : duplicationList) {
            CheckFileDuplicationIndex fileDuplicationIndex = new CheckFileDuplicationIndex();
            BeanUtil.copyProperties(checkBase, fileDuplicationIndex);
            String content = duplicationDetail.getContent();
            fileDuplicationIndex.setContent(content);
            Long tableId = idGeneratorManager.generateDistributionId(CheckFileDuplicationIndex.class);
            fileDuplicationIndex.setId(tableId);
            fileDuplicationIndex.setBaseId(checkBase.getId());
            fileDuplicationIndices.add(fileDuplicationIndex);
            // 为重复
            for (DuplicationFileDetail fileDetail : duplicationDetail.getFileList()) {
                CheckFile checkFile = pathCheckFileMap.get(fileDetail.getPath());
                if (checkFile == null) {
                    // 这里由于前端扫描器有点bug，后端先过滤掉
                    continue;
                }
                CheckFileDuplication checkFileDuplication = new CheckFileDuplication();
                BeanUtil.copyProperties(checkBase, checkFileDuplication);
                BeanUtil.copyProperties(fileDetail, checkFileDuplication);
                checkFileDuplication.setFileId(checkFile.getId());
                checkFileDuplication.setIndexId(fileDuplicationIndex.getId());
                tableId = idGeneratorManager.generateDistributionId(CheckFileDuplication.class);
                checkFileDuplication.setId(tableId);
                checkFileDuplication.setBaseId(checkBase.getId());
                fileDuplications.add(checkFileDuplication);
            }
        }
        return Pair.of(fileDuplicationIndices, fileDuplications);
    }


    private String getFileSuffix(String[] dotSplitArray) {
        return dotSplitArray[dotSplitArray.length - 1];
    }

    private String getLevelPath(int i, String[] pathSplitArray) {
        StringBuilder sb = new StringBuilder();
        for (int j = 0; j < i; j++) {
            sb.append(pathSplitArray[j]);
            if (j != i - 1) {
                sb.append("/");
            }
        }
        return sb.toString();
    }

    @Override
    public FileContentDetailResponse getFileContentDetail(FileDuplicationRequest request) {
        // 流水线模式
        if (ScanModeEnum.PROCESS.getCode() == request.getScanMode()) {
            return getPipelineFileContentDetail(request);
        }
        // 离线模式
        return getOfflineFileContentDetail(request);
    }

    /**
     * 流水线模式获取文件详情，返回源代码即可
     * @param request
     * @return
     */
    public FileContentDetailResponse getPipelineFileContentDetail(FileDuplicationRequest request) {
        // 参数校验
        Pair<PCheckBase, PCheckFile> pair =
                checkPCheckBaseAndPFileExists(request.getCheckBaseId(), request.getFileId());
        PCheckBase pCheckBase = pair.getLeft();
        PCheckFile pCheckFile = pair.getRight();
        // 获取文件源码内容
        String highlightContent =
                gitOperations.getCachedHighlightContent(pCheckBase.getProjectId(), pCheckBase.getCommitId(),
                        pCheckFile.getLocation());
        // 返回
        return FileContentDetailResponse.builder()
                .scanMode(ScanModeEnum.PROCESS.getCode())
                .checkBaseId(pCheckBase.getId())
                .fileId(pCheckFile.getId())
                .path(pCheckFile.getLocation())
                .content(highlightContent)
                .duplicationList(Collections.emptyList()).build();
    }

    /**
     * 离线模式获取文件详情，返回源代码+重复代码块
     * @param request
     * @return
     */
    public FileContentDetailResponse getOfflineFileContentDetail(FileDuplicationRequest request) {
        // 参数校验
        Pair<CheckBase, CheckFile> pair =
                checkCheckBaseAndFileExists(request.getCheckBaseId(), request.getFileId());
        CheckBase checkBase = pair.getLeft();
        CheckFile checkFile = pair.getRight();
        // 找到此文件都有哪些重复块，拿到所属的indexId
        List<CheckFileDuplication> checkFileDuplicationList =
                checkFileDuplicationService.listByFileId(checkBase, request.getFileId());
        List<Long> indexIds = checkFileDuplicationList.stream()
                .map(CheckFileDuplication::getIndexId)
                .distinct()
                .collect(Collectors.toList());
        // 根据indexId当前file中每个块与其重复的块
        List<DuplicationBlock> duplicationList = Lists.newArrayList();
        for (Long indexId : indexIds) {
            duplicationList.addAll(getFileBlockDuplication(checkBase, checkFile.getId(), indexId));
        }
        // 拿到文件源码
        CheckRepo checkRepo = checkRepoService.getById(checkBase.getCheckRepoId());
        String highlightContent =
                gitOperations.getCachedHighlightContent(checkRepo.getGitProjectId(), checkBase.getCommitId(),
                        checkFile.getLocation());
        return FileContentDetailResponse.builder()
                .scanMode(ScanModeEnum.OFFLINE.getCode())
                .checkBaseId(checkBase.getId())
                .fileId(checkFile.getId())
                .path(checkFile.getLocation())
                .content(highlightContent)
                .duplicationList(duplicationList).build();
    }

    private List<DuplicationBlock> getFileBlockDuplication(CheckBase checkBase, long fileId, Long duplicationIndexId) {
        List<DuplicationBlock> fileBlockDuplicationList = Lists.newArrayList();
        // 这些代码块互相重复
        List<CheckFileDuplication> checkFileDuplications =
                checkFileDuplicationService.listByIndexId(checkBase, duplicationIndexId);
        if (CollectionUtils.isEmpty(checkFileDuplications)) {
            return fileBlockDuplicationList;
        }
        Map<Long, Long> fileId2CheckBaseIdMap = checkFileDuplications.stream().collect(
                Collectors.toMap(CheckFileDuplication::getFileId, CheckFileDuplication::getBaseId, (a, b) -> a));
        // 这些代码块所属文件path
        Map<Long, String> fileLocationMap =
                checkFileService.getFileLocationMap(checkBase, new ArrayList<>(fileId2CheckBaseIdMap.keySet()));
        // 过滤出当前查询的文件中的重复块
        List<CheckFileDuplication> curFileDuplications =
                checkFileDuplications.stream().filter(cf -> cf.getFileId() == fileId).collect(Collectors.toList());
        // 组合这些重复块 和 与其重复的块，得到 DuplicationBlock
        for (CheckFileDuplication curFileBlock : curFileDuplications) {
            // 与其重复的块，就是 除自己之外的其他块
            List<CheckFileDuplication> otherFileBlocks =
                    checkFileDuplications.stream().filter(cfd -> !sameDuplicateBlock(cfd, curFileBlock))
                            .collect(Collectors.toList());
            // 按文件聚合
            Map<Long, List<CheckFileDuplication>> otherFileBlocksMap =
                    otherFileBlocks.stream().collect(Collectors.groupingBy(CheckFileDuplication::getFileId));
            // 转换成blockList
            List<FileBlock> fileBlockList = otherFileBlocksMap.entrySet().stream().map(entry -> {
                long otherFileId = entry.getKey();
                List<CheckFileDuplication> blockList = entry.getValue();
                return FileBlock.builder()
                        .fileId(otherFileId)
                        .checkBaseId(fileId2CheckBaseIdMap.get(otherFileId))
                        .path(fileLocationMap.get(otherFileId))
                        .blocks(blockList.stream().map(
                                        b -> FileContentDetailResponse.Block.builder()
                                                .startLine(b.getStartLine())
                                                .endLine(b.getEndLine())
                                                .build())
                                .collect(Collectors.toList()))
                        .build();
            }).collect(Collectors.toList());
            // 组合 块 -> 重复列表
            DuplicationBlock duplicationBlock = DuplicationBlock.builder()
                    .startLine(curFileBlock.getStartLine())
                    .endLine(curFileBlock.getEndLine())
                    .fileList(fileBlockList)
                    .build();
            fileBlockDuplicationList.add(duplicationBlock);
        }
        return fileBlockDuplicationList;
    }

    private Pair<CheckBase, CheckFile> checkCheckBaseAndFileExists(long checkBaseId, long fileId) {
        CheckBase checkBase = checkBaseService.getById(checkBaseId);
        if (checkBase == null) {
            throw new ThemisException(ResultCodeConstant.NOT_FOUND_BASE_DATA);
        }
        CheckFile checkFile = checkFileService.getByFileId(checkBase.getCheckRepoId(), checkBase.getCheckRepoBranchId(),
                checkBase.getId(), fileId);
        if (checkFile == null) {
            throw new ThemisException(ResultCodeConstant.NOT_FOUND_CHECK_FILE_DATA);
        }
        return Pair.of(checkBase, checkFile);
    }

    private Pair<PCheckBase, PCheckFile> checkPCheckBaseAndPFileExists(long checkBaseId, long fileId) {
        PCheckBase checkBase = pCheckBaseService.getById(checkBaseId);
        if (checkBase == null) {
            throw new ThemisException(ResultCodeConstant.NOT_FOUND_BASE_DATA);
        }
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(checkBase.getProjectId());
        PCheckFile checkFile =
                pCheckFileService.getByFileId(checkRepo.getId(), checkBase.getBranch(), checkBase.getId(), fileId);
        if (checkFile == null) {
            throw new ThemisException(ResultCodeConstant.NOT_FOUND_CHECK_FILE_DATA);
        }
        return Pair.of(checkBase, checkFile);
    }


    private boolean sameDuplicateBlock(CheckFileDuplication fileBlock1, CheckFileDuplication fileBlock2) {
        return Objects.equals(fileBlock1.getFileId(), fileBlock2.getFileId())
                && Objects.equals(fileBlock1.getStartLine(), fileBlock2.getStartLine())
                && Objects.equals(fileBlock1.getEndLine(), fileBlock2.getEndLine());
    }
}
