package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 检查主表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PCheckBase对象", description = "检查主表")
public class PCheckBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "git的projectId")
    private Integer projectId;

    @ApiModelProperty(value = "git repo地址")
    private String repoUrl;

    @ApiModelProperty(value = "group路径")
    private String groupPath;

    @ApiModelProperty(value = "分支名")
    private String branch;

    @ApiModelProperty(value = "git commit id")
    private String commitId;

    @ApiModelProperty(value = "mr_id")
    private Integer mrId;

    @ApiModelProperty(value = "ksp的build_id")
    private Long kspBuildId;

    private String sponsor;

    private String buildModules;

    private String sonarBranch;

    private Integer scannerType;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    private String stuckPointSettings;

    private Integer scanLineCount;

    private Long scanDuration;

    private Integer stuckStatus;

    private Long localBuildId;

    private String status;

}
