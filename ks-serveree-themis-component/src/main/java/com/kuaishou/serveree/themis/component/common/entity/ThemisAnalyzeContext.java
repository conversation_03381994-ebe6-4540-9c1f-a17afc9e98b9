package com.kuaishou.serveree.themis.component.common.entity;

import java.util.List;

import com.kuaishou.serveree.themis.component.service.check.custom.entity.CustomRulePair;
import com.kuaishou.serveree.themis.component.service.check.custom.entity.DiffInfo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/7/29 11:08 上午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ThemisAnalyzeContext {

    private List<DiffInfo> diffInfos;

    private List<CustomRulePair> customRulePairs;

}
