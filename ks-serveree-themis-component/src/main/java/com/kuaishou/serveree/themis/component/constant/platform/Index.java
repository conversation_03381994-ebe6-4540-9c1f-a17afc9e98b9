package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Arrays;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-07-14
 */
@AllArgsConstructor
public enum Index {

    SCORE("score", "总分"),
    BUGS_SCORE("bugs_score", "代码缺陷"),
    CODE_SMELL_SCORE("code_smell_score", "代码异味"),
    COMPLEXITY_SCORE("complexity_score", "复杂度"),
    DUPLICATION_SCORE("duplication_score", "重复度"),
    VULNERABILITY_SCORE("vulnerability_score", "安全性"),
    BEST_PRACTICE_SCORE("best_practice_score", "最佳实践"),

    BUGS_COUNT("bugs_count", "缺陷数量"),
    CODE_SMELL_COUNT("code_smell_count", "异味数量"),

    MAINTAINABILITY_INDEX("maintainability_index", "可维护性指数"),

    FILE_STATE("file_state", "文件状态"),
    LANGUAGE_DISTRIBUTION("ncloc_language_distribution", "语言分布"),
    PURPOSE_DISTRIBUTION("ncloc_purpose_distribution", "文件类型分布"),
    NCLOC("ncloc", "代码行数"),

    DUPLICATED_LINES("duplicated_lines", "重复行数"),
    DUPLICATED_LINES_DENSITY("duplicated_lines_density", "重复行占比"),
    DUPLICATED_BLOCKS("duplicated_blocks", "重复的块数"),

    LINE_COUNT("line_count", "文件代码行数"),
    DUPLICATION_LINE_COUNT("duplication_line_count", "文件重复代码行"),
    DUPLICATION_BLOCK_COUNT("duplication_block_count", "文件重复代码块"),
    DUPLICATION_LINE_PERCENT("duplication_line_percent", "文件重复代码行占比"),

    FRAMEWORK("framework", "框架"),
    UI_LIBRARY("ui_library", "组件库"),
    STATE_LIBRARY("state_library", "状态管理库"),

    DEPENDENCY_COUNT("dependency_count", "依赖数量"),
    CLASS_COUNT("class_count", "类数量"),
    FUNCTION_COUNT("function_count", "方法数量"),
    DEPENDENCY_MODULES("dependency_modules", "依赖模块"),

    FILE_LOGIC_LINE_COUNT("logic_line_count", "逻辑代码行数"),
    FILE_AVG_LOGIC_LINE_COUNT("avg_logic_line_count", "平均逻辑代码行数"),
    FILE_COMPLEXITY("complexity", "圈复杂度"),
    FILE_AVG_COMPLEXITY("avg_complexity", "平均圈复杂度"),
    FILE_HALSTEAD_VOLUME("halstead_volume", "霍尔斯特德体积"),
    FILE_AVG_HALSTEAD_VOLUME("avg_halstead_volume", "平均霍尔斯特德体积"),

    MAINTAINABILITY_FACTOR_RATIO("maintainability_factor_ratio", "可维护性因子占比"),
    FIlE_MAINTAINABILITY_INDEX("maintainability_index", "可维护性指数");


    private static final Map<String, Index> KEY_INDEX_MAP = Maps.newHashMap();

    static {
        Arrays.stream(Index.values()).forEach(o -> KEY_INDEX_MAP.put(o.getKey(), o));
    }

    public static Index getByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        return KEY_INDEX_MAP.get(key);
    }

    private final String key;
    private final String des;

    public String getKey() {
        return key;
    }

    public String getDes() {
        return des;
    }
}
