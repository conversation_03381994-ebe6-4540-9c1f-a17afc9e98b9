package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/12/11 14:04 下午
 */
@Getter
public enum CheckNoticeTypeEnum {
    OFFLINE_NOTICE(1, "离线扫描通知"),
    PIPELINE_NOTICE(2, "流水线扫描通知"),
    ISSUE_TRANSITION_NOTICE(3, "问题打标通知"),
    ;
    private final Integer type;
    private final String display;

    CheckNoticeTypeEnum(Integer type, String display) {
        this.type = type;
        this.display = display;
    }

    private static final Map<Integer, CheckNoticeTypeEnum> TYPE_MAP = Maps.newHashMap();

    static {
        for (CheckNoticeTypeEnum checkNoticeType : CheckNoticeTypeEnum.values()) {
            TYPE_MAP.put(checkNoticeType.getType(), checkNoticeType);
        }
    }

    public static CheckNoticeTypeEnum getByType(Integer type) {
        return TYPE_MAP.get(type);
    }
}