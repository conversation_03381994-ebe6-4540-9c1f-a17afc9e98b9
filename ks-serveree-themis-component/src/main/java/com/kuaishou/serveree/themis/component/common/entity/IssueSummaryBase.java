package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarFlow;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "IssueSummaryBase对象", description = "IssueSummaryBase表")
@TableName(autoResultMap = true)
public class IssueSummaryBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "全局唯一issueId")
    private String issueUniqId;

    @ApiModelProperty(value = "git的projectId")
    private Integer gitProjectId;

    @ApiModelProperty(value = "问题状态 'OPEN','CLOSED','RESOLVED','TO_REVIEW'")
    private String status;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "修改人")
    private String modifier;


    @ApiModelProperty(value = "代码路径")
    private String location;

    @ApiModelProperty(value = "问题作者")
    private String author;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建时的commitId")
    private String createCommitId;

    @ApiModelProperty(value = "所属的第三方标志")
    private String source;

    @ApiModelProperty(value = "类型 ")
    private String type;

    @ApiModelProperty(value = "级别")
    private String severity;

    @ApiModelProperty(value = "错误提示")
    private String message;

    @ApiModelProperty(value = "规则")
    private String rule;

    @ApiModelProperty(value = "修复者")
    private String repairer;

    @ApiModelProperty(value = "修复时commitId")
    private String repairCommitId;

    @ApiModelProperty(value = "修复时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime repairTime;

    @ApiModelProperty(value = "第几行开始")
    private Integer startLine;

    @ApiModelProperty(value = "第几行结束")
    private Integer endLine;

    @ApiModelProperty(value = "起始偏移量")
    private Integer startOffset;

    @ApiModelProperty(value = "结束偏移量")
    private Integer endOffset;

    @ApiModelProperty(value = "问题跳转链接")
    private String issueLink;

    @ApiModelProperty(value = "git跳转的链接")
    private String gitLink;

    @ApiModelProperty(value = "执行的类型")
    private Integer executionReferType;

    @ApiModelProperty(value = "sonar的issueKey")
    private String sonarIssueKey;

    /**
     * 问题关联流数据 {@link SonarFlow} 的list json结构
     */
    @ApiModelProperty(value = "问题关联流数据")
    private String flows;

    @ApiModelProperty(value = "扫描器类型")
    private Integer scannerType;

    @ApiModelProperty(value = "通用issueUniqId")
    private String commonIssueUniqId;

    private String commonIssueUniqIdCopy;

    private String issueUniqIdCopy;

    /**
     * 使用新的策略生成的issueUniqId
     */
    private String issueUniqIdV2;

    @ApiModelProperty(value = "解决类型，1 代码变更；2 规则集变更")
    private Integer repairType;
}
