package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/27 3:31 下午
 */
@AllArgsConstructor
public enum SelectedType {

    ALL(0, "所有"),
    SELF_CREATED(1, "我创建的"),
    SELF_STARED(2, "我关注的"),
    ;

    @Getter
    private final int type;
    @Getter
    private final String desc;

    private static final Map<Integer, SelectedType> TYPE_MAP = Maps.newHashMap();

    static {
        for (SelectedType selectedType : SelectedType.values()) {
            TYPE_MAP.put(selectedType.getType(), selectedType);
        }
    }

    public static SelectedType getByType(int type) {
        return TYPE_MAP.get(type);
    }

}
