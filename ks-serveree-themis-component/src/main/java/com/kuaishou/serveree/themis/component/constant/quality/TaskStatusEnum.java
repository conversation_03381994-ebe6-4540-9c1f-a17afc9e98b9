package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import io.vavr.collection.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2020/11/2 6:06 下午
 */
@AllArgsConstructor
public enum TaskStatusEnum {

    CREATED("创建成功"),
    WAITING("等待执行中"),
    ABORTED("中止"),
    QUEUEING("排队中"),
    EXECUTING("执行中"),
    SUCCESS("成功"),
    FAIL("失败");

    @Getter
    @Setter
    private String description;

    private static final Map<String, String> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(TaskStatusEnum.values()).forEach(taskStatusEnum ->
                ENUM_MAP.put(taskStatusEnum.name(), taskStatusEnum.getDescription())
        );
    }

    public static String getDescriptionByEnumName(String name) {
        if (StringUtils.isEmpty(name)) {
            return StringUtils.EMPTY;
        }
        return ENUM_MAP.get(name);
    }

    public static boolean isFinished(String status) {
        return List.of(ABORTED.name(), SUCCESS.name(), FAIL.name()).contains(status);
    }

}
