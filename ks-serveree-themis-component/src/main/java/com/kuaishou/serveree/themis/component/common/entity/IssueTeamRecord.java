package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "IssueTeamRecord对象", description = "")
public class IssueTeamRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "全局唯一issueId")
    private String issueUniqId;

    @ApiModelProperty(value = "问题作者")
    private String author;

    @ApiModelProperty(value = "ksp的build_id")
    private String kspBuildId;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "0-成功， 1-失败")
    private Integer buildStatus;

    @ApiModelProperty(value = "创建失败的原因")
    private String failReason;

    @ApiModelProperty(value = "team任务的id")
    private String teamId;

    @ApiModelProperty(value = "git project id")
    private Integer gitProjectId;

    private String commonIssueUniqId;
    private String commonIssueUniqIdCopy;
    private String issueUniqIdCopy;


}
