package com.kuaishou.serveree.themis.component.constant.process;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/10/26 8:23 下午
 */
@AllArgsConstructor
public enum ProcessKCheckLevel {

    LEVEL1(1,
            Lists.newArrayList("blocker_violations"),
            Lists.newArrayList(ProcessExecutionReferType.MAVEN_SONAR),
            Lists.newArrayList(CheckIssueSeverity.SERIOUS)),
    LEVEL2(2,
            Lists.newArrayList("blocker_violations", "critical_violations"),
            Lists.newArrayList(ProcessExecutionReferType.MAVEN_SONAR),
            Lists.newArrayList(CheckIssueSeverity.SERIOUS, CheckIssueSeverity.MAJOR)),
    LEVEL3(3,
            Lists.newArrayList("blocker_violations", "critical_violations", "major_violations", "minor_violations",
                    "info_violations", "checkstyle_violations"),
            Lists.newArrayList(ProcessExecutionReferType.MAVEN_SONAR, ProcessExecutionReferType.CHECKSTYLE),
            Lists.newArrayList(CheckIssueSeverity.SERIOUS, CheckIssueSeverity.MAJOR, CheckIssueSeverity.COMMON)),
    ;

    @Getter
    private int levelCode;

    @Getter
    private List<String> measureKeys;

    @Getter
    private List<ProcessExecutionReferType> executionReferTypes;

    @Getter
    private List<CheckIssueSeverity> issueSeverities;

    private static final Map<Integer, List<String>> CODE_MAP = Maps.newHashMap();

    private static final Map<Integer, List<ProcessExecutionReferType>> CODE_REFER_TYPE_MAP = Maps.newHashMap();

    private static final Map<Integer, List<String>> CODE_SEVERITY_MAP = Maps.newHashMap();

    static {
        Arrays.stream(ProcessKCheckLevel.values()).forEach(checkLevel -> {
            CODE_MAP.put(checkLevel.getLevelCode(), checkLevel.getMeasureKeys());
            CODE_REFER_TYPE_MAP.put(checkLevel.getLevelCode(), checkLevel.getExecutionReferTypes());
            CODE_SEVERITY_MAP.put(checkLevel.getLevelCode(), checkLevel.getIssueSeverities()
                    .stream()
                    .map(CheckIssueSeverity::getKey)
                    .collect(Collectors.toList()));
        });
    }

    public static Map<Integer, List<String>> getLevelMeasureKeys() {
        return CODE_MAP;
    }

    public static List<ProcessExecutionReferType> getReferTypesByLevelCode(int levelCode) {
        return CODE_REFER_TYPE_MAP.get(levelCode);
    }

    public static List<String> getSeverityListByLevelCode(int levelCode) {
        return CODE_SEVERITY_MAP.get(levelCode);
    }

}
