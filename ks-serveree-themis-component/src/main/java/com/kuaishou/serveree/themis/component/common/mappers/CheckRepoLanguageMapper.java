package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoLanguage;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-28
 */
@Mapper
@Repository
public interface CheckRepoLanguageMapper extends BaseMapper<CheckRepoLanguage> {

    @Select({"<script>",
            "select distinct(language) from check_repo_language",
            "</script>"})
    List<String> selectLanguage();

    @Select("select l.* from check_repo r inner join check_repo_language l on l.check_repo_id = r.id "
            + "where r.git_project_id = #{gitProjectId} and r.version = 1 and r.deleted = 0 limit 1")
    CheckRepoLanguage getByGitProjectId(@Param("gitProjectId") Integer gitProjectId);
}
