package com.kuaishou.serveree.themis.component.service.platform.notice;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.entity.kim.IssueTransitionNoticeContext;
import com.kuaishou.serveree.themis.component.entity.scanner.ScannerSendKimContext;
/**
 * <AUTHOR>
 * @since 2023/12/25 10:22
 */
public interface CheckRepoNoticeActionService {
    /**
     * 消息补偿发送
     */
    void compensatePushNotice();
    /**
     * 发送离线扫描结束通知
     * @param checkBase
     */
    void sendOfflineScanNotice(CheckBase checkBase);
    /**
     * 发送流水线扫描结束通知
     * @param scanModeContext
     */
    void sendPipelineScanNotice(ScannerSendKimContext scanModeContext);
    /**
     * 发送Issue打标通知
     * @param context
     */
    void sendIssueTransitionNotice(IssueTransitionNoticeContext context);
}