package com.kuaishou.serveree.themis.component.service.platform.impl;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.INVALID_PARAMS;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.NOT_FOUND_CHECK_REPO;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.PARAMS_CAN_NOT_EMPTY;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.PROFILE_IS_USING_NOT_DELETE;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.PROFILE_NOT_EXIST;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.client.ares.AresApi;
import com.kuaishou.serveree.themis.component.common.entity.CheckActionLog;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfileRuleRelation;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranchProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckRule;
import com.kuaishou.serveree.themis.component.common.entity.KdevUserInfo;
import com.kuaishou.serveree.themis.component.common.entity.KsUserInfo;
import com.kuaishou.serveree.themis.component.common.entity.PlatformUserRoleRelation;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.CheckActionLogEnum;
import com.kuaishou.serveree.themis.component.constant.platform.CheckProfileLevelEnum;
import com.kuaishou.serveree.themis.component.constant.platform.CheckProfileType;
import com.kuaishou.serveree.themis.component.constant.platform.CheckRepoBranchVersion;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformCommonConstants;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformProfileOperationEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformRoleEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.entity.ares.AresMessageEntity;
import com.kuaishou.serveree.themis.component.entity.platform.CheckProfileSearchCondition;
import com.kuaishou.serveree.themis.component.service.CheckActionLogService;
import com.kuaishou.serveree.themis.component.service.CheckProfileRuleRelationService;
import com.kuaishou.serveree.themis.component.service.CheckProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.CheckRuleService;
import com.kuaishou.serveree.themis.component.service.PlatformUserRoleRelationService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformProfileService;
import com.kuaishou.serveree.themis.component.service.platform.scan.ScannerHelper;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.AllParentProfileRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.AllParentProfileResponseVo;
import com.kuaishou.serveree.themis.component.vo.request.AllParentProfileResponseVo.ProfileItem;
import com.kuaishou.serveree.themis.component.vo.request.ChangeParentProfileRequest;
import com.kuaishou.serveree.themis.component.vo.request.PlatformPermissionGrantRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileAddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileBaseInfoRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCopyRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCreateRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileGetByNameRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileSearchRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.PlatformPermissionGrantResponse;
import com.kuaishou.serveree.themis.component.vo.response.ProfileAddRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileBaseInfoResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileBaseInfoResponseVo.ProfileTypeInfo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileCopyResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileCreateResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileDeleteResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileDeleteRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileGetByNameResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileSearchResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileSearchResponseVo.RuleProfile;
import com.kuaishou.serveree.themis.component.vo.response.ProfileUpdateRuleResponse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.UUID;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/4/7 5:42 下午
 */
@Service
@Slf4j
public class PlatformProfileServiceImpl implements PlatformProfileService {

    @Autowired
    private CheckProfileService checkProfileService;
    @Autowired
    private CheckProfileRuleRelationService checkProfileRuleRelationService;
    @Autowired
    private CheckRepoBranchProfileService checkRepoBranchProfileService;
    @Autowired
    private PlatformPermissionService platformPermissionService;
    @Autowired
    private CheckRuleService checkRuleService;
    @Autowired
    private KsRedisClient ksRedisClient;
    @Autowired
    private ScannerHelper scannerHelper;
    @Autowired
    private CheckActionLogService checkActionLogService;
    @Autowired
    private AresApi aresApi;
    @Autowired
    private CheckRepoService checkRepoService;
    @Autowired
    private CheckRepoBranchService checkRepoBranchService;
    @Autowired
    private PlatformUserRoleRelationService platformUserRoleRelationService;

    private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Transactional
    @Override
    public ProfileCreateResponseVo create(ProfileCreateRequestVo requestVo, String userName) {
        checkUserLogin(userName);
        checkArguments(requestVo);
        LocalDateTime now = LocalDateTime.now();
        CheckProfile checkProfile = new CheckProfile();
        checkProfile.setCreator(userName);
        checkProfile.setUpdater(userName);
        checkProfile.setGmtCreate(now);
        checkProfile.setGmtModified(now);
        checkProfile.setLanguage(requestVo.getLanguage());
        checkProfile.setProfileName(UUID.fastUUID().toString().replace("-", ""));
        checkProfile.setDisplayName(StringUtils.trim(requestVo.getProfileName()));
        checkProfile.setScanner(requestVo.getScanner());
        checkProfile.setParentProfileName("");
        checkProfile.setLevel(0);
        checkProfile.setLayer(1);
        checkProfile.setRuleCount(0);
        // 继承某个规则集
        if (StringUtils.isNotBlank(requestVo.getParentProfileName())) {
            CheckProfile parentProfile = checkProfileService.getByName(requestVo.getParentProfileName());
            if (parentProfile == null) {
                throw new ThemisException(PROFILE_NOT_EXIST);
            }
            if (parentProfile.getLayer() > PlatformCommonConstants.PROFILE_MAX_INHERITABLE_LAYER) {
                throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "超过最大继承层数，建议使用复制功能！");
            }
            checkProfile.setParentProfileName(parentProfile.getProfileName());
            checkProfile.setRuleCount(parentProfile.getRuleCount());
            checkProfile.setLayer(parentProfile.getLayer() + 1);
        }
        try {
            checkProfileService.save(checkProfile);
        } catch (DuplicateKeyException e) {
            throw new ThemisException(ResultCodeConstant.PROFILE_NAME_EXIST);
        }
        requestVo.setProfileName(checkProfile.getProfileName());
        scannerHelper.afterProfileCreate(requestVo);
        return ProfileCreateResponseVo.builder()
                .profileId(checkProfile.getId())
                .profileName(checkProfile.getProfileName())
                .build();
    }

    private void checkArguments(ProfileCreateRequestVo requestVo) {
        if (StringUtils.isEmpty(requestVo.getLanguage())) {
            throw new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                    PARAMS_CAN_NOT_EMPTY.getMessage() + "language不能为空");
        }
        if (StringUtils.isEmpty(requestVo.getScanner())) {
            throw new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                    PARAMS_CAN_NOT_EMPTY.getMessage() + "scanner不能为空");
        }
        if (StringUtils.isEmpty(requestVo.getProfileName())) {
            throw new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                    PARAMS_CAN_NOT_EMPTY.getMessage() + "profileName不能为空");
        }
    }

    @Override
    public AllParentProfileResponseVo listAllParentProfiles(AllParentProfileRequestVo requestVo, String userName) {
        // 全部一级规则集列表
        CheckProfileSearchCondition searchCondition = CheckProfileSearchCondition.builder()
                .language(requestVo.getLanguage())
                .scanner(requestVo.getScanner())
                .search(null)
                .maxLayer(PlatformCommonConstants.PROFILE_MAX_INHERITABLE_LAYER)
                .page(1)
                .pageSize(Integer.MAX_VALUE)
                .build();
        IPage<CheckProfile> profileListPage = checkProfileService.pageByCondition(searchCondition);
        List<CheckProfile> parentProfiles = profileListPage.getRecords();
        if (CollectionUtils.isEmpty(parentProfiles)) {
            return AllParentProfileResponseVo.builder().profileList(Collections.emptyList()).build();
        }
        List<ProfileItem> profileItemList = parentProfiles.stream()
                .map(pp -> ProfileItem.builder()
                        .profileName(pp.getProfileName()).displayName(pp.getDisplayName()).build())
                .collect(Collectors.toList());
        return AllParentProfileResponseVo.builder().profileList(profileItemList).build();
    }

    @Override
    public Pair<String, String> getProfilePairByGitProjectId(Integer gitProjectId) {
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        if (Objects.isNull(checkRepo)) {
            throw new ThemisException(NOT_FOUND_CHECK_REPO);
        }
        List<CheckRepoBranch> checkRepoBranches = checkRepoBranchService.listInCheckRepoIdsAndVersion(
                Lists.newArrayList(checkRepo.getId()),
                CheckRepoBranchVersion.NEW_VERSION.getCode()
        );
        CheckRepoBranch checkRepoBranch = checkRepoBranches.get(0);
        List<CheckRepoBranchProfile> checkRepoBranchProfiles =
                checkRepoBranchProfileService.listByCheckRepoBranchIds(Lists.newArrayList(checkRepoBranch.getId()));
        Map<Integer, List<CheckRepoBranchProfile>> profileTypeProfiles = checkRepoBranchProfiles.stream()
                .collect(Collectors.groupingBy(CheckRepoBranchProfile::getProfileType));
        AtomicReference<String> pipelineProfileName = new AtomicReference<>();
        AtomicReference<String> offlineProfileName = new AtomicReference<>();
        Optional.ofNullable(profileTypeProfiles.get(CheckProfileType.PIPELINE.getType())).ifPresent(list -> {
            pipelineProfileName.set(list.get(0).getProfileName());
        });
        Optional.ofNullable(profileTypeProfiles.get(CheckProfileType.OFFLINE.getType())).ifPresent(list -> {
            offlineProfileName.set(list.get(0).getProfileName());
        });
        return Pair.of(pipelineProfileName.get(), offlineProfileName.get());
    }

    @Override
    public ProfileSearchResponseVo search(ProfileSearchRequestVo requestVo, String userName) {
        // 是否是父子结构
        boolean hierarchical = requestVo.getHierarchical() != null && requestVo.getHierarchical();
        CheckProfileSearchCondition searchCondition = CheckProfileSearchCondition.builder()
                .language(requestVo.getLanguage())
                .scanner(requestVo.getScanner())
                .search(requestVo.getSearch())
                .hierarchical(hierarchical)
                .maxLayer(hierarchical ? 1 : 0)
                .page(requestVo.getPage() == null ? 1 : requestVo.getPage())
                .pageSize(requestVo.getPageSize() == null ? 10 : requestVo.getPageSize())
                .build();
        IPage<CheckProfile> profileListPage = checkProfileService.pageMostUsedByCondition(searchCondition);
        // 规则集列表
        List<CheckProfile> profileList = profileListPage.getRecords();
        if (CollectionUtils.isEmpty(profileList)) {
            return ProfileSearchResponseVo.builder().ruleProfileList(Collections.emptyList()).build();
        }
        List<RuleProfile> ruleProfileList;
        // 非层级形式
        if (!hierarchical) {
            // 先转换vo，只转换需要返回的这些数据
            ruleProfileList = convert2RuleProfileListFromCheckProfileList(userName, profileList);
        } else {
            // 层级形式，那么分页只查询了一级规则集
            Set<String> profiles = profileList.stream().map(CheckProfile::getProfileName).collect(Collectors.toSet());
            // 先加入全部子规则集。
            addAllChildrenProfilesRecursively(profileList);
            // 再全部转换为vo
            ruleProfileList = convert2RuleProfileListFromCheckProfileList(userName, profileList);
            // 构建树形层级关系
            buildHierarchical(ruleProfileList, profileList);
            // 只返回一级目录
            ruleProfileList = ruleProfileList.stream()
                    .filter(r -> profiles.contains(r.getProfileName())).collect(Collectors.toList());
        }
        // 按照使用项目数排序
        sortDescByProjectsInUse(ruleProfileList);
        return ProfileSearchResponseVo.builder()
                .total(profileListPage.getTotal())
                .ruleProfileList(ruleProfileList)
                .build();
    }


    /**
     * 构建层级关系
     */
    private void buildHierarchical(List<RuleProfile> ruleProfileList, List<CheckProfile> profileList) {
        // 转map
        Map<String, RuleProfile> ruleProfileMap =
                ruleProfileList.stream().collect(Collectors.toMap(RuleProfile::getProfileName, Function.identity(), (a, b) -> a));
        // 根据父规则集分组
        Map<String, List<CheckProfile>> childrenProfileMap =
                profileList.stream().collect(Collectors.groupingBy(CheckProfile::getParentProfileName));
        // 找到所有一级规则集
        List<CheckProfile> firstLayerProfiles = childrenProfileMap.getOrDefault(StringUtils.EMPTY, Collections.emptyList());
        // 递归构建层级关系
        buildHierarchical(ruleProfileMap, childrenProfileMap, null, firstLayerProfiles);
    }

    /**
     * 逐层向下构建层级关系
     */
    private void buildHierarchical(Map<String, RuleProfile> ruleProfileMap,
            Map<String, List<CheckProfile>> childrenProfileMap,
            RuleProfile parentRuleProfile, List<CheckProfile> childrenProfiles) {
        for (CheckProfile childrenProfile : childrenProfiles) {
            RuleProfile ruleProfile = ruleProfileMap.get(childrenProfile.getProfileName());
            // 和继承过来的规则key取并集并去重
            if (ruleProfile != null) {
                // 如果需要构建层级关系
                if (parentRuleProfile != null) {
                    parentRuleProfile.getChildren().add(ruleProfile);
                }
            }
            // 递归更新下一层
            buildHierarchical(ruleProfileMap, childrenProfileMap, ruleProfile,
                    childrenProfileMap.getOrDefault(childrenProfile.getProfileName(), Collections.emptyList()));
        }
    }

    /**
     * 根据使用项目数倒序排列，使用项目数相同时，按规则数倒序排序
     */
    private void sortDescByProjectsInUse(List<RuleProfile> ruleProfileList) {
        if (CollectionUtils.isEmpty(ruleProfileList)) {
            return;
        }
        ruleProfileList.sort((a, b) -> !Objects.equals(a.getLevel(), b.getLevel())
                                       ? b.getLevel() - a.getLevel()
                                       : (a.getProjectNum() == b.getProjectNum()
                                          ? b.getRuleNum() - a.getRuleNum() : b.getProjectNum() - a.getProjectNum())
        );
        for (RuleProfile ruleProfile : ruleProfileList) {
            sortDescByProjectsInUse(ruleProfile.getChildren());
        }
    }

    private List<RuleProfile> convert2RuleProfileListFromCheckProfileList(String userName,
            List<CheckProfile> profileList) {
        if (CollectionUtils.isEmpty(profileList)) {
            return Collections.emptyList();
        }
        List<String> profileNames = profileList.stream()
                .map(CheckProfile::getProfileName)
                .collect(Collectors.toList());
        Set<String> hasChildrenProfileNameSet = checkProfileService.selectExistingParentProfileNames(profileNames);
        List<String> creators = profileList.stream()
                .map(CheckProfile::getCreator)
                .collect(Collectors.toList());
        Map<String, CheckProfile> nameProfileMap = profileList.stream()
                .collect(Collectors.toMap(CheckProfile::getProfileName, Function.identity(), (a, b) -> a));
        Map<String, List<String>> profileEditorsMap =
                platformUserRoleRelationService.groupUsersWithEditPermByProfileNameIn(profileNames);
        Map<String, Long> profileNameProjectCountMap = checkRepoBranchProfileService
                .groupCountByProfileNameInProfileNames(profileNames);
        Set<String> allUsers = new HashSet<>(creators);
        profileEditorsMap.values().forEach(allUsers::addAll);
        List<KsUserInfo> ksUserInfos = aresApi.batchGetUserInfo(allUsers);
        Map<String, KsUserInfo> userNameInfoMap = ksUserInfos.stream()
                .collect(Collectors.toMap(KsUserInfo::getUserName, Function.identity(), (a, b) -> a));
        List<RuleProfile> ruleProfileList = Lists.newArrayList();
        for (String profileName : profileNames) {
            CheckProfile checkProfile = nameProfileMap.get(profileName);
            Long usedProjectNum = profileNameProjectCountMap.get(profileName);
            ruleProfileList.add(RuleProfile.builder()
                    .displayName(checkProfile.getDisplayName())
                    .profileName(profileName)
                    .level(CheckProfileLevelEnum.getByLevel(checkProfile.getLevel()).getCode())
                    .levelDesc(CheckProfileLevelEnum.getByLevel(checkProfile.getLevel()).getDesc())
                    .creator(convertToKdevUser(userNameInfoMap, checkProfile.getCreator()))
                    .maintainers(convertMaintainers(userNameInfoMap, checkProfile, profileEditorsMap))
                    .ruleNum(checkProfile.getRuleCount())
                    .projectNum(usedProjectNum != null ? usedProjectNum.intValue() : 0)
                    .language(checkProfile.getLanguage())
                    .updateDate(DTF.format(checkProfile.getGmtModified()))
                    .operations(convertOperations(usedProjectNum, checkProfile, userName,
                            hasChildrenProfileNameSet.contains(profileName)))
                    .children(Lists.newArrayList())
                    .build());
        }
        return ruleProfileList;
    }

    private List<KdevUserInfo> convertMaintainers(Map<String, KsUserInfo> userNameInfoMap, CheckProfile checkProfile,
            Map<String, List<String>> profileEditorsMap) {
        // 加入其他编辑者
        List<String> editors = profileEditorsMap.get(checkProfile.getProfileName());
        if (CollectionUtils.isEmpty(editors)) {
            return Collections.emptyList();
        }
        return editors.stream()
                .distinct()
                .filter(user -> !user.equals(checkProfile.getCreator()))
                .map(user -> convertToKdevUser(userNameInfoMap, user)).collect(Collectors.toList());
    }

    private List<String> convertOperations(Long usedProjectNum, CheckProfile checkProfile,
            String userName, boolean hasChildren) {
        List<String> operations = Lists.newArrayList();
        String creator = checkProfile.getCreator();
        // 管理员、创建者 拥有全部权限
        if (platformPermissionService.isAdmin(userName) || StringUtils.equals(userName, creator)) {
            operations.addAll(Arrays.stream(PlatformProfileOperationEnum.values())
                    .map(PlatformProfileOperationEnum::getKey)
                    .collect(Collectors.toList()));
        } else {
            // 普通人只有拷贝权限
            operations.add(PlatformProfileOperationEnum.COPY.getKey());
            // 如果有编辑权限
            if (platformPermissionService.hasProfileEditPermission(userName, checkProfile)) {
                operations.add(PlatformProfileOperationEnum.UPDATE.getKey());
            }
        }
        Integer level = checkProfile.getLevel();
        // 系统规则 或被项目使用 或 被别的规则集继承 的规则集 不允许删除
        if (CheckProfileLevelEnum.SYSTEM.getCode().equals(level)
                || (usedProjectNum != null && usedProjectNum > 0) || hasChildren) {
            operations.removeIf(o -> PlatformProfileOperationEnum.DELETE.getKey().equals(o));
        }
        return operations;
    }

    private KdevUserInfo convertToKdevUser(Map<String, KsUserInfo> userNameInfoMap, String userName) {
        KsUserInfo ksUserInfo = userNameInfoMap.get(userName);
        KdevUserInfo kdevUserInfo = new KdevUserInfo();
        if (ksUserInfo == null) {
            return kdevUserInfo;
        }
        kdevUserInfo.setEmail(ksUserInfo.getEmail());
        kdevUserInfo.setAvatarUrl(ksUserInfo.getPhoto());
        kdevUserInfo.setId(ksUserInfo.getUserId());
        kdevUserInfo.setName(ksUserInfo.getChineseName());
        kdevUserInfo.setUsername(ksUserInfo.getUserName());
        return kdevUserInfo;
    }


    /**
     * 为 指定规则集列表 加入 其全部子孙规则集。
     */
    private void addAllChildrenProfilesRecursively(List<CheckProfile> profileList) {
        if (Objects.isNull(profileList)) {
            profileList = Lists.newArrayList();
        }
        profileList.addAll(findAllChildrenProfilesRecursively(profileList));
    }

    /**
     * 查找指定规则集的全部子孙规则集
     * 如果传进来的列表中的元素本身有父子关系，则合并过程中会有重复情况，需要去重
     */
    private List<CheckProfile> findAllChildrenProfilesRecursively(List<CheckProfile> profileList) {
        List<CheckProfile> list = Lists.newArrayList();
        if (Objects.isNull(profileList)) {
            return list;
        }
        Set<String> originalProfiles = profileList.stream().map(CheckProfile::getProfileName).collect(Collectors.toSet());
        List<CheckProfile> current = profileList;
        while (!current.isEmpty()) {
            List<String> profileNameList = current.stream().map(CheckProfile::getProfileName).collect(Collectors.toList());
            // 直接子规则集
            List<CheckProfile> childrenProfiles = checkProfileService.listByParentProfileNames(profileNameList);
            for (CheckProfile p : childrenProfiles) {
                if (!originalProfiles.contains(p.getProfileName())) {
                    list.add(p);
                }
            }
            current = childrenProfiles;
        }
        return list;
    }

    @Override
    @Transactional
    public ProfileAddRuleResponseVo addRule(ProfileAddRuleRequestVo requestVo, String userName) {
        // 1. 校验参数
        checkUserLogin(userName);
        checkArguments(requestVo);

        // 2. 获取profile规则集
        String profileName = requestVo.getProfileName();
        CheckProfile checkProfile = checkProfileService.getByName(profileName);
        if (checkProfile == null) {
            throw new ThemisException(PROFILE_NOT_EXIST);
        }

        // 3. 校验用户

        // 4. 获取db中的check_profile_rule_relation关系
        Map<String, String> ruleSeverityMap = requestVo.getRuleInfos().stream().collect(Collectors
                .toMap(ProfileAddRuleRequestVo.RuleInfo::getRuleKey, ProfileAddRuleRequestVo.RuleInfo::getSeverity,
                        (v1, v2) -> v1));
        // 保存之rule-severity的数据,用于添加操作日志
        Map<String, String> preRuleSeverity = mapRelationRuleKeySeverityByProfileName(checkProfile.getProfileName());
        String scanner = checkProfile.getScanner();
        List<String> activeRuleKeys =
                checkIllegalRuleKeysAndReturnActiveCheckRules(CollUtil.newArrayList(ruleSeverityMap.keySet()), scanner);
        // 当前profile已在数据库中存在的规则集合
        List<CheckProfileRuleRelation> dbRuleRelationList = checkProfileRuleRelationService
                .listAllByProfileNameAndRuleKeysAllStatus(profileName, activeRuleKeys);

        Map<String, CheckProfileRuleRelation> ruleKeyRelationMap = dbRuleRelationList.stream()
                .collect(Collectors.toMap(CheckProfileRuleRelation::getRuleKey, Function.identity(), (a, b) -> a));

        // 5. 计算更新relation和新增relation 并入库
        List<CheckProfileRuleRelation> needSaveRelationList = Lists.newArrayList();
        List<CheckProfileRuleRelation> needUpdateRelationList = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        fillUpRelationList(needSaveRelationList, needUpdateRelationList, ruleSeverityMap, ruleKeyRelationMap,
                profileName, userName, now);
        boolean changed = false;
        if (CollectionUtils.isNotEmpty(needSaveRelationList)) {
            checkProfileRuleRelationService.saveBatch(needSaveRelationList);
            changed = true;
        }
        if (CollectionUtils.isNotEmpty(needUpdateRelationList)) {
            checkProfileRuleRelationService.updateBatchById(needUpdateRelationList);
            changed = true;
        }

        // 无变更
        if (!changed) {
            return new ProfileAddRuleResponseVo(true);
        }

        // 更新整个链路上的profile
        updateAllChildrenProfile(checkProfile);

        // 6. 更新当前profile更新人
        checkProfileService.update(new LambdaUpdateWrapper<CheckProfile>()
                .eq(CheckProfile::getId, checkProfile.getId())
                .set(CheckProfile::getUpdater, userName));

        // 7. 添加操作日志
        CheckActionLog actionLog = fillUpActionLog(checkProfile, now, preRuleSeverity, ruleSeverityMap, userName);
        checkActionLogService.save(actionLog);

        // 8. 删除baseInfo的缓存
        deleteProfileCache(profileName);
        requestVo.setScanner(scanner);

        // 9. 异步通知维护者
        // 查询子孙规则集
        List<CheckProfile> childProfileList = findAllChildrenProfilesRecursively(List.of(checkProfile));
        CompletableFuture.runAsync(() -> notifyHeirs(checkProfile, childProfileList,
                needSaveRelationList, needUpdateRelationList, Collections.emptyList(), preRuleSeverity, userName));

        // 10. 变更底层sonarqube数据
        scannerHelper.afterProfileAddRule(requestVo);
        return new ProfileAddRuleResponseVo(true);
    }

    /**
     * 更新全部子孙规则集：规则数、规则集关联规则、层级
     */
    private void updateAllChildrenProfile(CheckProfile profile) {
        if (profile == null) {
            return;
        }
        // 找到顶层规则集
        while (StringUtils.isNotBlank(profile.getParentProfileName())) {
            profile = checkProfileService.getByName(profile.getParentProfileName());
        }
        // 查找顶层规则集激活的规则
        Map<String, String> ancestorRuleSeverityMap =
                checkProfileRuleRelationService.listActiveByProfileName(profile.getProfileName()).stream().collect(
                        Collectors.toMap(CheckProfileRuleRelation::getRuleKey,
                                CheckProfileRuleRelation::getRuleSeverity, (a, b) -> a));
        // 更新顶层规则集
        profile.setRuleCount(ancestorRuleSeverityMap.keySet().size());
        profile.setLayer(1);
        profile.setUpdater("system");
        profile.setGmtModified(LocalDateTime.now());
        checkProfileService.updateById(profile);

        // 逐层向下更新
        updateChildrenProfilesRecursively(ancestorRuleSeverityMap, profile);
    }


    private void updateChildrenProfilesRecursively(Map<String, String> ancestorRuleSeverityMap, CheckProfile parentProfile) {
        // 找到直接子规则集
        List<CheckProfile> checkProfiles = checkProfileService.listByParentProfileName(parentProfile.getProfileName());
        if (checkProfiles.isEmpty()) {
            return;
        }
        // 转map
        Map<String, CheckProfile> profileMap =
                checkProfiles.stream().collect(Collectors.toMap(CheckProfile::getProfileName, Function.identity(), (a, b) -> a));
        // 查找子规则集对应的规则
        Map<String, List<CheckProfileRuleRelation>> profileRuleRelationMap =
                checkProfileRuleRelationService.groupRuleRelationListByProfileNameInProfileNames(
                        Lists.newArrayList(profileMap.keySet()));
        LocalDateTime now = LocalDateTime.now();
        // 遍历直接子规则集
        for (CheckProfile checkProfile : checkProfiles) {
            // 复制一份
            Map<String, String> ruleSeverityMap = new HashMap<>(ancestorRuleSeverityMap);
            // 找到关联的规则列表
            List<CheckProfileRuleRelation> ruleRelationList =
                    profileRuleRelationMap.getOrDefault(checkProfile.getProfileName(), Collections.emptyList());
            // 遍历
            List<CheckProfileRuleRelation> needUpdateList = Lists.newArrayList();
            for (CheckProfileRuleRelation relation : ruleRelationList) {
                String parentSeverity = ruleSeverityMap.get(relation.getRuleKey());
                // 自身独有
                if (StringUtils.isEmpty(parentSeverity)) {
                    ruleSeverityMap.put(relation.getRuleKey(), relation.getRuleSeverity());
                } else {
                    // 等级与父规则集一致 或 低于父规则集，需要删除
                    if (parentSeverity.equals(relation.getRuleSeverity())
                            || !CheckIssueSeverity.getByAboveLevel(parentSeverity)
                            .contains(relation.getRuleSeverity())) {
                        relation.setDeleted(true);
                        relation.setUpdater("system");
                        relation.setGmtModified(now);
                        needUpdateList.add(relation);
                    } else {
                        ruleSeverityMap.put(relation.getRuleKey(), relation.getRuleSeverity());
                    }
                }
            }
            if (!needUpdateList.isEmpty()) {
                checkProfileRuleRelationService.updateBatchById(needUpdateList);
            }
            // 更新当前规则集内规则数量
            checkProfile.setRuleCount(ruleSeverityMap.keySet().size());
            checkProfile.setLayer(parentProfile.getLayer() + 1);
            checkProfile.setUpdater("system");
            checkProfile.setGmtModified(now);
            // 递归更新下层规则集
            updateChildrenProfilesRecursively(ruleSeverityMap, checkProfile);
        }
        // 批量更新规则集
        checkProfileService.updateBatchById(checkProfiles);
    }

    private void notifyHeirs(CheckProfile checkProfile,
            List<CheckProfile> childProfileList,
            List<CheckProfileRuleRelation> needSaveRelationList,
            List<CheckProfileRuleRelation> needUpdateRelationList,
            List<CheckProfileRuleRelation> needRemoveRelationList,
            Map<String, String> preRuleSeverityMap, String userName) {
        // 没有要通知的子孙规则集
        if (CollectionUtils.isEmpty(childProfileList)) {
            return;
        }
        // 没有规则变更
        if (CollectionUtils.isEmpty(needSaveRelationList) && CollectionUtils.isEmpty(needUpdateRelationList)
                && CollectionUtils.isEmpty(needRemoveRelationList)) {
            return;
        }
        String time;
        if (CollectionUtils.isNotEmpty(needSaveRelationList)) {
            time = DTF.format(needSaveRelationList.get(0).getGmtModified());
        } else if (CollectionUtils.isNotEmpty(needUpdateRelationList)) {
            time = DTF.format(needUpdateRelationList.get(0).getGmtModified());
        } else {
            time = DTF.format(needRemoveRelationList.get(0).getGmtModified());
        }
        StringBuilder sb = new StringBuilder();
        // 消息尾部
        sb.append("操作人  【<font color='red'>").append(userName).append("</font>】\n");
        sb.append("操作时间【<font color='red'>").append(time).append("</font>】");
        String msgTail = sb.toString();
        // 变更内容
        sb.setLength(0);
        String ruleUrl = "https://kdev.corp.kuaishou.com/web/codescan/scanrule/detail?ruleKey=%s";
        for (CheckProfileRuleRelation ruleRelation : needSaveRelationList) {
            sb.append("- <font color='green'>新增</font>规则【[").append(ruleRelation.getRuleKey()).append("](")
                    .append(String.format(ruleUrl, ruleRelation.getRuleKey())).append(")】，严重等级为【<font color='blue'>")
                    .append(CheckIssueSeverity.valueOf(ruleRelation.getRuleSeverity()).getDesc())
                    .append("</font>】\n");
        }
        for (CheckProfileRuleRelation ruleRelation : needUpdateRelationList) {
            sb.append("- <font color='orange'>修改</font>规则【[").append(ruleRelation.getRuleKey()).append("](")
                    .append(String.format(ruleUrl, ruleRelation.getRuleKey())).append(")】，严重等级由【<font color='blue'>")
                    .append(CheckIssueSeverity.valueOf(preRuleSeverityMap.get(ruleRelation.getRuleKey())).getDesc())
                    .append("</font>】变更为【<font color='blue'>")
                    .append(CheckIssueSeverity.valueOf(ruleRelation.getRuleSeverity()).getDesc())
                    .append("</font>】\n");
        }
        for (CheckProfileRuleRelation relation : needRemoveRelationList) {
            sb.append("- <font color='orange'>删除</font>规则【[").append(relation.getRuleKey()).append("](")
                    .append(String.format(ruleUrl, relation.getRuleKey())).append(")】\n");
        }
        // 构造消息头 + 消息体
        String msgContent = sb.toString();
        String parentProfileName = checkProfile.getProfileName();
        String parentDisplayName = checkProfile.getDisplayName();
        String profileUrl = "https://kdev.corp.kuaishou.com/web/codescan/rules/setting?profileName=%s";
        for (CheckProfile childProfile : childProfileList) {
            // 查询规则集维护者
            Set<String> profileEditors =
                    platformUserRoleRelationService.listProfileEditorsByProfileName(childProfile.getProfileName())
                            .stream().map(PlatformUserRoleRelation::getUserName).collect(Collectors.toSet());
            profileEditors.add(childProfile.getCreator());
            String profileName = childProfile.getProfileName();
            String displayName = childProfile.getDisplayName();
            sb.setLength(0);
            sb.append("## 代码扫描平台-规则集规则变更通知\n")
                    .append(childProfile.getCreator()).append("您好，您维护的规则集【[").append(displayName).append("](")
                    .append(String.format(profileUrl, profileName)).append(")】")
                    .append("所继承的父规则集【[").append(parentDisplayName).append("](")
                    .append(String.format(profileUrl, parentProfileName)).append(")】")
                    .append("中，发生了如下变更：\n");
            sb.append(msgContent);
            sb.append("\n");
            sb.append(msgTail);
            // 发送
            aresApi.sendKimNotice(AresMessageEntity.builder()
                            .templateId(0)
                            .msgTypes(Lists.newArrayList(7))
                            .userNames(profileEditors)
                            .text(sb.toString())
                    .build());
        }
    }


    @Override
    @Transactional
    public ProfileUpdateRuleResponse updateRule(ProfileAddRuleRequestVo requestVo, String userName) {
        return null;
    }

    private List<String> listRelationRuleKeyByProfileName(String profileName) {
        List<CheckProfileRuleRelation> ruleRelationList =
                checkProfileRuleRelationService.listActiveByProfileName(profileName);
        return ruleRelationList.stream()
                .map(CheckProfileRuleRelation::getRuleKey)
                .collect(Collectors.toList());
    }

    private Map<String, String> mapRelationRuleKeySeverityByProfileName(String profileName) {
        List<CheckProfileRuleRelation> ruleRelationList =
                checkProfileRuleRelationService.listActiveByProfileName(profileName);
        return ruleRelationList.stream()
                .collect(Collectors.toMap(CheckProfileRuleRelation::getRuleKey,
                        CheckProfileRuleRelation::getRuleSeverity, (v1, v2) -> v1));
    }

    private CheckActionLog fillUpActionLog(CheckProfile checkProfile, LocalDateTime now, Object preRuleKeyList,
            Object afterRuleKeyList, String userName) {
        return CheckActionLog.builder()
                .gmtModified(now)
                .gmtCreate(now)
                .profileName(checkProfile.getProfileName())
                .type(CheckActionLogEnum.PROFILE_RULE_UPDATE.getType())
                .changeBefore(JSONUtils.serialize(preRuleKeyList))
                .changeAfter(JSONUtils.serialize(afterRuleKeyList))
                .operator(userName)
                .build();
    }

    private void checkUserLogin(String userName) {
        if (StringUtils.isEmpty(userName)) {
            throw new ThemisException(ResultCodeConstant.NO_PERMISSION);
        }
    }

    private void checkArguments(ProfileAddRuleRequestVo requestVo) {
        if (StringUtils.isEmpty(requestVo.getProfileName())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "profileName不能为空");
        }
        if (CollectionUtils.isEmpty(requestVo.getRuleInfos())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "ruleList不能为空");
        }
    }

    private void fillUpRelationList(List<CheckProfileRuleRelation> needSaveRelationList,
            List<CheckProfileRuleRelation> needUpdateRelationList, Map<String, String> ruleSeverityMap,
            Map<String, CheckProfileRuleRelation> ruleKeyRelationMap, String profileName, String userName,
            LocalDateTime now) {
        Set<String> dbRelationKeys = ruleKeyRelationMap.keySet();
        for (Entry<String, String> entry : ruleSeverityMap.entrySet()) {
            String activeRuleKey = entry.getKey();
            String severity = entry.getValue();
            if (dbRelationKeys.contains(activeRuleKey)) {
                CheckProfileRuleRelation ruleRelation = ruleKeyRelationMap.get(activeRuleKey);
                ruleRelation.setDeleted(false);
                ruleRelation.setUpdater(userName);
                ruleRelation.setGmtModified(now);
                ruleRelation.setRuleSeverity(severity);
                needUpdateRelationList.add(ruleRelation);
            } else {
                CheckProfileRuleRelation ruleRelation = new CheckProfileRuleRelation();
                ruleRelation.setProfileName(profileName);
                ruleRelation.setRuleKey(activeRuleKey);
                ruleRelation.setCreator(userName);
                ruleRelation.setUpdater(userName);
                ruleRelation.setGmtCreate(now);
                ruleRelation.setGmtModified(now);
                ruleRelation.setRuleSeverity(severity);
                needSaveRelationList.add(ruleRelation);
            }
        }
    }

    private List<String> checkIllegalRuleKeysAndReturnActiveCheckRules(List<String> ruleKeyList, String scanner) {
        List<CheckRule> checkRules = checkRuleService.listInRuleKeys(ruleKeyList);
        if (CollectionUtils.isEmpty(checkRules)) {
            throw new ThemisException(ResultCodeConstant.RULE_NOT_EXIST);
        }
        List<String> activeRuleKeys = Lists.newArrayList();
        for (CheckRule checkRule : checkRules) {
            if (!scanner.equals(checkRule.getScanner())) {
                // 允许加入其他扫描器的规则
                log.warn("[规则集增加规则] 扫描器类型不匹配，扫描器：{},规则：{}", scanner, checkRule.getRuleKey());
            }
            activeRuleKeys.add(checkRule.getRuleKey());
        }
        return activeRuleKeys;
    }

    @Override
    @Transactional
    public ProfileDeleteRuleResponseVo deleteRule(ProfileDeleteRuleRequestVo requestVo, String userName) {
        checkUserLogin(userName);
        checkArguments(requestVo);
        String profileName = requestVo.getProfileName();
        List<String> ruleKeyList = requestVo.getRuleInfos().stream().map(ProfileAddRuleRequestVo.RuleInfo::getRuleKey)
                .collect(Collectors.toList());
        CheckProfile checkProfile = checkProfileService.getByName(profileName);
        if (checkProfile == null) {
            throw new ThemisException(PROFILE_NOT_EXIST);
        }
        LocalDateTime now = LocalDateTime.now();
        List<String> preRuleKeyList = listRelationRuleKeyByProfileName(checkProfile.getProfileName());

        List<CheckProfileRuleRelation> ruleRelationList =
                checkProfileRuleRelationService.listAllByProfileNameAndRuleKeys(profileName, ruleKeyList);
        if (CollectionUtils.isEmpty(ruleRelationList)) {
            return new ProfileDeleteRuleResponseVo();
        }
        for (CheckProfileRuleRelation ruleRelation : ruleRelationList) {
            ruleRelation.setGmtModified(now);
            ruleRelation.setDeleted(true);
            ruleRelation.setUpdater(userName);
        }

        checkProfileRuleRelationService.updateBatchById(ruleRelationList);

        // 更新整个链路上的profile
        updateAllChildrenProfile(checkProfile);

        // 更新当前profile更新人
        checkProfileService.update(new LambdaUpdateWrapper<CheckProfile>()
                .eq(CheckProfile::getId, checkProfile.getId())
                .set(CheckProfile::getUpdater, userName));


        List<String> afterRuleKeyList = listRelationRuleKeyByProfileName(checkProfile.getProfileName());
        CheckActionLog actionLog = fillUpActionLog(checkProfile, now, preRuleKeyList, afterRuleKeyList, userName);
        checkActionLogService.save(actionLog);

        // 删除baseInfo的缓存
        deleteProfileCache(profileName);
        requestVo.setScanner(checkProfile.getScanner());

        // 异步通知维护者
        // 查找所有子孙规则集
        List<CheckProfile> childProfileList = findAllChildrenProfilesRecursively(List.of(checkProfile));
        CompletableFuture.runAsync(() -> notifyHeirs(checkProfile, childProfileList, Collections.emptyList(),
                    Collections.emptyList(), ruleRelationList, Collections.emptyMap(), userName));

        // 变更底层sonarqube数据
        scannerHelper.afterProfileDeleteRule(requestVo);

        return new ProfileDeleteRuleResponseVo(true);
    }

    private void checkArguments(ProfileDeleteRuleRequestVo requestVo) {
        if (StringUtils.isEmpty(requestVo.getProfileName())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "profileName不能为空");
        }
        if (CollectionUtils.isEmpty(requestVo.getRuleInfos())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "ruleList不能为空");
        }
    }

    @Override
    public ProfileBaseInfoResponseVo baseInfo(ProfileBaseInfoRequestVo requestVo) {
        checkArguments(requestVo);
        String profileName = requestVo.getProfileName();
        final String redisKey = KsRedisPrefixConstant.PLATFORM_PROFILE_BASE_INFO_PREFIX + profileName;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            List<ProfileTypeInfo> typeInfoList = JSONUtils.deserializeList(redisVal, ProfileTypeInfo.class);
            return ProfileBaseInfoResponseVo
                    .builder()
                    .typeInfoList(typeInfoList)
                    .build();
        }
        CheckProfile checkProfile = checkProfileService.getByName(profileName);
        if (checkProfile == null) {
            throw new ThemisException(PROFILE_NOT_EXIST);
        }
        List<CheckProfileRuleRelation> relations = checkProfileRuleRelationService.listActiveByProfileName(profileName);
        List<String> ruleKeys = relations.stream()
                .map(CheckProfileRuleRelation::getRuleKey)
                .collect(Collectors.toList());
        List<CheckRule> checkRules = checkRuleService.listInRuleKeys(ruleKeys);
        Map<String, Long> typeCount = checkRules.stream()
                .collect(Collectors.groupingBy(CheckRule::getRuleType, Collectors.counting()));
        List<ProfileTypeInfo> typeInfoList = typeCount.entrySet().stream()
                .map(entry -> ProfileTypeInfo.builder().type(entry.getKey()).count(entry.getValue()).build())
                .collect(Collectors.toList());
        ksRedisClient.sync().setex(redisKey, TimeUnit.DAYS.toSeconds(1), JSONUtils.serialize(typeInfoList));
        return ProfileBaseInfoResponseVo.builder().typeInfoList(typeInfoList).build();
    }

    @Override
    public ProfileGetByNameResponseVo getByProfileName(ProfileGetByNameRequestVo requestVo) {
        checkArguments(requestVo);
        String profileName = requestVo.getProfileName();
        final String redisKey = KsRedisPrefixConstant.PLATFORM_PROFILE_INFO_PREFIX + profileName;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return JSONUtils.deserialize(redisVal, ProfileGetByNameResponseVo.class);
        }
        CheckProfile checkProfile = checkProfileService.getByName(requestVo.getProfileName());
        if (checkProfile == null) {
            throw new ThemisException(PROFILE_NOT_EXIST);
        }
        ProfileGetByNameResponseVo getByNameResponseVo = ProfileGetByNameResponseVo.builder()
                .displayName(checkProfile.getDisplayName())
                .build();
        ksRedisClient.sync().setex(redisKey, TimeUnit.DAYS.toSeconds(1), JSONUtils.serialize(getByNameResponseVo));
        return getByNameResponseVo;
    }

    @Override
    @Transactional
    public ProfileDeleteResponseVo delete(ProfileDeleteRequestVo requestVo, String userName) {
        checkArguments(requestVo);
        String profileName = requestVo.getProfileName();
        CheckProfile checkProfile = checkProfileService.getByName(profileName);
        if (checkProfile == null) {
            throw new ThemisException(PROFILE_NOT_EXIST);
        }
        checkCanDelete(checkProfile, profileName);
        checkUserPermission(checkProfile, userName);
        checkProfileService.updateProfileToDelete(profileName, userName);
        checkProfileRuleRelationService.updateRelationToDelete(profileName, userName);
        LocalDateTime now = LocalDateTime.now();
        checkActionLogService.save(CheckActionLog.builder()
                .profileName(profileName)
                .operator(userName)
                .gmtCreate(now)
                .gmtModified(now)
                .changeBefore(StringUtils.EMPTY)
                .changeAfter(StringUtils.EMPTY)
                .type(CheckActionLogEnum.PROFILE_RULE_DELETE.getType())
                .build());
        requestVo.setScanner(checkProfile.getScanner());
        requestVo.setLanguage(checkProfile.getLanguage());
        // 移除授权的用户记录
        platformUserRoleRelationService.removeProfileEditorsByProfileName(profileName);
        scannerHelper.afterProfileDelete(requestVo);
        return new ProfileDeleteResponseVo(true);
    }

    @Override
    @Transactional
    public ProfileCopyResponseVo copyProfile(ProfileCopyRequestVo requestVo, String userName) {
        checkArguments(requestVo);
        String profileName = requestVo.getProfileName();
        CheckProfile checkProfile = checkProfileService.getByName(profileName);
        if (checkProfile == null) {
            throw new ThemisException(PROFILE_NOT_EXIST);
        }

        // 创建base数据
        LocalDateTime now = LocalDateTime.now();
        CheckProfile newCheckProfile = new CheckProfile();
        newCheckProfile.setUpdater(userName);
        newCheckProfile.setCreator(userName);
        newCheckProfile.setGmtCreate(now);
        newCheckProfile.setGmtModified(now);
        newCheckProfile.setDisplayName(StringUtils.trim(requestVo.getDisplayName()));
        newCheckProfile.setLanguage(checkProfile.getLanguage());
        newCheckProfile.setProfileName(UUID.fastUUID().toString().replace("-", ""));
        newCheckProfile.setScanner(checkProfile.getScanner());
        newCheckProfile.setParentProfileName("");
        newCheckProfile.setParentProfileName(checkProfile.getParentProfileName());
        newCheckProfile.setLayer(checkProfile.getLayer());
        newCheckProfile.setRuleCount(checkProfile.getRuleCount());
        try {
            checkProfileService.save(newCheckProfile);
        } catch (DuplicateKeyException e) {
            throw new ThemisException(ResultCodeConstant.PROFILE_NAME_EXIST);
        }

        String newProfileName = newCheckProfile.getProfileName();
        // 创建扩展数据
        // 被copy的数据
        List<CheckProfileRuleRelation> checkProfileRuleRelations =
                checkProfileRuleRelationService.listActiveByProfileName(requestVo.getProfileName());
        for (CheckProfileRuleRelation checkProfileRuleRelation : checkProfileRuleRelations) {
            checkProfileRuleRelation.setId(null);
            checkProfileRuleRelation.setGmtCreate(now);
            checkProfileRuleRelation.setGmtModified(now);
            checkProfileRuleRelation.setProfileName(newProfileName);
        }
        checkProfileRuleRelationService.saveBatch(checkProfileRuleRelations);

        requestVo.setScanner(checkProfile.getScanner());
        requestVo.setToProfileKey(newProfileName);
        requestVo.setLanguage(checkProfile.getLanguage());
        scannerHelper.afterProfileCopy(requestVo);
        return ProfileCopyResponseVo.builder().displayName(requestVo.getDisplayName()).build();
    }

    private void checkArguments(ProfileCopyRequestVo requestVo) {
        String profileName = requestVo.getProfileName();
        Assert.notEmpty(profileName, () -> new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                PARAMS_CAN_NOT_EMPTY.getMessage() + "profileName"));

        String displayName = requestVo.getDisplayName();
        Assert.notEmpty(displayName, () -> new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                PARAMS_CAN_NOT_EMPTY.getMessage() + "displayName"));
    }

    /**
     * 检查用户是否有规则集删除权限
     */
    private void checkUserPermission(CheckProfile checkProfile, String userName) {
        String creator = checkProfile.getCreator();
        if (platformPermissionService.isAdmin(userName)) {
            return;
        }
        platformPermissionService.checkUserNameEquals(userName, creator);
    }

    private void checkCanDelete(CheckProfile checkProfile, String profileName) {
        // 检查有多少项目在使用
        Map<String, Long> profileNameProjectCountMap = checkRepoBranchProfileService
                .groupCountByProfileNameInProfileNames(Lists.newArrayList(profileName));
        Long usedProjectNum = profileNameProjectCountMap.get(profileName);
        if (usedProjectNum != null && usedProjectNum > 0) {
            throw new ThemisException(PROFILE_IS_USING_NOT_DELETE);
        }
        // 检查是否有子规则集
        if (CollectionUtils.isNotEmpty(checkProfileService.listByParentProfileName(checkProfile.getProfileName()))) {
            throw new ThemisException(PROFILE_IS_USING_NOT_DELETE);
        }
    }

    private void checkArguments(ProfileDeleteRequestVo requestVo) {
        String profileName = requestVo.getProfileName();
        Assert.notEmpty(profileName, () -> new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                PARAMS_CAN_NOT_EMPTY.getMessage() + "profileName"));
    }

    private void checkArguments(ProfileGetByNameRequestVo requestVo) {
        if (StringUtils.isEmpty(requestVo.getProfileName())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "profileName不能为空");
        }
    }

    private void checkArguments(ProfileBaseInfoRequestVo requestVo) {
        if (StringUtils.isEmpty(requestVo.getProfileName())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "profileName不能为空");
        }
    }

    private void deleteProfileCache(String profileName) {
        final String redisKey = KsRedisPrefixConstant.PLATFORM_PROFILE_BASE_INFO_PREFIX + profileName;
        ksRedisClient.sync().del(redisKey);
    }

    @Transactional
    @Override
    public PlatformPermissionGrantResponse grantProfileEditPermission(PlatformPermissionGrantRequestVo requestVo,
            String userName) {
        checkParams(requestVo);
        CheckProfile checkProfile = checkProfileService.getByName(requestVo.getProfileName());
        // 已经有权限的用户
        List<String> existTargetUsers =
                platformUserRoleRelationService.listProfileEditorsByProfileName(checkProfile.getProfileName())
                        .stream().map(PlatformUserRoleRelation::getUserName).collect(Collectors.toList());
        existTargetUsers.add(checkProfile.getCreator());
        log.info("规则集管理权限授权：操作人：{}, 授权对象：{}，已经存在权限的用户：{}",
                userName, requestVo.getTargetUsers().toString(), existTargetUsers.toString());
        // 需要授权的用户
        List<String> userList = CollUtil.subtractToList(requestVo.getTargetUsers(), existTargetUsers);
        if (CollectionUtils.isEmpty(userList)) {
            return PlatformPermissionGrantResponse.builder().result(true).build();
        }
        // 新增记录
        LocalDateTime now = LocalDateTime.now();
        List<PlatformUserRoleRelation> needSaveList = userList.stream().map(
                targetUser -> PlatformUserRoleRelation.builder()
                .creator(userName)
                .userName(targetUser)
                .roleName(PlatformRoleEnum.PROFILE_EDITOR.getKey())
                .profileName(checkProfile.getProfileName())
                .gmtCreate(now)
                .build()).collect(Collectors.toList());
        platformUserRoleRelationService.saveBatch(needSaveList);

        return PlatformPermissionGrantResponse.builder().result(true).build();
    }

    @Override
    public Boolean changeParentProfile(ChangeParentProfileRequest requestVo) {
        CheckProfile checkProfile = checkProfileService.getNonnullByName(requestVo.getProfileName());
        // 已经是指定的了
        if (checkProfile.getParentProfileName().equals(requestVo.getParentProfileName())) {
            return true;
        }
        CheckProfile parentProfile = checkProfileService.getNonnullByName(requestVo.getParentProfileName());
        if (parentProfile.getLayer() > PlatformCommonConstants.PROFILE_MAX_INHERITABLE_LAYER) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "超过最大继承层数，建议使用复制功能！");
        }
        // 更新父规则集
        checkProfile.setParentProfileName(requestVo.getParentProfileName());
        // 更新层级
        checkProfile.setLayer(parentProfile.getLayer() + 1);
        // 这里给个默认值就行，等下面方法更新链路
        checkProfile.setRuleCount(0);
        checkProfile.setUpdater("system");
        checkProfile.setGmtModified(LocalDateTime.now());
        checkProfileService.updateById(checkProfile);

        // 自顶向下更新所有子孙规则集的层级
        updateAllChildrenProfile(parentProfile);

        // 变更底层sonarqube数据
        requestVo.setScanner(checkProfile.getScanner());
        requestVo.setLanguage(checkProfile.getLanguage());
        scannerHelper.afterChangeParentProfile(requestVo);
        return true;
    }

    private void checkParams(PlatformPermissionGrantRequestVo requestVo) {
        if (CollectionUtils.isEmpty(requestVo.getTargetUsers())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "targetUsers不能为空");
        }
        if (StringUtils.isBlank(requestVo.getProfileName())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "profileName不能为空");
        }
    }

}
