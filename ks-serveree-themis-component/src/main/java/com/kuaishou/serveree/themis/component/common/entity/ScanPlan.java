package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;
import com.kuaishou.serveree.themis.component.entity.statics.ScanProjectInfo;
import com.kuaishou.serveree.themis.component.entity.statics.UserScanProjectInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ScanPlan对象", description = "")
public class ScanPlan implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    /**
     * 扫描项目配置 {@link ScanProjectInfo} 的json结构
     */
    @ApiModelProperty(value = "扫描项目信息")
    private String projectInfo;

    /**
     * 扫描任务 为List<Integer>的json结构
     */
    @ApiModelProperty(value = "扫描case")
    private String scanCaseTypes;

    private Boolean deleted;

    private String applicant;

    private String scanCases;

    private Integer sponsorChannel;

    /**
     * 用户自己输入的projectInfo {@link UserScanProjectInfo} 的json结构
     */
    private String userProjectInfo;


}
