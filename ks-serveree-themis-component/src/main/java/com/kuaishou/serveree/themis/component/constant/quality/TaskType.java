package com.kuaishou.serveree.themis.component.constant.quality;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/5/26 11:44 AM
 */
@AllArgsConstructor
public enum TaskType {

    PLATFORM_OFFLINE("PLATFORM_OFFLINE", "平台离线扫描"),
    PLATFORM_PIPELINE("PLATFORM_PIPELINE", "平台流水线扫描"),
    ;

    @Getter
    private final String type;
    @Getter
    private final String desc;

    public static TaskType getByType(String type) {
        for (TaskType value : TaskType.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        throw new IllegalArgumentException("unknown task type: " + type);
    }

}
