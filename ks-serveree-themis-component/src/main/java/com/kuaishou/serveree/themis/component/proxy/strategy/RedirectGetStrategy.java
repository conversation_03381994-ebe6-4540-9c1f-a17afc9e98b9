package com.kuaishou.serveree.themis.component.proxy.strategy;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.proxy.SonarConfigEnum;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.vo.response.proxy.SonarConfigResp;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-16
 */
@Component
public class RedirectGetStrategy implements ProxyRedirectStrategy {
    @Value("${sonar.timeout}")
    private Integer timeout;
    @Autowired
    private ParamsHandleService paramsHandleService;

    @Override
    public String doRedirect(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, Object> params = CommonUtils.transferToHashMap(parameterMap);
        SonarConfigResp configResp = paramsHandleService.findProjectIdFromParams(params, request.getRequestURI());
        SonarConfigEnum configEnum = configResp.getConfigEnum();
        String sonarDomain = configResp.getSonarDomain();
        String resultResp = HttpRequest
                .get(sonarDomain)
                .header(Header.AUTHORIZATION, configEnum.getBasicAuth())
                .form(params)
                .timeout(timeout)//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            throw new ThemisException(ResultCodeConstant.TRANSFER_FAILED);
        }
        return resultResp;
    }
}
