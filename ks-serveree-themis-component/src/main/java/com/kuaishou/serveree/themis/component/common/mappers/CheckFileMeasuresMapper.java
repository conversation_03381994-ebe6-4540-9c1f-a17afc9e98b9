package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.kuaishou.serveree.themis.component.common.entity.CheckFileMeasures;
import com.kuaishou.serveree.themis.component.config.mybatis.RootMapper;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Mapper
@Repository
public interface CheckFileMeasuresMapper extends RootMapper<CheckFileMeasures> {

    List<CheckFileMeasures> listByMetricKeyAndSuffix(@Param("checkRepoId") long repoId,
            @Param("checkRepoBranchId") long branchId,
            @Param("baseId") long baseId, @Param("metricKey") String metricKey,
            @Param("filesOfSuffixes") List<Long> filesOfSuffixes, @Param("asc") boolean asc, @Param("limit") Integer limit);

    Integer listByFileIdAndLimit(@Param("checkRepoId") long repoId, @Param("checkRepoBranchId") long branchId,
            @Param("baseId") long baseId, @Param("metricKey") String metricKey, @Param("fileIds") List<Long> fileIds,
            @Param("minLines") int minLines);
}
