package com.kuaishou.serveree.themis.component.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/5/22 11:36 AM
 */
@Configuration
public class EmailConfig {

    private static final Kconf<MailProperties> EMAIL_KCONF =
            Kconfs.ofJson("qa.themis.mailProperties", new MailProperties(), MailProperties.class).build();

    @Bean
    JavaMailSender javaMailSender() {
        MailProperties mailProperties = EMAIL_KCONF.get();
        JavaMailSenderImpl javaMailSender = new JavaMailSenderImpl();
        javaMailSender.setHost(mailProperties.getHost());
        javaMailSender.setPort(mailProperties.getPort());
        javaMailSender.setUsername(mailProperties.getUsername());
        javaMailSender.setPassword(mailProperties.getPassword());
        return javaMailSender;
    }

    @Data
    static class MailProperties {
        /**
         * 用户
         */
        private String username;

        /**
         * 密码
         */
        private String password;

        /**
         * host
         */
        private String host;

        /**
         * 端口
         */
        private Integer port;

    }


}
