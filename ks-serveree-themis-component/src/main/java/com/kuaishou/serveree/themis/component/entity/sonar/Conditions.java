package com.kuaishou.serveree.themis.component.entity.sonar;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/7/21 4:52 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Conditions {

    private String metric;
    private String operator;
    private String value;
    private String status;
    private String errorThreshold;

}
