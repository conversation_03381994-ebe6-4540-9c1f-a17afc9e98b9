package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Paging;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarProject;

/**
 * <AUTHOR>
 * @since 2022/12/16 9:57 AM
 */
public class ProfileProjectsResp {

    private Paging paging;
    private List<SonarProject> results;

    public ProfileProjectsResp() {
    }

    public Paging getPaging() {
        return paging;
    }

    public void setPaging(Paging paging) {
        this.paging = paging;
    }

    public List<SonarProject> getResults() {
        return results;
    }

    public void setResults(List<SonarProject> results) {
        this.results = results;
    }

}
