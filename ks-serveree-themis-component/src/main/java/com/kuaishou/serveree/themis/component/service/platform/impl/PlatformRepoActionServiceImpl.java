package com.kuaishou.serveree.themis.component.service.platform.impl;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterSimpleFactory;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarOperations;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranchProfile;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.CheckProfileType;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.entity.platform.LanguageSetting;
import com.kuaishou.serveree.themis.component.entity.sonar.Profiles;
import com.kuaishou.serveree.themis.component.entity.sonar.req.QualityProfileReq;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.QualityProfileResp;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoActionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoService;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.PlatformSonarInfoUtils;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;

import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @since 2023/1/9 4:28 PM
 */
@Service
public class PlatformRepoActionServiceImpl implements PlatformRepoActionService {

    @Autowired
    private CheckRepoService checkRepoService;
    @Autowired
    private CheckRepoBranchService checkRepoBranchService;
    @Autowired
    private CheckRepoBranchProfileService checkRepoBranchProfileService;
    @Autowired
    private PlatformRepoService platformRepoService;
    @Autowired
    private GitOperations gitOperations;
    @Autowired
    private SonarClusterSimpleFactory sonarClusterSimpleFactory;
    @Autowired
    private PlatformSonarInfoUtils platformSonarInfoUtils;

    @Override
    public void createIfNotExit(Integer projectId, String language, String languageVersion) {
        createIfNotExit(projectId, language, languageVersion, "");
    }

    @Override
    public void createIfNotExit(Integer projectId, String language, String languageVersion, String profileName) {
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(projectId);
        profileName = StringUtils.isNotEmpty(profileName) ? profileName : "Sonar way [kuaishou]";
        if (checkRepo == null || checkRepo.getVersion() != 1) {
            RepoCreateRequest createRequest = RepoCreateRequest.builder()
                    .gitProjectId(projectId)
                    .branch("master")
                    .repoUrl(gitOperations.getCacheProject(projectId).getSshUrl())
                    .profileName(profileName)
                    .scanner(PlatformScannerEnum.SONAR_SCANNER_NEW.getScanner())
                    .triggerCron("0 0 00 ? * MON,WED,FRI,SUN,SAT,THU,TUE")
                    .languageSetting(LanguageSetting.builder()
                            .language(language)
                            .version(languageVersion)
                            .build()
                    )
                    .build();
            platformRepoService.create(createRequest, "system");
        } else {
            List<CheckRepoBranchProfile> checkRepoBranchProfiles =
                    checkRepoBranchProfileService.listByCheckRepoId(checkRepo.getId());
            if (checkRepoBranchProfiles.size() >= 2) {
                return;
            }

            this.createPipelineProfileAndBindSonarqube(checkRepoBranchProfiles, profileName, projectId, language,
                    checkRepo);
        }
    }

    private void createPipelineProfileAndBindSonarqube(List<CheckRepoBranchProfile> checkRepoBranchProfiles,
            String profileName, Integer projectId, String language, CheckRepo checkRepo) {
        CheckRepoBranchProfile pipelineCheckRepoBranchProfile = new CheckRepoBranchProfile();
        BeanUtil.copyProperties(checkRepoBranchProfiles.get(0), pipelineCheckRepoBranchProfile, "id");
        pipelineCheckRepoBranchProfile.setProfileName(profileName);
        pipelineCheckRepoBranchProfile.setProfileType(CheckProfileType.PIPELINE.getType());
        pipelineCheckRepoBranchProfile.setCheckRepoId(checkRepo.getId());
        LocalDateTime now = LocalDateTime.now();
        pipelineCheckRepoBranchProfile.setGmtCreate(now);
        pipelineCheckRepoBranchProfile.setGmtModified(now);
        checkRepoBranchProfileService.save(pipelineCheckRepoBranchProfile);

        SonarOperations sonarOperations = sonarClusterSimpleFactory.getClusterOperations(Long.valueOf(projectId));
        SonarCommonApi sonarCommonApi = sonarOperations.sonarApi();
        // 增加流水线项目的配置与绑定
        String pipelineProjectKey = platformSonarInfoUtils.getPipelineFinalProjectKey(projectId);
        String pipelineProjectName = platformSonarInfoUtils.getPipelineProjectName(
                GitUtils.getRepoName(gitOperations.getProject(projectId).getSshUrl())
        );
        // 创建项目 并且绑定profile
        try {
            sonarCommonApi.createProject(pipelineProjectName, pipelineProjectKey, "private");
        } catch (ThemisException e) {
            if (ResultCodeConstant.SONAR_PROJECTKEY_ALREADY_EXISTS.getCode() != e.getCode()) {
                throw new RuntimeException(e);
            }
        }
        sonarCommonApi.addProjectQualityprofile(language, pipelineProjectKey, profileName);
    }

    private String getProjectIdPipelineProfileName(Integer projectId) {
        SonarOperations clusterOperations = sonarClusterSimpleFactory.getClusterOperations(Long.valueOf(projectId));
        QualityProfileResp qualityProfile = clusterOperations.sonarApi().getQualityProfile(
                QualityProfileReq.builder().project(String.valueOf(projectId)).language("java").build());
        Profiles profiles = qualityProfile.getProfiles().get(0);
        return profiles.getName();
    }

}
