package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;
import com.kuaishou.serveree.themis.component.constant.kdev.MrStuckPointResultStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * MR检查点检查结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MrCheckPointResult对象", description = "MR检查点检查结果表")
public class MrCheckPointResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "git仓库ID")
    private Long gitProjectId;

    @ApiModelProperty(value = "CR单的ID")
    private Long mrId;

    @ApiModelProperty(value = "源分支的commitId")
    private String commitId;

    /**
     * {@link com.kuaishou.serveree.themis.component.constant.kdev.MrCheckpointEnum}
     */
    @ApiModelProperty(value = "检查点名称")
    private String checkpointName;

    /**
     * {@link MrStuckPointResultStatus}
     */
    @ApiModelProperty(value = "检查状态")
    private String status;

    @ApiModelProperty(value = "流水线id")
    private Long pipelineId;

    @ApiModelProperty(value = "流水线构建id")
    private Long buildId;

    @ApiModelProperty(value = "流水线地址")
    private String buildUrl;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;
}
