package com.kuaishou.serveree.themis.component.entity.ares;

import java.util.Collection;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/10/12 8:05 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AresMessageEntity {

    private String appId;

    private List<Integer> msgTypes;

    private String text;

    private Integer templateId;

    private Collection<String> userNames;

    private RobotParam robotParam;

}
