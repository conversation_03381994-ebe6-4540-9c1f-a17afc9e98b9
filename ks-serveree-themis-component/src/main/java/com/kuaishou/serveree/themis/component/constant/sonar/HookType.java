package com.kuaishou.serveree.themis.component.constant.sonar;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/12/22 8:12 下午
 */
@AllArgsConstructor
public enum HookType {

    MAVEN_SCANNER_NEW_PLUGIN_HOOK("maven_scanner_new_plugin_hook", "maven scanner扫描插件hook"),
    COMMON_PROCESS_HOOK("common_process_hook", "通用hook插件"),
    ;

    @Getter
    private String type;

    @Getter
    private String desc;

    private static final Map<String, HookType> TYPE_MAP = Maps.newHashMap();

    static {
        for (HookType hookType : HookType.values()) {
            TYPE_MAP.put(hookType.getType(), hookType);
        }
    }

    public static HookType getByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return null;
        }
        return TYPE_MAP.get(type);
    }

}
