package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.kuaishou.serveree.themis.component.common.entity.CheckFileIssue;
import com.kuaishou.serveree.themis.component.config.mybatis.RootMapper;
import com.kuaishou.serveree.themis.component.entity.platform.FileIssueNumDto;


/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Mapper
@Repository
public interface CheckFileIssueMapper extends RootMapper<CheckFileIssue> {

    List<FileIssueNumDto> fileIssueNumSort(@Param("repoId") long repoId, @Param("branchId") long branchId,
            @Param("baseId") long baseId, @Param("limit") Integer limit);


    List<FileIssueNumDto> ruleIssueNumSort(@Param("repoId") long repoId, @Param("branchId") long branchId,
            @Param("baseId") long baseId, @Param("limit") Integer limit);
}
