package com.kuaishou.serveree.themis.component.constant.quality;

/**
 * <AUTHOR>
 * @since 2020/11/2 6:10 下午
 */
public class CheckResultConstant {

    public static final String GIT_CLONE_FAIL = "clone代码失败";

    public static final String BUILD_CHECK_FAIL = "编译检查失败";

    public static final String BUILD_CHECK_ERROR = "编译运行异常";

    public static final String PLUGIN_SERVICE_CHECK_ERROR = "插件检查服务出现异常";

    public static final String JAVA_KS_PLUGIN_CHECK_ERROR = "java快手插件检查异常";

    public static final String CHECK_SUCCESS = "检查成功";

    public static final String DESERIALIZE_USER_PARAMS_ERROR = "解析用户参数失败";

    public static final String TASK_EXECUTE_FAIL_RE_RUN = "任务调度失败，重新发起";

    public static final String CONTAINER_RESTART_RE_LAUNCH = "容器重启，重新发起";
    public static final String SONAR_ANALYSE_TASK_FAILED = "Sonar分析失败";

}
