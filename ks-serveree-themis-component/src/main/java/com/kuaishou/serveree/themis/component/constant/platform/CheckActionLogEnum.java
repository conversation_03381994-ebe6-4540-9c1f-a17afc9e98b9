package com.kuaishou.serveree.themis.component.constant.platform;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/6/16 3:44 PM
 */
@AllArgsConstructor
public enum CheckActionLogEnum {

    PROJECT_PROFILE_UPDATE(1, "修改项目的规则集"),
    PROFILE_RULE_UPDATE(2, "规则集修改规则"),

    PROFILE_RULE_DELETE(3, "删除规则集"),
    ;

    @Getter
    private final Integer type;
    @Getter
    private final String desc;

}
