package com.kuaishou.serveree.themis.component.entity.statics;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/3/20 3:04 PM
 */
public class UserScanProjectInfo {

    private List<Integer> gitProjectIds;

    private List<Integer> gitGroupIds;

    private boolean containsImport;

    private boolean allProjects;

    public UserScanProjectInfo() {
    }

    private UserScanProjectInfo(Builder builder) {
        setGitProjectIds(builder.gitProjectIds);
        setGitGroupIds(builder.gitGroupIds);
        setContainsImport(builder.containsImport);
        setAllProjects(builder.allProjects);
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public List<Integer> getGitProjectIds() {
        return gitProjectIds;
    }

    public boolean isAllProjects() {
        return allProjects;
    }

    public void setAllProjects(boolean allProjects) {
        this.allProjects = allProjects;
    }

    public void setGitProjectIds(List<Integer> gitProjectIds) {
        this.gitProjectIds = gitProjectIds;
    }

    public List<Integer> getGitGroupIds() {
        return gitGroupIds;
    }

    public void setGitGroupIds(List<Integer> gitGroupIds) {
        this.gitGroupIds = gitGroupIds;
    }

    public boolean isContainsImport() {
        return containsImport;
    }

    public void setContainsImport(boolean containsImport) {
        this.containsImport = containsImport;
    }

    public static final class Builder {
        private List<Integer> gitProjectIds;
        private List<Integer> gitGroupIds;
        private boolean containsImport;
        private boolean allProjects;

        private Builder() {
        }

        public Builder gitProjectIds(List<Integer> val) {
            gitProjectIds = val;
            return this;
        }

        public Builder gitGroupIds(List<Integer> val) {
            gitGroupIds = val;
            return this;
        }

        public Builder containsImport(boolean val) {
            containsImport = val;
            return this;
        }

        public Builder allProjects(boolean val) {
            allProjects = val;
            return this;
        }

        public UserScanProjectInfo build() {
            return new UserScanProjectInfo(this);
        }
    }
}
