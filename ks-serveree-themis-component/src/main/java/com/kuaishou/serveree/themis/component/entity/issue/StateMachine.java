package com.kuaishou.serveree.themis.component.entity.issue;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.CheckForNull;

import com.google.common.base.Preconditions;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ListMultimap;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-24
 * issue的状态机，定义issue的状态集有哪些，以及这些状态中可流转的起始状态对有哪些情况
 */
public class StateMachine {
    private final List<String> keys;
    private final Map<String, State> byKey;

    private StateMachine(Builder builder) {
        this.keys = ImmutableList.copyOf(builder.states);
        ImmutableMap.Builder<String, State> mapBuilder = ImmutableMap.builder();
        for (String stateKey : builder.states) {
            List<Transition> outTransitions = builder.outTransitions.get(stateKey);
            State state = new State(stateKey, outTransitions.toArray(new Transition[0]));
            mapBuilder.put(stateKey, state);
        }
        byKey = mapBuilder.build();
    }

    @CheckForNull
    public State state(String stateKey) {
        return byKey.get(stateKey);
    }

    public List<String> stateKeys() {
        return keys;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final Set<String> states = new LinkedHashSet<>();
        // 初始状态到转换的映射关系
        private final ListMultimap<String, Transition> outTransitions = ArrayListMultimap.create();

        private Builder() {
        }

        public Builder states(List<String> keys) {
            states.addAll(keys);
            return this;
        }

        public Builder transition(Transition transition) {
            Preconditions.checkArgument(states.contains(transition.from()), "Originating state does not exist: " + transition.from());
            Preconditions.checkArgument(states.contains(transition.to()), "Destination state does not exist: " + transition.to());
            outTransitions.put(transition.from(), transition);
            return this;
        }

        public StateMachine build() {
            Preconditions.checkArgument(!states.isEmpty(), "At least one state is required");
            return new StateMachine(this);
        }
    }
}
