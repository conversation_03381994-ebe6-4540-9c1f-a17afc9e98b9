package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckRepoNoticeRecord对象", description = "")
public class CheckRepoNoticeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "check_repo的id")
    private Long checkRepoId;

    @ApiModelProperty(value = "扫描分支")
    private String branch;

    @ApiModelProperty(value = "消息内容")
    private String msgContent;

    @ApiModelProperty(value = "发送的时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime sendTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "接收人")
    private String receivers;

    @ApiModelProperty(value = "kim群机器人地址")
    private String kimRobot;

    @ApiModelProperty(value = "kim群内要@的人")
    private String mentionedUsers;

    @ApiModelProperty(value = "发送状态")
    private Integer sendStatus;


}
