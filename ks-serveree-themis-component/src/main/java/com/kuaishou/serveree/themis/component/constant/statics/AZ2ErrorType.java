package com.kuaishou.serveree.themis.component.constant.statics;

/**
 * <AUTHOR>
 * @since 2022/11/28 8:36 PM
 */
public enum AZ2ErrorType {

    NO_MASTER(1, "无master分支"),
    MORE_THAN_100_M(2, "超过100M"),
    GIT_CLONE_ERROR(3, "拉取代码失败"),

    IN_WHITELIST(4, "命中白名单"),
    PROJECT_EXCEPTION(5, "项目异常"),
    ;

    private final Integer type;
    private final String desc;

    AZ2ErrorType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
