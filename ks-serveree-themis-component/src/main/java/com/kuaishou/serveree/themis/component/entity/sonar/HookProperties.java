package com.kuaishou.serveree.themis.component.entity.sonar;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/7/22 5:07 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class HookProperties {

    @JsonProperty("sonar.analysis.checkBaseId")
    private String checkBaseId;
    @JsonProperty("sonar.analysis.checkTaskId")
    private String taskId;
    @JsonProperty("sonar.analysis.executionId")
    private String executionId;
    @JsonProperty("sonar.analysis.repoUrl")
    private String repoUrl;
    @JsonProperty("sonar.analysis.commitId")
    private String commitId;
    @JsonProperty("sonar.analysis.branch")
    private String branch;
    @JsonProperty("sonar.analysis.buildUserName")
    private String pipelineBuildUserName;
    @JsonProperty("sonar.analysis.projectId")
    private String gitProjectId;
    @JsonProperty("sonar.analysis.pExecutionId")
    private String pExecutionId;
    @JsonProperty("sonar.analysis.pBaseId")
    private String pBaseId;
    @JsonProperty("sonar.analysis.kspBuildId")
    private String kspBuildId;
    @JsonProperty("sonar.analysis.kspPipelineId")
    private String kspPipelineId;
    @JsonProperty("sonar.analysis.stuckPoint")
    private boolean stuckPoint;
    @JsonProperty("sonar.analysis.modules")
    private String modules;
    @JsonProperty("sonar.analysis.scannerType")
    private Integer scannerType;
    @JsonProperty("sonar.analysis.referType")
    private Integer referType;
    @JsonProperty("sonar.analysis.incrementMode")
    private boolean incrementMode;
    @JsonProperty("sonar.analysis.incrementType")
    private Integer incrementType;
    @JsonProperty("sonar.analysis.platformOfflineCheck")
    private boolean platformOfflineCheck;
    @JsonProperty("sonar.analysis.testEnv")
    private boolean testEnv;
    @JsonProperty("sonar.analysis.sendKimNotice")
    private boolean sendKimNotice;
    @JsonProperty("sonar.analysis.stuckPointBody")
    private String stuckPointBody;
    @JsonProperty("sonar.analysis.mrId")
    private String mrId;
    @JsonProperty("sonar.analysis.buildLanguage")
    private String language;
    @JsonProperty("sonar.analysis.buildLanguageVersion")
    private String languageVersion;

    @JsonProperty("sonar.analysis.localBuildId")
    private Long localBuildId;

}
