package com.kuaishou.serveree.themis.component.constant.bpm;

import lombok.Getter;

/**
 * bpm流程事件类型
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-08
 */
@Getter
public enum BpmEventType {
    PROCESS_START("PROCESS_START"),
    PROCESS_END("PROCESS_END"),
    TASK_CREATE("TASK_CREATE"),
    TASK_COMPLETE("TASK_COMPLETE"),
    PROCESS_CANCELLED("PROCESS_CANCELLED"),
    PROCESS_ROLL_BACK("PROCESS_ROLL_BACK"),
    ;

    private final String type;

    BpmEventType(String type) {
        this.type = type;
    }
}
