package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ComplexityMethod对象", description = "")
public class ComplexityMethod implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "文件复杂度表id")
    private Long fileId;

    @ApiModelProperty(value = "方法名")
    private String methodName;

    @ApiModelProperty(value = "圈复杂度值")
    private Integer cyclomaticComplexity;

    @ApiModelProperty(value = "认知复杂度值")
    private Integer cognitiveComplexity;

    @ApiModelProperty(value = "起始行")
    private Integer beginLine;

    @ApiModelProperty(value = "结束行")
    private Integer endLine;

    @ApiModelProperty(value = "起始列")
    private Integer beginColumn;

    @ApiModelProperty(value = "结束列")
    private Integer endColumn;


}
