package com.kuaishou.serveree.themis.component.constant.sonar;

import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;

/**
 * <AUTHOR>
 * @since 2021/7/23 2:46 下午
 */
public interface SonarConstants {

    String METRIC_KEYS = "duplicated_lines_density,duplicated_lines,duplicated_blocks,cognitive_complexity,complexity,"
            + "comment_lines,comment_lines_density,ncloc_language_distribution,bugs,new_bugs,vulnerabilities,"
            + "new_vulnerabilities,code_smells,new_code_smells,ncloc,coverage";

    String SONAR_CLUSTER_DEFAULT_ACCOUNT = "admin";

    String NEED_TRANSFER_PARAMS = "componentKey,projectKey,component,projectId,key,project";

    String ISSUE_OPENED_STATUS_STRING =
            String.join(",", CheckIssueStatus.OPEN.getStatus(), CheckIssueStatus.RE_OPEN.getStatus());

    String ISSUE_NO_RESOLVED_STRING = String.join(",",
            CheckIssueStatus.OPEN.getStatus(),
            CheckIssueStatus.RE_OPEN.getStatus(),
            CheckIssueStatus.CLOSED.getStatus(),
            CheckIssueStatus.TO_REVIEW.getStatus()
    );
}
