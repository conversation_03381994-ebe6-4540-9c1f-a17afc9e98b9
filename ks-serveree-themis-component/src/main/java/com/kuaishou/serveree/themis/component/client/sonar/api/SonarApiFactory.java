package com.kuaishou.serveree.themis.component.client.sonar.api;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/7/2 11:28 上午
 */
@Component
public class SonarApiFactory {

    @Autowired
    private CorpSonarApi corpSonarApi;

    @Autowired
    private KFormatSonarApi kFormatSonarApi;

    @Autowired
    private List<SonarCommonApi> sonarCommonApiList;

    public SonarCommonApi getRealSonarApi(boolean isKformat) {
        if (isKformat) {
            return kFormatSonarApi;
        }
        return corpSonarApi;
    }

    public SonarCommonApi getRealSonarApi(String serverUrl) {
        Optional<SonarCommonApi> apiOptional = sonarCommonApiList.stream()
                .filter(sonarCommonApi -> sonarCommonApi.sonarUrl().equals(serverUrl))
                .findFirst();
        return apiOptional.orElse(corpSonarApi);
    }

}
