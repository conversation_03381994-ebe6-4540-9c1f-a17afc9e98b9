package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Paging;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarComponent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/5/21 2:02 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MeasuresComponentTreeResponse {

    private Paging paging;
    private List<SonarComponent> components;

}
