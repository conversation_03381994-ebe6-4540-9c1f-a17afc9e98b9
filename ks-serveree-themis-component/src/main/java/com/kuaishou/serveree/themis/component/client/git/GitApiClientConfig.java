package com.kuaishou.serveree.themis.component.client.git;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import com.kuaishou.gitlab.client.sdk.GitApiClient;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.annotation.Kconfig;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-09
 */
@Component
public class GitApiClientConfig {

    @Value("${kdev.server-url}")
    private String gitApiServer;

    @Kconfig("qa.themis.gitApiToken")
    private Kconf<String> gitApiToken;

    @Bean
    public GitApiClient gitApiClient() {
        return new GitApiClient(gitApiServer, gitApiToken.get());
    }
}
