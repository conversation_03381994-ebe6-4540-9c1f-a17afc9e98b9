package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/18 2:49 下午
 */
@Slf4j
@AllArgsConstructor
public enum CheckIssueSeverity implements FacetNode {

    SERIOUS("SERIOUS", "阻断", "BLOCKER", 0, "I", true),
    MAJOR("MAJOR", "严重", "CRITICAL", 1, "II", true),
    COMMON("COMMON", "主要", "MAJOR", 2, "III", true),
    NOT_INDUCED("NOT_INDUCED", "未归纳", "", 5, "", true),
    ;

    @Getter
    private String key;

    @Getter
    private String desc;

    @Getter
    private String sonarStatus;

    @Getter
    private Integer level;

    @Getter
    private String levelDisplay;

    @Getter
    // 前端页面是否显示该元素
    private Boolean show;

    private static final Map<String, CheckIssueSeverity> ENUM_MAP = new HashMap<>();

    private static final Map<String, CheckIssueSeverity> KEY_MAP = new HashMap<>();
    private static final Map<String, CheckIssueSeverity> DESC_MAP = new HashMap<>();

    private static final Map<String, String> KEY_DISPLAY_MAP = new HashMap<>();

    static {
        Arrays.stream(CheckIssueSeverity.values()).forEach(checkTypeEnum -> {
            ENUM_MAP.put(checkTypeEnum.getSonarStatus(), checkTypeEnum);
            KEY_MAP.put(checkTypeEnum.getKey(), checkTypeEnum);
            DESC_MAP.put(checkTypeEnum.getDesc(), checkTypeEnum);
            KEY_DISPLAY_MAP.put(checkTypeEnum.getKey(), checkTypeEnum.getLevelDisplay());
        });
    }

    public static String getKeyBySonarStatus(String status) {
        if (StringUtils.isEmpty(status)) {
            return NOT_INDUCED.getKey();
        }
        CheckIssueSeverity checkIssueSeverity = ENUM_MAP.get(status);
        if (checkIssueSeverity == null) {
            return COMMON.getKey();
        }
        return checkIssueSeverity.getKey();
    }

    public static String getSonarStatusByKey(String key) {
        CheckIssueSeverity checkIssueSeverity = KEY_MAP.get(key);
        if (checkIssueSeverity == null) {
            return COMMON.getSonarStatus();
        }
        return checkIssueSeverity.getSonarStatus();
    }

    public static boolean correctType(String type) {
        if (StringUtils.isEmpty(type)) {
            return false;
        }
        return KEY_MAP.containsKey(type);
    }

    public static String getLevelDisplayByKey(String key) {
        return KEY_DISPLAY_MAP.get(key);
    }

    public static String getSonarStatusByThemisKey(String key) {
        if (NOT_INDUCED.key.equals(key)) {
            return "MINOR,INFO";
        }
        CheckIssueSeverity checkIssueSeverity = KEY_MAP.get(key);
        if (checkIssueSeverity == null) {
            return "";
        }
        return checkIssueSeverity.sonarStatus;
    }

    public static String getThemisKeyBySonarStatus(String status) {
        if (StringUtils.isEmpty(status)) {
            return "";
        }
        CheckIssueSeverity checkIssueSeverity = ENUM_MAP.get(status);
        if (checkIssueSeverity == null) {
            return NOT_INDUCED.getKey();
        }
        return checkIssueSeverity.getKey();
    }

    public static List<String> getByAboveLevel(String severity) {
        List<String> severities = Lists.newArrayList();
        if (StringUtils.isEmpty(severity)) {
            return severities;
        }
        switch (severity) {
            case "SERIOUS":
                severities.add("SERIOUS");
                break;
            case "MAJOR":
                severities.add("SERIOUS");
                severities.add("MAJOR");
                break;
            case "COMMON":
                severities.add("SERIOUS");
                severities.add("MAJOR");
                severities.add("COMMON");
                break;
            default:
                // ignore
                break;
        }
        return severities;
    }

    public static List<String> querySeverityList() {
        return Arrays.stream(values())
                .map(CheckIssueSeverity::getKey)
                .filter(severityKey -> !severityKey.equals(NOT_INDUCED.getKey()))
                .collect(Collectors.toList());
    }

    public static String querySonarStatus(String severity) {
        for (CheckIssueSeverity value : values()) {
            if (!value.getKey().equals(NOT_INDUCED.getKey()) && value.getKey().equals(severity)) {
                return value.getSonarStatus();
            }
        }
        return null;
    }

    @Override
    public CheckIssueSeverity valueOfName(String name) {
        try {
            return valueOf(name);
        } catch (IllegalArgumentException e) {
            log.info("[{}]在枚举中不存在", name);
        }
        return null;
    }

    @Override
    public Boolean showOfName(String name) {
        FacetNode node = valueOfName(name);
        return Objects.nonNull(node) && node.getShow();
    }

    public static String descZh2SonarSeverity(String severity) {
        // 手动映射一下中文传值
        switch (severity) {
            case "阻断":
                severity = "blocker";
                break;
            case "严重":
                severity = "critical";
                break;
            // "主要" 或其他，默认为 major
            default:
                severity = "major";
                break;
        }
        return severity;
    }

    public static CheckIssueSeverity getByDesc(String desc) {
        return DESC_MAP.get(desc);
    }

    public static String getDescByThemisKey(String key) {
        CheckIssueSeverity severity = KEY_MAP.get(key);
        if (Objects.isNull(severity)) {
            return COMMON.getDesc();
        }
        return severity.getDesc();
    }

    public static CheckIssueSeverity getByKey(String key) {
        return KEY_MAP.get(key);
    }
}
