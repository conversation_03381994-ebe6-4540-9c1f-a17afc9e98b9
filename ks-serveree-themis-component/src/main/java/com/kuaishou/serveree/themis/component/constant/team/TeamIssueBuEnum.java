package com.kuaishou.serveree.themis.component.constant.team;

import lombok.Getter;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-27 14:21
 **/
@Getter
public enum TeamIssueBuEnum {
    COMMERCE_BU_SONAR(1, "商业化sonar问题管理", "laiq<PERSON><PERSON>"),
    API_MANAGE_BU(2, "质效-API管理", "xieshijie"),
    TEAM_GRAY_VERIFY(3, "team-gray，仅用于验证", "xieshijie"),
    COMMON_TEAM(4, "team的公共逻辑空间，抽象一套创建逻辑", ""),
    ;

    private final int code;
    private final String desc;
    private final String defaultOperator;

    TeamIssueBuEnum(int code, String desc, String defaultOperator) {
        this.code = code;
        this.desc = desc;
        this.defaultOperator = defaultOperator;
    }


}
