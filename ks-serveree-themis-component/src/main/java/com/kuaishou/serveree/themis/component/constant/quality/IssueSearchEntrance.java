package com.kuaishou.serveree.themis.component.constant.quality;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.INVALID_PARAMS;

import com.kuaishou.serveree.themis.component.common.exception.ThemisException;

import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-04-22
 */
@Getter
public enum IssueSearchEntrance {
    PLUGIN("pluginIssueSearchImpl"),
    COMMIT("commitIssueSearchImpl"),
    CR("crIssueSearchImpl"),
    ;

    private String instanceName;

    IssueSearchEntrance(String instanceName) {
        this.instanceName = instanceName;
    }

    public static IssueSearchEntrance getByName(String name) {
        try {
            return IssueSearchEntrance.valueOf(name);
        } catch (IllegalArgumentException e) {
            throw new ThemisException(INVALID_PARAMS, "entrance参数不合法");
        }
    }
}
