package com.kuaishou.serveree.themis.component.client.sonar.api;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-16
 */
@Slf4j
@Component
public class ClusterNode5Api implements SonarCommonApi {
    @Value("${sonar.cluster.node5.url}")
    private String sonarUrl;

    @Value("${sonar.timeout}")
    private Integer timeout;

    @Value("${sonar.cluster.node5.basic-auth}")
    private String basicAuth;

    @Override
    public String sonarUrl() {
        return sonarUrl;
    }

    @Override
    public Integer timeout() {
        return timeout;
    }

    @Override
    public String basicAuth() {
        return basicAuth;
    }
}
