package com.kuaishou.serveree.themis.component.proxy.strategy.content.type;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.proxy.SonarConfigEnum;
import com.kuaishou.serveree.themis.component.proxy.strategy.ParamsHandleService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.vo.response.proxy.SonarConfigResp;

import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-17
 */
@Component
public class MultiPartStrategy implements ContentTypeStrategy {
    @Value("${sonar.timeout}")
    private Integer timeout;
    @Autowired
    private ParamsHandleService paramsHandleService;

    @Override
    public String doPostRedirect(HttpServletRequest request) {
        Map<String, Object> params = CommonUtils.transferToHashMap(request.getParameterMap());
        SonarConfigResp configResp = paramsHandleService.findProjectIdFromParams(params, request.getRequestURI());
        SonarConfigEnum configEnum = configResp.getConfigEnum();
        String sonarDomain = configResp.getSonarDomain();
        return HttpRequest
                .post(sonarDomain)
                .header(Header.AUTHORIZATION, configEnum.getBasicAuth())
                .contentType(ContentType.MULTIPART.getValue())
                .form(params)
                .timeout(timeout) //超时，毫秒
                .execute()
                .body();
    }
}
