package com.kuaishou.serveree.themis.component.client.sonar.operations;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.client.sonar.api.MetricSonarApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;

/**
 * <AUTHOR>
 * @since 2021/8/5 4:17 下午
 */
@Component
public class MetricSonarOperations implements SonarOperations {

    @Autowired
    private MetricSonarApi metricSonarApi;

    @Override
    public SonarCommonApi sonarApi() {
        return metricSonarApi;
    }
}
