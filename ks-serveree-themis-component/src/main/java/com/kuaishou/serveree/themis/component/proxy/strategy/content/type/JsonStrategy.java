package com.kuaishou.serveree.themis.component.proxy.strategy.content.type;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.proxy.SonarConfigEnum;
import com.kuaishou.serveree.themis.component.proxy.strategy.ParamsHandleService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.response.proxy.SonarConfigResp;

import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-17
 */
@Component
public class JsonStrategy implements ContentTypeStrategy {
    @Value("${sonar.timeout}")
    private Integer timeout;
    @Autowired
    private ParamsHandleService paramsHandleService;

    @Override
    public String doPostRedirect(HttpServletRequest request) {
        StringBuilder paramsBuilder = new StringBuilder();
        try {
            BufferedReader reader = request.getReader();
            reader.lines().forEach(paramsBuilder::append);
        } catch (IOException e) {
            throw new ThemisException(e);
        }
        Map<String, Object> params = JSONUtils.deserialize(paramsBuilder.toString(), Map.class);
        SonarConfigResp configResp = paramsHandleService.findProjectIdFromParams(params, request.getRequestURI());
        SonarConfigEnum configEnum = configResp.getConfigEnum();
        String sonarDomain = configResp.getSonarDomain();
        return HttpRequest
                .post(sonarDomain)
                .header(Header.AUTHORIZATION, configEnum.getBasicAuth())
                .contentType(ContentType.JSON.getValue())
                .body(JSONUtils.serialize(params), ContentType.JSON.getValue())
                .timeout(timeout) //超时，毫秒
                .execute()
                .body();
    }
}
