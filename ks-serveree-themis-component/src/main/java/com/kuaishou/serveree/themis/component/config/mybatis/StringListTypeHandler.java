package com.kuaishou.serveree.themis.component.config.mybatis;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;

@SuppressWarnings("rawtypes")
@MappedJdbcTypes(JdbcType.VARCHAR)
public class StringListTypeHandler extends BaseTypeHandler<List> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List parameter,
            JdbcType jdbcType) throws SQLException {
        if (parameter == null || parameter.size() == 0) {
            ps.setString(i, "");
        } else {
            ps.setString(i, Joiner.on(",").skipNulls().join(parameter));
        }
    }

    @Override
    public List getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String columnValue = rs.getString(columnName);
        return convertStringArray(columnValue);
    }

    @Override
    public List getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String columnValue = rs.getString(columnIndex);
        return convertStringArray(columnValue);
    }

    @Override
    public List getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String columnValue = cs.getString(columnIndex);
        return convertStringArray(columnValue);
    }

    private List convertStringArray(String columnStrValue) {
        if (StringUtils.isNotEmpty(columnStrValue)) {
            String[] stringArray = columnStrValue.split(",");
            return Arrays.stream(stringArray).filter(StringUtils::isNotEmpty).map(String::trim)
                    .collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }
}