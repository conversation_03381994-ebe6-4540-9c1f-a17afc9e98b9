package com.kuaishou.serveree.themis.component.entity.sonar;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/7/28 5:31 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SonarMeasures {

    private String metric;
    private String value;
    private boolean bestValue;
    private List<Periods> periods;

}
