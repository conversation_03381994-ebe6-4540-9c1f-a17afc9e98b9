package com.kuaishou.serveree.themis.component.entity.plugin;

import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/7/25 4:54 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SonarNewPluginSettings {

    private SonarNewPluginMeta bugMeta;

    private SonarNewPluginMeta vulnerabilityMeta;

    // 构造一个默认配置项
    public static final SonarNewPluginSettings DEFAULT_SETTING;

    static {
        String commonSeverityKey = CheckIssueSeverity.COMMON.getSonarStatus().toLowerCase();
        SonarNewPluginMeta defaultSetting =
                SonarNewPluginMeta.builder().stuckSwitch(false).severity(commonSeverityKey).count(0).build();
        DEFAULT_SETTING = new SonarNewPluginSettings(defaultSetting, defaultSetting);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class SonarNewPluginMeta {
        private boolean stuckSwitch;
        private String severity;

        private int count;
    }

}
