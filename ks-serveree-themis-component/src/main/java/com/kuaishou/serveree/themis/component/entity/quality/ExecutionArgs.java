package com.kuaishou.serveree.themis.component.entity.quality;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import com.google.common.collect.Lists;
import com.kuaishou.infra.scheduler.SimpleTaskInfo;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/4/13 4:03 下午
 */
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionArgs {
    /**
     * 是否使用db里的repo
     */
    private boolean useDbRepo;
    /**
     * repo的类型
     */
    private String repoType;
    /**
     * 使用dbrepo的收集日期
     */
    private String useDbRepoDate;
    /**
     * 是否是全量的项目
     */
    private boolean allProjects;
    /**
     * 需要执行的groupId集合
     */
    private List<String> groupIds;
    /**
     * 需要执行的subGroupId集合
     */
    private List<String> subGroupIds;
    /**
     * 排除的subGroupId集合
     */
    private List<String> excludeSubGroupIds;
    /**
     * 需要执行的projectId集合
     */
    private List<String> projectIds;
    /**
     * 排除的projectId集合
     */
    private List<String> excludeProjectIds;
    /**
     * checkstyle的级别 空为不检查
     */
    private String checkStyleLevel;
    /**
     * cicheck的级别 空为不检查
     */
    private String cicheckLevel;
    /**
     * kcheck的级别 空为不检查
     */
    private String kcheckLevel;
    /**
     * 基础信息
     */
    private SimpleTaskInfo simpleTaskInfo;
    /**
     * 传递的mvnArgs;
     */
    private String mvnArgs = StringUtils.EMPTY;
    /**
     * 分析计划id
     */
    private String analyzePlanId;
    /**
     * 执行类型
     */
    private String executionType;
    /**
     * excution执行关联类型
     */
    private String referType;
    /**
     * 跳过maven java的校验
     */
    private boolean skipJavaPom;
    /**
     * 一个月活跃的项目
     */
    private boolean oneMonthActiveProjects;

    public boolean isOneMonthActiveProjects() {
        return oneMonthActiveProjects;
    }

    public void setOneMonthActiveProjects(boolean oneMonthActiveProjects) {
        this.oneMonthActiveProjects = oneMonthActiveProjects;
    }

    public boolean isSkipJavaPom() {
        return skipJavaPom;
    }

    public void setSkipJavaPom(boolean skipJavaPom) {
        this.skipJavaPom = skipJavaPom;
    }


    public String getReferType() {
        return referType;
    }

    public void setReferType(String referType) {
        this.referType = referType;
    }

    public String getExecutionType() {
        return executionType;
    }

    public void setExecutionType(String executionType) {
        this.executionType = executionType;
    }

    public boolean isUseDbRepo() {
        return useDbRepo;
    }

    public void setUseDbRepo(boolean useDbRepo) {
        this.useDbRepo = useDbRepo;
    }

    public String getUseDbRepoDate() {
        return useDbRepoDate;
    }

    public void setUseDbRepoDate(String useDbRepoDate) {
        this.useDbRepoDate = useDbRepoDate;
    }

    public SimpleTaskInfo getSimpleTaskInfo() {
        return simpleTaskInfo;
    }

    public void setSimpleTaskInfo(SimpleTaskInfo simpleTaskInfo) {
        this.simpleTaskInfo = simpleTaskInfo;
    }

    public Long getAnalyzePlanId() {
        return NumberUtils.toLong(analyzePlanId, 0);
    }

    public void setAnalyzePlanId(String analyzePlanId) {
        this.analyzePlanId = analyzePlanId;
    }

    public boolean isAllProjects() {
        return allProjects;
    }

    public void setAllProjects(boolean allProjects) {
        this.allProjects = allProjects;
    }

    public List<Integer> getGroupIds() {
        if (groupIds == null) {
            return Lists.newArrayList();
        }
        return groupIds.stream().map(Integer::new).collect(Collectors.toList());
    }

    public void setGroupIds(List<String> groupIds) {
        this.groupIds = groupIds;
    }

    public List<Integer> getSubGroupIds() {
        if (subGroupIds == null) {
            return Lists.newArrayList();
        }
        return subGroupIds.stream().map(Integer::new).collect(Collectors.toList());
    }

    public void setSubGroupIds(List<String> subGroupIds) {
        this.subGroupIds = subGroupIds;
    }

    public List<Integer> getExcludeSubGroupIds() {
        if (excludeSubGroupIds == null) {
            return Lists.newArrayList();
        }
        return excludeSubGroupIds.stream().map(Integer::new).collect(Collectors.toList());
    }

    public void setExcludeSubGroupIds(List<String> excludeSubGroupIds) {
        this.excludeSubGroupIds = excludeSubGroupIds;
    }

    public List<Integer> getProjectIds() {
        if (projectIds == null) {
            return Lists.newArrayList();
        }
        return projectIds.stream().map(Integer::new).collect(Collectors.toList());
    }

    public void setProjectIds(List<String> projectIds) {
        this.projectIds = projectIds;
    }

    public List<Integer> getExcludeProjectIds() {
        if (excludeProjectIds == null) {
            return Lists.newArrayList();
        }
        return excludeProjectIds.stream().map(Integer::new).collect(Collectors.toList());
    }

    public void setExcludeProjectIds(List<String> excludeProjectIds) {
        this.excludeProjectIds = excludeProjectIds;
    }

    public String getCheckStyleLevel() {
        return checkStyleLevel;
    }

    public void setCheckStyleLevel(String checkStyleLevel) {
        this.checkStyleLevel = checkStyleLevel;
    }

    public String getCicheckLevel() {
        return cicheckLevel;
    }

    public void setCicheckLevel(String cicheckLevel) {
        this.cicheckLevel = cicheckLevel;
    }

    public String getKcheckLevel() {
        return kcheckLevel;
    }

    public void setKcheckLevel(String kcheckLevel) {
        this.kcheckLevel = kcheckLevel;
    }

    public String getMvnArgs() {
        return mvnArgs;
    }

    public void setMvnArgs(String mvnArgs) {
        this.mvnArgs = mvnArgs;
    }

    public String getRepoType() {
        return repoType;
    }

    public void setRepoType(String repoType) {
        this.repoType = repoType;
    }
}
