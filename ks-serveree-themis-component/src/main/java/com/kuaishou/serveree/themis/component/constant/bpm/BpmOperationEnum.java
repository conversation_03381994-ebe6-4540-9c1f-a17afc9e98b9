package com.kuaishou.serveree.themis.component.constant.bpm;

import lombok.Getter;

/**
 * bpm审批操作类型
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-08
 */
@Getter
public enum BpmOperationEnum {
    // 通过
    PASSED("Passed", "已完成"),
    // 拒绝
    REFUSE("Refuse", "已否决"),
    // 前加签
    FRONT_ADD_SIGN("FrontAddSign"),
    // 后加签
    AFTER_ADD_SIGN("AfterAddSign"),
    // 转签
    SHIFT_SIGN("ShiftSign"),
    // 征询
    CONSULT("Consult"),
    // 提交
    SUBMIT("Submit", "申请中"),
    ;

    private final String type;

    private final String status;

    BpmOperationEnum(String type, String status) {
        this.type = type;
        this.status = status;
    }

    BpmOperationEnum(String type) {
        this(type, type);
    }
}
