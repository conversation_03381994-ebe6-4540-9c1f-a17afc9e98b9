package com.kuaishou.serveree.themis.component.service.platform.scan.scanners;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterSimpleFactory;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoLanguage;
import com.kuaishou.serveree.themis.component.common.entity.CheckTriggerRecord;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.config.EnvironmentConfig;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.platform.TriggerStatus;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckPluginInputConstants;
import com.kuaishou.serveree.themis.component.constant.quality.CheckTypeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.JobTypeEnum;
import com.kuaishou.serveree.themis.component.entity.platform.ExecuteScanContext;
import com.kuaishou.serveree.themis.component.entity.scanner.ScannerSendKimContext;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckExecutionService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoLanguageService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.CheckTriggerRecordService;
import com.kuaishou.serveree.themis.component.service.QualityCheckService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.PlatformSonarInfoUtils;
import com.kuaishou.serveree.themis.component.vo.request.ChangeParentProfileRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProfileAddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCreateRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;

/**
 * <AUTHOR>
 * @since 2022/10/12 7:13 PM
 */
@Service
public class SonarScannerNewScanner extends NewSonarScanner {

    @Autowired
    private CheckRepoBranchService checkRepoBranchService;

    @Autowired
    private CheckBaseService checkBaseService;

    @Autowired
    private CheckRepoService checkRepoService;

    @Autowired
    private CheckExecutionService checkExecutionService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskConfigService taskConfigService;

    @Autowired
    private QualityCheckService qualityCheckService;

    @Autowired
    private CheckTriggerRecordService checkTriggerRecordService;

    @Autowired
    private SonarClusterSimpleFactory sonarClusterSimpleFactory;

    @Autowired
    private CheckRepoLanguageService checkRepoLanguageService;

    @Autowired
    private PlatformSonarInfoUtils sonarInfoUtils;

    @Autowired
    private EnvironmentConfig environmentConfig;

    @Autowired
    private GitOperations gitOperations;

    @Override
    public void scan(ExecuteScanContext context) {
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.getById(context.getCheckRepoBranchId());
        CheckRepo checkRepo = checkRepoService.getById(checkRepoBranch.getCheckRepoId());
        LocalDateTime now = LocalDateTime.now();
        CheckBase checkBase = fillUpCheckBase(checkRepo, checkRepoBranch, now, gitOperations);
        checkBaseService.save(checkBase);
        Task task = fillUpTask(checkRepo, checkRepoBranch, now);
        taskService.save(task);
        CheckExecution checkExecution;
        checkExecution = fillUpCheckExecution(checkBase, now, context.getTriggerRecordId(), task,
                ProcessExecutionReferType.SONAR_SCANNER.getType());
        checkExecutionService.save(checkExecution);
        TaskConfig taskConfig = fillUpTaskConfig(task, now, checkRepo, checkBase, checkExecution, checkRepoBranch);
        taskConfigService.save(taskConfig);
        qualityCheckService.sponsorKspPipeline(task, taskConfig);
        CheckTriggerRecord checkTriggerRecord = checkTriggerRecordService.getById(context.getTriggerRecordId());
        checkTriggerRecord.setTriggerStatus(TriggerStatus.EXECUTING.getCode());
        checkTriggerRecord.setGmtModified(now);
        checkTriggerRecordService.updateById(checkTriggerRecord);
    }

    private TaskConfig fillUpTaskConfig(Task task,
            LocalDateTime now,
            CheckRepo checkRepo,
            CheckBase checkBase,
            CheckExecution checkExecution,
            CheckRepoBranch checkRepoBranch) {
        String finalProjectKey = getFinalProjectKey(checkRepo.getGitProjectId(), sonarInfoUtils);
        String finalProjectName = getFinalProjectName(GitUtils.getRepoName(checkRepo.getRepoUrl()), sonarInfoUtils);
        boolean test = environmentConfig.isTest();
        String args = " -Dsonar.projectKey=" + finalProjectKey
                + " -Dsonar.projectName=" + finalProjectName
                + " -Dsonar.branch.name=" + checkRepoBranch.getBranchName()
                + " -Dsonar.analysis.checkBaseId=" + checkBase.getId()
                + " -Dsonar.analysis.executionId=" + checkExecution.getId()
                + " -Dsonar.analysis.repoUrl=" + checkRepo.getRepoUrl()
                + " -Dsonar.analysis.branch=" + checkRepoBranch.getBranchName()
                + " -Dsonar.analysis.platformOfflineCheck=true"
                + " -Dsonar.analysis.testEnv=" + test;
        Map<String, Object> executionParamsMap = Maps.newHashMap();
        executionParamsMap.put(CheckPluginInputConstants.MVN_BUILD_ARGUMENTS, args);
        return TaskConfig.builder()
                .taskId(task.getId())
                .executionType(CheckTypeEnum.SONAR_SCANNER_NEW.name())
                .executionResult(StringUtils.EMPTY)
                .scriptType(StringUtils.EMPTY)
                .jobType(JobTypeEnum.KSP_PIPELINE.name())
                .originalKspPipelineId(NumberUtils.LONG_ZERO)
                .originalKspBuildId(NumberUtils.LONG_ZERO)
                .originalKspStepId(NumberUtils.LONG_ZERO)
                .originalKspName(StringUtils.EMPTY)
                .executionParams(JSONUtils.serialize(executionParamsMap))
                .pluginCheckRunType(NumberUtils.INTEGER_ZERO)
                .updatedTime(now)
                .createdTime(now)
                .language(getLanguage(checkRepo.getId()))
                .build();
    }

    private String getLanguage(Long checkRepoId) {
        List<CheckRepoLanguage> checkRepoLanguages = checkRepoLanguageService.listByCheckRepoId(checkRepoId);
        if (CollectionUtils.isEmpty(checkRepoLanguages)) {
            return "";
        }
        return checkRepoLanguages.get(0).getLanguage();
    }

    @Override
    public PlatformScannerEnum platformScanner() {
        return PlatformScannerEnum.SONAR_SCANNER_NEW;
    }

    @Override
    public void afterProjectCreate(RepoCreateRequest searchRequest) {
        afterProjectCreate(searchRequest, sonarClusterSimpleFactory, sonarInfoUtils,
                searchRequest.getLanguageSetting().getLanguage());
    }

    @Override
    public void afterProfileCreate(ProfileCreateRequestVo requestVo) {
        afterProfileCreate(requestVo, sonarInfoUtils);
    }

    @Override
    public void afterProfileAddRule(ProfileAddRuleRequestVo requestVo) {
        afterProfileAddRule(requestVo, sonarInfoUtils);
    }

    @Override
    public void afterProfileDeleteRule(ProfileDeleteRuleRequestVo requestVo) {
        afterProfileDeleteRule(requestVo, sonarInfoUtils);
    }

    @Override
    public void afterProjectUpdateProfile(RepoProfileUpdateRequest updateRequest) {
        afterProjectUpdateProfile(updateRequest, sonarInfoUtils);
    }

    @Override
    public void afterProfileDelete(ProfileDeleteRequestVo requestVo) {
        afterProfileDelete(requestVo, sonarInfoUtils);
    }

    @Override
    public void afterIssueTransition(IssueSummary issueSummary, String transition) {
        afterIssueTransition(issueSummary, transition, sonarClusterSimpleFactory);
    }

    @Override
    public void sendPipelineKimNotice(ScannerSendKimContext scannerSendKimContext) {

    }

    @Override
    public void afterChangeParentProfile(ChangeParentProfileRequest requestVo) {
        afterChangeParentProfile(requestVo, sonarInfoUtils);
    }
}
