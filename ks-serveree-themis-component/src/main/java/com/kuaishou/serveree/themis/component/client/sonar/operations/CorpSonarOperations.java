package com.kuaishou.serveree.themis.component.client.sonar.operations;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.client.sonar.api.CorpSonarApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/10/21 10:17 上午
 */
@Component
@Slf4j
public class CorpSonarOperations implements SonarOperations {

    @Autowired
    private CorpSonarApi corpSonarApi;

    @Override
    public SonarCommonApi sonarApi() {
        return corpSonarApi;
    }

}
