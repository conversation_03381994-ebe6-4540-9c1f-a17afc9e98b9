package com.kuaishou.serveree.themis.component.constant.platform;

import lombok.AllArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-08-11
 */
@AllArgsConstructor
public enum IssueGroupKeyEnum {

    RULE("rule", "规则"),
    AUTHOR("author", "负责人");

    private final String groupKey;
    private final String des;


    public String getKey() {
        return groupKey;
    }

    public String getDes() {
        return des;
    }
}
