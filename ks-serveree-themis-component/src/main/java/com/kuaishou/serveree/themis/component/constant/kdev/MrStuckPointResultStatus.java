package com.kuaishou.serveree.themis.component.constant.kdev;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.entity.issue.MrCheckpointIssueBo;

import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-05
 *
 * 给kdev上报mr代码扫描结果，kdev定的枚举
 */
@Getter
public enum MrStuckPointResultStatus {

    SUCCESS("扫描通过"),
    FAILED("扫描未通过"),
    PROCESSING("执行中"),
    TASK_FAILED("流水线执行失败"),
    WARNING("扫描通过，但存在潜在问题"),
    ;

    private final String desc;

    private static final Map<String, MrStuckPointResultStatus> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(values()).forEach(en -> ENUM_MAP.put(en.name(), en));
    }

    public static MrStuckPointResultStatus getByName(String statusName) {
        return ENUM_MAP.get(statusName);
    }

    MrStuckPointResultStatus(String desc) {
        this.desc = desc;
    }

    public static MrStuckPointResultStatus getByMrCheckpointIssues(List<MrCheckpointIssueBo> issueList) {
        if (Objects.isNull(issueList)) {
            return TASK_FAILED;
        }
        // 过滤待处理的
        List<MrCheckpointIssueBo> list =
                issueList.stream().filter(i -> CheckIssueStatus.isOpen(i.getStatus())).collect(Collectors.toList());
        if (list.isEmpty()) {
            return SUCCESS;
        }
        // 是否有卡点issue
        boolean exist = list.stream()
                .filter(i -> CheckIssueStatus.isOpen(i.getStatus()))
                .anyMatch(i -> Boolean.TRUE.equals(i.getStuck()));
        return exist ? FAILED : WARNING;
    }
}
