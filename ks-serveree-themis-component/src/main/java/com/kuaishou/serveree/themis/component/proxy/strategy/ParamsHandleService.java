package com.kuaishou.serveree.themis.component.proxy.strategy;

import static com.kuaishou.serveree.themis.component.constant.sonar.SonarConstants.NEED_TRANSFER_PARAMS;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.EnvironmentConfig;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.proxy.SonarConfigEnum;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.vo.response.proxy.SonarConfigResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-17
 */
@Slf4j
@Service
public class ParamsHandleService {
    @Autowired
    private EnvironmentConfig environmentConfig;

    public SonarConfigResp findProjectIdFromParams(Map<String, Object> params, String uri) {
        List<String> keys = Stream.of(NEED_TRANSFER_PARAMS.split(",")).collect(Collectors.toList());
        Long projectId = null;
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String paramValue = entry.getValue().toString();
            if (keys.contains(entry.getKey()) && StringUtils.isNotEmpty(paramValue)) {
                //某些接口key是projectId，某些接口key是projectId:文件全路径形式，需要特殊解析一下
                if (paramValue.matches("^[0-9]*$")) {
                    projectId = Long.valueOf(paramValue);
                } else {
                    try {
                        String substring = paramValue.substring(0, paramValue.lastIndexOf(":"));
                        String[] split = substring.split(":");
                        if (split.length == 1) {
                            projectId = Long.parseLong(split[0]);
                        } else {
                            projectId = Long.parseLong(split[1]);
                        }
                    } catch (Exception e) {
                        log.error("projectId转换异常，key:value是：" + entry.toString(), e);
                    }
                }
                if (projectId != null) {
                    break;
                }
            }
        }
        if (projectId == null) {
            throw new ThemisException(ResultCodeConstant.TRANSFER_PROJECT_ID_ERROR);
        }
        SonarConfigEnum configEnum = SonarConfigEnum.modByProjectId(projectId);
        String sonarDomain = CommonUtils.buildRedirectUrl(configEnum, uri, environmentConfig.isProd());
        return SonarConfigResp.builder()
                .configEnum(configEnum)
                .sonarDomain(sonarDomain)
                .build();
    }
}
