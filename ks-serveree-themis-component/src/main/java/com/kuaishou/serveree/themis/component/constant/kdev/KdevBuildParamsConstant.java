package com.kuaishou.serveree.themis.component.constant.kdev;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.constant.quality.CheckPluginInputConstants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/12/15 11:23 上午
 */
@AllArgsConstructor
public enum KdevBuildParamsConstant {

    CUSTOM_ARGUMENTS("MVN_BUILD_ARGUMENTS", "用户输入参数"),
    BUILD_MODULES("BUILD_MODULES", "构建模块"),
    SONAR_BUILD_MODULES(CheckPluginInputConstants.SONAR_BUILD_MODULES, "要扫描的模块"),
    JAVA_VERSION("JAVA_VERSION", "jdk版本"),
    BETA_PACKAGE_VERSIONS("BETA_PACKAGE_VERSIONS", "灰度包"),
    MODULES_TYPE_MAP("MODULES_TYPE_MAP", "模块名称及类型"),
    SKIP_COPY_DEPENDENCY("SKIP_COPY_DEPENDENCY", "跳过copy-dependency")
    ;

    @Getter
    private String paramKey;

    @Getter
    private String desc;

    private static final Map<String, KdevBuildParamsConstant> KEY_MAP = Maps.newHashMap();

    static {
        for (KdevBuildParamsConstant kdevBuildParamsConstant : KdevBuildParamsConstant.values()) {
            KEY_MAP.put(kdevBuildParamsConstant.getParamKey(), kdevBuildParamsConstant);
        }
    }

    public static List<String> paramsKeys() {
        return Arrays.stream(KdevBuildParamsConstant.values())
                .map(KdevBuildParamsConstant::getParamKey)
                .collect(Collectors.toList());
    }

    public static List<String> paramKeysMavenScannerCares() {
        return Stream.of(CUSTOM_ARGUMENTS, BUILD_MODULES, SONAR_BUILD_MODULES, JAVA_VERSION, BETA_PACKAGE_VERSIONS)
                .map(KdevBuildParamsConstant::getParamKey)
                .collect(Collectors.toList());
    }

    public static KdevBuildParamsConstant getEnumByKey(String key) {
        return KEY_MAP.get(key);
    }

    @Getter
    public enum ModuleInfoEnum {
        MODULE_NAME("MODEL", "模块名"),
        MODULE_TYPE("TYPE", "模块类型，runner/api"),
        MODULE_PRODUCT("PRODUCT", "产品库");

        private final String key;
        private final String desc;

        ModuleInfoEnum(String key, String desc) {
            this.key = key;
            this.desc = desc;
        }
    }

}
