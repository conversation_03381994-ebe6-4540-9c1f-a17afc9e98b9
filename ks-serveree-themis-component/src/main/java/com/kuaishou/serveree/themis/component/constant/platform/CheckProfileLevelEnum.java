package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import org.springframework.http.HttpStatus;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/7/12 11:23 AM
 */
@AllArgsConstructor
public enum CheckProfileLevelEnum {

    PERSON(0, "个人级"),
    SYSTEM(100, "系统内置"),
    COMPONY(80, "公司级"),
    DEPARTMENT(60, "部门级"),
    TEAM(40, "团队级"),
    PROJECT(20, "项目级"),
    ;

    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    private static final Map<Integer, CheckProfileLevelEnum> LEVEL_ENUM_MAP = Maps.newHashMap();

    static {
        for (CheckProfileLevelEnum levelEnum : values()) {
            LEVEL_ENUM_MAP.put(levelEnum.getCode(), levelEnum);
        }
    }

    public static CheckProfileLevelEnum getByLevel(Integer level) {
        CheckProfileLevelEnum checkProfileLevelEnum = LEVEL_ENUM_MAP.get(level);
        if (checkProfileLevelEnum == null) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "未知level:" + level);
        }
        return checkProfileLevelEnum;
    }

}
