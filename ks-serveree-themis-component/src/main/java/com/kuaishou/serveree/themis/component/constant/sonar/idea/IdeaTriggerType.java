package com.kuaishou.serveree.themis.component.constant.sonar.idea;

/**
 * <AUTHOR>
 * @since 2023/3/16 3:34 PM
 */
public enum IdeaTriggerType {

    SELECTED_FILE(1, "选中的文件"),
    OPEN_FILE(2, "打开的文件"),
    UPDATED_FILE(3, "更新的文件"),
    PROJECT_FILES(4, "全项目的文件"),
    MODULE_FILES(5, "模块下的文件"),
    PACKAGE_FILES(6, "包下的文件"),
    ;

    private final Integer type;
    private final String desc;

    IdeaTriggerType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

}
