package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckProfile对象", description = "")
public class CheckProfile implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "语言类型")
    private String language;

    @ApiModelProperty(value = "配置名")
    private String profileName;

    private String displayName;

    @ApiModelProperty(value = "扫描器")
    private String scanner;

    @ApiModelProperty(value = "创建者")
    private String creator;

    @ApiModelProperty(value = "修改者")
    private String updater;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    private String extraJson;

    private Integer level;

    private Boolean deleted;

    @ApiModelProperty(value = "父规则集")
    private String parentProfileName;

    /**
     * 规则集层级深度
     */
    private Integer layer;

    /**
     * 规则集内规则数
     */
    private Integer ruleCount;


}
