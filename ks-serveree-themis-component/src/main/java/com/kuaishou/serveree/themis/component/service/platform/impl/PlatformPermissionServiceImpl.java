package com.kuaishou.serveree.themis.component.service.platform.impl;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.kdev.SelfKdevApi;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.PlatformUserRoleRelation;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.kdev.GitRoleEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformRoleEnum;
import com.kuaishou.serveree.themis.component.entity.kdev.IdAndName;
import com.kuaishou.serveree.themis.component.entity.platform.PlatformUserRoleSearchCondition;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.PlatformUserRoleRelationService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;

/**
 * <AUTHOR>
 * @since 2022/5/5 2:09 PM
 */
@Service
public class PlatformPermissionServiceImpl implements PlatformPermissionService {

    @Autowired
    private SelfKdevApi selfKdevApi;

    @Autowired
    private PlatformUserRoleRelationService platformUserRoleRelationService;

    @Autowired
    private GitOperations gitOperations;

    @Autowired
    private CheckRepoService checkRepoService;

    private static final Kconf<List<String>> ADMIN_LIST_KCONF =
            Kconfs.ofStringList("qa.themis.platformAdminList", Lists.newArrayList("lixiaoxin"))
                    .build();


    @Override
    public void checkKdevGitPermission(Integer gitProjectId, String username) {
        checkKdevGitPermission(checkRepoService.getCheckRepoByProjectId(gitProjectId), username);
    }

    @Override
    public void checkKdevGitPermission(CheckRepo checkRepo, String username) {
        checkUserLogin(username);
        if (isAdmin(username)) {
            return;
        }
        // kbuild工程
        if (checkRepo.getUseKbuild()) {
            // 获取真实gitProjectId
            checkGitPermission(gitOperations.getProjectByUrl(checkRepo.getRepoUrl()).getId(), username);
            return;
        }
        checkGitPermission(checkRepo.getGitProjectId(), username);
    }

    private void checkGitPermission(Integer gitProjectId, String username) {
        //用户身份的鉴权，具有本项目的角色的用户才可以更改issue的状态流
        List<IdAndName> gitRoles =
                selfKdevApi.listGitRolesByProjectId(gitProjectId, username);
        if (CollectionUtils.isEmpty(gitRoles)
                || gitRoles.stream().map(IdAndName::getName).noneMatch(GitRoleEnum::isGitRole)) {
            throw new ThemisException(ResultCodeConstant.NO_OPERATION_PERMISSION);
        }
    }

    @Override
    public void checkUserNameEquals(String creator, String userName) {
        if (isAdmin(userName)) {
            return;
        }
        if (!StringUtils.equals(creator, userName)) {
            throw new ThemisException(ResultCodeConstant.NO_OPERATION_PERMISSION);
        }
    }

    @Override
    public boolean isAdmin(String userName) {
        return ADMIN_LIST_KCONF.get().contains(userName);
    }

    @Override
    public boolean hasProfileEditPermission(String userName, CheckProfile checkProfile) {
        if (isAdmin(userName) || StringUtils.equals(checkProfile.getCreator(), userName)) {
            return true;
        }
        PlatformUserRoleRelation userRoleRelation = platformUserRoleRelationService.getOneByCondition(
                PlatformUserRoleSearchCondition.builder()
                        .userName(userName)
                        .roleName(PlatformRoleEnum.PROFILE_EDITOR.getKey())
                        .profileName(checkProfile.getProfileName())
                        .build());
        return Objects.nonNull(userRoleRelation);
    }

    private void checkUserLogin(String userName) {
        if (StringUtils.isEmpty(userName)) {
            throw new ThemisException(ResultCodeConstant.NO_PERMISSION);
        }
    }

}
