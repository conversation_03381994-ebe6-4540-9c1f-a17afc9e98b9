package com.kuaishou.serveree.themis.component.entity.sonar;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 since 2021/7/26 5:08 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Profiles {

    private String key;
    private String name;
    private String language;
    private String languageName;
    private boolean isInherited;
    private boolean isDefault;
    private int activeRuleCount;
    private int activeDeprecatedRuleCount;
    private String rulesUpdatedAt;
    private String userUpdatedAt;
    private String organization;
    private boolean isBuiltIn;
    private Actions actions;

}
