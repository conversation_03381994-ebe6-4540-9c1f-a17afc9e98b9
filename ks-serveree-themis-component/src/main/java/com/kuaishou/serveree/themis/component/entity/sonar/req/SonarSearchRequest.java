package com.kuaishou.serveree.themis.component.entity.sonar.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/7/21 5:17 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SonarSearchRequest {

    private String componentKeys;
    private String branch;
    private String from;
    private String to;
    private String types;
    private Boolean resolved;
    private String createdAt;
    private String statuses;
    private String severities;
    private String facets;
    private int page;
    private int pageSize;
    private boolean sinceLeakPeriod;
    private String additionalFields;

    private String rules; //规则

    private String resolutions; // 解决方式 FALSE-POSITIVE, WONTFIX, FIXED, REMOVED
}
