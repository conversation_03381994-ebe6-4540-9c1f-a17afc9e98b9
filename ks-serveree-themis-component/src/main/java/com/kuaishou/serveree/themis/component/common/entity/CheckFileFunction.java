package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckFileFunction对象", description = "")
public class CheckFileFunction implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "file的id")
    private Long fileId;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "起始行")
    private Integer startLine;

    @ApiModelProperty(value = "结束行")
    private Integer endLine;

    @ApiModelProperty(value = "代码总行数")
    private Integer lineCount;

    @ApiModelProperty(value = "逻辑代码行数")
    private Integer logicLineCount;

    @ApiModelProperty(value = "圈复杂度")
    private Integer complexity;

    @ApiModelProperty(value = "霍尔斯特德体积")
    private Integer halsteadVolume;

    @ApiModelProperty(value = "check_base的主键id")
    private Long baseId;

    @ApiModelProperty(value = "check_repo_branch主键id")
    private Long checkRepoBranchId;

    @ApiModelProperty(value = "check_repo表主键id")
    private Long checkRepoId;

    @ApiModelProperty(value = "方法名称")
    private String functionName;

}
