package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/5/18 2:31 PM
 */
@AllArgsConstructor
public enum PlatformSonarInteractiveType {

    CREATE_PROFILE(1, "创建质量配置"),
    PROFILE_ADD_RULE(2, "质量配置增加规则"),
    PROFILE_DELETE_RULE(3, "质量配置减少规则"),

    PROJECT_UPDATE_PROFILE(4, "项目更新规则集配置"),
    PROFILE_DELETE(5, "删除质量配置"),

    PROFILE_COPY(6, "规则集copy"),

    CREATE_PROJECT(7, "创建项目"),

    PROFILE_CHANGE_PARENT(8, "修改规则集父规则集")
    ;

    @Getter
    private final Integer type;
    @Getter
    private final String desc;

    private static final Map<Integer, PlatformSonarInteractiveType> TYPE_MAP = Maps.newHashMap();

    static {
        for (PlatformSonarInteractiveType interactiveType : PlatformSonarInteractiveType.values()) {
            TYPE_MAP.put(interactiveType.getType(), interactiveType);
        }
    }

    public static PlatformSonarInteractiveType getByType(Integer type) {
        return TYPE_MAP.get(type);
    }

}
