package com.kuaishou.serveree.themis.component.proxy.algorithm;

import java.util.LinkedList;
import java.util.List;
import java.util.SortedMap;
import java.util.TreeMap;

import com.kuaishou.serveree.themis.component.proxy.SonarConfigEnum;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-23
 */
public class CodeScanningHashUtil {
    //服务器的列表数目
    private static int serverNum = SonarConfigEnum.values().length;
    //待添加入Hash环的服务器列表
    private static List<String> servers = new LinkedList<>();
    //key表示服务器的hash值，value表示服务器
    private static SortedMap<Integer, String> sortedMap = new TreeMap<>();

    //程序初始化，将所有的服务器放入sortedMap中
    static {
        servers.add(SonarConfigEnum.NODE1.getIp() + ":" + SonarConfigEnum.NODE1.getPort());
        servers.add(SonarConfigEnum.NODE2.getIp() + ":" + SonarConfigEnum.NODE2.getPort());
        servers.add(SonarConfigEnum.NODE3.getIp() + ":" + SonarConfigEnum.NODE3.getPort());
        servers.add(SonarConfigEnum.NODE4.getIp() + ":" + SonarConfigEnum.NODE4.getPort());
        for (int i = 0; i < servers.size(); i++) {
            int hash = getHash(servers.get(i));
            sortedMap.put(hash, servers.get(i));
        }
    }

    //得到应当路由到的结点
    public static String getServer(String key) {
        //得到该key的hash值
        int hash = getHash(key);
        //得到大于该Hash值的所有Map
        SortedMap<Integer, String> subMap = sortedMap.tailMap(hash);
        if (subMap.isEmpty()) {
            //如果没有比该key的hash值大的，则从第一个node开始
            Integer i = sortedMap.firstKey();
            //返回对应的服务器
            return sortedMap.get(i);
        } else {
            //第一个Key就是顺时针过去离node最近的那个结点
            Integer i = subMap.firstKey();
            //返回对应的服务器
            return subMap.get(i);
        }
    }

    private static int getHash(String str) {
        int hash = Math.abs(str.hashCode()) % serverNum;
        return hash;
    }
}
