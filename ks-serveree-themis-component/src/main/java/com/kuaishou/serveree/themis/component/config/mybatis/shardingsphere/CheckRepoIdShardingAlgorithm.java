package com.kuaishou.serveree.themis.component.config.mybatis.shardingsphere;

import java.util.Collection;

import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

/**
 * <AUTHOR>
 * @since 2022/7/7 4:44 PM
 */
public class CheckRepoIdShardingAlgorithm implements StandardShardingAlgorithm<Long> {

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> preciseShardingValue) {
        Long checkRepoId = preciseShardingValue.getValue();
        int tableSize = availableTargetNames.size();
        long suffixNo = checkRepoId % tableSize;
        return availableTargetNames.stream()
                .filter(targetName -> targetName.endsWith(suffixNo + ""))
                .findFirst()
                .orElse(null);
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
            RangeShardingValue<Long> rangeShardingValue) {
        return availableTargetNames;
    }

    @Override
    public void init() {

    }

    @Override
    public String getType() {
        return "CLASS_BASED";
    }

}
