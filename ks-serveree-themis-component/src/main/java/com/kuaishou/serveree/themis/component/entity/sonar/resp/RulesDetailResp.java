package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Active;
import com.kuaishou.serveree.themis.component.entity.sonar.RuleDetail;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RulesDetailResp {
    private RuleDetail rule;

    private List<Active> actives;
}
