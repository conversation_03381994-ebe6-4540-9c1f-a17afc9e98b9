package com.kuaishou.serveree.themis.component.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020/10/28 3:36 下午
 */
@AllArgsConstructor
public enum ResultCodeConstant {

    SUCCESS(200, "success"),
    NO_PERMISSION(401, "访问无权限"),
    NO_OPERATION_PERMISSION(401, "您没有操作权限"),
    API_RESP_ERROR(500, "异常信息：%s | 返回结果：%s"),

    SAVE_FAIL(10001, "数据保存失败"),
    PARAMS_CAN_NOT_EMPTY(10002, "参数不能为空"),
    INVALID_PARAMS(10003, "不合理的参数"),
    TASK_NOT_EXIST(10004, "任务不存在"),
    GIT_CLONE_ERROR(10005, "clone代码错误"),
    NOT_FOUND_CHECK_SERVICE(10006, "找不到对应的检查服务"),
    TASK_NOT_PERMISSION_TO_OPERATE(10007, "当前账户没有操作当前任务的权限"),
    READ_LOG_FILE_ERROR(10008, "读取log文件出现异常"),
    NOT_FOUND_CAN_DOWNLOAD_FILE(10009, "没有找到可下载的文件"),
    DOWNLOAD_FILE_ERROR(10010, "下载过程中出现异常，请重试"),
    USER_PARAMS_DESERIALIZE_ERROR(10011, "解析用户参数异常"),
    NOT_FOUND_JOB_TYPE(10012, "找不到对应的job类型"),
    MAKING_FILE(10013, "正在生成文件中"),
    CHECK_FAIL(10014, "检查失败"),
    NOT_FOUND_ANALYZE(10015, "未发现分析分析主数据"),
    NOT_FOUND_ANALYZE_LABELS(10016, "此构建未发现分析标签"),
    NOT_FOUND_CHECK_RUN_TYPE(10017, "未找到对应的运行类型"),
    RUNE_CHECK_TYPE_LOGIC_ERROR(10018, "运行检查类型逻辑出现异常"),
    DUPLICATE_KEY_ERROR(10019, "重复数据"),
    ADD_WHITE_LIST_ERROR(10020, "增加白名单出现异常"),
    DEL_CACHE_ERROR(10021, "删除缓存失败"),

    SYNC_FAIL(10022, "同步失败"),
    NOT_FOUND_HANDLER(10023, "未找到需要执行的handler"),
    SONAR_PROJECTKEY_ALREADY_EXISTS(10033, "sonar project key already exists"),
    ILLEGAL_EXECUTION(1024, "执行非法，校验项目归属未通过"),
    // 特殊code请勿修改
    NOT_FOUND_CHECK_REPO(1025, "此repo没有初始化"),
    // 特殊code请勿修改
    NOT_BRANCH_CHECK(1025, "此分支没有初始化"),
    // 特殊code请勿修改
    NOT_FOUND_SNAPSHOT(1025, "当前分支未发现初始化过的快照数据"),
    NOT_SEARCH_AUTH(1026, "此repo没有搜索的权限"),
    ISSUE_SIZE_NOT_MATCH(1028, "issue与传入的id数量不匹配"),
    CHECK_ACTION_FAIL(1029, "上报操作失败"),
    NOT_SELECT_COMMIT_ID(1030, "未检索出commitId"),
    NOT_AUTH_TO_UPDATE(1031, "没有修改权限"),
    NOT_AUTH_TO_DEL(1032, "没有删除权限"),
    NOT_FOUND_PIPELINE_TYPE(1033, "未找到pipeline执行器"),
    NOT_FOUND_BASE_DATA(1034, "未发现扫描base主数据"),
    SELECT_COUNT_ERROR(1035, "检查流程查询数量失败"),
    PROCESS_ISSUE_COUNT_FUTURE_FAIL(1036, "检查流程查询数量Future get失败"),
    SYNCING_SONAR_RESULT(1037, "正在同步sonar结果中"),

    TRANSITION_NOT_EXIST(1038, "没有可转换的状态"),
    ISSUE_NOT_EXIST(1039, "问题不存在"),
    TRANSFER_PROJECT_ID_ERROR(1040, "无法解析到projectId，不能路由转发"),
    TRANSFER_FAILED(1041, "调用sonar返回结果为空"),
    TRANSFER_PARAMS_ERROR(1042, "参数解析异常，不能路由转发"),
    CONTENT_TYPE_NOT_SUPPORTED(1043, "ContentType暂不支持，不能路由转发，ContentType是：%s"),
    UNKNOWN_STATE(1044, "未知的状态：%s，issue状态集无法流转"),
    BASE_NOT_EXIST(1045, "构建id不存在"),
    ISSUE_ONLY_SUPPORT_RESOLVED_BY_CODE(1046, "问题只能通过变更代码解决"),

    STAR_ERROR(1047, "关注操作出现异常"),
    REPO_LIST_SPONSOR_ASYNC_TASK_ERROR(1048, "项目列表发起异步请求出现错误"),
    REPO_LIST_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR(1049, "项目列表获取线程数据失败"),
    DETAIL_ASYNC_GET_TASK_VALUE_ERROR(1050, "项目详情发起异步请求出现错误"),
    DETAIL_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR(1051, "项目详情获取线程数据失败"),
    ISSUE_LIST_SPONSOR_ASYNC_TASK_ERROR(1052, "issue列表发起异步请求出现错误"),
    ISSUE_LIST_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR(1053, "issue列表获取线程数据失败"),
    PROFILE_NAME_EXIST(1054, "规则集名称已存在"),
    PROFILE_NOT_EXIST(1055, "规则集不存在"),
    RULE_NOT_EXIST(1056, "规则不存在"),

    RULE_NOT_MATCH_PROFILE(1057, "规则与规则集不匹配,不匹配的规则有：%"),

    REPO_NOT_ANALYSIS(1058, "项目没有分析过"),

    REPO_NOT_CREATE(1059, "项目未创建"),
    PROFILE_IS_USING_NOT_DELETE(1060, "规则集合正在被使用，不可被删除"),

    SKY_EYE_DETAIL_ASYNC_GET_TASK_VALUE_ERROR(1061, "天眼项目详情发起异步请求出现错误"),
    SKY_EYE_DETAIL_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR(1062, "天眼项目详情获取线程数据失败"),
    BASE_ID_SKYEYE_REPORT_EXIST(1063, "baseId关联的本地报告已经上报过"),
    SKYEYE_LOCAL_REPORT_NOT_EXIST(1064, "本地报告不存在"),
    SKYEYE_LOCAL_REPORT_HAS_DELETED(1065, "本地报告已经被删除"),

    BRANCH_NOT_INIT_RULESET(1067, "此分支未初始化规则集"),
    LANGUAGE_VERSION_CHANGE_FAIL(1068, "变更扫描语言版本失败"),
    CRON_CHANGE_FAIL(1069, "变更扫描cron表达式失败"),
    DELETE_PROJECT_LACK_PARAM(1070, "删除项目缺乏有效参数"),
    PROFILE_ALREADY_HAVE_PARENT(1071, "只允许继承一级规则集"),
    LANGUAGE_INFO_ERROR(1072, "语言配置信息异常，请联系lixiaoxin"),
    SONAR_DATA_REMOVE_ASYNC_TASK_ERROR(1073, "sonar节点删除三个月前数据异步任务失败"),
    NOT_FOUND_CHECK_FILE_DATA(1074, "不存在对应的checkFile数据"),

    RULE_SET_INFO_ERROR(1075, "规则集合数据异常，请联系lixiaoxin"),
    MR_STUCK_SPONSOR_CODE_SCAN_FAIL(1076, "MR卡点发起代码扫描失败"),
    OPEN_API_KDEV_PIPELINE_CREATE_FAIL(1077, "openapi创建kdev流水线失败"),
    OPEN_API_KDEV_PIPELINE_TRIGGER_FAIL(1078, "openapi触发kdev流水线失败"),
    MR_STUCK_PIPELINE_RECORD_NOT_EXIST(1079, "不存在有效的MR卡点流水线执行记录"),
    UNSUPPORTED_LANGUAGE(1080, "不支持的语言类型"),
    PIPELINE_SCAN_REPORT_FAILED(1081, "扫描结果上报失败"),
    FETCH_SONAR_CACHE_REPORT_FAILED(1082, "获取代码扫描缓存结果失败")
    ;


    @Getter
    private final int code;

    @Getter
    private final String message;

}
