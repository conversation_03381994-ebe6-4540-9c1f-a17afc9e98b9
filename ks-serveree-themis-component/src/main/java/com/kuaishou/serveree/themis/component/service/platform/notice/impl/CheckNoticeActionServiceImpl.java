package com.kuaishou.serveree.themis.component.service.platform.notice.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.github.phantomthief.util.MoreFunctions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.client.ares.AresApi;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckNotice;
import com.kuaishou.serveree.themis.component.common.entity.CheckNoticeRecord;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.constant.kdev.MrStuckPointResultStatus;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformSendStatus;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.entity.ares.AresMessageEntity;
import com.kuaishou.serveree.themis.component.entity.issue.MrCheckpointIssueBo;
import com.kuaishou.serveree.themis.component.entity.mr.MrStuckResultData;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.db.CheckNoticeRecordService;
import com.kuaishou.serveree.themis.component.service.db.CheckNoticeService;
import com.kuaishou.serveree.themis.component.service.platform.notice.CheckNoticeActionService;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.cron.pattern.CronPatternUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/11/18 5:23 PM
 */
@Service
@Slf4j
public class CheckNoticeActionServiceImpl implements CheckNoticeActionService {

    @Autowired
    private CheckNoticeService checkNoticeService;

    @Autowired
    private CheckNoticeRecordService checkNoticeRecordService;

    @Autowired
    private IssueSummaryService issueSummaryService;

    @Autowired
    private CheckRepoService checkRepoService;

    @Autowired
    private CheckRepoBranchService checkRepoBranchService;

    private static final String SEND_CONTENT = "您的%s，在%s，命中了代码扫描的高优规则，请[登录](%s)进行治理。";

    @Autowired
    private AresApi aresApi;

    private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss");

    @Value("${scan-platform.url}")
    private String platformUrl;

    @Override
    public void sendNotice(CheckBase checkBase) {
        Long checkRepoId = checkBase.getCheckRepoId();
        Long checkRepoBranchId = checkBase.getCheckRepoBranchId();

        CheckRepo checkRepo = checkRepoService.getById(checkRepoId);
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.getById(checkRepoBranchId);

        CheckNotice checkNotice = checkNoticeService.getByCheckRepoBranchId(checkRepoBranchId);
        if (checkNotice == null) {
            return;
        }

        List<IssueSummary> openIssueSummaries = issueSummaryService.listOpenByProjectBranch(checkRepo.getGitProjectId(),
                checkRepoBranch.getBranchName(),
                ScanModeEnum.OFFLINE.getCode());
        if (CollectionUtils.isEmpty(openIssueSummaries)) {
            return;
        }

        Date now = new Date();
        DateTime endOfDay = DateUtil.endOfDay(now);
        DateTime beginOfDay = DateUtil.beginOfDay(now);

        String sendStartCron = checkNotice.getSendStartCron();
        String sendEndCron = checkNotice.getSendEndCron();
        List<Date> startDates = CronPatternUtil.matchedDates(sendStartCron, beginOfDay, endOfDay, 1, true);
        if (CollectionUtils.isEmpty(startDates)) {
            return;
        }
        List<Date> endDates = CronPatternUtil.matchedDates(sendEndCron, beginOfDay, endOfDay, 1, true);
        Date startDate = startDates.get(0);
        Map<String, String> userContentMap =
                generateNoticeContent(checkRepo, checkBase, checkRepoBranch, openIssueSummaries, checkNotice);
        if (MapUtils.isEmpty(userContentMap)) {
            return;
        }
        // 如果今天可以发送 但是还没到时间
        if (now.before(startDate)) {
            List<CheckNoticeRecord> checkNoticeRecords = generateCheckRecords(userContentMap, checkRepoId,
                    checkRepoBranchId, startDate, PlatformSendStatus.WAITING.getStatus());
            checkNoticeRecordService.saveBatch(checkNoticeRecords);
            return;
        }
        // 如果超出了今天的最后发送时间 那就明天发
        if (!matchEndDate(endDates, now)) {
            List<CheckNoticeRecord> checkNoticeRecords = generateCheckRecords(userContentMap, checkRepoId,
                    checkRepoBranchId, DateUtils.addDays(startDate, 1), PlatformSendStatus.WAITING.getStatus());
            checkNoticeRecordService.saveBatch(checkNoticeRecords);
            return;
        }
        // 这里就不考虑分布式事务了 直接发送完了保存
        userContentMap.forEach((key, val) -> {
            AresMessageEntity aresMessageEntity = AresMessageEntity.builder()
                    .msgTypes(Lists.newArrayList(7))
                    .templateId(0)
                    .text(val)
                    .userNames(Lists.newArrayList(key))
                    .build();
            MoreFunctions.runCatching(() -> {
                String result = aresApi.sendKimNotice(aresMessageEntity);
                log.info("platform notice send context is {},result is {}", JSONUtils.serialize(aresMessageEntity),
                        result);
            });
        });
        // 发送记录保存下来
        List<CheckNoticeRecord> checkNoticeRecords = generateCheckRecords(userContentMap, checkRepoId,
                checkRepoBranchId, now, PlatformSendStatus.SENT.getStatus());
        checkNoticeRecordService.saveBatch(checkNoticeRecords);
    }

    /**
     * mr卡点流水线执行结束，发送消息
     * @param resultData
     */
    @Override
    public void sendMrStuckResultNotice(MrStuckResultData resultData) {
        if (StringUtils.isBlank(resultData.getMrCreator())) {
            log.warn("[kim通知MR创建者]：未获取到MR创建者，不发送扫描结束消息！扫描结果：{}", resultData);
            return;
        }
        if (!MrStuckPointResultStatus.FAILED.name().equals(resultData.getStatus())
                && !MrStuckPointResultStatus.TASK_FAILED.name().equals(resultData.getStatus())) {
            log.info("[kim通知MR创建者]：状态不是[扫描未通过]或[流水线执行失败]，不发送kim消息！扫描结果：{}", resultData);
            return;
        }
        MoreFunctions.runCatching(() -> doSendMrStuckResultNotice(resultData));
    }

    private void doSendMrStuckResultNotice(MrStuckResultData resultData) {
        // 构建消息内容并发送kim
        AresMessageEntity aresMessageEntity = AresMessageEntity.builder()
                .msgTypes(Lists.newArrayList(7))
                .templateId(0)
                .text(generateMrStuckScanResultNoticeContent(resultData))
                .userNames(Lists.newArrayList(resultData.getMrCreator()))
                .build();
        aresApi.sendKimNotice(aresMessageEntity);
    }

    /**
     * 生成Mr卡点流水线执行结束，kim通知的消息内容
     * @param resultData
     * @return
     */
    private String generateMrStuckScanResultNoticeContent(MrStuckResultData resultData) {
        String statusDesc = MrStuckPointResultStatus.getByName(resultData.getStatus()).getDesc();
        List<String> openStatuses = List.of(CheckIssueStatus.OPEN.getStatus(), CheckIssueStatus.RE_OPEN.getStatus());
        // 过滤还未解决的问题
        List<MrCheckpointIssueBo> openIssues = resultData.getIssues().stream()
                .filter(issue -> Boolean.TRUE.equals(issue.getStuck()))
                .filter(issue -> openStatuses.contains(issue.getStatus()))
                .collect(Collectors.toList());
        // 拼接mr详细地址
        String mrDetailUrl = getMrDetailUrl(resultData.getMrUrl(), openIssues);
        // 得到issue统计结果字符串
        String issueStatisticString = getIssueStatisticString(openIssues);
        // 没有问题（扫描过程失败）
        if (StringUtils.isBlank(issueStatisticString)) {
            return String.format("### 代码扫描检查【%s】\n"
                            + "合并请求中的代码扫描检查【%s】，请及时关注\n"
                            + "合并请求：%s\n"
                            + "创建人：%s\n"
                            + "[查看详情](%s)",
                    statusDesc, statusDesc, resultData.getMrTitle(), resultData.getMrCreator(), mrDetailUrl);
        }
        // 有问题（扫描未通过）
        return String.format("### 代码扫描检查【%s】\n"
                        + "合并请求中的代码扫描检查【%s】，请及时关注\n"
                        + "合并请求：%s\n"
                        + "创建人：%s\n"
                        + "问题数：%s\n"
                        + "[查看详情](%s)",
                statusDesc, statusDesc, resultData.getMrTitle(), resultData.getMrCreator(),
                issueStatisticString, mrDetailUrl);
    }

    /**
     * 拼接mr地址+第一个issue的定位信息
     * @param mrUrl
     * @param issueVoList
     * @return
     */
    private String getMrDetailUrl(String mrUrl, List<MrCheckpointIssueBo> issueVoList) {
        // 没问题就返回原MR地址
        if (CollectionUtils.isEmpty(issueVoList)) {
            return mrUrl;
        }
        // 拼接第一个issue
        MrCheckpointIssueBo issue = issueVoList.get(0);
        String template = mrUrl
                + "&tabKey=changedFiles&position={\"prePatchId\":null,\"prePatchName\":\"\",\"nextPatchId\":null,\"nextPatchName\":\"\","
                + "\"diffId\":\"\",\"filePath\":\"%s\",\"side\":\"RIGHT\",\"leftLineNum\":0,\"rightLineNum\":%d,"
                + "\"issueId\":\"%d\"}";
        return String.format(template, issue.getLocation(), issue.getEndLine(), issue.getIssueId());
    }

    private String getIssueStatisticString(List<MrCheckpointIssueBo> issueVoList) {
        if (CollectionUtils.isEmpty(issueVoList)) {
            return StringUtils.EMPTY;
        }
        StringBuilder sb = new StringBuilder();
        Map<String, List<MrCheckpointIssueBo>> severityMap = issueVoList.stream()
                .collect(Collectors.groupingBy(MrCheckpointIssueBo::getSeverity));
        int i = 0;
        for (Entry<String, List<MrCheckpointIssueBo>> entry : severityMap.entrySet()) {
            String severityDesc = CheckIssueSeverity.getDescByThemisKey(entry.getKey());
            sb.append(String.format("%s问题（%d）", severityDesc, entry.getValue().size()));
            if (++i < severityMap.size()) {
                sb.append("，");
            }
        }
        return sb.toString();
    }

    @Override
    public void compensatePushNotice() {
        // 查询 今天的待发送通知
        List<CheckNoticeRecord> needSendRecords = checkNoticeRecordService.listByCurrentToSendRecords();
        if (CollectionUtils.isEmpty(needSendRecords)) {
            return;
        }
        // 查询今天是否有已经发送的记录了
        List<Long> checkRepoBranchIds = needSendRecords.stream()
                .map(CheckNoticeRecord::getCheckRepoBranchId)
                .collect(Collectors.toList());
        List<CheckNoticeRecord> hasSentRecords =
                checkNoticeRecordService.listCurrentDayHasSendRecords(checkRepoBranchIds);
        Map<Long, List<CheckNoticeRecord>> checkRepoBranchIdRecordsMap = hasSentRecords.stream()
                .collect(Collectors.groupingBy(CheckNoticeRecord::getCheckRepoBranchId));
        LocalDateTime now = LocalDateTime.now();
        for (CheckNoticeRecord needSendRecord : needSendRecords) {
            Long checkRepoBranchId = needSendRecord.getCheckRepoBranchId();
            String receiver = needSendRecord.getReceiver();
            List<CheckNoticeRecord> checkNoticeRecords = checkRepoBranchIdRecordsMap.get(checkRepoBranchId);
            Optional<String> findReceiverHasSent = Optional.ofNullable(checkNoticeRecords)
                    .orElse(Lists.newArrayList())
                    .stream()
                    .map(CheckNoticeRecord::getReceiver)
                    .filter(o -> o.equals(receiver))
                    .findAny();
            // 如果已经发送过了 那就不发送了 切记 每天最多只发送一次
            if (findReceiverHasSent.isPresent()) {
                needSendRecord.setSendStatus(PlatformSendStatus.SENT.getStatus());
                needSendRecord.setGmtModified(now);
                continue;
            }
            // 发送完了记录
            AresMessageEntity aresMessageEntity = AresMessageEntity.builder()
                    .msgTypes(Lists.newArrayList(7))
                    .templateId(0)
                    .text(needSendRecord.getMsgContent())
                    .userNames(Lists.newArrayList(needSendRecord.getReceiver()))
                    .build();
            MoreFunctions.runCatching(() -> {
                String result = aresApi.sendKimNotice(aresMessageEntity);
                log.info("platform notice send context is {},result is {}", JSONUtils.serialize(aresMessageEntity), result);
            });
            needSendRecord.setSendStatus(PlatformSendStatus.SENT.getStatus());
            needSendRecord.setGmtModified(now);
        }
        checkNoticeRecordService.updateBatchById(needSendRecords);
    }

    private List<CheckNoticeRecord> generateCheckRecords(Map<String, String> userContentMap, Long checkRepoId,
            Long checkRepoBranchId, Date startDate, Integer status) {
        LocalDateTime now = LocalDateTime.now();
        List<CheckNoticeRecord> checkNoticeRecords = Lists.newArrayList();
        userContentMap.forEach((key, val) -> {
            CheckNoticeRecord checkNoticeRecord = new CheckNoticeRecord();
            checkNoticeRecord.setCheckRepoId(checkRepoId);
            checkNoticeRecord.setCheckRepoBranchId(checkRepoBranchId);
            checkNoticeRecord.setSendTime(startDate.getTime());
            checkNoticeRecord.setMsgContent(val);
            checkNoticeRecord.setReceiver(key);
            checkNoticeRecord.setGmtCreate(now);
            checkNoticeRecord.setGmtModified(now);
            checkNoticeRecord.setSendStatus(status);
            checkNoticeRecords.add(checkNoticeRecord);
        });
        return checkNoticeRecords;
    }

    private boolean matchEndDate(List<Date> endDates, Date now) {
        if (CollectionUtils.isEmpty(endDates)) {
            return true;
        }
        Date endDate = endDates.get(0);
        return now.before(endDate);
    }

    private Map<String, String> generateNoticeContent(CheckRepo checkRepo, CheckBase checkBase,
            CheckRepoBranch checkRepoBranch, List<IssueSummary> openIssueSummaries, CheckNotice checkNotice) {
        Map<String, String> userContentMap = Maps.newHashMap();
        String severity = checkNotice.getSeverity();
        List<String> aboveSeverities = CheckIssueSeverity.getByAboveLevel(severity);
        if (CollectionUtils.isEmpty(aboveSeverities)) {
            return null;
        }
        Map<String, List<IssueSummary>> authorIssueSummariesMap = openIssueSummaries.stream()
                .filter(o -> aboveSeverities.contains(o.getSeverity()))
                .collect(Collectors.groupingBy(IssueSummary::getAuthor));
        // 查询当天当前项目发送的记录
        List<CheckNoticeRecord> checkNoticeRecords =
                checkNoticeRecordService.listByCheckRepoBranchRecordsByCurrentDayHasSent(checkRepoBranch.getId());
        List<String> needExcludeUsers = checkNoticeRecords.stream()
                .map(CheckNoticeRecord::getReceiver)
                .collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        for (String author : authorIssueSummariesMap.keySet()) {
            if (needExcludeUsers.contains(author)) {
                continue;
            }
            StringBuilder sb = new StringBuilder("### 代码扫描通知").append("\n");
            sb.append(String.format(SEND_CONTENT,
                    GitUtils.getRepoName(checkRepo.getRepoUrl()),
                    DTF.format(now),
                    String.format("%s/web/codescan/project/issueList?gitProjectId=%s&branch=%s&authors=%s&severities=%s",
                            platformUrl, checkRepo.getGitProjectId(), checkRepoBranch.getBranchName(),
                            author, StringUtils.join(aboveSeverities, ","))));
            userContentMap.put(author, sb.toString());
        }
        return userContentMap;
    }

}