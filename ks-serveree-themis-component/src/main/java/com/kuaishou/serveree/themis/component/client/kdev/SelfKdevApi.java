package com.kuaishou.serveree.themis.component.client.kdev;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.entity.kdev.GitProjectPerm;
import com.kuaishou.serveree.themis.component.entity.kdev.GitProjectPerm.GitRole;
import com.kuaishou.serveree.themis.component.entity.kdev.IdAndName;
import com.kuaishou.serveree.themis.component.entity.kdev.UserProjectIdsResponse;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

/**
 * <AUTHOR>
 * @since 2022/4/28 3:44 下午
 */
@Component
public class SelfKdevApi {

    @Autowired
    private KdevApi kdevApi;

    @Autowired
    private KsRedisClient ksRedisClient;
    private static final long USER_INFO_TTL_SECONDS = 600L;

    public UserProjectIdsResponse getUserProjectIds(String username) {
        final String redisKey = KsRedisPrefixConstant.GIT_USER_PROJECT_IDS_PREFIX + username;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return JSONUtils.deserialize(redisVal, UserProjectIdsResponse.class);
        }
        UserProjectIdsResponse userProjectIdsResponse = kdevApi.getUserGitProjectIds(username);
        // 如果是null的话不加缓存 防止超时造成问题
        if (userProjectIdsResponse == null) {
            UserProjectIdsResponse response = new UserProjectIdsResponse();
            response.setTotal(0);
            response.setList(Lists.newArrayList());
            return response;
        }
        ksRedisClient.sync().setex(redisKey, USER_INFO_TTL_SECONDS, JSONUtils.serialize(userProjectIdsResponse));
        return userProjectIdsResponse;
    }

    public List<IdAndName> listGitRolesByProjectId(Integer projectId, String userName) {
        final String redisKey = KsRedisPrefixConstant.GIT_USER_PROJECT_ROLES_PREFIX + userName + ":" + projectId;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return JSONUtils.deserializeList(redisVal, IdAndName.class);
        }
        List<GitRole> gitRoleList = kdevApi.listGitRolesByProjectId(projectId, userName).getRoleList();
        if (CollectionUtils.isEmpty(gitRoleList)) {
            return Collections.emptyList();
        }
        List<IdAndName> idAndNames = gitRoleList.stream()
                .map(role -> IdAndName.builder().name(role.getName()).desc(role.getDescription()).build())
                .collect(Collectors.toList());
        ksRedisClient.sync().setex(redisKey, USER_INFO_TTL_SECONDS, JSONUtils.serialize(idAndNames));
        return idAndNames;
    }

    public boolean hasGitProjectConfigAdminPerm(Integer projectId, String userName) {
        final String redisKey = KsRedisPrefixConstant.GIT_USER_PROJECT_PERMS_PREFIX + userName + ":" + projectId;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return Optional.ofNullable(JSONUtils.deserialize(redisVal, GitProjectPerm.class))
                    .map(GitProjectPerm::isHasConfigAdminPerm).orElse(false);
        }
        GitProjectPerm gitProjectPerm = kdevApi.listGitRolesByProjectId(projectId, userName);
        ksRedisClient.sync().setex(redisKey, USER_INFO_TTL_SECONDS, JSONUtils.serialize(gitProjectPerm));
        return gitProjectPerm.isHasConfigAdminPerm();
    }

}
