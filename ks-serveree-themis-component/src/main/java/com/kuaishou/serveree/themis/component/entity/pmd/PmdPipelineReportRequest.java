package com.kuaishou.serveree.themis.component.entity.pmd;

import java.util.List;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-16
 */
// CHECKSTYLE:OFF
@Data
public class PmdPipelineReportRequest {

    @Min(value = 1, message = "kspBuildId无效")
    private Long kspBuildId;
    @Min(value = 1, message = "gitProjectId无效")
    private Integer gitProjectId;
    @NotBlank(message = "branch无效")
    private String branch;
    @NotBlank(message = "commitId无效")
    private String commitId;
    @NotNull(message = "mrId无效")
    private Integer mrId;
    @NotNull(message = "incrementMode无效")
    private Boolean incrementMode = Boolean.FALSE;
    @NotNull(message = "scanMode无效")
    private Integer scanMode = ScanModeEnum.PROCESS.getCode();
    @NotNull(message = "scanDuration无效")
    private Long scanDuration;
    @NotBlank(message = "status无效")
    private String status;
    private String sponsor;

    private Integer formatVersion;
    private String pmdVersion; // "7.0.0-rc4",
    private Long timestamp;
    private List<FileReport> files;
    private List<FileReport> suppressedViolations;
    private List<ProcessingError> processingErrors;
    private List<ConfigurationError> configurationErrors;

    @Data
    public static class ProcessingError {
        private String filename;
        private String message;
        private String detail;
    }

    @Data
    public static class ConfigurationError {
        private String rule;
        private String ruleSet;
        private String message;
    }

    @Data
    public static class FileReport {
        private String filename;

        private List<Violation> violations;
    }

    @Data
    public static class Violation {
        private Integer beginline;
        private Integer endline;
        private Integer begincolumn;
        private Integer endcolumn;

        private String description;
        private String rule;
        private String ruleset;
        private Integer priority;

        private String externalInfoUrl;
    }
}
