package com.kuaishou.serveree.themis.component.dto.bpm;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-08
 */
@NoArgsConstructor
@Data
public class BpmMessageDto {
    @JsonProperty("id")
    private String id;
    @JsonProperty("eventType")
    private String eventType;
    @JsonProperty("processState")
    private String processState;
    @JsonProperty("processInstanceId")
    private String processInstanceId;
    @JsonProperty("processName")
    private String processName;
    @JsonProperty("variables")
    private Map<String, Object> variables;
    @JsonProperty("businessId")
    private String businessId;
    @JsonProperty("processKey")
    private String processKey;
    @JsonProperty("sysCode")
    private Object sysCode;
    @JsonProperty("processDefinitionKey")
    private String processDefinitionKey;
    @JsonProperty("formType")
    private String formType;
    @JsonProperty("initiatorId")
    private String initiatorId;
    @JsonProperty("initiatorUsername")
    private String initiatorUsername;
    @JsonProperty("initiatorName")
    private String initiatorName;
    @JsonProperty("initiatorOrg")
    private String initiatorOrg;
    @JsonProperty("initiatorTime")
    private String initiatorTime;
    @JsonProperty("applyExplain")
    private String applyExplain;
    @JsonProperty("taskDefinitionKey")
    private String taskDefinitionKey;
    @JsonProperty("taskId")
    private String taskId;
    @JsonProperty("taskName")
    private String taskName;
    @JsonProperty("assignees")
    private String assignees;
    @JsonProperty("operation")
    private String operation;
    @JsonProperty("commments")
    private String commments;
    @JsonProperty("assigneeInUsername")
    private List<String> assigneeInUsername;
    @JsonProperty("processEnded")
    private Boolean processEnded;
    @JsonProperty("approveTime")
    private Object approveTime;
}
