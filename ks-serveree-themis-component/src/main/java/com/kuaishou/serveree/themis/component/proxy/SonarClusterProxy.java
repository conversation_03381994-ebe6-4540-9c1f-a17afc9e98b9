package com.kuaishou.serveree.themis.component.proxy;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterOperations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterSimpleFactory;
import com.kuaishou.serveree.themis.component.proxy.strategy.RedirectGetStrategy;
import com.kuaishou.serveree.themis.component.proxy.strategy.RedirectPostStrategy;
import com.kuaishou.serveree.themis.component.vo.response.proxy.ProxyLoginInfoVo;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-14
 */
@Service
public class SonarClusterProxy {
    @Autowired
    private RedirectGetStrategy getStrategy;
    @Autowired
    private RedirectPostStrategy postStrategy;
    @Autowired
    private SonarClusterSimpleFactory sonarClusterSimpleFactory;

    /**
     * sonar多实例路由策略核心根据projectId%4来决定数据存储到哪个实例或者从哪个实例获取数据
     * 从projectId查询与projectKey的映射关系，真正调用sonarApi需要用projectKey来执行
     * 梳理下代码检查核心报告页都需要哪些接口，这些接口入参是什么，具体怎么转发
     * /api/issues/search?componentKeys=*** Search for issues
     * /api/components/app?component=*** Coverage data required for rendering the component viewer.
     * /api/sources/lines?key=kuaishou:ks-serveree-cr:*** Show source code with line oriented info
     * .需要解析key参数，前缀是projectKey
     */

    public ProxyLoginInfoVo getSonarLoginIdByProjectId(Long projectId) {
        SonarClusterOperations clusterOperations = sonarClusterSimpleFactory.getClusterOperations(projectId);
        SonarCommonApi sonarCommonApi = clusterOperations.sonarApi();
        String basicAuth = sonarCommonApi.basicAuth();
        String sonarUrl = sonarCommonApi.sonarUrl();
        String loginId = clusterOperations.loginId();
        return new ProxyLoginInfoVo(loginId, sonarUrl, basicAuth, sonarUrl);
    }

    /**
     * 代理转发sonar的接口，返回结果是sonarApi的json串
     */
    public String proxyRedirectHandler(HttpServletRequest request) {
        String method = request.getMethod();
        if (RequestMethod.GET.name().equals(method)) {
            return getStrategy.doRedirect(request);
        } else if (RequestMethod.POST.name().equals(method)) {
            return postStrategy.doRedirect(request);
        } else {
            return "redirect method is not supported";
        }
    }
}
