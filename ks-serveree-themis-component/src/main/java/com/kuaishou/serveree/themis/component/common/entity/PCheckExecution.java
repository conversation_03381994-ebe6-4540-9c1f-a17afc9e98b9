package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 检查执行表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PCheckExecution对象", description = "检查执行表")
public class PCheckExecution implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "主表id")
    private Long pBaseId;

    @ApiModelProperty(value = "1.sonar")
    private Integer referType;

    @ApiModelProperty(value = "task表主键id")
    private Long taskId;

    @ApiModelProperty(value = "同步状态")
    private Boolean sync;

    /**
     * 是否是增量模式
     */
    private Boolean incrementMode;

    private Boolean moreProcess;

    private Boolean realCompile;

    private Integer ideaTriggerType;

    /**
     * {@link com.kuaishou.serveree.themis.component.constant.plugin.IncrementType}
     */
    private Integer incrementType;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;


}
