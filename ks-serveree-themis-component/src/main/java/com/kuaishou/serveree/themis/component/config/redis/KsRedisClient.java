package com.kuaishou.serveree.themis.component.config.redis;

import javax.annotation.Nonnull;

import com.kuaishou.framework.jedis.JedisClusterConfig;

import kuaishou.common.BizDef;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2020-05-26
 */
public enum KsRedisClient implements JedisClusterConfig {

    // 生产
    servereeThemisPro,
    // beta环境
    servereeThemisBeta,
    // 测试
    servereeThemisTest,

    // PRT环境
    servereeThemisPRT;

    @Override
    public String bizName() {
        return name();
    }

    @Nonnull
    @Override
    public BizDef bizDef() {
        return BizDef.SERVER_EE;
    }
}
