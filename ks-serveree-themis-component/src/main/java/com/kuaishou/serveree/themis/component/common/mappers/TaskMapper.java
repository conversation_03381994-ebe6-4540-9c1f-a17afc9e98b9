package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.entity.platform.CheckRecord;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-29
 */
@Mapper
@Repository
public interface TaskMapper extends BaseMapper<Task> {

    List<CheckRecord> listCheckRecord(@Param("gitProjectId") int gitProjectId, @Param("startTime")long startTime,
            @Param("limit") int limit);

    Task getFirstTimeOfLatestNSuccessRecords(
            @Param("gitProjectId") Integer gitProjectId,
            @Param("startTime") long startTime,
            @Param("limit") int limit);
}
