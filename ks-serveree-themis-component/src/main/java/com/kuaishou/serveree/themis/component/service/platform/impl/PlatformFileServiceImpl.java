package com.kuaishou.serveree.themis.component.service.platform.impl;

import static com.kuaishou.serveree.themis.component.client.git.GitOperations.GIT_FILE_CONTENT_CACHE_DAY;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.INVALID_PARAMS;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.PARAMS_CAN_NOT_EMPTY;
import static com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant.LOCAL_BUILD_ID_FILE_CONTENT;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jgit.api.Git;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import com.github.phantomthief.util.MoreFunctions;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.git.SelfGitApi;
import com.kuaishou.serveree.themis.component.client.kdev.KdevApi;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformFileService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;
import com.kuaishou.serveree.themis.component.utils.JGitUtils;
import com.kuaishou.serveree.themis.component.vo.request.FileDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.FileSegmentsRequest;
import com.kuaishou.serveree.themis.component.vo.response.FileDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.FileSegmentsResponse;
import com.kuaishou.serveree.themis.component.vo.response.FileSegmentsResponse.LineInfo;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.RandomUtil;

/**
 * <AUTHOR>
 * @since 2022/7/19 3:51 PM
 */
@Service
public class PlatformFileServiceImpl implements PlatformFileService {

    @Autowired
    private GitOperations gitOperations;
    @Autowired
    private PlatformPermissionService platformPermissionService;
    @Autowired
    private PCheckBaseService pCheckBaseService;
    @Autowired
    private KsRedisClient ksRedisClient;
    @Autowired
    private KdevApi kdevApi;
    @Autowired
    private JGitUtils jGitUtils;
    @Autowired
    private SelfGitApi selfGitApi;
    @Autowired
    private CheckRepoService checkRepoService;

    private static final String CLONE_DIR = "/local-build-dir/%s/%s";

    @Override
    public FileDetailResponse fileDetail(FileDetailRequest request, String userName) {
        Integer gitProjectId = request.getGitProjectId();
        String commitId = request.getCommitId();
        String filePath = request.getFilePath();
        Assert.notNull(gitProjectId, () -> new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                PARAMS_CAN_NOT_EMPTY.getMessage() + "gitProjectId"));
        Assert.notEmpty(commitId, () -> new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                PARAMS_CAN_NOT_EMPTY.getMessage() + "commitId"));
        Assert.notNull(filePath, () -> new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                PARAMS_CAN_NOT_EMPTY.getMessage() + "filePath"));
        // 查询仓库
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        Assert.notNull(checkRepo, () -> new ThemisException(INVALID_PARAMS.getCode(),
                INVALID_PARAMS.getMessage() + "gitProjectId"));
        platformPermissionService.checkKdevGitPermission(checkRepo, userName);
        Long kspBuildId = request.getKspBuildId();
        if (checkRepo.getUseKbuild()) {
            gitProjectId = gitOperations.getProjectByUrl(checkRepo.getRepoUrl()).getId();
        }
        String cachedHighlightContent = null;
        if (kspBuildId != null) {
            cachedHighlightContent = getLocalBuildFile(gitProjectId, commitId, filePath, kspBuildId);
        }
        if (StringUtils.isEmpty(cachedHighlightContent)) {
            cachedHighlightContent = getCommonFile(gitProjectId, commitId, filePath);
        }
        return FileDetailResponse.builder()
                .highlightContent(cachedHighlightContent)
                .build();
    }

    @Override
    public FileSegmentsResponse fileSegments(FileSegmentsRequest request) {
        // 范围无效
        int startLine = request.getStartLine(), endLine = request.getEndLine();
        if (startLine > endLine) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "范围无效:[" + startLine + "," + endLine + "]");
        }
        // 加上范围限制
        final int maxLines = 50;
        if (endLine - startLine + 1 > maxLines) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "片段大小不允许超过50");
        }
        // 获取原文件内容
        String content = gitOperations.getCachedRawContent(request.getGitProjectId(), request.getCommitId(), request.getFilePath());
        if (StringUtils.isBlank(content)) {
            return FileSegmentsResponse.builder().totalLine(0).lines(Collections.emptyList()).build();
        }
        String[] rawArray = content.split("\\n");
        if (startLine > rawArray.length) {
            return FileSegmentsResponse.builder().totalLine(rawArray.length).lines(Collections.emptyList()).build();
        }
        // 截取
        StringBuilder builder = new StringBuilder();
        for (int i = startLine - 1; i < Math.min(endLine, rawArray.length); i++) {
            builder.append(rawArray[i]).append("\n");
        }
        // 高亮
        String highlightContent = selfGitApi.getHighlightContent(request.getFilePath(), builder.toString());
        if (StringUtils.isBlank(highlightContent)) {
            return FileSegmentsResponse.builder().totalLine(rawArray.length).lines(Collections.emptyList()).build();
        }
        String[] highlightArray = highlightContent.split("\\n");
        List<LineInfo> lines = Lists.newArrayList();
        for (int i = 0; i < highlightArray.length; i++) {
            lines.add(LineInfo.builder().lineNumber(startLine + i).highlightContent(highlightArray[i]).build());
        }
        return FileSegmentsResponse.builder().totalLine(rawArray.length).lines(lines).build();
    }

    private String getLocalBuildFile(Integer gitProjectId, String commitId, String filePath, Long kspBuildId) {
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(kspBuildId);
        if (pCheckBase == null) {
            return null;
        }
        Long localBuildId = pCheckBase.getLocalBuildId();
        if (localBuildId == null || localBuildId == 0L) {
            return null;
        }
        // 先从缓存里取
        String cacheContent = ksRedisClient.sync()
                .get(LOCAL_BUILD_ID_FILE_CONTENT + gitProjectId + ":" + localBuildId + ":" + filePath);
        if (StringUtils.isNotEmpty(cacheContent)) {
            return cacheContent;
        }
        // 如果不在diffFiles里,按照通用逻辑构建
        List<String> diffFiles = kdevApi.getDiffFilesPathByLocalBuildId(localBuildId);
        if (!diffFiles.contains(filePath)) {
            return getCommonFile(gitProjectId, commitId, filePath);
        }
        String repoPath = getCloneRepoPath(gitProjectId);
        // 取不到进行本地merge
        MoreFunctions.runThrowing(
                () -> applyCode(gitProjectId, commitId, pCheckBase.getBranch(), localBuildId, repoPath)
        );
        // apply以后,把所有的变更文件都缓存下来 下次就直接取缓存即可
        ksRedisClient.get().pipeline(diffFiles,
                (p, it) -> p.setex(LOCAL_BUILD_ID_FILE_CONTENT + gitProjectId + ":" + localBuildId + ":" + it,
                        (int) TimeUnit.DAYS.toSeconds(GIT_FILE_CONTENT_CACHE_DAY),
                        getFileContent(repoPath, it))
        );
        String fileContent = getFileContent(repoPath, filePath);
        // 删除本地的merge项目
        MoreFunctions.catching(() -> FileUtil.del(new File(repoPath)));
        return fileContent;
    }

    private String getFileContent(String repoPath, String filePath) {
        String originContent = FileUtil.readUtf8String(repoPath + "/" + filePath);
        return selfGitApi.getHighlightContent(filePath, originContent);
    }

    private String getCloneRepoPath(Integer gitProjectId) {
        return String.format(CLONE_DIR, RandomUtil.getRandom().nextLong(1000), gitProjectId);
    }

    private void applyCode(Integer gitProjectId, String commitId, String branch, Long localBuildId, String repoPath)
            throws IOException {
        String sshUrl = gitOperations.getCacheProject(gitProjectId).getSshUrl();
        jGitUtils.cloneProject(sshUrl, new File(repoPath), branch, commitId, "");
        Git gitRepo = Git.open(new File(repoPath));
        InputStream inputStream = kdevApi.getLocalBuildDiffInputStream(localBuildId);
        jGitUtils.applyPatch(gitRepo, inputStream);
    }

    private String getCommonFile(Integer gitProjectId, String commitId, String filePath) {
        return gitOperations.getCachedHighlightContent(gitProjectId, commitId, filePath);
    }

}
