package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-13
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ThemisAnalyzePlanIssue对象", description = "")
public class ThemisAnalyzePlanIssue implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "分析计划id")
    private Long analyzePlanExecuteId;

    @ApiModelProperty(value = "规则id")
    private String ruleId;

    @ApiModelProperty(value = "违规文件")
    private String illegalFile;

    @ApiModelProperty(value = "违反规则的依赖")
    private String illegalDependency;

    @ApiModelProperty(value = "违反的错误日志")
    private String illegalErrorMsg;

    @ApiModelProperty(value = "违反的内容")
    private String illegalContent;

    @ApiModelProperty(value = "违反的行号")
    private Integer illegalLineNo;

    @ApiModelProperty(value = "违反的列号")
    private Integer illegalColumnNo;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createdTime;

}
