package com.kuaishou.serveree.themis.component.constant.process;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/10/20 8:31 下午
 */
@AllArgsConstructor
public enum ProcessSponsorType {

    PIPELINE(1, "流水线"),
    MR(2, "merge request"),
    IDEA(3, "idea插件"),
    MR_STUCK(4, "mr卡点触发扫描")
    ;

    @Getter
    private int type;

    @Getter
    private String desc;

}
