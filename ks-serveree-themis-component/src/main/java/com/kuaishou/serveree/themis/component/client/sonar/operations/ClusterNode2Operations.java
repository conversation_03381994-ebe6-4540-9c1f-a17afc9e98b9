package com.kuaishou.serveree.themis.component.client.sonar.operations;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.client.sonar.api.ClusterNode2Api;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-16
 */
@Slf4j
@Component
public class ClusterNode2Operations implements SonarClusterOperations {

    @Value("${sonar.cluster.node2.login-id}")
    private String loginId;

    @Autowired
    private ClusterNode2Api clusterNode2Api;

    @Override
    public SonarCommonApi sonarApi() {
        return clusterNode2Api;
    }

    @Override
    public Integer nodeNumber() {
        return 2;
    }

    @Override
    public String loginId() {
        return loginId;
    }
}
