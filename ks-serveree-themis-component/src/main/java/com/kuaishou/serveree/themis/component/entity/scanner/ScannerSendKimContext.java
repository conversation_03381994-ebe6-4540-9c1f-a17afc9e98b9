package com.kuaishou.serveree.themis.component.entity.scanner;

import java.util.List;

import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/11/8 3:19 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ScannerSendKimContext {

    private String scanner;

    private List<PCheckIssue> pCheckIssueList;

    private PCheckBase pCheckBase;

    private PCheckExecution pCheckExecution;

}
