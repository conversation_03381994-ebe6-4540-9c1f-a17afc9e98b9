package com.kuaishou.serveree.themis.component.constant.plugin;

import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-06-11
 *
 * 增量扫描增量类型
 */
@Getter
public enum IncrementType {

    FILE(1, "文件维度"),
    LINE(2, "diff行维度");

    private final int type;
    private final String desc;

    IncrementType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
