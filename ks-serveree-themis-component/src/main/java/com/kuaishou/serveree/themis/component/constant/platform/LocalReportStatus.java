package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/7/20 2:59 PM
 */
@AllArgsConstructor
public enum LocalReportStatus {

    NOT_EXIST(0, "不存在"),
    EXIST(1, "已存在"),
    DELETED(2, "已删除"),
    ;

    @Getter
    private final Integer status;
    @Getter
    private final String desc;

    private static final Map<Integer, LocalReportStatus> STATUS_MAP = Maps.newHashMap();

    static {
        for (LocalReportStatus reportStatus : LocalReportStatus.values()) {
            STATUS_MAP.put(reportStatus.getStatus(), reportStatus);
        }
    }

    public static LocalReportStatus getByStatus(Integer status) {
        return STATUS_MAP.get(status);
    }

}
