package com.kuaishou.serveree.themis.component.client.sonar.operations;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.proxy.SonarConfigEnum;

/**
 * <AUTHOR>
 * @since 2023/5/30 2:07 PM
 */
@Component
public class UnderJdk17SonarCluster {

    @Autowired
    private ClusterNode1Operations node1Operations;
    @Autowired
    private ClusterNode2Operations node2Operations;
    @Autowired
    private ClusterNode3Operations node3Operations;
    @Autowired
    private ClusterNode4Operations node4Operations;
    private Map<String, SonarClusterOperations> operationsMap;

    @PostConstruct
    public void initMap() {
        operationsMap = new HashMap<>();
        operationsMap.put(SonarConfigEnum.NODE1.getIp() + ":" + SonarConfigEnum.NODE1.getPort(), node1Operations);
        operationsMap.put(SonarConfigEnum.NODE2.getIp() + ":" + SonarConfigEnum.NODE2.getPort(), node2Operations);
        operationsMap.put(SonarConfigEnum.NODE3.getIp() + ":" + SonarConfigEnum.NODE3.getPort(), node3Operations);
        operationsMap.put(SonarConfigEnum.NODE4.getIp() + ":" + SonarConfigEnum.NODE4.getPort(), node4Operations);
    }

    public SonarClusterOperations underJdk17NodeRoute(Integer projectId) {
        SonarConfigEnum configEnum = SonarConfigEnum.modByProjectId(Long.valueOf(projectId));
        //对于无法根据projectId路由的场景，默认第一个实例，后续需要扩展随机策略和最近使用次数策略
        if (configEnum == null) {
            return node1Operations;
        }
        return operationsMap.getOrDefault(configEnum.getIp() + ":" + configEnum.getPort(), node1Operations);
    }

}
