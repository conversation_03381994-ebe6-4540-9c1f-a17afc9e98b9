package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/8/13 5:23 下午
 */
@AllArgsConstructor
public enum CheckRepoType {

    SERVER_JAVA_MAVEN_JDK8(1, "后端java项目 maven管理 jdk8", Lists.newArrayList()),
    FRONT(2, "前端项目", Lists.newArrayList("business-front")),
    C_PLUS_PLUS(3, "c++项目", Lists.newArrayList("sonar-cplus")),
    UN_GROUPED(0, "未分组项目", Lists.newArrayList());

    @Getter
    private Integer code;

    @Getter
    private String desc;

    @Getter
    private List<String> tokenSources;


    private static final Map<String, Integer> SOURCE_CODE_MAP = new HashMap<>();

    static {
        Arrays.stream(CheckRepoType.values()).forEach(checkRepoType -> {
            List<String> tokenSources = checkRepoType.getTokenSources();
            if (CollectionUtils.isNotEmpty(tokenSources)) {
                tokenSources.forEach(tokenSource -> SOURCE_CODE_MAP.put(tokenSource, checkRepoType.getCode()));
            }
        });
    }

    public static Integer getCodeBySource(String tokenSource) {
        if (StringUtils.isEmpty(tokenSource)) {
            return UN_GROUPED.getCode();
        }
        Integer code = SOURCE_CODE_MAP.get(tokenSource);
        if (code == null) {
            return UN_GROUPED.getCode();
        }
        return code;
    }

}
