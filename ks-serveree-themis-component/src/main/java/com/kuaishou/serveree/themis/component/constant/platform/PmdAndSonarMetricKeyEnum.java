package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;
import java.util.Objects;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;

import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-01-10
 *
 * pmd 指标key 和 sonar 指标 key 映射关系
 */
@Getter
public enum PmdAndSonarMetricKeyEnum {
    CYCLOMATIC_COMPLEXITY("complexity", "CyclomaticComplexity", "圈复杂度"),
    COGNITIVE_COMPLEXITY("cognitive_complexity", "CognitiveComplexity", "认知复杂度");

    private static final Map<String, String> PMD_2_SONAR_KEY_MAP = Maps.newHashMap();
    private static final Map<String, String> SONAR_2_PMD_KEY_MAP = Maps.newHashMap();

    static {
        for (PmdAndSonarMetricKeyEnum metricKeyEnum : values()) {
            PMD_2_SONAR_KEY_MAP.put(metricKeyEnum.getPmdKey(), metricKeyEnum.getSonarKey());
            SONAR_2_PMD_KEY_MAP.put(metricKeyEnum.getSonarKey(), metricKeyEnum.getPmdKey());
        }
    }

    private final String sonarKey;
    private final String pmdKey;
    private final String desc;

    PmdAndSonarMetricKeyEnum(String sonarKey, String pmdKey, String desc) {
        this.sonarKey = sonarKey;
        this.pmdKey = pmdKey;
        this.desc = desc;
    }

    /**
     * 根据sonar key 获取pmd对应的指标key
     */
    public static String getPmdKeyBySonarKey(String sonarKey) {
        String pmdKey = SONAR_2_PMD_KEY_MAP.get(sonarKey);
        if (Objects.isNull(pmdKey)) {
            throw new ThemisException(ResultCodeConstant.INVALID_PARAMS, "未知指标：" + sonarKey);
        }
        return pmdKey;
    }

    /**
     * 根据pmd key 获取sonar对应的指标key
     */
    public static String getSonarKeyByPmdKey(String pmdKey) {
        String sonarKey = PMD_2_SONAR_KEY_MAP.get(pmdKey);
        if (Objects.isNull(sonarKey)) {
            return pmdKey;
        }
        return sonarKey;
    }
}
