package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckRepoNotice对象", description = "")
public class CheckRepoNotice implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "check_repo表id")
    private Long checkRepoId;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "创建人")
    private String creator;

    @ApiModelProperty(value = "修改人")
    private String updater;

    @ApiModelProperty(value = "接收人，逗号分隔")
    private String receivers;

    @ApiModelProperty(value = "kim群机器人地址")
    private String kimRobot;

    @ApiModelProperty(value = "是否通知问题作者具体问题")
    private Boolean noticeAuthor;

    @ApiModelProperty(value = "通知类型1离线，2流水线，3问题标注")
    private Integer noticeType;

    @ApiModelProperty(value = "增量区间，最近多少天")
    private Integer incrementInterval;


}
