package com.kuaishou.serveree.themis.component.config.mybatis;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;


@MappedTypes({LocalDateTime.class})
@MappedJdbcTypes({JdbcType.BIGINT})
public class KsLocalDateTimeTypeHandler implements TypeHandler<LocalDateTime> {

    @Override
    public void setParameter(PreparedStatement preparedStatement, int i, LocalDateTime dateTime, JdbcType jdbcType)
            throws SQLException {
        if (dateTime == null) {
            preparedStatement.setLong(i, System.currentTimeMillis());
        } else {
            preparedStatement.setLong(i, dateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
        }
    }

    @Override
    public LocalDateTime getResult(ResultSet resultSet, String s) throws SQLException {
        if (s == null || s.length() == 0) {
            return null;
        }
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(resultSet.getLong(s)), ZoneOffset.of("+8"));
    }

    @Override
    public LocalDateTime getResult(ResultSet resultSet, int i) throws SQLException {
        if (i < 0) {
            return null;
        }
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(resultSet.getLong(i)), ZoneOffset.of("+8"));
    }

    @Override
    public LocalDateTime getResult(CallableStatement callableStatement, int i) throws SQLException {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(callableStatement.getLong(i)), ZoneOffset.of("+8"));
    }
}
