package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/7/6 6:08 PM
 */
@AllArgsConstructor
public enum MetricKeyEnum {

    FILE_ISSUE_COUNT("file_issue_count", "文件问题数"),
    RULE_KEY("rule_key", "规则"),
    FOLDER_MAINTAINABILITY("folder_maintainability", "目录可维护性"),
    FOLDER_ISSUE_DISTRIBUTION("folder_issue_distribution", "目录问题分布"),
    COMMON("common", "通用"),

    ;

    @Getter
    private final String key;
    @Getter
    private final String desc;

    private static final Map<String, MetricKeyEnum> METRIC_KEY_MAP = Maps.newHashMap();

    static {
        for (MetricKeyEnum metricKeyEnum : MetricKeyEnum.values()) {
            METRIC_KEY_MAP.put(metricKeyEnum.getKey(), metricKeyEnum);
        }
    }

    public static MetricKeyEnum getByKey(String metricKey) {
        MetricKeyEnum metricKeyEnum = METRIC_KEY_MAP.get(metricKey);
        if (metricKeyEnum == null) {
            metricKeyEnum = COMMON;
        }
        return metricKeyEnum;
    }


}
