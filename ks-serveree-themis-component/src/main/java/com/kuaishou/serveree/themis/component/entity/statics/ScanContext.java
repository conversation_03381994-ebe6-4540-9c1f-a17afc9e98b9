package com.kuaishou.serveree.themis.component.entity.statics;

import java.util.List;

import com.kuaishou.serveree.themis.component.common.entity.ScanCase;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/10/30 5:05 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScanContext {

    private ScanProject scanProject;

    private ScanFilterConfig scanFilterConfig;

    private List<ScanCase> scanCaseList;

}
