package com.kuaishou.serveree.themis.component.config;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-12
 */
@EnableAsync
@Configuration
public class AsyncConfig {
    private static final String THREAD_NAME_PREFIX = "Async-Executor-";
    public static final int QUEUE_CAPACITY = 100;


    @Bean("asyncTaskExecutor")
    public Executor executor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        int coreSize = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(coreSize + 2);
        executor.setMaxPoolSize(coreSize * 3);
        executor.setQueueCapacity(QUEUE_CAPACITY);
        executor.setThreadNamePrefix(THREAD_NAME_PREFIX);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
