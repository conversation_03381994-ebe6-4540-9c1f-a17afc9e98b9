package com.kuaishou.serveree.themis.component.service.process.impl;

import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckConfig;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.PCheckMeasures;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.process.ProcessCheckType;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.process.ProcessKCheckLevel;
import com.kuaishou.serveree.themis.component.constant.process.ProcessMetricKey;
import com.kuaishou.serveree.themis.component.constant.quality.CheckResultEnum;
import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PCheckConfigService;
import com.kuaishou.serveree.themis.component.service.PCheckExecutionService;
import com.kuaishou.serveree.themis.component.service.PCheckIssueService;
import com.kuaishou.serveree.themis.component.service.PCheckMeasuresService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.service.process.ProcessCheckIssueHandler;
import com.kuaishou.serveree.themis.component.service.process.ProcessCheckService;
import com.kuaishou.serveree.themis.component.service.process.ProcessExecutionHandler;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.ProcessDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProcessIssueListRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProcessSponsorRequest;
import com.kuaishou.serveree.themis.component.vo.response.ProcessDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.ProcessDetailResponse.BuildProcessResult;
import com.kuaishou.serveree.themis.component.vo.response.ProcessDetailResponse.CustomProcessResult;
import com.kuaishou.serveree.themis.component.vo.response.ProcessDetailResponse.KsCheckLevelResult;
import com.kuaishou.serveree.themis.component.vo.response.ProcessDetailResponse.KsCheckProcessResult;
import com.kuaishou.serveree.themis.component.vo.response.ProcessDetailResponse.SponsorBuildInfo;
import com.kuaishou.serveree.themis.component.vo.response.ProcessIssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.ProcessSponsorResponse;

/**
 * <AUTHOR>
 * @since 2021/10/20 8:18 下午
 */
@Service
public class ProcessCheckServiceImpl implements ProcessCheckService {

    @Autowired
    private ProcessExecutionHandler executionHandler;

    @Autowired
    private PCheckConfigService pCheckConfigService;

    @Autowired
    private PCheckBaseService pCheckBaseService;

    @Autowired
    private PCheckExecutionService pCheckExecutionService;

    @Autowired
    private PCheckIssueService pCheckIssueService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskConfigService taskConfigService;

    @Autowired
    private PCheckMeasuresService pCheckMeasuresService;

    @Autowired
    private KsRedisClient ksRedisClient;

    @Autowired
    private List<ProcessCheckIssueHandler> processCheckIssueHandlerList;

    @Override
    @Transactional
    public ProcessSponsorResponse sponsor(ProcessSponsorRequest sponsorRequest) {
        return executionHandler.execution(sponsorRequest);
    }

    @Override
    public ProcessDetailResponse resultDetail(ProcessDetailRequest detailRequest) {
        this.checkParam(detailRequest);
        String redisKey;
        if (detailRequest.getBuildId() != null && detailRequest.getBuildId() != 0) {
            redisKey = KsRedisPrefixConstant.PROCESS_DETAIL_RESULT_BUILD_PREFIX + detailRequest.getBuildId();
        } else {
            redisKey = KsRedisPrefixConstant.PROCESS_DETAIL_RESULT_MR_PREFIX + detailRequest.getProjectId()
                    + ":" + detailRequest.getMrId()
                    + ":" + detailRequest.getCommitId();
        }
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return JSONUtils.deserialize(redisVal, ProcessDetailResponse.class);
        }
        PCheckBase pCheckBase = this.getPCheckBase(detailRequest);
        if (pCheckBase == null) {
            throw new ThemisException(ResultCodeConstant.NOT_FOUND_BASE_DATA);
        }
        PCheckConfig pCheckConfig = pCheckConfigService.getByPBaseId(pCheckBase.getId());
        ProcessDetailResponse detailResponse = new ProcessDetailResponse();
        detailResponse.setRepoUrl(pCheckBase.getRepoUrl());
        detailResponse.setBranch(pCheckBase.getBranch());
        detailResponse.setSponsor(pCheckBase.getSponsor());
        detailResponse.setSponsorTime(DateTimeFormatter.ofPattern("yyyy-MM-dd").format(pCheckBase.getGmtCreate()));
        String mrDetailLink = pCheckConfig.getMrDetailLink();
        if (StringUtils.isNotEmpty(mrDetailLink)) {
            detailResponse.setMrDetailLink(mrDetailLink);
        }
        // start pipeline plugin
        Integer kCheckLevel = pCheckConfig.getKCheckLevel();
        Integer baseCheckType = pCheckConfig.getBaseCheckType();
        if (baseCheckType == 2) {
            // 如果build发起的全面检查 那么就是k-check-level1
            kCheckLevel = 1;
        }
        // start package k-check
        List<PCheckExecution> pCheckExecutionList = pCheckExecutionService.listByPBaseId(pCheckBase.getId());
        List<PCheckMeasures> pCheckMeasuresList = pCheckMeasuresService.listByPBaseId(pCheckBase.getId());
        if (kCheckLevel != 0) {
            this.packageKCheckLevelData(detailResponse, kCheckLevel, pCheckExecutionList, pCheckMeasuresList);
        } else {
            this.packageNoScanKCheckData(detailResponse);
        }
        // start package custom check
        Boolean customCheck = pCheckConfig.getCustomCheck();
        if (customCheck) {
            this.packageCustomCheckData(detailResponse, pCheckMeasuresList);
        } else {
            this.packageNoScanCustomCheckData(detailResponse);
        }
        if (baseCheckType != 0) {
            this.packageBuildCheckData(detailResponse, baseCheckType, pCheckExecutionList, pCheckMeasuresList);
        }
        List<PCheckExecution> syncPCheckExecutions =
                pCheckExecutionList.stream().filter(PCheckExecution::getSync).collect(Collectors.toList());
        if (syncPCheckExecutions.size() == pCheckExecutionList.size()) {
            ksRedisClient.sync().setex(redisKey, TimeUnit.DAYS.toSeconds(1), JSONUtils.serialize(detailResponse));
        }
        return detailResponse;
    }

    private void packageBuildCheckData(ProcessDetailResponse detailResponse, Integer baseCheckType,
            List<PCheckExecution> pCheckExecutionList, List<PCheckMeasures> pCheckMeasuresList) {
        if (baseCheckType == 1) {
            List<PCheckExecution> checkstyleExecution = pCheckExecutionList.stream()
                    .filter(o -> ProcessExecutionReferType.CHECKSTYLE.getType() == o.getReferType())
                    .collect(Collectors.toList());
            PCheckExecution pCheckExecution = checkstyleExecution.get(0);
            Task task = taskService.getById(pCheckExecution.getTaskId());
            TaskConfig taskConfig = taskConfigService.getTaskConfigByTaskId(pCheckExecution.getTaskId());
            // 封装执行参数
            List<SponsorBuildInfo> sponsorBuildInfos = Lists.newArrayList();
            SponsorBuildInfo sponsorBuildInfo = new SponsorBuildInfo();
            sponsorBuildInfo.setExecutionReferType(pCheckExecution.getReferType());
            sponsorBuildInfo.setPipelineResult(task.getTaskStatus());
            sponsorBuildInfo.setBuildId(taskConfig.getSponsorKspBuildId());
            sponsorBuildInfo.setPipelineId(taskConfig.getSponsorKspPipelineId());
            sponsorBuildInfos.add(sponsorBuildInfo);
            BuildProcessResult buildProcessResult = new BuildProcessResult();
            buildProcessResult.setSponsorBuildInfoList(sponsorBuildInfos);
            List<PCheckMeasures> checkstyleMeasures = pCheckMeasuresList.stream()
                    .filter(o -> ProcessMetricKey.CHECKSTYLE_VIOLATIONS.equals(o.getMetricKey()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(checkstyleMeasures)) {
                buildProcessResult.setCheckResult(CheckResultEnum.SCANNING.name());
            } else {
                PCheckMeasures pCheckMeasures = checkstyleMeasures.get(0);
                boolean noCheckstyleViolations = Integer.parseInt(pCheckMeasures.getMetricValue()) == 0;
                buildProcessResult.setCheckResult(
                        noCheckstyleViolations ? CheckResultEnum.SUCCESS.name() : CheckResultEnum.FAIL.name());
            }
            detailResponse.setBuildProcessResult(buildProcessResult);
        } else {
            KsCheckProcessResult ksCheckProcessResult = detailResponse.getKsCheckProcessResult();
            BuildProcessResult buildProcessResult = new BuildProcessResult();
            buildProcessResult.setSponsorBuildInfoList(ksCheckProcessResult.getSponsorBuildInfoList());
            List<String> checkStatus = Lists.newArrayList();
            String kCheckLevel1Result = ksCheckProcessResult.getKsCheckLevelResult().getLevel1Result();
            checkStatus.add(kCheckLevel1Result);
            List<PCheckMeasures> checkstyleMeasures = pCheckMeasuresList.stream()
                    .filter(o -> ProcessMetricKey.CHECKSTYLE_VIOLATIONS.equals(o.getMetricKey()))
                    .collect(Collectors.toList());
            String checkstyleCheckStatus;
            if (CollectionUtils.isEmpty(checkstyleMeasures)) {
                checkstyleCheckStatus = CheckResultEnum.SCANNING.name();
            } else {
                PCheckMeasures pCheckMeasures = checkstyleMeasures.get(0);
                boolean noCheckstyleViolations = Integer.parseInt(pCheckMeasures.getMetricValue()) == 0;
                checkstyleCheckStatus =
                        noCheckstyleViolations ? CheckResultEnum.SUCCESS.name() : CheckResultEnum.FAIL.name();
            }
            checkStatus.add(checkstyleCheckStatus);
            if (checkStatus.contains(CheckResultEnum.SCANNING.name())) {
                buildProcessResult.setCheckResult(CheckResultEnum.SCANNING.name());
            } else if (checkStatus.contains(CheckResultEnum.FAIL.name())) {
                buildProcessResult.setCheckResult(CheckResultEnum.FAIL.name());
            } else {
                buildProcessResult.setCheckResult(CheckResultEnum.SUCCESS.name());
            }
            detailResponse.setBuildProcessResult(buildProcessResult);
        }
    }

    private void packageNoScanCustomCheckData(ProcessDetailResponse detailResponse) {
        detailResponse.setCustomProcessResult(new CustomProcessResult(CheckResultEnum.NO_SCAN.name()));
    }

    private void packageNoScanKCheckData(ProcessDetailResponse detailResponse) {
        KsCheckProcessResult ksCheckProcessResult = new KsCheckProcessResult();
        ksCheckProcessResult.setSponsorBuildInfoList(Collections.emptyList());

        KsCheckLevelResult ksCheckLevelResult = new KsCheckLevelResult();
        ksCheckLevelResult.setLevel1Result(CheckResultEnum.NO_SCAN.name());
        ksCheckLevelResult.setLevel2Result(CheckResultEnum.NO_SCAN.name());
        ksCheckLevelResult.setLevel3Result(CheckResultEnum.NO_SCAN.name());
        ksCheckProcessResult.setKsCheckLevelResult(ksCheckLevelResult);

        detailResponse.setKsCheckProcessResult(ksCheckProcessResult);
    }

    private void packageCustomCheckData(ProcessDetailResponse detailResponse, List<PCheckMeasures> pCheckMeasuresList) {
        List<PCheckMeasures> customCheckMeasures = pCheckMeasuresList.stream()
                .filter(pCheckMeasures -> ProcessMetricKey.CUSTOM_CHECK.equals(pCheckMeasures.getMetricKey()))
                .collect(Collectors.toList());
        PCheckMeasures pCheckMeasures = customCheckMeasures.get(0);
        String metricValue = pCheckMeasures.getMetricValue();
        CustomProcessResult customProcessResult = new CustomProcessResult();
        customProcessResult.setResult(metricValue);
        detailResponse.setCustomProcessResult(customProcessResult);
    }

    private void packageKCheckLevelData(ProcessDetailResponse detailResponse, Integer kCheckLevel,
            List<PCheckExecution> pCheckExecutionList, List<PCheckMeasures> pCheckMeasuresList) {
        // 说明有kcheck的检查
        // 筛选出k-check的pCheckExecution
        List<Integer> types = ProcessExecutionReferType.getTypeListByCheckType(ProcessCheckType.K_CHECK.getKey());
        List<PCheckExecution> kCheckPExecutions = pCheckExecutionList.stream()
                .filter(execution -> types.contains(execution.getReferType()))
                .collect(Collectors.toList());

        List<Long> pExecutionIds = kCheckPExecutions.stream().map(PCheckExecution::getId).collect(Collectors.toList());
        List<PCheckMeasures> kCheckMeasuresList = pCheckMeasuresList.stream()
                .filter(pCheckMeasures -> pExecutionIds.contains(pCheckMeasures.getPExecutionId()))
                .collect(Collectors.toList());
        // cover measures信息
        KsCheckLevelResult ksCheckLevelResult = new KsCheckLevelResult();
        Map<Integer, List<String>> levelMeasureKeys = ProcessKCheckLevel.getLevelMeasureKeys();
        switch (kCheckLevel) {
            case 1:
                this.fillLevelResult(kCheckLevel, levelMeasureKeys, ksCheckLevelResult, kCheckMeasuresList);
                ksCheckLevelResult.setLevel2Result(CheckResultEnum.NO_SCAN.name());
                ksCheckLevelResult.setLevel3Result(CheckResultEnum.NO_SCAN.name());
                break;
            case 2:
                this.fillLevelResult(kCheckLevel, levelMeasureKeys, ksCheckLevelResult, kCheckMeasuresList);
                ksCheckLevelResult.setLevel3Result(CheckResultEnum.NO_SCAN.name());
                break;
            case 3:
                this.fillLevelResult(kCheckLevel, levelMeasureKeys, ksCheckLevelResult, kCheckMeasuresList);
                break;
            default:
                break;
        }

        // 根据流水线信息进行覆盖封装
        List<Long> taskIds = kCheckPExecutions.stream().map(PCheckExecution::getTaskId).collect(Collectors.toList());
        List<Task> tasks = taskService.listByIds(taskIds);
        List<TaskConfig> taskConfigs = taskConfigService.listByTaskIds(taskIds);

        Map<Long, Task> taskIdMap = tasks.stream().collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> existing));
        Map<Long, TaskConfig> configMap = taskConfigs.stream().collect(Collectors.toMap(TaskConfig::getTaskId, o -> o, (existing, replacement) -> existing));

        KsCheckProcessResult ksCheckProcessResult = new KsCheckProcessResult();
        // 封装 流水线执行信息
        List<SponsorBuildInfo> buildInfoList = Lists.newArrayList();
        for (PCheckExecution kCheckPExecution : kCheckPExecutions) {
            Task task = taskIdMap.get(kCheckPExecution.getTaskId());
            TaskConfig taskConfig = configMap.get(kCheckPExecution.getTaskId());
            SponsorBuildInfo sponsorBuildInfo = new SponsorBuildInfo();
            sponsorBuildInfo.setPipelineId(taskConfig.getSponsorKspPipelineId());
            sponsorBuildInfo.setBuildId(taskConfig.getSponsorKspBuildId());
            sponsorBuildInfo.setPipelineResult(task.getTaskStatus());
            sponsorBuildInfo.setExecutionReferType(kCheckPExecution.getReferType());
            if (TaskStatusEnum.FAIL.name().equals(task.getTaskStatus())) {
                // 如果流水线失败，那么就将所有的等级都置为失败
                ksCheckLevelResult.setLevel1Result(CheckResultEnum.FAIL.name());
                ksCheckLevelResult.setLevel2Result(CheckResultEnum.FAIL.name());
                ksCheckLevelResult.setLevel3Result(CheckResultEnum.FAIL.name());
            }
            buildInfoList.add(sponsorBuildInfo);
        }

        long noSyncCount = kCheckPExecutions.stream().filter(execution -> !execution.getSync()).count();
        if (noSyncCount > 0) {
            ksCheckLevelResult.setLevel1Result(CheckResultEnum.SCANNING.name());
            ksCheckLevelResult.setLevel1Result(CheckResultEnum.SCANNING.name());
            ksCheckLevelResult.setLevel1Result(CheckResultEnum.SCANNING.name());
        }

        ksCheckProcessResult.setSponsorBuildInfoList(buildInfoList);
        ksCheckProcessResult.setKsCheckLevelResult(ksCheckLevelResult);
        detailResponse.setKsCheckProcessResult(ksCheckProcessResult);
    }

    private void fillLevelResult(Integer kCheckLevel, Map<Integer, List<String>> levelMeasureKeys,
            KsCheckLevelResult ksCheckLevelResult, List<PCheckMeasures> pCheckMeasuresList) {
        for (int i = 1; i <= kCheckLevel; i++) {
            List<String> diffLevelKeys = levelMeasureKeys.get(i);
            List<PCheckMeasures> diffHitMeasures = pCheckMeasuresList.stream()
                    .filter(measure -> diffLevelKeys.contains(measure.getMetricKey()))
                    .collect(Collectors.toList());
            int diffInvalidCount = 0;
            for (PCheckMeasures checkMeasures : diffHitMeasures) {
                diffInvalidCount += Integer.parseInt(checkMeasures.getMetricValue());
            }
            if (diffInvalidCount > 0) {
                switch (i) {
                    case 1:
                        ksCheckLevelResult.setLevel1Result(CheckResultEnum.FAIL.name());
                        break;
                    case 2:
                        ksCheckLevelResult.setLevel2Result(CheckResultEnum.FAIL.name());
                        break;
                    case 3:
                        ksCheckLevelResult.setLevel3Result(CheckResultEnum.FAIL.name());
                        break;
                    default:
                        break;
                }
            } else {
                switch (i) {
                    case 1:
                        ksCheckLevelResult.setLevel1Result(CheckResultEnum.SUCCESS.name());
                        break;
                    case 2:
                        ksCheckLevelResult.setLevel2Result(CheckResultEnum.SUCCESS.name());
                        break;
                    case 3:
                        ksCheckLevelResult.setLevel3Result(CheckResultEnum.SUCCESS.name());
                        break;
                    default:
                        break;
                }
            }
        }
    }

    @Override
    public ProcessIssueListResponse issueList(ProcessIssueListRequest issueListRequest) {
        this.checkParam(issueListRequest);
        int page = issueListRequest.getPage();
        if (page < 1) {
            issueListRequest.setPage(1);
        }
        int pageSize = issueListRequest.getPageSize();
        if (pageSize < 1) {
            issueListRequest.setPageSize(100);
        }
        List<Integer> types = ProcessExecutionReferType.getTypeListByCheckType(issueListRequest.getSelectTab());
        Long buildId = issueListRequest.getBuildId();
        String commitId = issueListRequest.getCommitId();
        Integer mrId = issueListRequest.getMrId();
        Integer projectId = issueListRequest.getProjectId();
        PCheckBase pCheckBase;
        if (buildId != null) {
            pCheckBase = pCheckBaseService.getByBuildId(buildId);
        } else {
            pCheckBase = pCheckBaseService.getByMrInfo(projectId, mrId, commitId);
        }
        if (pCheckBase == null) {
            throw new ThemisException(ResultCodeConstant.NOT_FOUND_BASE_DATA);
        }
        Page<PCheckIssue> issuePage = pCheckIssueService.pagingListGroupByFilePath(pCheckBase, types, page, pageSize);
        if (CollectionUtils.isEmpty(issuePage.getRecords())) {
            return ProcessIssueListResponse.builder()
                    .page(page)
                    .pageSize(pageSize)
                    .total(issuePage.getTotal())
                    .processIssueList(Collections.emptyList())
                    .build();
        }
        ProcessCheckIssueHandler processCheckIssueHandler = processCheckIssueHandlerList.stream()
                .filter(handler -> handler.checkType().getKey().equals(issueListRequest.getSelectTab()))
                .findFirst()
                .orElseThrow(() -> new ThemisException(ResultCodeConstant.NOT_FOUND_HANDLER));
        return processCheckIssueHandler.listResponse(issuePage);
    }

    private void checkParam(ProcessIssueListRequest issueListRequest) {
        Long buildId = issueListRequest.getBuildId();
        String commitId = issueListRequest.getCommitId();
        Integer mrId = issueListRequest.getMrId();
        Integer projectId = issueListRequest.getProjectId();
        String selectTab = issueListRequest.getSelectTab();
        if (buildId == null && (StringUtils.isEmpty(commitId) || mrId == null || projectId == null)) {
            throw new IllegalArgumentException("请求参数非法");
        }
        if (StringUtils.isEmpty(selectTab)) {
            throw new IllegalArgumentException("selectTab 请求参数非法");
        }
    }

    private PCheckBase getPCheckBase(ProcessDetailRequest detailRequest) {
        Long buildId = detailRequest.getBuildId();
        Integer projectId = detailRequest.getProjectId();
        Integer mrId = detailRequest.getMrId();
        String commitId = detailRequest.getCommitId();
        PCheckBase pCheckBase;
        if (buildId != null) {
            pCheckBase = pCheckBaseService.getByBuildId(buildId);
        } else {
            pCheckBase = pCheckBaseService.getByMrInfo(projectId, mrId, commitId);
        }
        return pCheckBase;
    }

    private void checkParam(ProcessDetailRequest detailRequest) {
        Long buildId = detailRequest.getBuildId();
        String commitId = detailRequest.getCommitId();
        Integer mrId = detailRequest.getMrId();
        Integer projectId = detailRequest.getProjectId();
        if (buildId != null && buildId != 0) {
            return;
        }
        if (StringUtils.isNotEmpty(commitId) && mrId != null && projectId != null) {
            return;
        }
        throw new IllegalArgumentException("请求参数非法");
    }

}
