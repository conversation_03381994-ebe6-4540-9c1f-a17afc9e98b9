package com.kuaishou.serveree.themis.component.config.redis;

import java.util.Arrays;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 * @since 2020/6/1 2:02 下午
 */
@Configuration
public class RedisConfig {

    @Autowired
    private Environment environment;

    @Bean
    KsRedisClient ksRedisClient() {
        List<String> activeProfiles = Arrays.asList(environment.getActiveProfiles());
        if (activeProfiles.contains("prod") || activeProfiles.contains("pre")) {
            // 线上
            return KsRedisClient.servereeThemisPro;
        } else if (activeProfiles.contains("beta")) {
            // beta
            return KsRedisClient.servereeThemisBeta;
        } else if (activeProfiles.contains("prt")) {
            // prt
            return KsRedisClient.servereeThemisPRT;
        }
        //  测试
        return KsRedisClient.servereeThemisTest;
    }

}
