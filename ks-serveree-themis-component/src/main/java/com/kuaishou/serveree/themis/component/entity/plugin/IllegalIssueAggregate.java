package com.kuaishou.serveree.themis.component.entity.plugin;

import java.util.Collection;
import java.util.Map;

import com.google.common.collect.Maps;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-23
 */
public class IllegalIssueAggregate {

    private Map<String, Collection<String>> illegalInfoMap = Maps.newHashMap();

    public Map<String, Collection<String>> getIllegalInfoMap() {
        return illegalInfoMap;
    }

    public void setIllegalInfoMap(Map<String, Collection<String>> illegalInfoMap) {
        this.illegalInfoMap = illegalInfoMap;
    }

}
