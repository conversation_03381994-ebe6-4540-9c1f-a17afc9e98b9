package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.serveree.themis.component.common.entity.ComplexityRepository;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Mapper
@Repository
public interface ComplexityRepositoryMapper extends BaseMapper<ComplexityRepository> {

    @Select("select id from complexity_repository where exec_record_id = #{execRecordId} limit 10000")
    List<Long> selectIdsByExecRecordId(Long execRecordId);
}
