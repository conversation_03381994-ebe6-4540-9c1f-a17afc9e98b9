package com.kuaishou.serveree.themis.component.service.platform.scan.scanners;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.phantomthief.util.MoreFunctions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.client.ares.AresApi;
import com.kuaishou.serveree.themis.component.common.entity.CheckActionLog;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckSnapshot;
import com.kuaishou.serveree.themis.component.common.entity.CheckTriggerRecord;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.SonarPipelineMeasure;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.platform.CheckActionLogEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.platform.TriggerStatus;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckTypeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.JobTypeEnum;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.entity.ares.AresMessageEntity;
import com.kuaishou.serveree.themis.component.entity.platform.ExecuteScanContext;
import com.kuaishou.serveree.themis.component.entity.scanner.ScannerSendKimContext;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarPipelineMeasureElement;
import com.kuaishou.serveree.themis.component.service.CheckActionLogService;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckExecutionService;
import com.kuaishou.serveree.themis.component.service.CheckIssueService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.CheckSnapshotService;
import com.kuaishou.serveree.themis.component.service.CheckTriggerRecordService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.QualityCheckService;
import com.kuaishou.serveree.themis.component.service.SonarPipelineMeasureService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.ProfileAddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCreateRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSettingsRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/4/19 11:36 上午
 */
@Service
@Slf4j
public class SkyeyeScanner implements PlatformScanner {

    @Autowired
    private CheckRepoBranchService checkRepoBranchService;

    @Autowired
    private CheckRepoService checkRepoService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private CheckBaseService checkBaseService;

    @Autowired
    private TaskConfigService taskConfigService;

    @Autowired
    private QualityCheckService qualityCheckService;

    @Autowired
    private CheckExecutionService checkExecutionService;

    @Autowired
    private CheckTriggerRecordService checkTriggerRecordService;

    @Autowired
    private CheckActionLogService checkActionLogService;

    @Autowired
    private CheckIssueService checkIssueService;

    @Autowired
    private IssueSummaryService issueSummaryService;

    @Autowired
    private CheckMeasuresService checkMeasuresService;

    @Autowired
    private CheckSnapshotService checkSnapshotService;

    @Autowired
    private SonarPipelineMeasureService sonarPipelineMeasureService;

    @Value("${skyeye-scanner.issue-list-url}")
    private String kimNoticeUrl;

    @Autowired
    private AresApi aresApi;

    @Autowired
    private KsRedisClient ksRedisClient;

    @Override
    @Transactional
    public void scan(ExecuteScanContext context) {
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.getById(context.getCheckRepoBranchId());
        CheckRepo checkRepo = checkRepoService.getById(checkRepoBranch.getCheckRepoId());
        LocalDateTime now = LocalDateTime.now();
        CheckBase checkBase = fillUpCheckBase(checkRepo, checkRepoBranch, now);
        checkBaseService.save(checkBase);
        Task task = fillUpTask(checkRepo, checkRepoBranch, now);
        taskService.save(task);
        CheckExecution checkExecution = fillUpCheckExecution(checkBase, now, context.getTriggerRecordId(), task,
                ProcessExecutionReferType.SKY_EYE.getType());
        checkExecutionService.save(checkExecution);
        TaskConfig taskConfig = fillUpTaskConfig(task, now);
        taskConfigService.save(taskConfig);
        qualityCheckService.sponsorKspPipeline(task, taskConfig);

        CheckTriggerRecord checkTriggerRecord = checkTriggerRecordService.getById(context.getTriggerRecordId());
        checkTriggerRecord.setTriggerStatus(TriggerStatus.EXECUTING.getCode());
        checkTriggerRecord.setGmtModified(now);
        checkTriggerRecordService.updateById(checkTriggerRecord);
    }

    private CheckBase fillUpCheckBase(CheckRepo checkRepo, CheckRepoBranch checkRepoBranch, LocalDateTime now) {
        return CheckBase.builder()
                .checkRepoBranchId(checkRepoBranch.getId())
                .checkRepoId(checkRepo.getId())
                .repoUrl(checkRepo.getRepoUrl())
                .branch(checkRepoBranch.getBranchName())
                .gmtCreate(now)
                .gmtModified(now)
                .build();
    }

    private TaskConfig fillUpTaskConfig(Task task, LocalDateTime now) {
        return TaskConfig.builder()
                .taskId(task.getId())
                .executionType(CheckTypeEnum.FRONT_PIPELINE_CHECK.name())
                .executionResult(StringUtils.EMPTY)
                .scriptType(StringUtils.EMPTY)
                .jobType(JobTypeEnum.KSP_PIPELINE.name())
                .originalKspPipelineId(NumberUtils.LONG_ZERO)
                .originalKspBuildId(NumberUtils.LONG_ZERO)
                .originalKspStepId(NumberUtils.LONG_ZERO)
                .originalKspName(StringUtils.EMPTY)
                .executionParams(JSONUtils.serialize(Maps.newHashMap()))
                .pluginCheckRunType(NumberUtils.INTEGER_ZERO)
                .updatedTime(now)
                .createdTime(now)
                .build();
    }

    @Override
    public PlatformScannerEnum platformScanner() {
        return PlatformScannerEnum.SKY_EYE;
    }

    @Override
    public void afterProjectCreate(RepoCreateRequest searchRequest) {
        Long checkRepoId = searchRequest.getCheckRepoId();
        Long checkRepoBranchId = searchRequest.getCheckRepoBranchId();
        // 历史数据清除逻辑
        checkIssueService.deleteByRepoIdAndBranchId(checkRepoId, checkRepoBranchId);
        checkMeasuresService.deleteByCheckRepoBranchId(checkRepoBranchId);
        List<CheckSnapshot> snapshots = checkSnapshotService.listSnapshot(checkRepoBranchId);
        if (CollectionUtils.isNotEmpty(snapshots)) {
            LocalDateTime now = LocalDateTime.now();
            for (CheckSnapshot snapshot : snapshots) {
                snapshot.setGmtModified(now);
                snapshot.setIsLast(false);
            }
            checkSnapshotService.updateBatchById(snapshots);
        }
    }

    @Override
    public void afterProfileCreate(ProfileCreateRequestVo requestVo) {

    }

    @Override
    public void afterProfileAddRule(ProfileAddRuleRequestVo requestVo) {

    }

    @Override
    public void afterProfileDeleteRule(ProfileDeleteRuleRequestVo requestVo) {

    }

    @Override
    public void afterProjectUpdateProfile(RepoProfileUpdateRequest updateRequest) {
        String redisKey = KsRedisPrefixConstant.PLATFORM_CODE_QUALITY_SCORE_INFO
                + ":" + updateRequest.getGitProjectId()
                + ":" + updateRequest.getBranch();
        ksRedisClient.sync().del(redisKey);
    }

    @Override
    @Transactional
    public boolean judgeNeedAllScan(RepoSettingsRequest request) {
        Long checkRepoId = request.getCheckRepoId();
        Long checkRepoBranchId = request.getCheckRepoBranchId();
        CheckBase checkBase = checkBaseService.getIsLastCheckBaseByBranchId(checkRepoBranchId);
        if (checkBase == null) {
            return true;
        }
        LocalDateTime gmtCreate = checkBase.getGmtCreate();
        // 查询是否有变更
        List<CheckActionLog> repoActionLogs = checkActionLogService.listByCheckBranchIdGeTime(checkRepoBranchId,
                gmtCreate,
                CheckActionLogEnum.PROJECT_PROFILE_UPDATE.getType());

        String profileName = request.getProfileName();
        List<CheckActionLog> profileNameActionLogs = checkActionLogService.listByProfileNameGeTime(profileName,
                gmtCreate,
                CheckActionLogEnum.PROFILE_RULE_UPDATE.getType());

        boolean needAllScan = CollectionUtils.isNotEmpty(repoActionLogs)
                || CollectionUtils.isNotEmpty(profileNameActionLogs);

        if (needAllScan) {
            Integer gitProjectId = request.getGitProjectId();
            String branch = request.getBranch();
            checkIssueService.deleteByRepoIdAndBranchId(checkRepoId, checkRepoBranchId);
            issueSummaryService.deleteByGitProjectIdBranchAndScanMode(gitProjectId, branch,
                    ScanModeEnum.OFFLINE.getCode());
        }

        return needAllScan;
    }

    @Override
    public void afterProfileDelete(ProfileDeleteRequestVo requestVo) {

    }

    @Override
    public void afterIssueTransition(IssueSummary issueSummary, String transition) {

    }

    @Override
    public void sendPipelineKimNotice(ScannerSendKimContext scannerSendKimContext) {
        PCheckBase pCheckBase = scannerSendKimContext.getPCheckBase();
        Long kspBuildId = pCheckBase.getKspBuildId();
        SonarPipelineMeasure sonarPipelineMeasure = sonarPipelineMeasureService.getByKspBuildId(kspBuildId);
        AresMessageEntity aresMessageEntity = getMarkDownMessageAresMessageEntity(sonarPipelineMeasure, pCheckBase);
        MoreFunctions.runCatching(() -> {
            String sendResult = aresApi.sendKimNotice(aresMessageEntity);
            log.info("buildId is {},ScanModeService send kim result is {}", kspBuildId, sendResult);
        });
    }

    private AresMessageEntity getMarkDownMessageAresMessageEntity(SonarPipelineMeasure sonarPipelineMeasure,
            PCheckBase pCheckBase) {
        StringBuilder sb = new StringBuilder("### 天眼扫描通知").append("\n");
        sb.append("项目: ").append(GitUtils.getRepoName(pCheckBase.getRepoUrl())).append("\n");
        sb.append("触发人: ").append(pCheckBase.getSponsor()).append("\n");
        sb.append(getResultDetail(sonarPipelineMeasure)).append("\n");
        String format = String.format(kimNoticeUrl, pCheckBase.getKspBuildId().toString());
        sb.append("[扫描报告链接](").append(format).append(")");
        return AresMessageEntity.builder()
                .msgTypes(Lists.newArrayList(7))
                .templateId(0)
                .text(sb.toString())
                .userNames(Lists.newArrayList(pCheckBase.getSponsor()))
                .build();
    }

    private String getResultDetail(SonarPipelineMeasure sonarPipelineMeasure) {
        StringBuilder sb = new StringBuilder();
        String bugMessage = getTypeMessage("BUG", sonarPipelineMeasure.getBugMeasure());
        if (StringUtils.isNotEmpty(bugMessage)) {
            sb.append(bugMessage).append("\n");
        }
        String codeSmellMessage = getTypeMessage("代码异味", sonarPipelineMeasure.getCodeSmellMeasure());
        if (StringUtils.isNotEmpty(codeSmellMessage)) {
            sb.append(codeSmellMessage);
        }
        return sb.toString();
    }

    private String getTypeMessage(String type, String typeMeasure) {
        SonarPipelineMeasureElement typeMeasureElement =
                JSONUtils.deserialize(typeMeasure, SonarPipelineMeasureElement.class);
        int blockerCount = typeMeasureElement.getBlockerCount();
        int criticalCount = typeMeasureElement.getCriticalCount();
        int majorCount = typeMeasureElement.getMajorCount();
        return type + ":" + "阻断级别" + blockerCount + "个，"
                + "严重级别" + criticalCount + "个，"
                + "主要级别" + majorCount + "个。";
    }

}
