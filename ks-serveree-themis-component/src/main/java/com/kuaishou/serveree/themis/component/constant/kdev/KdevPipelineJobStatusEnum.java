package com.kuaishou.serveree.themis.component.constant.kdev;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/12/07 21:23 下午
 */
@Getter
public enum KdevPipelineJobStatusEnum {
    NONE(-1, "未运行"),
    CREATED(0, "已创建"),
    READY(1, "待执行"),
    RUNNING(2, "执行中"),
    SUCCESS(3, "成功"),
    FAIL(4, "失败"),
    CANCEL(5, "已取消"),
    PAUSE(6, "已暂停"), // 不再调度
    WARN(7, "警告"), // 成功的一种
    RETRY_READY(11, "重试待执行"),
    FAIL_RETRY(14, "失败可重试");

    private final int code;
    private final String desc;

    KdevPipelineJobStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}