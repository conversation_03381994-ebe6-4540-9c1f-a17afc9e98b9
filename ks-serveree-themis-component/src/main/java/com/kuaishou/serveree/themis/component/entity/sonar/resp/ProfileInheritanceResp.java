package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProfileInheritanceResp {

    private ProfileInfo profile;

    private List<ProfileInfo> ancestors;

    private List<ProfileInfo> children;

    @Data
    public static class ProfileInfo {
        private String key;
        private String name;
        private String parent;
        private Integer activeRuleCount;
        private Integer overridingRuleCount;
        private Boolean isBuiltIn;
    }
}
