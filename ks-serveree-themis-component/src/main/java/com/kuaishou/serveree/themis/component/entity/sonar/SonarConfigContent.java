package com.kuaishou.serveree.themis.component.entity.sonar;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/10/11 4:32 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SonarConfigContent {

    private List<String> robotIds;

    private Map<String, Map<String, String>> settingsMap;

}
