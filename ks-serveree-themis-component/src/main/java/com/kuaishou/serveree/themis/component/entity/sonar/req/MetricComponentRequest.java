package com.kuaishou.serveree.themis.component.entity.sonar.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-05-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MetricComponentRequest extends GitCommonRequest {
    private MeasureComponentRequest measureComponent;
}
