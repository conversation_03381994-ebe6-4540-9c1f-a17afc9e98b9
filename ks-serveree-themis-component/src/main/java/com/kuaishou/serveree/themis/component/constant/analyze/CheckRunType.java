package com.kuaishou.serveree.themis.component.constant.analyze;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/1/29 5:02 下午
 */
@AllArgsConstructor
public enum CheckRunType {

    RUN_FAIL_ERROR(0, "运行报错"),
    RUN_FAIL_REPORT(1, "运行出错上报"),
    SONAR_CHECK(2, "sonar扫描"),
    RUN_MR_CHECK(3, "mr检查"),
    ;

    @Getter
    private Integer type;

    @Getter
    private String desc;

}
