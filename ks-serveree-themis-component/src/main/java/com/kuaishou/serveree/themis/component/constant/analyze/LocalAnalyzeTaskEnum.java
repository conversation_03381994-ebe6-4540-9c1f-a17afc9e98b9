package com.kuaishou.serveree.themis.component.constant.analyze;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2021/3/30 2:50 下午
 */
@AllArgsConstructor
public enum LocalAnalyzeTaskEnum {

    LOCAL_DEPENDENCY_ANALYZE("serveree_local_dependency_analysis_task", "本地依赖分析任务"),
    LOCAL_ANALYZE("serveree_local_analysis_task", "本地分析任务"),
    LOCAL_OFFLINE_CHECK("serveree_local_offline_check_task", "本地java离线扫描任务"),
    LOCAL_FRONT_METRIC_CHECK("serveree_local_front_metric_check", "本地前端代码度量扫描");

    // change line number
    // change line number
    // change line number
    // change line number
    // change line number
    // change line number
    // change line number
    // change line number
    // change line number
    // change line number
    // change line number
    @Getter
    @Setter
    private String taskName;

    @Getter
    @Setter
    private String description;

    private static final Map<String, LocalAnalyzeTaskEnum> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(LocalAnalyzeTaskEnum.values()).forEach(taskEnum ->
                ENUM_MAP.put(taskEnum.getTaskName(), taskEnum)
        );
    }

    public static LocalAnalyzeTaskEnum getEnumByEnumName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return ENUM_MAP.get(name);
    }

}
