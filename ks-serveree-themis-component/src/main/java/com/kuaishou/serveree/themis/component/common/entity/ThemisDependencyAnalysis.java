package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 依赖解析表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-14
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ThemisDependencyAnalysis对象", description = "依赖解析表")
public class ThemisDependencyAnalysis implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "执行任务id")
    private Long taskId;

    @ApiModelProperty(value = "分析来源")
    private String analyzeSource;

    @ApiModelProperty(value = "repo地址")
    private String repoUrl;

    @ApiModelProperty(value = "分支名")
    private String branch;

    @ApiModelProperty(value = "是否有违规")
    private Boolean violation;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createdTime;

}
