package com.kuaishou.serveree.themis.component.proxy.strategy;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.proxy.strategy.content.type.ContentTypeSimpleFactory;
import com.kuaishou.serveree.themis.component.proxy.strategy.content.type.ContentTypeStrategy;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-16
 */
@Component
public class RedirectPostStrategy implements ProxyRedirectStrategy {
    @Autowired
    private ContentTypeSimpleFactory factory;

    @Override
    public String doRedirect(HttpServletRequest request) {
        String contentType = request.getContentType();
        ContentTypeStrategy strategy = factory.getInstance(contentType);
        if (strategy == null) {
            throw ThemisException.builder()
                    .code(ResultCodeConstant.CONTENT_TYPE_NOT_SUPPORTED.getCode())
                    .message(String.format(ResultCodeConstant.CONTENT_TYPE_NOT_SUPPORTED.getMessage(), contentType))
                    .build();
        }
        String resultResp = strategy.doPostRedirect(request);
        if (StringUtils.isEmpty(resultResp)) {
            throw new ThemisException(ResultCodeConstant.TRANSFER_FAILED);
        }
        return resultResp;
    }
}
