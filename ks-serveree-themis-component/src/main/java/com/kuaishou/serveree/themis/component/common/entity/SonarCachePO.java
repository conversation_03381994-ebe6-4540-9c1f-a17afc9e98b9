package com.kuaishou.serveree.themis.component.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-12
 */
@Data
@TableName(value = "sonar_cache_info")
public class SonarCachePO {
    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "ksp的buildId")
    private Long kspBuildId;

    private Long kspPipelineId;

    private Long gitProjectId;

    private String commitId;

    private Long profileId;

    private String profileVersion;

    private String scanModules;

    private Boolean incrementMode;

    private String incrementType;

    private String sourceCommitId;

    private Boolean calcCycloSwitch;

    //补充信息json格式
    private String ext;
    //执行时长
    private Long jobDuration;

    private String creator;         // 创建人

    private Long createdTime;        // 创建时间

    private String updater;         // 最后修改人

    private Long updatedTime;        // 最后修改时间

}
