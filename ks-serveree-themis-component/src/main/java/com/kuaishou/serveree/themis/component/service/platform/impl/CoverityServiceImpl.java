package com.kuaishou.serveree.themis.component.service.platform.impl;

import static com.kuaishou.serveree.themis.component.constant.quality.TaskType.PLATFORM_OFFLINE;
import static com.kuaishou.serveree.themis.component.constant.quality.TaskType.PLATFORM_PIPELINE;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.CheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.CheckMeasures;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CoverityAgent;
import com.kuaishou.serveree.themis.component.common.entity.CoverityAgentTask;
import com.kuaishou.serveree.themis.component.common.entity.IssueChanges;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.KbuildProject;
import com.kuaishou.serveree.themis.component.common.entity.MrStuckRecord;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckConfig;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.platform.CheckProfileType;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.process.ProcessSponsorType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckPriorityEnum;
import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.constant.quality.TaskType;
import com.kuaishou.serveree.themis.component.entity.plugin.SonarNewPluginSettings;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarMeasures;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckExecutionService;
import com.kuaishou.serveree.themis.component.service.CheckIssueService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresSnapshotService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.CoverityAgentService;
import com.kuaishou.serveree.themis.component.service.CoverityAgentTaskService;
import com.kuaishou.serveree.themis.component.service.IssueChangesService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryBaseService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.KbuildProjectService;
import com.kuaishou.serveree.themis.component.service.MrStuckRecordService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PCheckConfigService;
import com.kuaishou.serveree.themis.component.service.PCheckExecutionService;
import com.kuaishou.serveree.themis.component.service.ScanDirectoryService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.service.pipeline.impl.PipelineIssueReportService;
import com.kuaishou.serveree.themis.component.service.platform.CoverityService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformIssueSummaryService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRuleService;
import com.kuaishou.serveree.themis.component.service.platform.notice.CheckRepoNoticeActionService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.DateUtils;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.IssueUtils;
import com.kuaishou.serveree.themis.component.vo.request.coverity.CoverityAgentRegisterReq;
import com.kuaishou.serveree.themis.component.vo.request.coverity.CoverityAgentTaskReport;
import com.kuaishou.serveree.themis.component.vo.request.coverity.CoverityAgentTaskReport.CovIssueVo;
import com.kuaishou.serveree.themis.component.vo.request.coverity.CoverityAgentTaskStatusReportReq;
import com.kuaishou.serveree.themis.component.vo.request.coverity.CoverityAnalyseTaskSubmitReq;
import com.kuaishou.serveree.themis.component.vo.request.sonar.PluginPipelineReportRequest;
import com.kuaishou.serveree.themis.component.vo.response.PipelineReportResponse;
import com.kuaishou.serveree.themis.component.vo.response.coverity.CoverityAgentStatusVo;
import com.kuaishou.serveree.themis.component.vo.response.coverity.CoverityAgentTaskVo;
import com.kuaishou.serveree.themis.component.vo.response.coverity.CoverityAgentVo;

import cn.hutool.core.collection.CollectionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-05
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class CoverityServiceImpl implements CoverityService, PipelineIssueReportService {

    // 一次查询多少条待执行任务数
    private static final Integer AGENT_TASK_SELECT_LIMIT = 20;

    private final CoverityAgentService coverityAgentService;
    private final CoverityAgentTaskService coverityAgentTaskService;
    private final CheckRepoService checkRepoService;
    private final PCheckBaseService pCheckBaseService;
    private final PCheckExecutionService pCheckExecutionService;
    private final PCheckConfigService pCheckConfigService;
    private final TaskService taskService;
    private final TaskConfigService taskConfigService;
    private final CheckRepoNoticeActionService checkRepoNoticeActionService;
    private final PlatformRuleService platformRuleService;
    private final CheckBaseService checkBaseService;
    private final CheckExecutionService checkExecutionService;
    private final PlatformIssueSummaryService platformIssueSummaryService;
    private final IssueSummaryService issueSummaryService;
    private final IssueSummaryBaseService issueSummaryBaseService;
    private final IssueChangesService issueChangesService;
    private final CheckIssueService checkIssueService;
    private final CheckMeasuresService checkMeasuresService;
    private final CheckMeasuresSnapshotService checkMeasuresSnapshotService;
    private final KbuildProjectService kbuildProjectService;
    private final ScanDirectoryService scanDirectoryService;
    private final MrStuckRecordService mrStuckRecordService;

    @Transactional
    @Override
    public Long submitCovAnalyseTask(CoverityAnalyseTaskSubmitReq req) {
        CoverityAgentTask agentTask = new CoverityAgentTask();
        agentTask.setKspBuildId(req.getKspBuildId());
        agentTask.setKspPipelineId(req.getKspPipelineId());
        agentTask.setPriority(CheckPriorityEnum.getPriorityNoByPriorityName(req.getPriority()));
        agentTask.setCovDownloadUrl(req.getCovDownloadUrl());
        LocalDateTime now = LocalDateTime.now();
        agentTask.setGmtCreate(now);
        agentTask.setGmtModified(now);
        agentTask.setAgentId(0L);
        agentTask.setStatus(TaskStatusEnum.WAITING.name());
        agentTask.setTaskType(req.getTaskType());
        coverityAgentTaskService.save(agentTask);
        return agentTask.getId();
    }

    @Transactional
    @Override
    public Long registerAgent(CoverityAgentRegisterReq req) {
        CoverityAgent coverityAgent = coverityAgentService.getByIpAndName(req.getIp(), req.getName());
        LocalDateTime now = LocalDateTime.now();
        if (coverityAgent == null) {
            coverityAgent = new CoverityAgent();
            coverityAgent.setIp(req.getIp());
            coverityAgent.setName(req.getName());
            coverityAgent.setGmtCreate(now);
            coverityAgent.setDeleted(false);
        }
        coverityAgent.setHeartbeatTime(now);
        coverityAgent.setMonitorData("");
        coverityAgent.setGmtModified(now);
        coverityAgentService.saveOrUpdate(coverityAgent);
        return coverityAgent.getId();
    }

    @Transactional
    @Override
    public CoverityAgentVo agentHeartbeat(long agentId) {
        CoverityAgent coverityAgent = coverityAgentService.getById(agentId);
        if (coverityAgent == null) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "未知agent");
        }
        LocalDateTime now = LocalDateTime.now();
        coverityAgent.setHeartbeatTime(now);
        coverityAgent.setGmtModified(now);
        coverityAgentService.updateById(coverityAgent);
        // 返回
        CoverityAgentVo vo = new CoverityAgentVo();
        BeanUtils.copyProperties(coverityAgent, vo);
        return vo;
    }

    @Transactional
    @Override
    public Boolean agentTaskHeartbeat(CoverityAgentTaskStatusReportReq req) {
        CoverityAgent agent = coverityAgentService.getById(req.getAgentId());
        if (agent == null) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "未知agent");
        }
        CoverityAgentTask agentTask = coverityAgentTaskService.getById(req.getTaskId());
        if (agentTask == null) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "未知agentTask");
        }
        // 数据库中任务已经是结束
        if (TaskStatusEnum.isFinished(agentTask.getStatus())) {
            log.warn("[Coverity][Agent]task已结束！请求数据:{}, task:{}", req, agentTask);
            return false;
        }
        agentTask.setStatus(req.getStatus());
        LocalDateTime now = LocalDateTime.now();
        agentTask.setGmtModified(now);
        agentTask.setHeartbeatTime(now);
        // 任务结束
        if (TaskStatusEnum.isFinished(req.getStatus())) {
            agentTask.setExecuteEnd(now);
        }
        coverityAgentTaskService.updateById(agentTask);
        return true;
    }

    @Transactional
    @Override
    public Boolean agentTaskReport(long taskId, CoverityAgentTaskReport report) {
        CoverityAgentTask agentTask = coverityAgentTaskService.getById(taskId);
        if (agentTask == null) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "未知agentTask");
        }
        // 更新任务
        // 正常不会执行到这里
        if (!TaskStatusEnum.SUCCESS.name().equals(agentTask.getStatus())) {
            agentTask.setStatus(TaskStatusEnum.SUCCESS.name());
        }
        // 用这个字段来记录最终处理结果
        agentTask.setResult(TaskStatusEnum.SUCCESS.name());
        agentTask.setGmtModified(LocalDateTime.now());
        coverityAgentTaskService.updateById(agentTask);
        // 获取任务详情。
        CoverityAgentTaskVo taskVo = convertFromAgentTask(agentTask, false);
        // 流水线扫描结果上报
        if (taskVo.getTaskType() == PLATFORM_PIPELINE) {
            pipelineReport(agentTask, report);
            return true;
        }
        // 查询checkBase和checkExecution
        TaskConfig taskConfig =
                taskConfigService.getOneBySponsorPipelineInfo(taskVo.getKspPipelineId(), taskVo.getKspBuildId());
        List<CheckExecution> checkExecutions = checkExecutionService.listByTaskId(taskConfig.getTaskId());
        if (CollectionUtils.isEmpty(checkExecutions)) {
            log.error("[Coverity][AgentTask][Report]未找到执行记录，taskId:{}, report:{}", taskId, report);
            // 设置处理失败
            agentTask.setResult(TaskStatusEnum.FAIL.name());
            coverityAgentTaskService.updateById(agentTask);
            return false;
        }
        CheckExecution checkExecution = checkExecutions.get(0);
        CheckBase checkBase = checkBaseService.getById(checkExecution.getBaseId());
        // 构造issue数据
        List<CheckIssue> checkIssues = report.getIssues().stream()
                .map(iv -> iv.convertToCheckIssue(checkBase, checkExecution, taskVo.getGitProjectId()))
                .collect(Collectors.toList());
        // 去重(同一类、同一方法、同代码、不同行)
        checkIssues = IssueUtils.processIssuesWithSameSourceInSameFunc(checkIssues);
        // 对比历史结果
        Pair<List<IssueSummary>, List<IssueSummary>> summaryPair =
                platformIssueSummaryService.compareAndGenSummaryIssues(checkIssues, checkBase, checkExecution);
        List<IssueSummary> needSaveList = summaryPair.getLeft();
        List<IssueSummary> needUpdateList = summaryPair.getRight();
        // 处理新增
        issueSummaryService.saveBatch(needSaveList);
        // 处理更新
        issueSummaryService.updateBatchByProjectBranchUniqId(needUpdateList);
        // 更新issue summary base
        issueSummaryBaseService.batchCreateOrUpdate(CollectionUtils.union(needSaveList, needUpdateList));
        // 构造changes数据
        List<IssueChanges> changesIssueList = issueChangesService.packageChangesIssues(
                Lists.newArrayList(CollectionUtil.union(needSaveList, needUpdateList)));
        if (CollectionUtils.isNotEmpty(changesIssueList)) {
            issueChangesService.saveBatch(changesIssueList);
        }
        // 删除历史的issues
        checkIssueService.deleteByRepoIdAndBranchId(checkBase.getCheckRepoId(), checkBase.getCheckRepoBranchId());
        // 保存issue数据
        checkIssueService.saveBatch(checkIssues);
        // 保存统计信息
        updateRepoMeasures(taskVo, checkBase, checkExecution);
        List<CheckBase> dbCheckBase = checkBaseService.listByCheckRepoBranchId(checkBase.getCheckRepoBranchId());
        // 记录之前记录isLast字段为false
        List<CheckBase> needChangeBaseList = dbCheckBase.stream()
                .filter(o -> !checkBase.getId().equals(o.getId()))
                .map(o -> o.setIsLast(false))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needChangeBaseList)) {
            checkBaseService.updateBatchById(needChangeBaseList);
        }
        // 记录当前isLast字段为true
        checkBase.setIsLast(true);
        checkBase.setGmtModified(LocalDateTime.now());
        checkBaseService.updateById(checkBase);
        // 发送离线扫描通知
        checkRepoNoticeActionService.sendOfflineScanNotice(checkBase);
        return true;
    }

    public PipelineReportResponse pipelineReport(CoverityAgentTask agentTask, CoverityAgentTaskReport report) {
        Long kspBuildId = agentTask.getKspBuildId();
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(kspBuildId);
        PCheckExecution pCheckExecution = pCheckExecutionService.getByPBaseIdAndType(pCheckBase.getId(),
                ProcessExecutionReferType.COVERITY.getType());
        PCheckConfig pCheckConfig = pCheckConfigService.getByPBaseId(pCheckBase.getId());
        // 构造通用的上报request
        PluginPipelineReportRequest request =
                buildPluginPipelineReportRequest(agentTask, report, kspBuildId, pCheckBase, pCheckExecution, pCheckConfig);
        // 上报
        return report(request, pCheckBase, pCheckExecution, pCheckConfig);
    }

    private PluginPipelineReportRequest buildPluginPipelineReportRequest(CoverityAgentTask agentTask,
            CoverityAgentTaskReport report, Long kspBuildId, PCheckBase pCheckBase, PCheckExecution pCheckExecution,
            PCheckConfig pCheckConfig) {
        PluginPipelineReportRequest request = new PluginPipelineReportRequest();
        request.setKspPipelineId(agentTask.getKspPipelineId());
        request.setSendKimNotice(false);
        request.setSourceCommitId("");
        request.setKspBuildId(kspBuildId);
        request.setGitProjectId(pCheckBase.getProjectId());
        request.setGitBranch(pCheckBase.getBranch());
        request.setCommitId(pCheckBase.getCommitId());
        request.setBuildModules(pCheckBase.getBuildModules());
        request.setMrId(Long.valueOf(pCheckBase.getMrId()));
        request.setSponsor(pCheckBase.getSponsor());
        request.setLocalBuildId(pCheckBase.getLocalBuildId());
        request.setRealCompile(true);
        request.setIncrementMode(pCheckExecution.getIncrementMode());
        request.setIncrementType(pCheckExecution.getIncrementType());
        request.setExecutionReferType(pCheckExecution.getReferType());
        request.setScanMode(ScanModeEnum.PROCESS.getCode());
        if (pCheckConfig.getSponsorType() == ProcessSponsorType.MR_STUCK.getType()) {
            request.setMrStuck(true);
            MrStuckRecord mrStuckRecord = mrStuckRecordService.getByKspBuildId(kspBuildId);
            request.setOnlyDiffIssue(mrStuckRecord.getOnlyDiff());
        }
        request.setIssues(Lists.newArrayList(report.getIssues()));
        return request;
    }

    @Override
    public List<PCheckIssue> generatePCheckIssues(PluginPipelineReportRequest request, PCheckBase pCheckBase,
            PCheckExecution pCheckExecution, SonarNewPluginSettings stuckPointSetting) {
        // 构造issue数据
        List<PCheckIssue> pCheckIssueList = IssueUtils.initPCheckIssues(request, pCheckBase, pCheckExecution, stuckPointSetting,
                (pipelineIssue, pCheckIssue, gitProjectId) -> {
                    CovIssueVo covIssueVo = (CovIssueVo) pipelineIssue;
                    pCheckIssue.setSonarIssueKey(covIssueVo.getMergeKey());
                    return covIssueVo.generateIssueUniqId(gitProjectId);
                });
        // 去重(同一类、同一方法、同代码、不同行)
        return IssueUtils.processIssuesWithSameSourceInSameFunc(pCheckIssueList,
                PCheckIssue::getIssueUniqId,
                i -> CommonUtils.getIssueLocationString(i.getStartLine(), i.getStartOffset(), i.getEndLine(), i.getEndOffset()),
                PCheckIssue::setIssueUniqId);
    }


    private void updateRepoMeasures(CoverityAgentTaskVo taskVo, CheckBase checkBase, CheckExecution checkExecution) {
        // 删除之前的
        checkMeasuresService.deleteByCheckRepoBranchId(checkBase.getCheckRepoBranchId());
        // 聚合数据
        // 查询当前还打开的问题。
        List<IssueSummary> openIssueSummaries =
                issueSummaryService.listOpenByProjectBranch(taskVo.getGitProjectId(), taskVo.getBranch(),
                        ScanModeEnum.OFFLINE.getCode());
        if (openIssueSummaries.isEmpty()) {
            return;
        }
        // 按issue类型分组
        Map<String, Integer> typeCountMap = openIssueSummaries.stream()
                .collect(Collectors.groupingBy(
                        IssueSummary::getType, Collectors.collectingAndThen(Collectors.toList(), List::size))
                );
        // 转成 SonarIssueMeasure
        List<SonarMeasures> sonarMeasures = Lists.newArrayList();
        for (Entry<String, Integer> entry : typeCountMap.entrySet()) {
            Integer cnt = entry.getValue();
            String key = "";
            switch (entry.getKey()) {
                case "BUG":
                    key = "bugs";
                    break;
                case "VULNERABILITY":
                    key = "vulnerabilities";
                    break;
                case "CODE_SMELL":
                    key = "code_smells";
                    break;
                default:
                    break;
            }
            sonarMeasures.add(new SonarMeasures(key, cnt.toString(), cnt == 0, List.of()));
        }
        // 保存最新统计信息
        List<CheckMeasures> checkMeasures = checkMeasuresService.saveLiveMeasures(sonarMeasures, checkBase.getId(),
                checkExecution.getId(), checkBase.getCheckRepoId(), checkBase.getCheckRepoBranchId());
        // 保存历史
        checkMeasuresSnapshotService.saveSnapshotMeasures(checkMeasures, 0L);
    }

    @Override
    public Boolean resetAgentTask(long taskId) {
        CoverityAgentTask agentTask = coverityAgentTaskService.getById(taskId);
        if (agentTask == null) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "未知agentTask");
        }
        log.warn("[Coverity][AgentTask]重置任务状态，重置前：{}", agentTask);
        agentTask.setStatus(TaskStatusEnum.WAITING.name());
        agentTask.setAgentId(0L);
        agentTask.setHeartbeatTime(DateUtils.ZERO);
        agentTask.setExecuteStart(DateUtils.ZERO);
        agentTask.setExecuteEnd(DateUtils.ZERO);
        agentTask.setResult("");
        agentTask.setGmtModified(LocalDateTime.now());
        return coverityAgentTaskService.updateById(agentTask);
    }

    @Transactional
    @Override
    public CoverityAgentTaskVo fetchAgentTask(long agentId) {
        CoverityAgent agent = coverityAgentService.getById(agentId);
        if (agent == null) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "未知agent");
        }
        // 抢占任务
        // 获取所有待执行的任务
        List<CoverityAgentTask> tasks =
                coverityAgentTaskService.listTasksByStatusOrderByPriority(TaskStatusEnum.WAITING,
                        AGENT_TASK_SELECT_LIMIT);
        // 没有可执行的任务
        if (tasks.isEmpty()) {
            return null;
        }
        // 轮询抢占
        for (CoverityAgentTask task : tasks) {
            // 抢占成功
            boolean lockTaskToRun = coverityAgentTaskService.lockTaskToRun(task.getId(), agentId);
            if (lockTaskToRun) {
                // 组装数据，这里重新获取最新数据
                return convertFromAgentTask(coverityAgentTaskService.getById(task.getId()), true);
            }
        }
        return null;
    }

    @Override
    public CoverityAgentStatusVo getCoverityAgentStatus(long agentId) {
        CoverityAgent agent = coverityAgentService.getById(agentId);
        if (agent == null) {
            throw new ThemisException(HttpStatus.BAD_REQUEST.value(), "未知agent");
        }
        CoverityAgentStatusVo vo = new CoverityAgentStatusVo();
        BeanUtils.copyProperties(agent, vo);
        List<CoverityAgentTask> agentTaskList = coverityAgentTaskService.listByAgentId(agentId);
        vo.setTaskList(
                agentTaskList.stream().map(t -> convertFromAgentTask(t, false)).collect(Collectors.toList()));
        return vo;
    }

    private CoverityAgentTaskVo convertFromAgentTask(CoverityAgentTask agentTask, boolean withRules) {
        if (agentTask == null) {
            return null;
        }
        TaskType taskType = TaskType.getByType(agentTask.getTaskType());
        CoverityAgentTaskVo taskVo = new CoverityAgentTaskVo();
        // 离线记录和流水线记录元数据在不同表
        if (taskType == PLATFORM_OFFLINE) {
            TaskConfig taskConfig =
                    taskConfigService.getOneBySponsorPipelineInfo(agentTask.getKspPipelineId(), agentTask.getKspBuildId());
            Task task = taskService.getById(taskConfig.getTaskId());
            taskVo.setGitProjectId(task.getProjectId());
            taskVo.setBranch(task.getBranch());
            taskVo.setSponsor(task.getChecker());
        } else {
            PCheckBase pCheckBase = pCheckBaseService.getByBuildId(agentTask.getKspBuildId());
            taskVo.setGitProjectId(pCheckBase.getProjectId());
            taskVo.setBranch(pCheckBase.getBranch());
            taskVo.setSponsor(pCheckBase.getSponsor());
        }
        // 这里为了让kbuild项目在cc上看出区别，把build_path也加上
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(taskVo.getGitProjectId());
        taskVo.setProjectName(GitUtils.getRepoName(checkRepo.getRepoUrl()));
        if (checkRepo.getUseKbuild()) {
            KbuildProject kbuildProject = kbuildProjectService.getById(taskVo.getGitProjectId());
            // 去掉 ./ 前缀 和 / 后缀，再将 / 转为 _
            String buildPath = kbuildProject.getBuildPath();
            buildPath = StringUtils.removeStart(buildPath, "./");
            buildPath = StringUtils.removeEnd(buildPath, "/");
            buildPath = buildPath.replace('/', '-');
            taskVo.setProjectName(taskVo.getProjectName() + "-" + buildPath);
        }
        taskVo.setKspPipelineId(agentTask.getKspPipelineId());
        taskVo.setKspBuildId(agentTask.getKspBuildId());
        taskVo.setCovDownloadUrl(agentTask.getCovDownloadUrl());
        taskVo.setPipelineUrl(String.format("https://halo.corp.kuaishou.com/devcloud/pipeline/builddetail/%s/%s",
                agentTask.getKspPipelineId(), agentTask.getKspBuildId()));
        taskVo.setTaskType(taskType);
        // 查询启用的扫描规则
        if (withRules) {
            taskVo.setRules(
                    // 任务开始执行时项目配置的分支可能变了，这里需要查最新的配置，不能写死分支
                    platformRuleService.projectRules(taskVo.getGitProjectId(),
                            PLATFORM_PIPELINE == taskType
                            ? CheckProfileType.PIPELINE.getType() : CheckProfileType.OFFLINE.getType())
            );
        }
        taskVo.setScanDirectorySetting(scanDirectoryService.getScanDirectorySettingByCheckRepoId(checkRepo.getId()));
        taskVo.setTaskId(agentTask.getId());
        taskVo.setExecuteStart(agentTask.getExecuteStart());
        taskVo.setExecuteEnd(agentTask.getExecuteEnd());
        taskVo.setStatus(agentTask.getStatus());
        taskVo.setLatestReportTime(agentTask.getHeartbeatTime());
        return taskVo;
    }
}
