package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Maps;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.component.dto.ProjectInitConfigDto;
import com.kuaishou.serveree.themis.component.entity.kconf.RuleKeyCanNotBeSkippedGrayConfig;
import com.kuaishou.serveree.themis.component.service.sonar.cache.SonarCacheSwitchConfig;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-08-15
 */
public class KconfConstants {

    /**
     * coverity对kbuild项目的扫描配置。
     */
    public static final Kconf<HashMap<String, Map<String, String>>> COVERITY_KBUILD_SCAN_CONF =
            Kconfs.ofMapMap("qa.themis.CoverityKbuildScanConf", Maps.newHashMap(), String.class, String.class, String.class).build();

    /**
     * 离线扫描，不同扫描器的并发数
     */
    public static final Kconf<Map<String, Integer>> SCAN_PLATFORM_PARALLEL_COUNT_MAP_KCONF = Kconfs
            .ofIntegerMap("qa.themis.serverScanPlatformParallelCountMp", Maps.newHashMap())
            .build();

    public static final Kconf<SonarCacheSwitchConfig> SONAR_CACHE_SWITCH_CONFIG = Kconfs
            .ofJson("qa.themis.sonarCacheSwitchInfo", new SonarCacheSwitchConfig(),
                    SonarCacheSwitchConfig.class).build();

    /**
     * 不同项目组新建项目采用的默认配置
     */
    public static final Kconf<HashMap<String, List<ProjectInitConfigDto>>> NEW_PROJECT_CONF =
            Kconfs.ofListMap("qa.themis.newProjectConf", Maps.newHashMap(), String.class, ProjectInitConfigDto.class).build();

    /**
     * 代码扫描在OpenApi上服务发布得到的key
     */
    public static final Kconf<String> CODE_SCAN_OPEN_API_SERVICE_KEY = Kconfs.ofString("qa.themis.codeScanOpenApiServiceKey", "").build();

    /**
     * 规则禁止跳过，灰度配置
     */
    public static final Kconf<RuleKeyCanNotBeSkippedGrayConfig> RULE_KEY_CAN_NOT_BE_SKIPPED_GRAY_CONFIG = Kconfs
            .ofJson("qa.themis.ruleKeyCanNotBeSkippedGrayConfig", new RuleKeyCanNotBeSkippedGrayConfig(),
                    RuleKeyCanNotBeSkippedGrayConfig.class).build();
}
