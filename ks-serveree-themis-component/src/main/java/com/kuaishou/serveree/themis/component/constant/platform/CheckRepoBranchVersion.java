package com.kuaishou.serveree.themis.component.constant.platform;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/24 7:52 下午
 */
@AllArgsConstructor
public enum CheckRepoBranchVersion {

    OLD_VERSION(0, "老版本"),
    NEW_VERSION(1, "新版本"),
    DELETE_VERSION(2, "删除版本"),
    EDIT_VERSION(3, "编辑版本,用户手动切换长期分支,之前的分支则为编辑版本"),
    ;

    @Getter
    private int code;
    @Getter
    private String desc;

}
