package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/27 2:39 下午
 */
@Slf4j
@AllArgsConstructor
public enum CheckIssueStatus implements FacetNode {

    OPEN("OPEN", "打开", true),
    CLOSED("CLOSED", "误判", true),
    RESOLVED("RESOLVED", "已解决", true),
    TO_REVIEW("TO_REVIEW", "暂不处理", true),
    RE_OPEN("RE_OPEN", "重新打开", true),
    ;

    @Getter
    private final String status;
    @Getter
    private final String desc;
    @Getter
    // 前端页面是否显示该元素
    private final Boolean show;

    private static final Map<String, CheckIssueStatus> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(CheckIssueStatus.values()).forEach(checkTypeEnum ->
                ENUM_MAP.put(checkTypeEnum.getStatus(), checkTypeEnum)
        );
    }

    public static List<String> allIssueStatus() {
        return Lists.newArrayList(ENUM_MAP.keySet());
    }

    public static boolean correctStatus(String status) {
        if (StringUtils.isEmpty(status)) {
            return false;
        }
        return ENUM_MAP.containsKey(status);
    }

    @Override
    public CheckIssueStatus valueOfName(String name) {
        try {
            return valueOf(name);
        } catch (IllegalArgumentException e) {
            log.info("[{}]在枚举中不存在", name);
        }
        return null;
    }

    @Override
    public Boolean showOfName(String name) {
        FacetNode node = valueOfName(name);
        return Objects.nonNull(node) && node.getShow();
    }

    public static boolean isOpen(String status) {
        return List.of(OPEN.status, RE_OPEN.status).contains(status);
    }

    public static String getDescByKey(String status) {
        return ENUM_MAP.get(status).getDesc();
    }

    public static List<String> unresolvedStatuses() {
        return List.of(OPEN.status, RE_OPEN.status, CLOSED.status, TO_REVIEW.status);
    }
}
