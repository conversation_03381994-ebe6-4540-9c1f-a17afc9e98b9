package com.kuaishou.serveree.themis.component.entity.plugin;

import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/1/18 5:59 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PluginRuleSettings {

    /**
     * 通用禁止规则
     */
    private BannedConfig commonBannedConfig;

    /**
     * 按照模块类型禁止规则
     */
    private Map<String, BannedConfig> artifactTypeBannedConfig;

}
