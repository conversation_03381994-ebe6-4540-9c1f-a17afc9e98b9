package com.kuaishou.serveree.themis.component.intercept;

import java.lang.reflect.InvocationTargetException;
import java.text.DateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Properties;
import java.util.concurrent.ExecutorService;

import javax.annotation.Resource;

import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-09 17:11
 **/
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
                RowBounds.class, ResultHandler.class})})
@Component
public class LogMybatisInterceptor implements Interceptor {

    @Resource
    @Qualifier("logExecutor")
    private ExecutorService logExecutor;

    private static final String RDS_CHAR_DOLLAR = "RDS_CHAR_DOLLAR";
    /**
     * LOGGER
     */
    private static final Logger LOG = LoggerFactory.getLogger(LogMybatisInterceptor.class);

    public static String getSql(Configuration configuration, BoundSql boundSql, String sqlId, long time,
            Object returnValue) {
        String sql = showSql(configuration, boundSql);
        StringBuilder str = new StringBuilder(100);
        str.append(sqlId);
        str.append(":");
        str.append(sql);
        str.append(":");
        str.append(time);
        str.append("ms");
        return str.toString();
    }

    private static String getParameterValue(Object obj) {
        String value = null;
        if (obj instanceof String) {
            value = "'" + obj.toString() + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter =
                    DateFormat.getDateTimeInstance(DateFormat.DEFAULT, DateFormat.DEFAULT, Locale.CHINA);
            value = "'" + formatter.format((Date) obj) + "'";
        } else {
            if (obj != null) {
                value = obj.toString();
            } else {
                value = "";
            }

        }
        return value;
    }

    public static String showSql(Configuration configuration, BoundSql boundSql) {

        final Integer sqlSize = 5000;
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");

        if ((null != sql) && (sql.indexOf("insert") != -1) && (sql.length() > sqlSize)) { // 批量插入不打印sql
            return sql;
        } else {
            if ((parameterMappings.size() > 0) && (parameterObject != null)) {
                TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
                if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                    String parameterValue = getParameterValue(parameterObject);

                    parameterValue = parameterValue.replaceAll("\\$", RDS_CHAR_DOLLAR);
                    sql = sql.replaceFirst("\\?", parameterValue);
                    sql = sql.replaceAll(RDS_CHAR_DOLLAR, "\\$");
                } else {
                    MetaObject metaObject = configuration.newMetaObject(parameterObject);
                    for (ParameterMapping parameterMapping : parameterMappings) {
                        String propertyName = parameterMapping.getProperty();
                        if (metaObject.hasGetter(propertyName)) {
                            Object obj = metaObject.getValue(propertyName);
                            String parameterValue = getParameterValue(obj);
                            parameterValue = parameterValue.replaceAll("\\$", RDS_CHAR_DOLLAR);
                            sql = sql.replaceFirst("\\?", parameterValue);
                            sql = sql.replaceAll(RDS_CHAR_DOLLAR, "\\$");
                        } else if (boundSql.hasAdditionalParameter(propertyName)) {
                            Object obj = boundSql.getAdditionalParameter(propertyName);
                            String parameterValue = getParameterValue(obj);
                            parameterValue = parameterValue.replaceAll("\\$", RDS_CHAR_DOLLAR);
                            sql = sql.replaceFirst("\\?", parameterValue);
                            sql = sql.replaceAll(RDS_CHAR_DOLLAR, "\\$");
                        }
                    }
                }
            }
        }
        return sql;
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        String sqlId = mappedStatement.getId();
        Object returnValue = null;
        long start = System.currentTimeMillis();
        try {
            returnValue = invocation.proceed();
            LOG.info("SqlId: {}, time cost: {}", sqlId, (System.currentTimeMillis() - start));
        } catch (Exception e) {
            long end = System.currentTimeMillis();
            long time = (end - start);
            Object parameter = null;
            if (invocation.getArgs().length > 1) {
                parameter = invocation.getArgs()[1];
            }
            BoundSql boundSql = mappedStatement.getBoundSql(parameter);
            Configuration configuration = mappedStatement.getConfiguration();
            String sql = getSql(configuration, boundSql, sqlId, time, returnValue);
            if (((InvocationTargetException) e).getTargetException().getMessage().startsWith(
                    "Duplicate entry")) {
                LOG.info("dup-key exception: {}", sql);
            } else {
                LOG.error("error", e);
                LOG.error("【 ERROR SQL:{}】 ", sql);
            }
            throw e;
        }

        return returnValue;
    }

    private void asynchronousComputeSql(Configuration configuration, BoundSql boundSql, String sqlId, long time, Object returnValue) {
        logExecutor.execute(() -> {
            String sql = getSql(configuration, boundSql, sqlId, time, returnValue);
            LOG.info("SQL==={}", sql);
        });
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {

    }
}