package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckFileIssue对象", description = "")
public class CheckFileIssue implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "check_file的主键id")
    private Long fileId;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "check_base表主键id")
    private Long baseId;

    @ApiModelProperty(value = "check_repo表主键id")
    private Long checkRepoId;

    @ApiModelProperty(value = "check_repo_branch表主键id")
    private Long checkRepoBranchId;

    @ApiModelProperty(value = "规则key")
    private String ruleKey;

    @ApiModelProperty(value = "描述信息")
    private String message;

    @ApiModelProperty(value = "起始行")
    private Integer startLine;

    @ApiModelProperty(value = "结束行")
    private Integer endLine;

    @ApiModelProperty(value = "起始列")
    private Integer startOffset;

    @ApiModelProperty(value = "结束列")
    private Integer endOffset;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "严重程度")
    private String severity;


}
