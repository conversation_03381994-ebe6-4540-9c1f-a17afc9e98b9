package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/7/14 4:06 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetWebHooksResp {

    private List<SonarWebHook> webhooks;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SonarWebHook {
        private String key;
        private String name;
        private String url;
    }

}
