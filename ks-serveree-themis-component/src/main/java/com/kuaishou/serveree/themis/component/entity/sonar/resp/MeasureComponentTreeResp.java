package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Paging;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarComponent;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-05-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MeasureComponentTreeResp {
    private Paging paging;
    private SonarComponent baseComponent;
    private List<SonarComponent> components;
}
