package com.kuaishou.serveree.themis.component.constant.process;

import java.util.Arrays;
import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/2/17 11:12 上午
 */
@AllArgsConstructor
public enum ProcessScannerType {

    JAVA_MAVEN_SCANNER(1, "java语言使用maven-scanner扫描", ""),
    SONAR_SCANNER(2, "sonar-scanner扫描", ""),
    CHECKSTYLE(3, "checkstyle扫描", ""),
    K_FORMAT(4, "c++ kformat扫描", "sonar-cplus"),
    SKY_EYE(5, "天眼扫描", "business-front"),
    KS_CPPCHECK(6, "kuaishou-cppcheck扫描", "ks-cppcheck"),
    ;

    @Getter
    private final int type;
    @Getter
    private final String desc;
    @Getter
    private final String source;

    private static final Map<String, ProcessScannerType> SOURCE_SCANNER_TYPE_MAP = Maps.newHashMap();

    static {
        Arrays.stream(ProcessScannerType.values()).forEach(o -> {
            // 这里会覆盖，但是覆盖的数据都是我不要的，所以就先这样搞，等需要的时候在加
            SOURCE_SCANNER_TYPE_MAP.put(o.getSource(), o);
        });
    }

    public static ProcessScannerType getByTokenSource(String source) {
        return SOURCE_SCANNER_TYPE_MAP.get(source);
    }

}
