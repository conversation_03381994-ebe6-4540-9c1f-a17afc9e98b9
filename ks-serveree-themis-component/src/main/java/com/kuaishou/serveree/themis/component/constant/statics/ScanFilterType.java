package com.kuaishou.serveree.themis.component.constant.statics;

import java.util.Arrays;
import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/10/30 5:26 下午
 */
@AllArgsConstructor
public enum ScanFilterType {

    DIRECT(1, "目录过滤"),
    FILE_EXTENSION(2, "文件扩展名过滤"),
    FILE_NAME(3, "指定文件名过滤"),
    FILE_EXTEND_INCLUDE(4, "指定文件扩展名扫描"),
    CUSTOM_EXCLUDE_STR(5, "自定义字符串扩展过滤"),
    IMPORT_ITEM_SCAN(6, "对import进行扫描"),
    ;

    @Getter
    private final int type;

    @Getter
    private final String desc;

    private static final Map<Integer, ScanFilterType> TYPE_ENUM = Maps.newHashMap();

    static {
        Arrays.stream(ScanFilterType.values()).forEach(filterType ->
                TYPE_ENUM.put(filterType.getType(), filterType)
        );
    }

    public static ScanFilterType getByType(int type) {
        return TYPE_ENUM.get(type);
    }

}
