package com.kuaishou.serveree.themis.component.config.mybatis;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.FileOutConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.TemplateConfig;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.google.common.collect.Maps;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

/**
 * Mybatis-Plus代码生成器
 * 快速生成 Entity、Mapper、Mapper XML、Service、Controller 等各个模块的代码
 */
public class MybatisPlusGenerator {


    /**
     * 数据源账号密码Kconf配置
     */
    public static final Kconf<HashMap<String, Map<String, String>>> KCONF_DATASOURCE_CONFIG =
            Kconfs.ofMapMap("qa.themis.dataSourceConfigMap", Maps.newHashMap(),
                    String.class, String.class, String.class).build();

    private static final String DATA_SOURCE_KEY = "db";
    private static final String DATA_SOURCE_USERNAME = "username";
    private static final String DATA_SOURCE_PASSWORD = "password";

    /**
     * RUN THIS
     * 参考配置
     */
    public static void main(String[] args) {

        // 这里修改为需要生成的表名
        String[] tablesName = new String[]{"mr_check_point_issue"};

        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        String projectPath = queryCurrentModelPath();
        mpg.setGlobalConfig(new GlobalConfig()//
                .setOutputDir(projectPath + "/src/main/java")//
                .setOpen(false)//
                .setAuthor("<EMAIL>")//
                .setSwagger2(true)//
                .setBaseResultMap(true)//
                .setIdType(IdType.AUTO)//
                .setServiceName("%sService")//
        );

        // 数据源配置
        mpg.setDataSource(new DataSourceConfig()//
                .setUrl("*********************************************************************************")//
                .setDriverName("com.mysql.jdbc.Driver")//
                .setUsername(KCONF_DATASOURCE_CONFIG.get().get(DATA_SOURCE_KEY).get(DATA_SOURCE_USERNAME))//
                .setPassword(KCONF_DATASOURCE_CONFIG.get().get(DATA_SOURCE_KEY).get(DATA_SOURCE_PASSWORD))//
        );



        mpg.setDataSource(new DataSourceConfig()//
                .setUrl("************************************************************************")//
                .setDriverName("com.mysql.jdbc.Driver")//
                .setUsername("ks_serveree_the_15596_v1_rw")//
                .setPassword("Kv11WIitxb7oPFgeyGKT4mXQ6a3CVNYw")//
        );

        // 包配置
        mpg.setPackageInfo(new PackageConfig()//
                .setParent("com.kuaishou.serveree.themis.component")//
                .setMapper("common.mappers")//
                .setEntity("common.entity")//
        );

        // 策略配置
        mpg.setStrategy(new StrategyConfig()//
                .setNaming(NamingStrategy.underline_to_camel)//
                .setColumnNaming(NamingStrategy.underline_to_camel)//
                .setEntityLombokModel(true)//
                .setInclude(tablesName)//
                .setControllerMappingHyphenStyle(true)//
        );

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {

            @Override
            public void initMap() {
                // to do nothing
            }
        };
        List<FileOutConfig> focList = new ArrayList<>();
        focList.add(new FileOutConfig("/templates/mapper.xml.ftl") {

            @Override
            public String outputFile(TableInfo tableInfo) {
                return projectPath + "/src/main/resources/mapper/" + tableInfo.getEntityName()
                        + "Mapper" + StringPool.DOT_XML;
            }
        });
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);
        mpg.setTemplate(new TemplateConfig().setXml(null));
        mpg.setTemplateEngine(new FreemarkerTemplateEngine());

        mpg.execute();
    }

    private static String queryCurrentModelPath() {
        String projectPath = System.getProperty("user.dir");
        String componentModule = "/ks-serveree-themis-component";
        if (!projectPath.contains(componentModule)) {
            projectPath += componentModule;
        }
        return projectPath;
    }

}
