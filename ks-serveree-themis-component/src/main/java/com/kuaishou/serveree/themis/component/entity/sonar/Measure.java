package com.kuaishou.serveree.themis.component.entity.sonar;

import com.kuaishou.serveree.themis.component.constant.sonar.Rating;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/21 3:28 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Measure {

    private Integer ncloc = 0;
    private Integer bugs = 0;
    private Integer violations = 0;
    private Integer criticalViolations = 0;
    private Integer vulnerabilities = 0;
    private Integer duplicatedBlocks = 0;
    private Rating reliabilityRating = Rating.A;
    private Rating securityRating = Rating.A;
    private Level alertStatus = Level.OK;

}
