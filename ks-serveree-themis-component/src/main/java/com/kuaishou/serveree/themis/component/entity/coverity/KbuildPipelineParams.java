package com.kuaishou.serveree.themis.component.entity.coverity;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.serveree.themis.component.common.entity.KbuildProject;
import com.kuaishou.serveree.themis.component.entity.kdev.NameAndValue;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-08-29
 *
 * kbuild 流水线的各个插件参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class KbuildPipelineParams {
    private String initName;
    private String baseTag;
    private String buildPath;
    private String multiGitUrl;
    private String beforeBuildCmd;
    private Boolean makeClangOpt;
    private List<NameAndValue> envs;

    public static KbuildPipelineParams convertFromKbuildProject(KbuildProject kbuildProject) {
        if (kbuildProject == null) {
            return null;
        }
        KbuildPipelineParams pipelineParams =
                JSONUtils.deserialize(kbuildProject.getBuildParams(), KbuildPipelineParams.class);
        if (pipelineParams == null) {
            return null;
        }
        // 后面这几个参数是新加的，之前的数据没有，这里反序列化后加一下默认值避免空指针就行，就不用刷库了
        if (Objects.isNull(pipelineParams.getMultiGitUrl())) {
            pipelineParams.setMultiGitUrl(StringUtils.EMPTY);
        }
        if (Objects.isNull(pipelineParams.getBeforeBuildCmd())) {
            pipelineParams.setBeforeBuildCmd(StringUtils.EMPTY);
        }
        if (Objects.isNull(pipelineParams.getMakeClangOpt())) {
            pipelineParams.setMakeClangOpt(Boolean.FALSE);
        }
        return pipelineParams;
    }
}
