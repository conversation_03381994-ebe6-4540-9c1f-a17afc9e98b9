package com.kuaishou.serveree.themis.component.entity.compile;

import org.springframework.http.HttpStatus;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-06
 */
@Data
public class CompileApiRsp<T> {

    private int status;
    private String message;
    private T data;
    private String traceId;
    private long timestamp;

    public static boolean success(CompileApiRsp rsp) {
        if (rsp == null) {
            return false;
        }
        return rsp.getStatus() == 0 || rsp.getStatus() == HttpStatus.OK.value();
    }
}
