package com.kuaishou.serveree.themis.component.config;

import org.gitlab.api.GitlabAPI;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.annotation.Kconfig;

/**
 * <AUTHOR>
 * @since 2020/10/29 4:01 下午
 */
@Configuration
public class GitConfig {

    @Kconfig("qa.themis.gitlabToken")
    private Kconf<String> gitlabToken;

    @Bean
    public GitlabAPI gitLabApi(@Value("${gitlab.host-url}") String gitlabHost) {

        return GitlabAPI.connect(gitlabHost, gitlabToken.get());
    }

}
