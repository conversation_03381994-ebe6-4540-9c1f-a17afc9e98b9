package com.kuaishou.serveree.themis.component.entity.process;

import java.util.List;

import org.gitlab.api.models.GitlabProject;

import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckConfig;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.service.check.custom.entity.CustomRulePair;
import com.kuaishou.serveree.themis.component.service.check.custom.entity.DiffInfo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/10/21 6:17 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExecutorContext {

    private Integer projectId;

    private String branch;

    private String commitId;

    private Integer mrId;

    private PCheckBase pCheckBase;

    private PCheckConfig pCheckConfig;

    private GitlabProject gitlabProject;

    private List<ProcessExecutionReferType> executorReferTypeList;

    private Integer baseCheckType;

    private Integer sponsorType;

    private List<DiffInfo> diffInfos;

    private List<CustomRulePair> customRulePairs;

    private String sponsor;

    private Integer kCheckLevel;

    private String mrDetailLink;
}
