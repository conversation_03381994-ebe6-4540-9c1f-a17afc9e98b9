package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;
import com.kuaishou.serveree.themis.component.entity.issue.DefaultIssue;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarFlow;
import com.kuaishou.serveree.themis.component.entity.sonar.TextRange;
import com.kuaishou.serveree.themis.component.utils.DateUtils;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 最新生效的issue表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "IssueSummary对象", description = "最新生效的issue表")
@TableName(autoResultMap = true)
public class IssueSummary implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "全局唯一issueId")
    private String issueUniqId;

    @ApiModelProperty(value = "代码路径")
    private String location;

    @ApiModelProperty(value = "问题作者")
    private String author;

    @ApiModelProperty(value = "创建时的commitId")
    private String createCommitId;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "所属的第三方标志")
    private String source;

    @ApiModelProperty(value = "类型 ")
    private String type;

    @ApiModelProperty(value = "级别")
    private String severity;

    @ApiModelProperty(value = "错误提示")
    private String message;

    @ApiModelProperty(value = "规则")
    private String rule;

    @ApiModelProperty(value = "问题状态 'OPEN','CLOSED','RESOLVED','TO_REVIEW'")
    private String status;

    @ApiModelProperty(value = "修复者")
    private String repairer;

    @ApiModelProperty(value = "修复时commitId")
    private String repairCommitId;

    @ApiModelProperty(value = "修复时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime repairTime;

    @ApiModelProperty(value = "1.存量 2.增量")
    private Integer effect;

    @ApiModelProperty(value = "第几行开始")
    private Integer startLine;

    @ApiModelProperty(value = "第几行结束")
    private Integer endLine;

    @ApiModelProperty(value = "起始偏移量")
    private Integer startOffset;

    @ApiModelProperty(value = "结束偏移量")
    private Integer endOffset;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "问题跳转链接")
    private String issueLink;

    @ApiModelProperty(value = "git跳转的链接")
    private String gitLink;

    @ApiModelProperty(value = "执行的类型")
    private Integer executionReferType;

    @ApiModelProperty(value = "git的projectId")
    private Integer gitProjectId;

    @ApiModelProperty(value = "git的分支名")
    private String gitBranch;

    private String sonarBranch;

    private String sonarIssueKey;

    /**
     * @link
     */
    private Integer scannerType;

    /**
     * 问题关联流数据 {@link SonarFlow} 的list json结构
     */
    private String flows;

    /**
     * 扫描模式
     */
    private Integer scanMode;

    private String commonIssueUniqId;

    private String commonIssueUniqIdCopy;
    private String issueUniqIdCopy;

    /**
     * 使用新的策略生成的issueUniqId
     */
    private String issueUniqIdV2;

    @ApiModelProperty(value = "解决类型，1 代码变更；2 规则集变更")
    private Integer repairType;

    public DefaultIssue newIssue() {
        DefaultIssue issue = new DefaultIssue();
        issue.setIssueId(id);
        issue.setIssueUniqId(issueUniqId);
        issue.setRule(rule);
        issue.setSeverity(severity);
        issue.setComponentKey(gitProjectId + ":" + location);
        issue.setProjectId(gitProjectId.longValue());
        issue.setAssignee(author);
        issue.setLine(endLine);
        issue.setTextRange(new TextRange(startLine, endLine, startOffset, endOffset));
        issue.setStatus(status);
        issue.setMessage(message);
        issue.setCreationDate(DateUtils.getStringDateFromLocalDateTime(createTime));
        issue.setUpdateDate(DateUtils.getStringDateFromLocalDateTime(gmtModified));
        issue.setType(type);
        return issue;
    }
}
