package com.kuaishou.serveree.themis.component.client.sonar.operations;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/7/14 3:36 下午
 */
@Component
public class SonarOperationFactory {

    @Autowired
    private CorpSonarOperations corpSonarOperations;

    @Autowired
    private List<SonarOperations> sonarOperationsList;

    public SonarOperations getRealOperations(String serverUrl) {
        if (StringUtils.isEmpty(serverUrl)) {
            return null;
        }
        // 如果是http的就将它转化为https
        if ((StringUtils.startsWithIgnoreCase(serverUrl, "http://"))) {
            serverUrl = serverUrl.replace("http://", "https://");
        }
        String finalServerUrl = serverUrl;
        Optional<SonarOperations> realSonarOperations = sonarOperationsList.stream()
                .filter(operation -> finalServerUrl.equals(operation.sonarApi().sonarUrl()))
                .findFirst();
        return realSonarOperations.orElse(corpSonarOperations);
    }
}
