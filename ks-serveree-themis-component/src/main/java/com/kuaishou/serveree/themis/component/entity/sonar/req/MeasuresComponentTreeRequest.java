package com.kuaishou.serveree.themis.component.entity.sonar.req;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/5/21 1:47 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MeasuresComponentTreeRequest {

    private String additionalFields;
    private Integer page;
    private Integer pageSize;
    private boolean asc;
    private String metricSort;
    private String s;
    private String metricSortFilter;
    @NotNull
    private String component;
    @NotEmpty
    private String metricKeys;
    private String strategy;
    private String branch;

}
