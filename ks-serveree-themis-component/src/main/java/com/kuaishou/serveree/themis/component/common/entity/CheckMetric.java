package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 度量基准表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckMetric对象", description = "度量基准表")
public class CheckMetric implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "度量key")
    private String metricKey;

    @ApiModelProperty(value = "度量描述")
    private String metricDesc;

    @ApiModelProperty(value = "详细描述")
    private String detail;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    private String language;

    private String scanner;

    private String iconUrl;

    private Integer iconType;

}
