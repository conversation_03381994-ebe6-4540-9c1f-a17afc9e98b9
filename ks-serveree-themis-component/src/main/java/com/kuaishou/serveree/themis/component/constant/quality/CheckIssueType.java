package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/25 3:18 下午
 */
@Slf4j
@AllArgsConstructor
public enum CheckIssueType implements FacetNode {

    BUG("BUG", "缺陷", true),
    VULNERABILITY("VULNERABILITY", "漏洞", true),
    CODE_SMELL("CODE_SMELL", "坏味道", false),
    DUPLICATION("DUPLICATION", "重复", false),
    COMPLEXITY("COMPLEXITY", "复杂度", false),
    ;

    @Getter
    private String type;
    @Getter
    private String desc;
    @Getter
    // 前端页面是否显示该元素
    private Boolean show;

    private static final Map<String, CheckIssueType> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(CheckIssueType.values()).forEach(checkIssueType -> {
            ENUM_MAP.put(checkIssueType.getType(), checkIssueType);
        });
    }

    public static boolean correctType(String type) {
        if (StringUtils.isEmpty(type)) {
            return false;
        }
        return ENUM_MAP.containsKey(type);
    }

    public static CheckIssueType getEnumByType(String type) {
        return ENUM_MAP.get(type);
    }

    /**
     * 页面展示（筛选）的类型
     * @return
     */
    public static List<String> queryTypeList() {
        return Stream.of(BUG, VULNERABILITY, CODE_SMELL).map(CheckIssueType::getType).collect(Collectors.toList());
    }

    @Override
    public CheckIssueType valueOfName(String name) {
        try {
            return valueOf(name);
        } catch (IllegalArgumentException e) {
            log.info("[{}]在枚举中不存在", name);
        }
        return null;
    }

    @Override
    public Boolean showOfName(String name) {
        FacetNode node = valueOfName(name);
        return Objects.nonNull(node) && node.getShow();
    }
}
