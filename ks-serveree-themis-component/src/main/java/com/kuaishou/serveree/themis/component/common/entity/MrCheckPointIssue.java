package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * MR检查点Issue表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "MrCheckPointIssue对象", description = "MR检查点Issue表")
public class MrCheckPointIssue implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "MR检查记录ID")
    private Long mrCheckId;

    /**
     * {@link com.kuaishou.serveree.themis.component.constant.kdev.MrCheckpointEnum}
     */
    @ApiModelProperty(value = "检查点名称")
    private String checkpointName;

    @ApiModelProperty(value = "类型 ")
    private String type;

    @ApiModelProperty(value = "级别")
    private String severity;

    @ApiModelProperty(value = "错误提示")
    private String message;

    @ApiModelProperty(value = "规则")
    private String rule;

    /**
     * {@link com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus}
     */
    @ApiModelProperty(value = "问题状态 'OPEN','CLOSED','RESOLVED','TO_REVIEW'")
    private String status;

    @ApiModelProperty(value = "代码路径")
    private String location;

    @ApiModelProperty(value = "是否卡点生效")
    private Boolean stuck;

    @ApiModelProperty(value = "第几行开始")
    private Integer startLine;

    @ApiModelProperty(value = "第几行结束")
    private Integer endLine;

    @ApiModelProperty(value = "起始偏移量")
    private Integer startOffset;

    @ApiModelProperty(value = "结束偏移量")
    private Integer endOffset;

    @ApiModelProperty(value = "问题作者")
    private String author;

    @ApiModelProperty(value = "MR内唯一issueId")
    private Long issueId;

    @ApiModelProperty(value = "MR内唯一issueId")
    private String issueUniqId;

    @ApiModelProperty(value = "问题作者")
    private String operator;

    @ApiModelProperty(value = "操作时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime operateTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;
}
