package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 快照度量表（全量）
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckMeasuresSnapshot对象", description = "快照度量表（全量）")
public class CheckMeasuresSnapshot implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "check_repo的主键id")
    private Long checkRepoId;

    @ApiModelProperty(value = "check_repo_branch的主键id")
    private Long checkRepoBranchId;

    @ApiModelProperty(value = "检查主表id")
    private Long baseId;

    @ApiModelProperty(value = "执行id")
    private Long executionId;

    @ApiModelProperty(value = "快照id")
    private Long snapshotId;

    @ApiModelProperty(value = "度量数据key")
    private String metricKey;

    @ApiModelProperty(value = "度量数据value")
    private String metricValue;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;


}
