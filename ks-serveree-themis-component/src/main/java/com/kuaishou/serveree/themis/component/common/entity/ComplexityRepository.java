package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ComplexityRepository对象", description = "")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComplexityRepository implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "项目在git的id")
    private Integer gitProjectId;

    @ApiModelProperty(value = "最新commit")
    private String commitId;

    @ApiModelProperty(value = "项目圈复杂度")
    private Integer cyclomaticComplexity;

    @ApiModelProperty(value = "项目认知复杂度")
    private Integer cognitiveComplexity;

    @ApiModelProperty(value = "执行记录的id")
    private Long execRecordId;
}
