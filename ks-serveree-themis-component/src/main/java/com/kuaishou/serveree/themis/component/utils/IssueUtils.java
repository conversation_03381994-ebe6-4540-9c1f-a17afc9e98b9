package com.kuaishou.serveree.themis.component.utils;


import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Nullable;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;

import com.github.phantomthief.util.TriFunction;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummaryBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.constant.platform.IssueGroupKeyEnum;
import com.kuaishou.serveree.themis.component.constant.plugin.IncrementType;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueType;
import com.kuaishou.serveree.themis.component.constant.quality.EffectEnum;
import com.kuaishou.serveree.themis.component.constant.quality.FacetEnum;
import com.kuaishou.serveree.themis.component.constant.quality.ScannerType;
import com.kuaishou.serveree.themis.component.entity.issue.DefaultIssue;
import com.kuaishou.serveree.themis.component.entity.issue.Flow;
import com.kuaishou.serveree.themis.component.entity.issue.Location;
import com.kuaishou.serveree.themis.component.entity.plugin.SonarNewPluginSettings;
import com.kuaishou.serveree.themis.component.entity.plugin.SonarNewPluginSettings.SonarNewPluginMeta;
import com.kuaishou.serveree.themis.component.entity.plugin.changedFiles.ChangedFilesResponse.ChangedFile;
import com.kuaishou.serveree.themis.component.entity.plugin.changedFiles.ChangedFilesResponse.ChangedFile.Fragment;
import com.kuaishou.serveree.themis.component.entity.sonar.BugMessage;
import com.kuaishou.serveree.themis.component.entity.sonar.Issue;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarFlow;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarLocation;
import com.kuaishou.serveree.themis.component.entity.sonar.TextRange;
import com.kuaishou.serveree.themis.component.service.plugin.SonarPluginService;
import com.kuaishou.serveree.themis.component.vo.request.IncrementChangedFilesRequest;
import com.kuaishou.serveree.themis.component.vo.request.sonar.PluginPipelineReportRequest;
import com.kuaishou.serveree.themis.component.vo.request.sonar.PluginPipelineReportRequest.PipelineIssue;
import com.kuaishou.serveree.themis.component.vo.response.Facet;
import com.kuaishou.serveree.themis.component.vo.response.Facet.FacetValue;
import com.kuaishou.serveree.themis.component.vo.response.IncrementChangedFilesResponse;

import cn.hutool.core.util.HashUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/2/18 11:48 上午
 */
@Slf4j
public class IssueUtils {

    private static final String MAVEN_SCANNER_NEW_ISSUE_LIST_LINK =
            "https://kdev.corp.kuaishou.com/web/codescan/project/issueList?executionReferType=1&kspBuildId=10471808"
                    + "&source=2";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssZ");

    private static final String ISSUE_KEY_SEPARATOR = "l";

    public static String genIssueUniqId(PCheckIssue pCheckIssue, Integer gitProjectId) {
        int lh = HashUtil.bkdrHash(pCheckIssue.getLocation());
        String ti = ""
                + pCheckIssue.getStartLine()
                + pCheckIssue.getEndLine()
                + pCheckIssue.getStartOffset()
                + pCheckIssue.getEndOffset();
        int rh = HashUtil.bkdrHash(pCheckIssue.getRule());
        int mh = HashUtil.bkdrHash(pCheckIssue.getMessage());
        return gitProjectId + ISSUE_KEY_SEPARATOR + lh + ISSUE_KEY_SEPARATOR + ti + ISSUE_KEY_SEPARATOR + rh + ISSUE_KEY_SEPARATOR + mh;
    }

    /**
     * 使用新策略生成issue唯一id
     * message = 基础message + 类.方法名 + 行源码
     */
    public static String genIssueUniqIdV2(PCheckIssue pCheckIssue, Integer gitProjectId, int referType) {
        // 非 sonar java issue，给空值
        if (!useIssueUniqIdV2(referType)) {
            return "";
        }
        String message = pCheckIssue.getMessage();
        try {
            BugMessage bugMessage = JSONUtils.deserialize(message, BugMessage.class);
            // message部分保留最基础的信息
            pCheckIssue.setMessage(bugMessage.getMessage());
            // gitProjectId + filePath + ruleKey + (class.method + sourcecode)
            int lh = HashUtil.bkdrHash(pCheckIssue.getLocation());
            int rh = HashUtil.bkdrHash(pCheckIssue.getRule());
            int mh = HashUtil.bkdrHash(bugMessage.getOwner() + bugMessage.getSource());
            return gitProjectId + ISSUE_KEY_SEPARATOR + lh + ISSUE_KEY_SEPARATOR + rh + ISSUE_KEY_SEPARATOR + mh;
        } catch (Exception e) {
            log.error("genIssueUniqIdV2 error, issue:{}", pCheckIssue);
            return "";
        }
    }

    public static String genIssueUniqId(CheckIssue checkIssue, Integer gitProjectId) {
        int lh = HashUtil.bkdrHash(checkIssue.getLocation());
        String ti = ""
                + checkIssue.getStartLine()
                + checkIssue.getEndLine()
                + checkIssue.getStartOffset()
                + checkIssue.getEndOffset();
        int rh = HashUtil.bkdrHash(checkIssue.getRule());
        int mh = HashUtil.bkdrHash(checkIssue.getMessage());
        return gitProjectId + ISSUE_KEY_SEPARATOR + lh + ISSUE_KEY_SEPARATOR + ti + ISSUE_KEY_SEPARATOR + rh + ISSUE_KEY_SEPARATOR + mh;
    }

    /**
     * 使用新策略生成issue唯一id
     * message = 基础message + 类.方法名 + 行源码
     */
    public static String genIssueUniqIdV2(CheckIssue checkIssue, Integer gitProjectId, int referType) {
        // 非 sonar java issue，给空值
        if (!useIssueUniqIdV2(referType)) {
            return "";
        }
        String message = checkIssue.getMessage();
        try {
            BugMessage bugMessage = JSONUtils.deserialize(message, BugMessage.class);
            // message部分保留最基础的信息
            checkIssue.setMessage(bugMessage.getMessage());
            // gitProjectId + filePath + ruleKey + (class.method + sourcecode)
            int lh = HashUtil.bkdrHash(checkIssue.getLocation());
            int rh = HashUtil.bkdrHash(checkIssue.getRule());
            int mh = HashUtil.bkdrHash(bugMessage.getOwner() + bugMessage.getSource());
            return gitProjectId + ISSUE_KEY_SEPARATOR + lh + ISSUE_KEY_SEPARATOR + rh + ISSUE_KEY_SEPARATOR + mh;
        } catch (Exception e) {
            log.error("genIssueUniqIdV2 error, issue:{}", checkIssue);
            return "";
        }
    }

    public static String genIssueUniqId(IssueSummaryBase issueSummaryBase) {
        int lh = HashUtil.bkdrHash(issueSummaryBase.getLocation());
        String ti = ""
                + issueSummaryBase.getStartLine()
                + issueSummaryBase.getEndLine()
                + issueSummaryBase.getStartOffset()
                + issueSummaryBase.getEndOffset();
        int rh = HashUtil.bkdrHash(issueSummaryBase.getRule());
        int mh = HashUtil.bkdrHash(issueSummaryBase.getMessage());
        return issueSummaryBase.getGitProjectId() + ISSUE_KEY_SEPARATOR + lh + ISSUE_KEY_SEPARATOR + ti
                + ISSUE_KEY_SEPARATOR + rh + ISSUE_KEY_SEPARATOR + mh;
    }

    public static String genIssueNewUniqId(IssueSummaryBase issueSummaryBase) {
        String split = "l";
        int lh = HashUtil.bkdrHash(issueSummaryBase.getLocation()); // 文件
        int rh = HashUtil.bkdrHash(issueSummaryBase.getRule());     // 规则
        int mh = HashUtil.bkdrHash(issueSummaryBase.getMessage());  // 报错内容（findbugs规则包含了类->方法的路径）
        return issueSummaryBase.getGitProjectId() + split + lh + split + rh + split + mh;
    }

    public static String genIssueUniqId(IssueSummary issueSummary) {
        int lh = HashUtil.bkdrHash(issueSummary.getLocation());
        String ti = ""
                + issueSummary.getStartLine()
                + issueSummary.getEndLine()
                + issueSummary.getStartOffset()
                + issueSummary.getEndOffset();
        int rh = HashUtil.bkdrHash(issueSummary.getRule());
        int mh = HashUtil.bkdrHash(issueSummary.getMessage());
        return issueSummary.getGitProjectId() + ISSUE_KEY_SEPARATOR + lh + ISSUE_KEY_SEPARATOR + ti
                + ISSUE_KEY_SEPARATOR + rh + ISSUE_KEY_SEPARATOR + mh;
    }

    public static Map<String, Long> convertMap(List<Map<String, Object>> typeGroupList) {
        Map<String, Long> map = Maps.newHashMap();
        for (Map<String, Object> countMap : typeGroupList) {
            String mapKey = "";
            Long count = 0L;
            for (Entry<String, Object> entry : countMap.entrySet()) {
                String key = entry.getKey();
                if ("count".equals(key)) {
                    count = (Long) entry.getValue();
                } else {
                    mapKey = (String) entry.getValue();
                }
            }
            map.put(mapKey, count);
        }
        return map;
    }

    public static Facet issueMapSort(Map<String, Long> issueMap, IssueGroupKeyEnum issueGroup) {
        List<FacetValue> facetValues = issueMap.entrySet().stream()
                .map(o -> FacetValue.builder()
                        .val(o.getKey())
                        .desc(o.getKey())
                        .count(o.getValue().intValue())
                        .build())
                .sorted((o1, o2) -> o2.getCount() - o1.getCount())
                .collect(Collectors.toList());
        return Facet.builder()
                .property(issueGroup.getKey())
                .propertyDesc(issueGroup.getDes())
                .values(facetValues)
                .build();
    }

    public static List<Facet> buildIssueFacets(Map<String, Long> typeMap, Map<String, Long> severityMap,
            Map<String, Long> statusMap) {
        List<Facet> facets = Lists.newArrayList();
        //类型
        Facet type = Facet.builder()
                .property(FacetEnum.types.getCode())
                .propertyDesc(FacetEnum.types.getDesc())
                .values(Stream.of(
                                Facet.FacetValue.builder()
                                        .val(CheckIssueType.BUG.getType())
                                        .desc(CheckIssueType.BUG.getDesc())
                                        .count(Objects.isNull(typeMap.get(CheckIssueType.BUG.getType())) ? 0
                                                                                                         : typeMap
                                                       .get(CheckIssueType.BUG.getType()).intValue()).build(),
                                Facet.FacetValue.builder()
                                        .val(CheckIssueType.VULNERABILITY.getType())
                                        .desc(CheckIssueType.VULNERABILITY.getDesc())
                                        .count(Objects.isNull(typeMap.get(CheckIssueType.VULNERABILITY.getType())) ? 0
                                                                                                                   :
                                               typeMap
                                                       .get(CheckIssueType.VULNERABILITY.getType()).intValue()).build(),
                                Facet.FacetValue.builder()
                                        .val(CheckIssueType.CODE_SMELL.getType())
                                        .desc(CheckIssueType.CODE_SMELL.getDesc())
                                        .count(Objects.isNull(typeMap.get(CheckIssueType.CODE_SMELL.getType())) ? 0
                                                                                                                :
                                               typeMap
                                                       .get(CheckIssueType.CODE_SMELL.getType()).intValue()).build())
                        .collect(Collectors.toList()))
                .build();
        facets.add(type);
        //严重程度
        Facet severity = Facet.builder()
                .property(FacetEnum.severities.getCode())
                .propertyDesc(FacetEnum.severities.getDesc())
                .values(Stream.of(
                        Facet.FacetValue.builder()
                                .val(CheckIssueSeverity.SERIOUS.getKey())
                                .desc(CheckIssueSeverity.SERIOUS.getDesc())
                                .count(Objects.isNull(severityMap.get(CheckIssueSeverity.SERIOUS.getKey())) ? 0
                                                                                                            :
                                       severityMap
                                               .get(CheckIssueSeverity.SERIOUS.getKey()).intValue()).build(),
                        Facet.FacetValue.builder()
                                .val(CheckIssueSeverity.MAJOR.getKey())
                                .desc(CheckIssueSeverity.MAJOR.getDesc())
                                .count(Objects.isNull(severityMap.get(CheckIssueSeverity.MAJOR.getKey())) ? 0
                                                                                                          : severityMap
                                               .get(CheckIssueSeverity.MAJOR.getKey()).intValue()).build(),
                        Facet.FacetValue.builder()
                                .val(CheckIssueSeverity.COMMON.getKey())
                                .desc(CheckIssueSeverity.COMMON.getDesc())
                                .count(Objects.isNull(severityMap.get(CheckIssueSeverity.COMMON.getKey())) ? 0
                                                                                                           : severityMap
                                               .get(CheckIssueSeverity.COMMON.getKey()).intValue()).build(),
                        Facet.FacetValue.builder()
                                .val(CheckIssueSeverity.NOT_INDUCED.getKey())
                                .desc(CheckIssueSeverity.NOT_INDUCED.getDesc())
                                .count(Objects.isNull(severityMap.get(CheckIssueSeverity.NOT_INDUCED.getKey())) ? 0
                                                                                                                :
                                       severityMap
                                               .get(CheckIssueSeverity.NOT_INDUCED.getKey()).intValue()).build()
                ).collect(Collectors.toList())).build();
        facets.add(severity);
        //状态，页面展示仅保留开启状态
        if (!MapUtils.isEmpty(statusMap)) {
            facets.add(Facet.builder()
                    .property(FacetEnum.statuses.getCode())
                    .propertyDesc(FacetEnum.statuses.getDesc())
                    .values(Stream.of(
                            Facet.FacetValue.builder()
                                    .val(CheckIssueStatus.OPEN.getStatus())
                                    .desc(CheckIssueStatus.OPEN.getDesc())
                                    .count(Objects.isNull(statusMap.get(CheckIssueStatus.OPEN.getStatus()))
                                           ? 0 : statusMap.get(CheckIssueStatus.OPEN.getStatus()).intValue())
                                    .build(),
                            Facet.FacetValue.builder()
                                    .val(CheckIssueStatus.RE_OPEN.getStatus())
                                    .desc(CheckIssueStatus.RE_OPEN.getDesc())
                                    .count(Objects.isNull(statusMap.get(CheckIssueStatus.RE_OPEN.getStatus()))
                                           ? 0 : statusMap.get(CheckIssueStatus.RE_OPEN.getStatus()).intValue())
                                    .build(),
                            Facet.FacetValue.builder()
                                    .val(CheckIssueStatus.CLOSED.getStatus())
                                    .desc(CheckIssueStatus.CLOSED.getDesc())
                                    .count(Objects.isNull(statusMap.get(CheckIssueStatus.CLOSED.getStatus()))
                                           ? 0 : statusMap.get(CheckIssueStatus.CLOSED.getStatus()).intValue())
                                    .build(),
                            Facet.FacetValue.builder()
                                    .val(CheckIssueStatus.TO_REVIEW.getStatus())
                                    .desc(CheckIssueStatus.TO_REVIEW.getDesc())
                                    .count(Objects.isNull(statusMap.get(CheckIssueStatus.TO_REVIEW.getStatus()))
                                           ? 0 : statusMap.get(CheckIssueStatus.TO_REVIEW.getStatus()).intValue())
                                    .build()
                    ).collect(Collectors.toList())).build());
        }
        return facets;
    }

    public static DefaultIssue convertCheckIssue2DefaultIssue(CheckIssue checkIssue) {
        return DefaultIssue.builder()
                .assignee(checkIssue.getAuthor())
                .creationDate(DateUtils.getStringDateFromLocalDateTime(checkIssue.getCreateTime()))
                .updateDate(DateUtils.getStringDateFromLocalDateTime(checkIssue.getGmtModified()))
                .componentKey(checkIssue.getSonarProjectKey() + ":" + checkIssue.getLocation())
                .issueUniqId(checkIssue.getIssueUniqId())
                .rule(checkIssue.getRule())
                .line(checkIssue.getEndLine())
                .message(checkIssue.getMessage())
                .severity(checkIssue.getSeverity())
                .status(checkIssue.getStatus())
                .textRange(new TextRange(checkIssue.getStartLine(), checkIssue.getEndLine(),
                        checkIssue.getStartOffset(), checkIssue.getEndOffset()))
                .type(checkIssue.getType())
                .projectId(checkIssue.getGitProjectId().longValue())
                .build();
    }

    public static List<PCheckIssue> initPCheckIssues(List<Issue> allIssues, PCheckBase pCheckBase,
            PCheckExecution pCheckExecution, String key) {
        LocalDateTime now = LocalDateTime.now();
        List<PCheckIssue> pCheckIssueList = Lists.newArrayList();
        String issueUrl = SpringUtil.getProperty("maven-scanner-new.issue-list-url");
        String stuckPointSettingStr = pCheckBase.getStuckPointSettings();
        SonarNewPluginSettings stuckPointSetting = JSONUtils.deserialize(stuckPointSettingStr, SonarNewPluginSettings.class);
        for (Issue issue : allIssues) {
            PCheckIssue checkIssue = new PCheckIssue();
            TextRange textRange = issue.getTextRange();
            if (textRange != null) {
                checkIssue.setEndLine(textRange.getEndLine());
                checkIssue.setStartLine(textRange.getStartLine());
                checkIssue.setStartOffset(textRange.getStartOffset());
                checkIssue.setEndOffset(textRange.getEndOffset());
            } else {
                checkIssue.setStartLine(issue.getLine());
                checkIssue.setEndLine(issue.getLine());
            }
            checkIssue.setMessage(issue.getMessage());
            checkIssue.setPBaseId(pCheckBase.getId());
            String author = issue.getAuthor();
            checkIssue.setAuthor(author.contains("@") ? author.split("@")[0] : author);
            checkIssue.setCreateTime(LocalDateTime.parse(issue.getCreationDate(), DATE_TIME_FORMATTER));
            checkIssue.setRule(issue.getRule());
            checkIssue.setSeverity(CheckIssueSeverity.getKeyBySonarStatus(issue.getSeverity()));
            checkIssue.setStatus(issue.getStatus());
            checkIssue.setType(issue.getType());
            // 这里直接写死
            checkIssue.setSource("admin-check");
            checkIssue.setGmtCreate(now);
            checkIssue.setGmtModified(now);
            checkIssue.setPExecutionId(pCheckExecution.getId());
            checkIssue.setLocation(issue.getComponent().replace(key + ":", ""));
            checkIssue.setExecutionReferType(pCheckExecution.getReferType());
            checkIssue.setEffect(EffectEnum.INCREMENT.getType());
            checkIssue.setGitLink(getGitLink(pCheckBase, checkIssue));
            checkIssue.setIssueUniqIdV2(IssueUtils.genIssueUniqIdV2(checkIssue, pCheckBase.getProjectId(),
                    pCheckExecution.getReferType()));
            checkIssue.setIssueUniqId(genIssueUniqId(checkIssue, pCheckBase.getProjectId()));
            checkIssue.setIssueLink(
                    String.format(issueUrl, pCheckBase.getKspBuildId()) + "&issueKeys=" + checkIssue.getIssueUniqId()
            );
            checkIssue.setSonarIssueKey(issue.getKey());
            checkIssue.setExecutionReferType(pCheckExecution.getReferType());
            checkIssue.setSonarBranch(pCheckBase.getSonarBranch());
            checkIssue.setFlows(JSONUtils.serialize(Lists.newArrayList()));
            if (CollectionUtils.isNotEmpty(issue.getFlows())) {
                fillUpFlows(issue, key, checkIssue);
            }
            checkIssue.setScannerType(pCheckBase.getScannerType());
            checkIssue.setCommonIssueUniqId(checkIssue.getIssueUniqId());
            checkIssue.setInDiff(true);
            checkIssue.setValidStuck(validStuck(stuckPointSetting, checkIssue.getType(), checkIssue.getSeverity()));
            pCheckIssueList.add(checkIssue);
        }
        return pCheckIssueList;
    }

    private static String getGitLink(PCheckBase pCheckBase, PCheckIssue checkIssue) {
        return GitUtils.getGitLink(GitUtils.getHttpsUrl(pCheckBase.getRepoUrl()), pCheckBase.getBranch(),
                checkIssue.getLocation(), checkIssue.getStartLine(), checkIssue.getEndLine());
    }

    public static String getGitLink(CheckBase checkBase, CheckIssue checkIssue) {
        return GitUtils.getGitLink(GitUtils.getHttpsUrl(checkBase.getRepoUrl()), checkBase.getBranch(),
                checkIssue.getLocation(), checkIssue.getStartLine(), checkIssue.getEndLine());
    }

    private static void fillUpFlows(Issue issue, String key, PCheckIssue checkIssue) {
        List<Location> locations = com.google.common.collect.Lists.newArrayList();
        for (SonarFlow sonarFlow : issue.getFlows()) {
            List<SonarLocation> sonarLocations = sonarFlow.getLocations();
            for (SonarLocation sonarLocation : sonarLocations) {
                Location location = new Location();
                location.setLocation(sonarLocation.getComponent().replace(key + ":", ""));
                location.setMsg(sonarLocation.getMsg());
                TextRange locationTextRange = sonarLocation.getTextRange();
                if (locationTextRange != null) {
                    location.setEndLine(locationTextRange.getEndLine());
                    location.setStartLine(locationTextRange.getStartLine());
                    location.setStartOffset(locationTextRange.getStartOffset());
                    location.setEndOffset(locationTextRange.getEndOffset());
                }
                locations.add(location);
            }
        }
        List<Flow> flows = com.google.common.collect.Lists.newArrayList();
        flows.add(Flow.builder().locations(locations).build());
        checkIssue.setFlows(JSONUtils.serialize(flows));
    }

    public static List<PCheckIssue> initPCheckIssues(PluginPipelineReportRequest request, PCheckBase pCheckBase,
            PCheckExecution pCheckExecution, SonarNewPluginSettings stuckPointSetting,
            @Nullable TriFunction<PipelineIssue, PCheckIssue, Integer, String> issueUniqIdGenerator) {
        LocalDateTime now = LocalDateTime.now();
        String issueUrl = SpringUtil.getProperty("scan-platform.pipeline-issue-list-url");
        List<PCheckIssue> pCheckIssueList = Lists.newArrayList();
        for (PipelineIssue pipelineIssue : request.getIssues()) {
            PCheckIssue pCheckIssue = PCheckIssue.builder()
                    .author(pipelineIssue.getAuthor())
                    .startLine(pipelineIssue.getStartLine())
                    .endLine(pipelineIssue.getEndLine())
                    .startOffset(pipelineIssue.getStartOffset())
                    .endOffset(pipelineIssue.getEndOffset())
                    .location(pipelineIssue.getLocation())
                    .executionReferType(pCheckExecution.getReferType())
                    .message(pipelineIssue.getMessage())
                    .type(pipelineIssue.getType())
                    .severity(pipelineIssue.getSeverity())
                    .status("OPEN")
                    .scannerType(ScannerType.MAVEN_SONAR.getCode())
                    .pBaseId(pCheckExecution.getPBaseId())
                    .pExecutionId(pCheckExecution.getId())
                    .rule(pipelineIssue.getRuleKey())
                    .gmtCreate(now)
                    .gmtModified(now)
                    .sonarBranch("")
                    .sonarIssueKey("")
                    .flows("")
                    .createCommitId("")
                    .source("")
                    .createTime(now)
                    .repairCommitId("")
                    .repairer("")
                    .repairTime(0L)
                    .effect(1)
                    .commonIssueUniqIdCopy("")
                    .issueUniqIdCopy("")
                    .inDiff(true) // 默认值
                    .validStuck(validStuck(stuckPointSetting, pipelineIssue.getType(), pipelineIssue.getSeverity()))
                    .build();
            pCheckIssue.setGitLink(getGitLink(pCheckBase, pCheckIssue));
            // 先新生成新id，并恢复message
            pCheckIssue.setIssueUniqIdV2(IssueUtils.genIssueUniqIdV2(pCheckIssue, pCheckBase.getProjectId(),
                    pCheckExecution.getReferType()));
            if (issueUniqIdGenerator != null) {
                pCheckIssue.setIssueUniqId(issueUniqIdGenerator.apply(pipelineIssue, pCheckIssue, pCheckBase.getProjectId()));
            } else {
                pCheckIssue.setIssueUniqId(IssueUtils.genIssueUniqId(pCheckIssue, pCheckBase.getProjectId()));
            }
            pCheckIssue.setCommonIssueUniqId(pCheckIssue.getIssueUniqId());
            pCheckIssue.setIssueLink(
                    String.format(issueUrl, pCheckExecution.getReferType(), pCheckBase.getKspBuildId()) + "&issueKeys=" + getIssueUniqkey(pCheckIssue)
            );
            pCheckIssueList.add(pCheckIssue);
        }
        filterAndResetIssuesInDiff(pCheckIssueList, request);
        return pCheckIssueList;
    }

    /**
     * 过滤diff行内的issue，并重置相关字段
     */
    private static void filterAndResetIssuesInDiff(List<PCheckIssue> pCheckIssueList, PluginPipelineReportRequest request) {
        if (pCheckIssueList.isEmpty()) {
            return;
        }
        // 非增量扫描，返回
        if (!request.getMrStuck() && !request.getIncrementMode()) {
            return;
        }
        // 获取diff列表
        IncrementChangedFilesResponse incrementResp = SpringUtil.getBean(SonarPluginService.class).getChangedFiles(
                IncrementChangedFilesRequest.builder()
                        .gitProjectId(request.getGitProjectId())
                        .branch(request.getGitBranch())
                        .increment(true)
                        .mrId(request.getMrId().intValue())
                        .currentCommitId(request.getCommitId())
                        .sourceCommitId(request.getSourceCommitId())
                        .kspBuildId(request.getKspBuildId())
                        .localBuildId(request.getLocalBuildId())
                        .build());
        if (incrementResp == null || incrementResp.getChangedFiles() == null) {
            log.error("扫描模式是行增量，但获取不到diff信息，kspBuildId: {}", request.getKspBuildId());
            throw new RuntimeException("扫描模式是行增量，但获取不到diff信息，kspBuildId:" + request.getKspBuildId());
        }
        // 聚合 <文件 --> 新增块列表>
        Map<String, List<Fragment>> fileFramentsMap = incrementResp.getChangedFiles().stream()
                .collect(Collectors.toMap(
                        ChangedFile::getFilePath,
                        f -> CollectionUtils.isEmpty(f.getFragments()) ? Collections.emptyList() : f.getFragments(),
                        (existing, replacement) -> existing
                )
        );
        // 只有 增量扫描且是行维度增量 或 MR触发且是只关注diff内Issue，才需要进行 diff行判断，否则只需要判断是否在增量文件内
        boolean needJudgeInDiff = (request.getMrStuck() && request.getOnlyDiffIssue())
                || (request.getIncrementMode() && request.getIncrementType() == IncrementType.LINE.getType());
        // 遍历issue
        for (PCheckIssue pCheckIssue : pCheckIssueList) {
            // issue所在文件的diff片段
            List<Fragment> fragmentList = fileFramentsMap.get(pCheckIssue.getLocation());
            Integer endLine = pCheckIssue.getEndLine();
            boolean inDiff = false;
            // 此issue所在文件是变更文件
            if (fragmentList != null) {
                // 不需要判断diff行
                if (!needJudgeInDiff) {
                    inDiff = true;
                } else {
                    int l = 0, r = fragmentList.size();
                    while (l < r) {
                        int m = l + r >> 1;
                        if (fragmentList.get(m).getEndLine() < endLine) {
                            l++;
                        } else if (fragmentList.get(m).getStartLine() > endLine) {
                            r = m;
                        } else {
                            inDiff = true;
                            break;
                        }
                    }
                }
            }
            // 不在diff内，卡点不生效
            if (!inDiff) {
                pCheckIssue.setInDiff(false);
                pCheckIssue.setValidStuck(false);
            }
        }
    }

    /**
     * 判断当前issue是否用于卡点（符合卡点设置）
     */
    public static boolean validStuck(SonarNewPluginSettings stuckPointSetting, String issueType, String issueSeverity) {
        // 代码异味不作为卡点
        if (CheckIssueType.CODE_SMELL.getType().equals(issueType)) {
            return false;
        }
        SonarNewPluginMeta bugConfig = stuckPointSetting.getBugMeta();
        SonarNewPluginMeta vulConfig = stuckPointSetting.getVulnerabilityMeta();
        // bug 类型的问题
        if (CheckIssueType.BUG.getType().equals(issueType)) {
            return validStuck(issueSeverity, bugConfig);
        }
        // Vulnerability 类型问题
        return validStuck(issueSeverity, vulConfig);
    }

    /**
     * 判断当前issue是否用于卡点（符合卡点设置）
     */
    private static boolean validStuck(String issueSeverity, SonarNewPluginMeta stuckConfig) {
        // 未开启卡点
        if (!stuckConfig.isStuckSwitch()) {
            return false;
        }
        // 卡点等级
        String stuckSeverity = stuckConfig.getSeverity();
        List<String> aboveSeverities = new ArrayList<>();
        switch (stuckSeverity) {
            case "major":
                aboveSeverities = CheckIssueSeverity.getByAboveLevel(CheckIssueSeverity.COMMON.getKey());
                break;
            case "critical":
                aboveSeverities = CheckIssueSeverity.getByAboveLevel(CheckIssueSeverity.MAJOR.getKey());
                break;
            case "blocker":
                aboveSeverities = CheckIssueSeverity.getByAboveLevel(CheckIssueSeverity.SERIOUS.getKey());
                break;
            default:
                break;
        }
        // 此问题等级是否卡点等级及之上
        return aboveSeverities.contains(issueSeverity);
    }

    /**
     * 去除重复issue
     */
    public static List<CheckIssue> filterDuplicateCheckIssue(List<CheckIssue> issueList) {
        // 先用旧key去重
        Set<String> issueUniqIdSet = new HashSet<>();
        List<CheckIssue> list = new ArrayList<>();
        for (CheckIssue checkIssue : issueList) {
            if (!issueUniqIdSet.add(checkIssue.getIssueUniqId())) {
                continue;
            }
            list.add(checkIssue);
        }
        // 其他扫描器，不需要再次通过 issue_uniq_v2 去重
        if (list.isEmpty() || StringUtils.isBlank(list.get(0).getIssueUniqIdV2())) {
            return list;
        }
        // 同一key的过个issue编号
        Map<String, Integer> issue2IdxMap = new HashMap<>();
        return list.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>((o1, o2) -> {
                    // 比较新的key
                    String id1 = o1.getIssueUniqIdV2();
                    String id2 = o2.getIssueUniqIdV2();
                    int cmp = id1.compareTo(id2);
                    if (cmp != 0) {
                        return cmp;
                    }
                    // 如果是同行同列（重复的规则 或 同一行发现两个问题，因为本行源代码一致导致新key也一致）
                    if (Objects.equals(o1.getStartLine(), o2.getStartLine())
                        && Objects.equals(o1.getStartOffset(), o2.getStartOffset())) {
                        return 0;
                    }
                    // 不同行（比如同一方法内 重复的两行）给key后面加上编号
                    // case: if (realAmount.compareTo(new BigDecimal(AMOUNT_LEVEL_2)) > -1
                    // && realAmount.compareTo(new BigDecimal(AMOUNT_LEVEL_3)) == -1)
                    Integer idx = issue2IdxMap.getOrDefault(o1.getIssueUniqIdV2(), 0);
                    o1.setIssueUniqIdV2(id1 + ++idx);
                    o2.setIssueUniqIdV2(id2 + ++idx);
                    issue2IdxMap.put(id1, idx);
                    return -1;
                })),
                ArrayList::new));
    }

    /**
     * 去除重复issue
     */
    public static List<PCheckIssue> filterDuplicatePCheckIssue(List<PCheckIssue> issueList) {
        // 先用旧key去重
        Set<String> issueUniqIdSet = new HashSet<>();
        List<PCheckIssue> list = new ArrayList<>();
        for (PCheckIssue pCheckIssue : issueList) {
            if (!issueUniqIdSet.add(pCheckIssue.getIssueUniqId())) {
                continue;
            }
            list.add(pCheckIssue);
        }
        // 其他扫描器，不需要再次通过 issue_uniq_v2 去重
        if (list.isEmpty() || StringUtils.isBlank(list.get(0).getIssueUniqIdV2())) {
            return list;
        }
        // 同一key的多个issue编号
        Map<String, Integer> issue2IdxMap = new HashMap<>();
        return list.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>((o1, o2) -> {
                    // 比较新的key
                    String id1 = o1.getIssueUniqIdV2();
                    String id2 = o2.getIssueUniqIdV2();
                    int cmp = id1.compareTo(id2);
                    // 新key不同
                    if (cmp != 0) {
                        return cmp;
                    }
                    // 新key相同
                    // 如果是同行同列（重复的规则 或 同一行发现两个问题，因为本行源代码一致导致新key也一致）
                    if (Objects.equals(o1.getStartLine(), o2.getStartLine())
                            && Objects.equals(o1.getStartOffset(), o2.getStartOffset())) {
                        return 0;
                    }
                    // 不同行（比如同一方法内 重复的两行）给key后面加上编号
                    // case: if (realAmount.compareTo(new BigDecimal(AMOUNT_LEVEL_2)) > -1
                    // && realAmount.compareTo(new BigDecimal(AMOUNT_LEVEL_3)) == -1)
                    Integer idx = issue2IdxMap.getOrDefault(o1.getIssueUniqIdV2(), 0);
                    o1.setIssueUniqIdV2(id1 + ++idx);
                    o2.setIssueUniqIdV2(id1 + ++idx);
                    issue2IdxMap.put(id1, idx);
                    return -1;
                })),
                ArrayList::new));
    }



    /**
     * 处理 在同一方法内，源代码相同，不同行的issue
     * 使用 issuUniqId
     */
    public static List<CheckIssue> processIssuesWithSameSourceInSameFunc(List<CheckIssue> issueList) {
        return processIssuesWithSameSourceInSameFunc(issueList,
                CheckIssue::getIssueUniqId,
                i -> CommonUtils.getIssueLocationString(i.getStartLine(), i.getStartOffset(), i.getEndLine(), i.getEndOffset()),
                CheckIssue::setIssueUniqId);
    }

    public static <T> List<T> processIssuesWithSameSourceInSameFunc(List<T> issueList,
            Function<T, String> keyExtractor, Function<T, String> positionLocator, BiConsumer<T, String> keyUpdater) {

        if (CollectionUtils.isEmpty(issueList)) {
            return Lists.newArrayList();
        }

        // 步骤1: 去重（保证每个原始键+位置唯一）
        Map<String, T> uniqueMap = issueList.stream()
                .collect(Collectors.toMap(
                        issue -> keyExtractor.apply(issue) + "#" + positionLocator.apply(issue),
                        issue -> issue,
                        (existing, replacement) -> existing
                ));

        // 步骤2: 按原始键分组 → 组内排序 → 编号
        return uniqueMap.values().stream()
                .collect(Collectors.groupingBy(
                        keyExtractor,
                        Collectors.toList()
                ))
                .entrySet().stream()
                .flatMap(entry -> {
                    String key = entry.getKey();
                    List<T> list = entry.getValue();

                    // 组内按 position 排序
                    list.sort(Comparator.comparing(positionLocator));

                    // 生成序号（第一个元素保留原键，后续加编号）
                    AtomicInteger counter = new AtomicInteger(0);
                    return list.stream().peek(issue -> {
                        int count = counter.getAndIncrement();
                        if (count > 0) {
                            keyUpdater.accept(issue, key + "_" + count);
                        }
                    });
                })
                .collect(Collectors.toList());
    }


    /**
     * 判断指定扫描器扫描出的issue是否使用新策略生成的key
     * @param executionReferType
     */
    public static boolean useIssueUniqIdV2(int executionReferType) {
        return ProcessExecutionReferType.MAVEN_SONAR.getType() == executionReferType
                || ProcessExecutionReferType.IDEA_SONAR_PLUGIN.getType() == executionReferType;
    }

    public static String getIssueUniqkey(PCheckIssue issue) {
        return StringUtils.isNotBlank(issue.getIssueUniqIdV2()) ? issue.getIssueUniqIdV2() : issue.getIssueUniqId();
    }
    public static String getIssueUniqkey(CheckIssue issue) {
        return StringUtils.isNotBlank(issue.getIssueUniqIdV2()) ? issue.getIssueUniqIdV2() : issue.getIssueUniqId();
    }
    public static String getIssueUniqkey(IssueSummary summary) {
        return StringUtils.isNotBlank(summary.getIssueUniqIdV2()) ? summary.getIssueUniqIdV2() : summary.getIssueUniqId();
    }
    public static String getIssueUniqkey(IssueSummaryBase summaryBase) {
        return StringUtils.isNotBlank(summaryBase.getIssueUniqIdV2())
               ? summaryBase.getIssueUniqIdV2() : summaryBase.getIssueUniqId();
    }

    /**
     * 从issueKey中解析出gitProjectId
     */
    public static Integer parseGitProjectId(String issueKey) {
        try {
            String[] split = issueKey.split(ISSUE_KEY_SEPARATOR);
            if (Character.isDigit(split[0].charAt(0))) {
                return Integer.parseInt(split[0]);
            }
            return Integer.parseInt(split[1]);
        } catch (Exception e) {
            return 0;
        }
    }
}
