package com.kuaishou.serveree.themis.component.service.platform.impl;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.INVALID_PARAMS;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.gitlab.api.models.GitlabBranch;
import org.gitlab.api.models.GitlabProject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.git.SelfGitApi;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranchProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckRule;
import com.kuaishou.serveree.themis.component.common.entity.CheckRuleLabelRelation;
import com.kuaishou.serveree.themis.component.common.entity.CheckSnapshot;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.CheckProfileType;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformLanguageEnum;
import com.kuaishou.serveree.themis.component.entity.git.GetGitBranchRequest;
import com.kuaishou.serveree.themis.component.entity.git.GetUserProjectsRequest;
import com.kuaishou.serveree.themis.component.entity.kdev.NameAndValue;
import com.kuaishou.serveree.themis.component.entity.platform.CheckRuleListCondition;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoLanguageService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.CheckRuleLabelRelationService;
import com.kuaishou.serveree.themis.component.service.CheckRuleService;
import com.kuaishou.serveree.themis.component.service.CheckSnapshotService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.openapi.IhrOpenApi;
import com.kuaishou.serveree.themis.component.service.openapi.IhrOpenApi.IhrUserInfo;
import com.kuaishou.serveree.themis.component.service.openapi.IhrOpenApi.IhrUserListResponse;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCommonService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.vo.request.GitBranchListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.GitRepoSearchRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.LabelListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.LanguageListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ScannerListRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.CreateLanguageListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.GitBranchListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.GitBranchListResponseVo.GitBranchInfo;
import com.kuaishou.serveree.themis.component.vo.response.GitProjectListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.GitProjectListResponseVo.GitProjectInfo;
import com.kuaishou.serveree.themis.component.vo.response.GitRepoSearchResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.GitRepoSearchResponseVo.GitRepoInfo;
import com.kuaishou.serveree.themis.component.vo.response.LabelListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.LabelListResponseVo.LabelDetailVo;
import com.kuaishou.serveree.themis.component.vo.response.LanguageListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.LanguageListResponseVo.LanguageInfo;
import com.kuaishou.serveree.themis.component.vo.response.ScannerListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ScannerListResponseVo.PlatformScannerInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/9/1 9:21 下午
 */
@Slf4j
@Service
public class PlatformCommonServiceImpl implements PlatformCommonService {

    @Autowired
    private CheckSnapshotService checkSnapshotService;

    @Autowired
    private CheckRuleService checkRuleService;

    @Autowired
    private CheckRepoService checkRepoService;

    @Autowired
    private CheckRuleLabelRelationService checkRuleLabelRelationService;

    @Autowired
    private SelfGitApi selfGitApi;

    @Autowired
    private CheckRepoLanguageService checkRepoLanguageService;

    @Autowired
    private CheckRepoBranchProfileService checkRepoBranchProfileService;

    @Autowired
    private IssueSummaryService issueSummaryService;

    @Autowired
    private IhrOpenApi ihrOpenApi;
    @Autowired
    private GitOperations gitOperations;

    @Override
    public Pair<List<Long>, CheckSnapshot> changeSnapshot(CheckBase checkBase, Long checkExecutionId) {
        Long checkRepoId = checkBase.getCheckRepoId();
        Long checkRepoBranchId = checkBase.getCheckRepoBranchId();
        List<CheckSnapshot> checkSnapshots = checkSnapshotService.listSnapshot(checkRepoBranchId);
        Pair<List<Long>, List<CheckSnapshot>> baseSnapshotsPair = this.filterCheckSnapshots(checkSnapshots);
        List<Long> needDelLiveBaseId = baseSnapshotsPair.getLeft();
        List<CheckSnapshot> needUpdateSnapshots = baseSnapshotsPair.getRight();
        if (CollectionUtils.isNotEmpty(needUpdateSnapshots)) {
            checkSnapshotService.updateBatchById(needUpdateSnapshots);
        }
        // 保存最新的快照数据
        CheckSnapshot checkSnapshot = new CheckSnapshot().setBaseId(checkBase.getId()).setCheckRepoId(checkRepoId)
                .setCheckRepoBranchId(checkRepoBranchId).setExecutionId(checkExecutionId)
                .setIsLast(true).setGmtCreate(LocalDateTime.now()).setGmtModified(LocalDateTime.now());
        checkSnapshotService.save(checkSnapshot);
        return ImmutablePair.of(needDelLiveBaseId, checkSnapshot);
    }

    @Override
    public LanguageListResponseVo languageList(LanguageListRequestVo requestVo) {
        LanguageListResponseVo languageListResponseVo = new LanguageListResponseVo();
        Map<String, Long> languageCountMap = groupByLanguage();
        List<LanguageInfo> languageList = Lists.newArrayList();
        for (PlatformLanguageEnum languageEnum : PlatformLanguageEnum.values()) {
            LanguageInfo languageInfo = new LanguageInfo();
            String name = languageEnum.getName();
            languageInfo.setName(name);
            languageInfo.setVersions(languageEnum.getVersions());
            languageInfo.setScanners(
                    languageEnum.getPlatformScannerEnumList()
                            .stream().map(e -> NameAndValue.builder().name(e.getDesc()).value(e.getScanner()).build())
                            .collect(Collectors.toList())
            );
            Long count = languageCountMap.get(name);
            languageInfo.setRuleCount(count == null ? 0L : count);
            languageList.add(languageInfo);
        }
        String search = requestVo.getSearch();
        if (StringUtils.isNotEmpty(search)) {
            languageList = languageList.stream()
                    .filter(o -> o.getName().contains(search))
                    .collect(Collectors.toList());
        }
        languageListResponseVo.setLanguageList(languageList);
        return languageListResponseVo;
    }

    private Map<String, Long> groupByLanguage() {
        List<Map<String, Object>> languageKeyList =
                checkRuleService.groupCountByCondition(new CheckRuleListCondition(), "language");
        return CommonUtils.convertCountMap(languageKeyList);
    }

    @Override
    public LabelListResponseVo labelList(LabelListRequestVo requestVo) {
        LabelListResponseVo labelListResponseVo = new LabelListResponseVo();
        CheckRuleListCondition listCondition = CheckRuleListCondition.builder()
                .language(requestVo.getLanguage())
                .scanners(List.of(StringUtils.defaultString(requestVo.getScanner())))
                .build();
        List<CheckRule> checkRules = checkRuleService.listByCondition(listCondition);
        if (CollectionUtils.isEmpty(checkRules)) {
            labelListResponseVo.setLabelList(Collections.emptyList());
            return labelListResponseVo;
        }
        List<String> ruleKeys = checkRules.stream().map(CheckRule::getRuleKey).collect(Collectors.toList());
        List<CheckRuleLabelRelation> labelRelationList = checkRuleLabelRelationService.listInRuleKeys(ruleKeys);
        Map<String, Long> labelCountMap = labelRelationList.stream()
                .collect(Collectors.groupingBy(CheckRuleLabelRelation::getLabelName, Collectors.counting()));
        List<LabelDetailVo> labelList = Lists.newArrayList();
        for (Entry<String, Long> entry : labelCountMap.entrySet()) {
            LabelDetailVo labelDetailVo = new LabelDetailVo();
            labelDetailVo.setName(entry.getKey());
            labelDetailVo.setCount(entry.getValue());
            labelList.add(labelDetailVo);
        }
        labelListResponseVo.setLabelList(labelList);
        return labelListResponseVo;
    }

    @Override
    public ScannerListResponseVo scannerList(ScannerListRequestVo requestVo) {
        checkArguments(requestVo);
        PlatformLanguageEnum enumByName = PlatformLanguageEnum.getEnumByName(requestVo.getLanguage());
        if (enumByName == null) {
            throw new ThemisException(-1, "暂不支持您输入的语言");
        }
        List<PlatformScannerInfo> scannerInfos = enumByName.getPlatformScannerEnumList().stream()
                .map(o -> PlatformScannerInfo.builder().name(o.getScanner()).build())
                .collect(Collectors.toList());
        return ScannerListResponseVo.builder()
                .scannerList(scannerInfos)
                .build();
    }

    private void checkArguments(ScannerListRequestVo requestVo) {
        if (StringUtils.isEmpty(requestVo.getLanguage())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "language不能为空");
        }
    }

    @Override
    public GitRepoSearchResponseVo gitRepoSearch(GitRepoSearchRequestVo requestVo, String userName) {
        checkUserLogin(userName);
        GetUserProjectsRequest request = GetUserProjectsRequest.builder()
                .page(1)
                .pageSize(100)
                .simple(true)
                .username(userName)
                .search(requestVo.getSearch())
                .build();
        List<GitlabProject> gitlabProjects = selfGitApi.getUserProjects(request);
        List<GitRepoInfo> repoList = Lists.newArrayList();
        for (GitlabProject gitlabProject : gitlabProjects) {
            GitRepoInfo gitRepoInfo = new GitRepoInfo();
            gitRepoInfo.setRepoUrl(gitlabProject.getSshUrl());
            gitRepoInfo.setGitProjectId(gitlabProject.getId());
            repoList.add(gitRepoInfo);
        }
        return GitRepoSearchResponseVo.builder().repoList(repoList).build();
    }

    private void checkUserLogin(String userName) {
        if (StringUtils.isEmpty(userName)) {
            throw new ThemisException(ResultCodeConstant.NO_PERMISSION);
        }
    }

    @Override
    public GitBranchListResponseVo gitBranchList(GitBranchListRequestVo requestVo) {
        checkArguments(requestVo);
        GetGitBranchRequest branchRequest = GetGitBranchRequest.builder()
                .gitProjectId(requestVo.getGitProjectId())
                .search(requestVo.getSearch())
                .page(1)
                .pageSize(100)
                .build();
        List<GitlabBranch> gitBranches = selfGitApi.getGitBranches(branchRequest);
        List<GitBranchInfo> branchInfos = Lists.newArrayList();
        for (GitlabBranch gitlabBranch : gitBranches) {
            GitBranchInfo gitBranchInfo = new GitBranchInfo();
            gitBranchInfo.setName(gitlabBranch.getName());
            branchInfos.add(gitBranchInfo);
        }
        return GitBranchListResponseVo.builder().branchList(branchInfos).build();
    }

    private void checkArguments(GitBranchListRequestVo requestVo) {
        if (requestVo.getGitProjectId() == null) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "gitProjectId不能为空");
        }
    }

    @Override
    public GitProjectListResponseVo grayGitProjectList() {
        List<CheckRepo> checkRepos = checkRepoService.listByVersion(1);
        List<GitProjectInfo> projectInfos = checkRepos.stream()
                .map(o -> GitProjectInfo.builder()
                        .gitProjectId(o.getGitProjectId())
                        .build())
                .collect(Collectors.toList());
        return GitProjectListResponseVo.builder()
                .projects(projectInfos)
                .build();
    }

    @Override
    public List<Integer> grayGitProjectIdList() {
        GitProjectListResponseVo gitProjectListResponseVo = grayGitProjectList();
        List<GitProjectInfo> projects = gitProjectListResponseVo.getProjects();
        if (CollectionUtils.isEmpty(projects)) {
            return Collections.emptyList();
        }
        return projects.stream().map(GitProjectInfo::getGitProjectId).collect(Collectors.toList());
    }

    @Override
    public CreateLanguageListResponseVo createRepoLanguageList() {
        List<String> languageList = checkRepoLanguageService.selectLanguage();
        List<LanguageInfo> languageInfos = languageList.stream()
                .map(o -> LanguageInfo.builder()
                        .name(o)
                        .build()
                )
                .collect(Collectors.toList());
        return CreateLanguageListResponseVo.builder()
                .languageList(languageInfos)
                .build();
    }

    @Override
    public void refreshHistoryData(String profile, String ruleKey, String newSeverity) {
        List<CheckRepoBranchProfile> checkRepoBranchProfiles =
                checkRepoBranchProfileService.listByProfileName(profile);
        if (CollectionUtils.isEmpty(checkRepoBranchProfiles)) {
            return;
        }
        List<Long> checkRepoIds = checkRepoBranchProfiles.stream()
                .map(CheckRepoBranchProfile::getCheckRepoId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(checkRepoIds)) {
            return;
        }
        List<CheckRepo> checkRepos = checkRepoService.listByIds(checkRepoIds);
        if (CollectionUtils.isEmpty(checkRepos)) {
            return;
        }
        Map<Long, CheckRepo> checkRepoIdMap = checkRepos.stream()
                .collect(Collectors.toMap(CheckRepo::getId, Function.identity(), (a, b) -> a));
        LocalDateTime now = LocalDateTime.now();
        for (CheckRepoBranchProfile checkRepoBranchProfile : checkRepoBranchProfiles) {
            CheckRepo checkRepo = checkRepoIdMap.get(checkRepoBranchProfile.getCheckRepoId());
            if (checkRepo == null) {
                continue;
            }
            Integer gitProjectId = checkRepo.getGitProjectId();
            Integer profileType = checkRepoBranchProfile.getProfileType();
            int scanModeCode = CheckProfileType.getByType(profileType).getScanModeEnum().getCode();
            List<IssueSummary> issueSummaries =
                    issueSummaryService.listByGitProjectIdAndScanMode(gitProjectId, ruleKey, scanModeCode);
            for (IssueSummary issueSummary : issueSummaries) {
                issueSummary.setSeverity(newSeverity);
                issueSummary.setGmtModified(now);
            }
            issueSummaryService.updateBatchById(issueSummaries);
        }
    }

    private Pair<List<Long>, List<CheckSnapshot>> filterCheckSnapshots(List<CheckSnapshot> checkSnapshots) {
        List<CheckSnapshot> needUpdateSnapshots = Lists.newCopyOnWriteArrayList();
        List<Long> needDelLiveBaseId = Lists.newCopyOnWriteArrayList();
        if (CollectionUtils.isEmpty(checkSnapshots)) {
            return ImmutablePair.of(needDelLiveBaseId, needUpdateSnapshots);
        }
        checkSnapshots.parallelStream().forEach(snapshot -> {
            if (snapshot.getIsLast()) {
                needUpdateSnapshots.add(snapshot.setIsLast(false).setGmtModified(LocalDateTime.now()));
            }
            needDelLiveBaseId.add(snapshot.getBaseId());
        });
        return new ImmutablePair<>(needDelLiveBaseId, needUpdateSnapshots);
    }

    @Override
    public List<IhrUserInfo> searchUserByUserName(String userName) {
        try {
            IhrUserListResponse response = ihrOpenApi.getPersonsLimited(userName, true).execute().body();
            if (!validIhrUserListResponse(response)) {
                log.error("调用OpenApi搜索用户列表失败，userName: {}", userName);
                return Collections.emptyList();
            }
            return response.getData();
        } catch (IOException e) {
            log.error("调用OpenApi搜索用户列表失败，userName: {}", userName, e);
            return Collections.emptyList();
        }
    }

    @Override
    public Integer getJavaProjectIdBySshUrl(String sshUrl) {
        if (StringUtils.isBlank(sshUrl)) {
            return 0;
        }
        GitlabProject project = gitOperations.getProjectByUrl(sshUrl);
        if (Objects.isNull(project)) {
            return 0;
        }
        if (!gitOperations.isJavaProject(project.getId())) {
            return 0;
        }
        return project.getId();
    }

    // CHECKSTYLE:OFF
    private boolean validIhrUserListResponse(IhrUserListResponse response) {
        return Objects.nonNull(response)
                && (response.getResult() == 0 || response.getResult() == 200)
                && Objects.nonNull(response.getData());
    }
    // CHECKSTYLE:ON
}
