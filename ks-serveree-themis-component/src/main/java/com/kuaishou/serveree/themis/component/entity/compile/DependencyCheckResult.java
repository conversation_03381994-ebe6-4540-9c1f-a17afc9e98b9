package com.kuaishou.serveree.themis.component.entity.compile;

/**
 * <AUTHOR>
 * @since 2022/11/24 9:32 AM
 */
public class DependencyCheckResult {

    /**
     * 0 代表warning
     * 1 代表error
     */
    private Integer type;

    /**
     * 违规信息
     */
    private String context;

    /**
     * 规则id
     */
    private Integer ruleId;

    public DependencyCheckResult() {
    }

    private DependencyCheckResult(Builder builder) {
        setType(builder.type);
        setContext(builder.context);
        setRuleId(builder.ruleId);
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public Integer getRuleId() {
        return ruleId;
    }

    public void setRuleId(Integer ruleId) {
        this.ruleId = ruleId;
    }

    public static final class Builder {
        private Integer type;
        private String context;
        private Integer ruleId;

        private Builder() {
        }

        public Builder type(Integer val) {
            type = val;
            return this;
        }

        public Builder context(String val) {
            context = val;
            return this;
        }

        public Builder ruleId(Integer val) {
            ruleId = val;
            return this;
        }

        public DependencyCheckResult build() {
            return new DependencyCheckResult(this);
        }
    }
}
