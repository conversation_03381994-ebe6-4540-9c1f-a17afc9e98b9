package com.kuaishou.serveree.themis.component.client.git;

import static com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant.REPO_FILE_DETAIL_CONTENT;
import static com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant.REPO_FILE_DETAIL_RAW_CONTENT;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Nullable;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.gitlab.api.GitlabAPI;
import org.gitlab.api.models.GitlabBranch;
import org.gitlab.api.models.GitlabCommit;
import org.gitlab.api.models.GitlabGroup;
import org.gitlab.api.models.GitlabNamespace;
import org.gitlab.api.models.GitlabProject;
import org.gitlab.api.models.GitlabRepositoryTree;
import org.gitlab.api.models.GitlabUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/10/30 6:51 下午
 */
@Component
@Slf4j
public class GitOperations {

    @Autowired
    private GitlabAPI gitlabApi;

    @Autowired
    private SelfGitApi selfGitApi;

    @Autowired
    private KsRedisClient ksRedisClient;

    public static final Long GIT_FILE_CONTENT_CACHE_DAY = 30L;

    private static final Integer DEFAULT_SIZE = 1000;

    private final LoadingCache<Integer, GitlabProject> projectIdGitCache = Caffeine.newBuilder()
            .initialCapacity(DEFAULT_SIZE)
            .expireAfterAccess(5L, TimeUnit.MINUTES)
            .maximumSize(DEFAULT_SIZE)
            .build(projectId -> {
                try {
                    return getCacheProject(projectId);
                } catch (Exception e) {
                    log.error("get projectId cache failed, projectId is {}", projectId, e);
                    return null;
                }
            });


    public boolean isJavaProject(Integer projectId) {
        Map<String, Double> languagesPercentage = selfGitApi.getLanguagesPercentage(projectId);
        if (languagesPercentage == null || languagesPercentage.size() == 0) {
            return true;
        }
        return languagesPercentage.containsKey("Java");
    }

    public String getMainLanguage(Integer projectId) {
        Map<String, Double> languagesPercentage = selfGitApi.getLanguagesPercentage(projectId);
        if (MapUtils.isEmpty(languagesPercentage)) {
            return "";
        }
        return languagesPercentage.entrySet().stream().min((o1, o2) -> Double.compare(o2.getValue(), o1.getValue()))
                .map(a -> a.getKey().toLowerCase()).orElse("");
    }

    /**
     * 不支持permission获取
     */
    public GitlabProject getCacheProjectByGroupAndRepoName(String groupName, String repoName) {
        if (StringUtils.isEmpty(groupName) || StringUtils.isEmpty(repoName)) {
            return null;
        }
        final String redisKey = KsRedisPrefixConstant.GITLAB_PROJECT_CACHE_PREFIX + groupName + ":" + repoName;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return JSONUtils.deserialize(redisVal, GitlabProject.class);
        }
        GitlabProject project = null;
        try {
            project = gitlabApi.getProject(groupName, repoName);
            if (project != null) {
                // 特殊化处理
                project.setPermissions(null);
                project.setSharedWithGroups(null);
                ksRedisClient.sync().setex(redisKey, TimeUnit.DAYS.toSeconds(1), JSONUtils.serialize(project));
            }
        } catch (IOException e) {
            log.error("gitlab api get project error", e);
        }
        return project;
    }

    /**
     * 获取project所有分支
     *
     * @param projectId Serializable
     * @return List<GitlabBranch>
     */
    public List<GitlabBranch> getProjectBranches(Serializable projectId) {
        return gitlabApi.getBranches(projectId);
    }

    /**
     * 获取project
     *
     * @param projectId Serializable
     * @return List<GitlabBranch>
     */
    public GitlabProject getProject(Serializable projectId) {
        try {
            return gitlabApi.getProject(projectId);
        } catch (IOException e) {
            throw ThemisException.builder().code(HttpStatus.HTTP_INTERNAL_ERROR).message(e.getMessage()).build();
        }
    }

    public GitlabGroup getGroup(Integer groupId) {
        try {
            return gitlabApi.getGroup(groupId);
        } catch (IOException e) {
            throw ThemisException.builder().code(HttpStatus.HTTP_INTERNAL_ERROR).message(e.getMessage()).build();
        }
    }

    /**
     * 获取所有gitlab用户username
     *
     * @return List<String>
     */
    public List<GitlabUser> getAllUsers() {
        List<GitlabUser> allUsers = new ArrayList<>();

        int page = 0;
        int pageSize = 100;
        List<GitlabUser> curUsers = selfGitApi.getUsers(++page, pageSize);
        while (curUsers != null && !curUsers.isEmpty()) {
            allUsers.addAll(curUsers);
            curUsers = selfGitApi.getUsers(++page, pageSize);
            log.info("getting users from gitlab, page={}, page_size={}", page, curUsers.size());
        }

        return allUsers;
    }

    public List<GitlabCommit> getLastCommitsByUrl(String url, String branch) {
        GitlabProject gitlabProject = this.getProjectByUrl(url);
        if (gitlabProject == null) {
            return null;
        }
        Integer projectId = gitlabProject.getId();
        try {
            return gitlabApi.getLastCommits(projectId, branch);
        } catch (IOException e) {
            log.error("gitlabApi getLastCommits error,url is {}", url, e);
        }
        return null;
    }

    public GitlabProject getProjectByUrl(String repoUrl) {
        String groupName = GitUtils.getGroupName(repoUrl);
        String repoName = GitUtils.getRepoName(repoUrl);
        try {
            return gitlabApi.getProject(groupName, repoName);
        } catch (IOException e) {
            log.error("gitlabApi getProject error,url is {}", repoUrl, e);
        }
        return null;
    }

    public GitlabProject getCacheProject(Integer projectId) {
        final String redisKey = KsRedisPrefixConstant.GITLAB_PROJECT_ID_CACHE_PREFIX + projectId;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return JSONUtils.deserialize(redisVal, GitlabProject.class);
        }
        GitlabProject project = this.getProject(projectId);
        // 特殊化处理
        project.setPermissions(null);
        project.setSharedWithGroups(null);
        ksRedisClient.sync().setex(redisKey, TimeUnit.DAYS.toSeconds(1), JSONUtils.serialize(project));
        return project;
    }

    @Nullable
    public GitlabProject getLocalCacheProject(Integer projectId) {
        return projectIdGitCache.get(projectId);
    }

    /**
     * 根据projectId获取所有的groups
     *
     * @param projectId git的projectId
     */
    public List<Integer> getGroupsByProjectId(Integer projectId) {
        if (projectId == null) {
            return Collections.emptyList();
        }
        List<Integer> groupIds = Lists.newArrayList();
        GitlabNamespace namespace = this.getProject(projectId).getNamespace();
        groupIds.add(namespace.getId());
        String parentId = namespace.getParentId();
        if (StringUtils.isEmpty(parentId)) {
            return groupIds;
        }
        Integer parentGroupId = Integer.valueOf(parentId);
        groupIds.add(parentGroupId);
        GitlabGroup gitlabGroup;
        while (true) {
            gitlabGroup = this.getGroup(parentGroupId);
            parentGroupId = gitlabGroup.getParentId();
            if (parentGroupId == null) {
                break;
            }
            groupIds.add(parentGroupId);
        }
        return groupIds;
    }

    public boolean isJavaMavenProject(GitlabProject gitlabProject) {
        try {
            if (!this.isJavaProject(gitlabProject.getId())) {
                log.warn("{} is not java project or empty project ,skip", gitlabProject.getWebUrl());
                return false;
            }
        } catch (Throwable e) {
            log.error("{} check java project error", gitlabProject.getWebUrl(), e);
            return false;
        }
        if (!this.isMavenProject(gitlabProject)) {
            log.warn("{} is not maven project or empty project ,skip", gitlabProject.getWebUrl());
            return false;
        }
        return true;
    }

    private boolean isMavenProject(GitlabProject gitlabProject) {
        try {
            List<GitlabRepositoryTree> treeList =
                    gitlabApi.getRepositoryTree(gitlabProject, "/", "master", false);
            return treeList.stream().anyMatch(tree -> tree.getName().equals("pom.xml"));
        } catch (Throwable e) {
            log.error("get repository tree error, gitlab id is {}", gitlabProject.getId(), e);
            return false;
        }
    }

    public List<GitlabCommit> getLastCommitsByGitProjectId(Integer gitProjectId, String branch) {
        try {
            return gitlabApi.getLastCommits(gitProjectId, branch);
        } catch (IOException e) {
            log.error("gitlabApi getLastCommits error, gitProjectId is {}", gitProjectId, e);
        }
        return null;
    }

    public String getCachedHighlightContent(Integer gitProjectId, String commitId, String filePath) {
        final String redisKey = REPO_FILE_DETAIL_CONTENT + gitProjectId + ":" + commitId + ":" + filePath;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return redisVal;
        }
        String content;
        try {
            byte[] rawFileContent = gitlabApi.getRawFileContent(gitProjectId, commitId, filePath);
            content = new String(rawFileContent);
        } catch (IOException e) {
            log.error("get file content error,gitProjectId is {},commitId is {},filePath is {}",
                    gitProjectId, commitId, filePath);
            return "";
        }
        String highlightContent = selfGitApi.getHighlightContent(filePath, content);
        ksRedisClient.sync().setex(redisKey, TimeUnit.DAYS.toSeconds(GIT_FILE_CONTENT_CACHE_DAY), highlightContent);
        return highlightContent;
    }

    /**
     * 缓存原始文件文件
     */
    public String getCachedRawContent(Integer gitProjectId, String commitId, String filePath) {
        final String redisKey = REPO_FILE_DETAIL_RAW_CONTENT + gitProjectId + ":" + commitId + ":" + filePath;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return redisVal;
        }
        String content;
        try {
            byte[] rawFileContent = gitlabApi.getRawFileContent(gitProjectId, commitId, filePath);
            content = new String(rawFileContent);
        } catch (IOException e) {
            log.error("get file content error,gitProjectId is {},commitId is {},filePath is {}",
                    gitProjectId, commitId, filePath, e);
            return "";
        }
        ksRedisClient.sync().setex(redisKey, TimeUnit.DAYS.toSeconds(5), content);
        return content;
    }

}
