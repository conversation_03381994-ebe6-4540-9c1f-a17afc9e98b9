package com.kuaishou.serveree.themis.component.proxy;

import static com.kuaishou.serveree.themis.component.constant.sonar.SonarConstants.SONAR_CLUSTER_DEFAULT_ACCOUNT;

import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-15
 */
@Getter
@AllArgsConstructor
public enum SonarConfigEnum {
    NODE1("10.44.101.47", "9011", SONAR_CLUSTER_DEFAULT_ACCOUNT, "NucrwhPhP2aZj3Zw", "7da7791e2d81bcb67de899d63657874883d86f29",
            "Basic YWRtaW46TnVjcndoUGhQMmFaajNadw==", "https://sonar-cluster-1.corp.kuaishou.com"),
    NODE2("10.44.101.47", "9012", SONAR_CLUSTER_DEFAULT_ACCOUNT, "2PdZCTO4bFlBjcYd", "931d183e0795e0f3dd53a284908d9db9903d436a",
            "Basic YWRtaW46MlBkWkNUTzRiRmxCamNZZA==", "https://sonar-cluster-2.corp.kuaishou.com"),
    NODE3("************", "9013", SONAR_CLUSTER_DEFAULT_ACCOUNT, "BUvXK9oBiWmWvi1D", "31df778a7954db5325be3ebd08cdf84428be62c1",
            "Basic YWRtaW46QlV2WEs5b0JpV21XdmkxRA==", "https://sonar-cluster-3.corp.kuaishou.com"),
    NODE4("************", "9014", SONAR_CLUSTER_DEFAULT_ACCOUNT, "SWBWeCC9w8ZMJjqD", "f7e16f0d7c41fdea24739699e1cf17a2fc558e2b",
            "Basic YWRtaW46U1dCV2VDQzl3OFpNSmpxRA==", "https://sonar-cluster-4.corp.kuaishou.com"),
    ;

    private String ip;
    private String port;
    private String account;
    private String password;
    private String loginId;
    private String basicAuth;
    private String domain;
    private static Map<String, SonarConfigEnum> CACHE = new HashMap<>();

    static {
        CACHE.put(NODE1.ip + ":" + NODE1.port, NODE1);
        CACHE.put(NODE2.ip + ":" + NODE2.port, NODE2);
        CACHE.put(NODE3.ip + ":" + NODE3.port, NODE3);
        CACHE.put(NODE4.ip + ":" + NODE4.port, NODE4);
    }

    public static SonarConfigEnum modByProjectId(Long projectId) {
        if (projectId == null) {
            return null;
        }
        int length = SonarConfigEnum.values().length;
        int result = (int) (projectId % length);
        switch (result) {
            case 0:
                return NODE1;
            case 1:
                return NODE2;
            case 2:
                return NODE3;
            case 3:
                return NODE4;
            default:
                return null;
        }
//        String server = CodeScanningHashUtil.getServer(projectId.toString());
//        return CACHE.get(server);
    }
}
