package com.kuaishou.serveree.themis.component.entity.statics;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/30 3:00 下午
 */
@Data
public class ScanProjectInfo {

    private String logicGroupName;

    private List<ScanGroupInfo> scanGroupInfos;

    private List<Integer> scanUserProjects;

    private boolean needCommitter;

    private boolean containsUserProjects;

    @Data
    public static class ScanGroupInfo {

        private Integer groupId;

        private String logicGroupName;

        private List<Integer> projectIds;

        private List<Integer> excludeSubGroupIds;

        private List<Integer> excludeProjectIds;
    }

}
