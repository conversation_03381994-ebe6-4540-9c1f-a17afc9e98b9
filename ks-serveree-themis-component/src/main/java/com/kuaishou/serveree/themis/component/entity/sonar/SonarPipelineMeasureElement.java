package com.kuaishou.serveree.themis.component.entity.sonar;

/**
 * <AUTHOR>
 * @since 2021/11/23 3:51 下午
 */
public class SonarPipelineMeasureElement {

    /**
     * 阻断数量
     */
    private int blockerCount;

    /**
     * 严重数量
     */
    private int criticalCount;

    /**
     * 主要数量
     */
    private int majorCount;

    public SonarPipelineMeasureElement(int blockerCount, int criticalCount, int majorCount) {
        this.blockerCount = blockerCount;
        this.criticalCount = criticalCount;
        this.majorCount = majorCount;
    }

    public SonarPipelineMeasureElement() {
    }

    public int getBlockerCount() {
        return blockerCount;
    }

    public void setBlockerCount(int blockerCount) {
        this.blockerCount = blockerCount;
    }

    public int getCriticalCount() {
        return criticalCount;
    }

    public void setCriticalCount(int criticalCount) {
        this.criticalCount = criticalCount;
    }

    public int getMajorCount() {
        return majorCount;
    }

    public void setMajorCount(int majorCount) {
        this.majorCount = majorCount;
    }
}
