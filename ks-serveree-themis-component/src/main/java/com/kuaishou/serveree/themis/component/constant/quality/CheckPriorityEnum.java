package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020/11/2 8:02 下午
 */
@AllArgsConstructor
public enum CheckPriorityEnum {

    HIGH("高优", 3),
    MIDDLE("中优", 2),
    LOW("低优", 1);

    @Getter
    private String display;

    @Getter
    private Integer priorityNo;

    private static final Map<String, Integer> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(CheckPriorityEnum.values()).forEach(priorityEnum -> {
            ENUM_MAP.put(priorityEnum.name(), priorityEnum.getPriorityNo());
        });
    }

    public static Integer getPriorityNoByPriorityName(String priorityName) {
        if (StringUtils.isEmpty(priorityName)) {
            return LOW.getPriorityNo();
        }
        Integer priorityNo = ENUM_MAP.get(priorityName);
        if (priorityNo == null) {
            return LOW.getPriorityNo();
        }
        return priorityNo;
    }

}
