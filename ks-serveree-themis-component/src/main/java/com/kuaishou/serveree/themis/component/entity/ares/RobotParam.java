package com.kuaishou.serveree.themis.component.entity.ares;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/10/12 8:06 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RobotParam {

    private String msgType;

    private List<String> robotIds;

    private List<String> mentionedUserIdList;

    private List<String> mentionedMobileList;

    private Integer mentionedAllFlag;

}
