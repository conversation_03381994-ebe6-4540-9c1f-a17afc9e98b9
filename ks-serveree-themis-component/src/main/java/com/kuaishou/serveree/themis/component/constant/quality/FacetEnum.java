package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-14
 */
@Getter
@AllArgsConstructor
public enum FacetEnum {

    severities("severities", "级别", CheckIssueSeverity.values()),
    types("types", "类别", CheckIssueType.values()),
    statuses("statuses", "状态", CheckIssueStatus.values()),
    ;

    private String code;
    private String desc;
    private FacetNode[] facetNodeList;

    private static final Map<String, FacetEnum> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(FacetEnum.values()).forEach(facetEnum -> {
            ENUM_MAP.put(facetEnum.getCode(), facetEnum);
        });
    }

    public static FacetEnum getByCode(String code) {
        return ENUM_MAP.get(code);
    }

}
