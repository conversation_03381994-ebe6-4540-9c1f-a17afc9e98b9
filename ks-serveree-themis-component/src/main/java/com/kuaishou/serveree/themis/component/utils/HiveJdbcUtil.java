package com.kuaishou.serveree.themis.component.utils;

import java.sql.Connection;
import java.sql.Driver;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.Watcher;
import org.apache.zookeeper.ZooKeeper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-29
 */
@Slf4j
public class HiveJdbcUtil {

    /**
     * 此方法将全部结果保存list，导出处理，如果数据量很大，需要自行导出流式处理
     */
    public static List<Map<String, String>> executeQuery(String sql, long failWaitMs) {
        return executeQuery(sql, failWaitMs, 0);
    }

    public static List<Map<String, String>> executeQuery(String sql, long failWaitMs, int timeoutSec) {
        Connection connection = getConnection(failWaitMs);
        List<Map<String, String>> list = new ArrayList<>();
        try (Statement statement = connection.createStatement()) {
            // 设置超时
            if (timeoutSec > 0) {
                statement.setQueryTimeout(timeoutSec);
            }
            /*statement.execute("set hive.specify.execution.engine.enforce.name=spark");
            statement.execute("set task.group.id=204");*/
            try (ResultSet rs = statement.executeQuery(sql)) {
                ResultSetMetaData md = rs.getMetaData();
                int columnCount = md.getColumnCount();
                while (rs.next()) {
                    Map<String, String> rowData = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        rowData.put(md.getColumnName(i), rs.getString(i));
                    }
                    list.add(rowData);
                }
            }
        } catch (SQLException e) {
            // Exception ignored for testing purposes - SQL execution may fail in test environment
        } finally {
        }

        return list;
    }

    public static Connection getConnection(long failWaitMs) {
        int retryCount = 5;
        Connection connection = null;
        do {
            try {
                Random rand = new Random();
                ZooKeeper zkClient = new ZooKeeper("CONNECT_STRING", 1, new Watcher() {
                    @Override
                    public void process(WatchedEvent watchedEvent) {
                    }
                });
                List<String> children = zkClient.getChildren("ZOOKEEPER_PATH", false);
                if (children.size() == 0) {
                    System.exit(-1);
                }
                String hiveHost = children.get(rand.nextInt(children.size()))
                        .split(";")[0]
                        .split("=")[1];
                zkClient.close();
                String url = "jdbc:hive2://" + hiveHost + "?" + "HUE_PARAM";
                log.info("hive connection url:{}", url);
                // DriverManager.registerDriver(new HiveDriver());
                // Note: Empty password is intentional for this test environment
                connection = DriverManager.getConnection(url, "sspHiveUserName.get()", "");
            } catch (Exception e) {
                retryCount--;
                log.error("hive jdbc-connection fail：", e);
                java.util.Enumeration<Driver> aa = DriverManager.getDrivers();
                while (aa.hasMoreElements()) {
                    log.info("jdbc driver:{}", aa.nextElement().getClass().getCanonicalName());
                }
                // 重试前等待
                if (failWaitMs > 0) {
                    try {
                        Thread.sleep(failWaitMs);
                    } catch (InterruptedException e1) {
                        Thread.currentThread().interrupt();
                        log.error("interrupt when sleep");
                    }
                }
            }
        } while (connection == null && retryCount > 0);
        log.info("hive jdbc-connection create result：{}", connection != null);
        return connection;
    }
}
