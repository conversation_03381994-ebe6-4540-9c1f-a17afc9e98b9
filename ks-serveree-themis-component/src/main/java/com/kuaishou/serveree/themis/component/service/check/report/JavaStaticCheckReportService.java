package com.kuaishou.serveree.themis.component.service.check.report;

import java.io.IOException;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.component.client.email.EmailApi;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.ThemisAnalyzePlan;
import com.kuaishou.serveree.themis.component.common.entity.ThemisAnalyzePlanExecute;
import com.kuaishou.serveree.themis.component.common.entity.ThemisAnalyzePlanIssue;
import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.service.ThemisAnalyzePlanExecuteService;
import com.kuaishou.serveree.themis.component.service.ThemisAnalyzePlanIssueService;
import com.kuaishou.serveree.themis.component.service.ThemisAnalyzePlanService;
import com.kuaishou.serveree.themis.component.service.check.report.vo.IssueMeasureDto;
import com.kuaishou.serveree.themis.component.utils.GitUtils;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/10/15 10:33 上午
 */
@Service
@Slf4j
public class JavaStaticCheckReportService {

    @Autowired
    private ThemisAnalyzePlanService themisAnalyzePlanService;

    @Autowired
    private ThemisAnalyzePlanExecuteService themisAnalyzePlanExecuteService;

    @Autowired
    private ThemisAnalyzePlanIssueService themisAnalyzePlanIssueService;

    @Autowired
    private TaskService taskService;

    @Value("${static-issue.download-url}")
    private String downloadUrl;

    @Resource
    private Configuration freemarkerConfiguration;

    @Autowired
    private EmailApi emailApi;

    public static final Kconf<List<String>> JAVA_STATIC_CHECK_EMAILS = Kconfs
            .ofStringList("qa.themis.serverJavaStaticCheckEmails", Lists.newArrayList())
            .build();

    public void report() {
        // 查询上周的最后一次的静态检查
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime pre1Week0h0m0s = now.minusWeeks(1);
        List<ThemisAnalyzePlan> analyzePlans = themisAnalyzePlanService.listByFromAndEndTime(pre1Week0h0m0s, now);
        if (CollectionUtils.isEmpty(analyzePlans)) {
            return;
        }
        analyzePlans.sort(Comparator.comparing(ThemisAnalyzePlan::getCreatedTime).reversed());
        ThemisAnalyzePlan themisAnalyzePlan = analyzePlans.get(0);
        List<ThemisAnalyzePlanExecute> planExecutes =
                themisAnalyzePlanExecuteService.listByPlanId(themisAnalyzePlan.getId());
        if (CollectionUtils.isEmpty(planExecutes)) {
            return;
        }
        // 应该不会有很多，所以直接全load出来就行了
        Set<Long> executeIds = planExecutes.stream().map(ThemisAnalyzePlanExecute::getId).collect(Collectors.toSet());
        List<ThemisAnalyzePlanIssue> planIssueList = themisAnalyzePlanIssueService.listInExecuteIds(executeIds);
        Map<Long, List<ThemisAnalyzePlanIssue>> planExecuteIdIssueListMap =
                planIssueList.stream().collect(Collectors.groupingBy(ThemisAnalyzePlanIssue::getAnalyzePlanExecuteId));
        Set<Long> taskIds = planExecutes.stream().map(ThemisAnalyzePlanExecute::getTaskId).collect(Collectors.toSet());
        List<Task> taskList = taskService.listByIds(taskIds);
        Map<Long, Task> taskIdMap = taskList.stream().collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> existing));
        List<IssueMeasureDto> issueMeasures = Lists.newArrayList();
        for (ThemisAnalyzePlanExecute planExecute : planExecutes) {
            Long taskId = planExecute.getTaskId();
            Task task = taskIdMap.get(taskId);
            String taskStatus = task.getTaskStatus();
            if (!TaskStatusEnum.SUCCESS.name().equals(taskStatus)) {
                continue;
            }
            Long planExecuteId = planExecute.getId();
            List<ThemisAnalyzePlanIssue> executeIdPlanIssueList = planExecuteIdIssueListMap.get(planExecuteId);
            int issueCount = 0;
            if (CollectionUtils.isNotEmpty(executeIdPlanIssueList)) {
                issueCount = executeIdPlanIssueList.size();
            }
            String repoUrl = task.getRepoUrl();
            IssueMeasureDto issueMeasureDto = new IssueMeasureDto();
            issueMeasureDto.setGitLink(GitUtils.getHttpsUrl(repoUrl).replace("https://", ""));
            issueMeasureDto.setRepoName(GitUtils.getRepoName(repoUrl));
            issueMeasureDto.setIssueCount(issueCount);
            issueMeasureDto.setIssueDownloadLink(downloadUrl + "/api/quality/report/issue/download?executeId=" + planExecuteId);
            issueMeasures.add(issueMeasureDto);
        }
        // 按照数量多少排序
        issueMeasures.sort(Comparator.comparing(IssueMeasureDto::getIssueCount).reversed());
        Map<String, Object> dataModel = new HashMap<>();
        dataModel.put("issueMeasures", issueMeasures);
        dataModel.put("caption", "商业化后端静态代码检测报表");
        String content = null;
        try {
            Template tpl = freemarkerConfiguration.getTemplate("java_static_report_template.ftl");
            StringWriter writer = new StringWriter();
            tpl.process(dataModel, writer);
            content = writer.toString();
        } catch (IOException | TemplateException e) {
            log.error("generate java static report error!", e);
        }
        emailApi.send("商业化后端静态代码检测报表", content, JAVA_STATIC_CHECK_EMAILS.get());
    }

}
