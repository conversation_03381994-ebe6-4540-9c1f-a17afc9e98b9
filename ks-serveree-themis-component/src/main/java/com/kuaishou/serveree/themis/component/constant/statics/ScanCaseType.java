package com.kuaishou.serveree.themis.component.constant.statics;

import java.util.Arrays;
import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/11/1 4:09 下午
 */
@AllArgsConstructor
public enum ScanCaseType {

    HARD_CODE_DOMAIN_CASE(1, "域名硬编码扫描"),
    KCONF_KEY_CASE(2, "kconf key值扫描"),
    PATH_KEY_CASE(3, "Path硬编码扫描"),

    GRPC_CLIENT_OLD_1_CASE(5, "grpc-old-1客户端扫描"),
    KSPAY_HARD_CODE_DOMAIN(6, "支付硬编码扫描"),

    PTP_TRANSFER_CASE(7, "压测机房迁移直连扫描"),

    GRPC_CLIENT_OLD_ALL_CASE(8, "grpc-old-all客户端扫描"),

    PLATECO_HARD_CODE(9, "电商域名硬编码"),

    PLATECO_HARD_CODE2(10, "电商域名硬编码 second"),

    INFRA_LOGBACK_PACKING_DATA(11, "基础架构 logback配置"),

    DYNAMIC_USER_INPUT(12, "用户临时输入的一些扫描case"),

    IPV4_TO_IPV6(13, "ipv4到ipv6改造"),

    IPV4_TO_IPV6_ALL(14, "ipv4改造全case"),
    AZ2_STRUCT(15, "az2调用扫描"),

    ZK_DIRECT_INVOKE(16, "直接使用zk"),

    SINGLE_AZ(17, "SingleAz"),
    ;

    @Getter
    private final int type;

    @Getter
    private final String desc;

    private static final Map<Integer, String> TYPE_CASE_MAP = Maps.newHashMap();

    static {
        Arrays.stream(ScanCaseType.values())
                .forEach(caseType -> TYPE_CASE_MAP.put(caseType.getType(), caseType.getDesc()));
    }

    public static String getDescByType(Integer caseType) {
        return TYPE_CASE_MAP.get(caseType);
    }
}
