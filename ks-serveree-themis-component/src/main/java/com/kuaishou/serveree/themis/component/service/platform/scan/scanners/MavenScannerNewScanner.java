package com.kuaishou.serveree.themis.component.service.platform.scan.scanners;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterSimpleFactory;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoLanguage;
import com.kuaishou.serveree.themis.component.common.entity.CheckTriggerRecord;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.config.EnvironmentConfig;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformLanguageEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.platform.TriggerStatus;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckPluginInputConstants;
import com.kuaishou.serveree.themis.component.constant.quality.CheckTypeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.JobTypeEnum;
import com.kuaishou.serveree.themis.component.entity.platform.ExecuteScanContext;
import com.kuaishou.serveree.themis.component.entity.scanner.ScannerSendKimContext;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckExecutionService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoLanguageService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.CheckTriggerRecordService;
import com.kuaishou.serveree.themis.component.service.QualityCheckService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.PlatformSonarInfoUtils;
import com.kuaishou.serveree.themis.component.vo.request.ChangeParentProfileRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProfileAddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCopyRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCreateRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;

/**
 * <AUTHOR>
 * @since 2022/4/19 11:36 上午
 */
@Service
public class MavenScannerNewScanner extends NewSonarScanner {

    @Autowired
    private CheckRepoBranchService checkRepoBranchService;

    @Autowired
    private CheckBaseService checkBaseService;

    @Autowired
    private CheckRepoService checkRepoService;

    @Autowired
    private CheckExecutionService checkExecutionService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskConfigService taskConfigService;

    @Autowired
    private QualityCheckService qualityCheckService;

    @Autowired
    private CheckTriggerRecordService checkTriggerRecordService;

    @Autowired
    private SonarClusterSimpleFactory sonarClusterSimpleFactory;

    @Autowired
    private CheckRepoLanguageService checkRepoLanguageService;

    @Autowired
    private PlatformSonarInfoUtils sonarInfoUtils;

    @Autowired
    private EnvironmentConfig environmentConfig;

    @Autowired
    private GitOperations gitOperations;

    private static final Kconf<List<Integer>> NEED_UT_GIT_PROJECT_IDS =
            Kconfs.ofIntegerList("qa.themis.platformNeedUtGitProjectIds", Lists.newArrayList()).build();

    @Override
    @Transactional
    public void scan(ExecuteScanContext context) {
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.getById(context.getCheckRepoBranchId());
        LocalDateTime now = LocalDateTime.now();
        CheckRepo checkRepo = checkRepoService.getById(checkRepoBranch.getCheckRepoId());
        CheckBase checkBase = fillUpCheckBase(checkRepo, checkRepoBranch, now, gitOperations);
        checkBaseService.save(checkBase);
        Task task = fillUpTask(checkRepo, checkRepoBranch, now);
        taskService.save(task);
        CheckExecution checkExecution;
        checkExecution = fillUpCheckExecution(checkBase, now, context.getTriggerRecordId(), task,
                ProcessExecutionReferType.MAVEN_SONAR.getType());
        checkExecutionService.save(checkExecution);
        List<CheckRepoLanguage> checkRepoLanguages = checkRepoLanguageService.listByCheckRepoId(checkRepo.getId());
        TaskConfig taskConfig = fillUpTaskConfig(task, now, checkRepoLanguages, checkRepo, checkBase, checkExecution,
                checkRepoBranch);
        taskConfigService.save(taskConfig);
        qualityCheckService.sponsorKspPipeline(task, taskConfig);
        CheckTriggerRecord checkTriggerRecord = checkTriggerRecordService.getById(context.getTriggerRecordId());
        checkTriggerRecord.setTriggerStatus(TriggerStatus.EXECUTING.getCode());
        checkTriggerRecord.setTaskId(task.getId());
        checkTriggerRecord.setGmtModified(now);
        checkTriggerRecordService.updateById(checkTriggerRecord);
    }

    private TaskConfig fillUpTaskConfig(Task task,
            LocalDateTime now,
            List<CheckRepoLanguage> checkRepoLanguages,
            CheckRepo checkRepo,
            CheckBase checkBase,
            CheckExecution checkExecution,
            CheckRepoBranch checkRepoBranch) {
        Optional<CheckRepoLanguage> languageOptional = checkRepoLanguages.stream()
                .filter(o -> PlatformLanguageEnum.JAVA.getName().equals(o.getLanguage()))
                .findFirst();
        int version = 8;
        if (languageOptional.isPresent()) {
            version = Integer.parseInt(languageOptional.get().getVersion());
        }
        String finalProjectKey = getFinalProjectKey(checkRepo.getGitProjectId(), sonarInfoUtils);
        String finalProjectName = getFinalProjectName(GitUtils.getRepoName(checkRepo.getRepoUrl()), sonarInfoUtils);
        boolean test = environmentConfig.isTest();
        String specialArgs = getSpecialArgs(checkRepo.getGitProjectId());
        String args = " -Dsonar.projectKey=" + finalProjectKey
                + " -Dsonar.projectName=" + finalProjectName
                + " -Dsonar.branch.name=" + checkRepoBranch.getBranchName()
                + " -Dsonar.analysis.checkBaseId=" + checkBase.getId()
                + " -Dsonar.analysis.executionId=" + checkExecution.getId()
                + " -Dsonar.analysis.repoUrl=" + checkRepo.getRepoUrl()
                + " -Dsonar.analysis.branch=" + checkRepoBranch.getBranchName()
                + " -Dsonar.analysis.platformOfflineCheck=true"
                + " -Dsonar.analysis.testEnv=" + test
                + specialArgs;
        Map<String, Object> executionParamsMap = Maps.newHashMap();
        executionParamsMap.put(CheckPluginInputConstants.MVN_BUILD_ARGUMENTS, args);
        return TaskConfig.builder()
                .taskId(task.getId())
                .executionType(CheckTypeEnum.MAVEN_SONAR_NEW.name())
                .executionResult(StringUtils.EMPTY)
                .scriptType(StringUtils.EMPTY)
                .jobType(JobTypeEnum.KSP_PIPELINE.name())
                .originalKspPipelineId(NumberUtils.LONG_ZERO)
                .originalKspBuildId(NumberUtils.LONG_ZERO)
                .originalKspStepId(NumberUtils.LONG_ZERO)
                .originalKspName(StringUtils.EMPTY)
                .executionParams(JSONUtils.serialize(executionParamsMap))
                .pluginCheckRunType(NumberUtils.INTEGER_ZERO)
                .updatedTime(now)
                .javaVersion(version)
                .createdTime(now)
                .build();
    }

    private String getSpecialArgs(Integer gitProjectId) {
        List<Integer> needUtProjectIds = NEED_UT_GIT_PROJECT_IDS.get();
        if (!needUtProjectIds.contains(gitProjectId)) {
            return "";
        }
        return " test -Dmaven.test.failure.ignore=true";
    }

    @Override
    public PlatformScannerEnum platformScanner() {
        return PlatformScannerEnum.MAVEN_SCANNER_NEW;
    }

    @Override
    public void afterProjectCreate(RepoCreateRequest searchRequest) {
        afterProjectCreate(searchRequest, sonarClusterSimpleFactory, sonarInfoUtils,
                PlatformLanguageEnum.JAVA.getName());
    }

    @Override
    public void afterProfileCreate(ProfileCreateRequestVo requestVo) {
        afterProfileCreate(requestVo, sonarInfoUtils);
    }

    @Override
    public void afterProfileAddRule(ProfileAddRuleRequestVo requestVo) {
        afterProfileAddRule(requestVo, sonarInfoUtils);
    }

    @Override
    public void afterProfileDeleteRule(ProfileDeleteRuleRequestVo requestVo) {
        afterProfileDeleteRule(requestVo, sonarInfoUtils);
    }

    @Override
    public void afterProjectUpdateProfile(RepoProfileUpdateRequest updateRequest) {
        afterProjectUpdateProfile(updateRequest, sonarInfoUtils);
    }

    @Override
    public void afterProfileDelete(ProfileDeleteRequestVo requestVo) {
        afterProfileDelete(requestVo, sonarInfoUtils);
    }

    @Override
    public void afterIssueTransition(IssueSummary issueSummary, String transition) {
        afterIssueTransition(issueSummary, transition, sonarClusterSimpleFactory);
    }

    @Override
    public void sendPipelineKimNotice(ScannerSendKimContext scannerSendKimContext) {

    }

    @Override
    public void afterProfileCopy(ProfileCopyRequestVo requestVo) {
        afterProfileCopy(requestVo, sonarInfoUtils);
    }

    @Override
    public void afterChangeParentProfile(ChangeParentProfileRequest requestVo) {
        afterChangeParentProfile(requestVo, sonarInfoUtils);
    }
}
