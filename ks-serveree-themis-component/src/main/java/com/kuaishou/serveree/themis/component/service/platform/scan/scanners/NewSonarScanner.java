package com.kuaishou.serveree.themis.component.service.platform.scan.scanners;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterSimpleFactory;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarOperations;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.CheckProfileType;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformSonarInteractiveType;
import com.kuaishou.serveree.themis.component.entity.platform.PlatformSonarInteractiveContext;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.PlatformSonarInfoUtils;
import com.kuaishou.serveree.themis.component.vo.request.ChangeParentProfileRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProfileAddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCopyRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCreateRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSettingsRequest;

/**
 * <AUTHOR>
 * @since 2022/10/17 5:44 PM
 */
public abstract class NewSonarScanner implements PlatformScanner {

    @Value("${kafka.platform_sonar_interactive_topic}")
    private String topic;

    protected void afterProjectCreate(RepoCreateRequest searchRequest,
            SonarClusterSimpleFactory sonarClusterSimpleFactory,
            PlatformSonarInfoUtils sonarInfoUtils,
            String language) {
        // 如果是maven-scanner-new的话需要直接绑定sonar的profile
        int gitProjectId = searchRequest.getGitProjectId();
        SonarOperations clusterOperations = sonarClusterSimpleFactory.getClusterOperations((long) gitProjectId);
        SonarCommonApi sonarCommonApi = clusterOperations.sonarApi();
        String projectKey = getFinalProjectKey(gitProjectId, sonarInfoUtils);
        String finalProfileName = getFinalProfileName(searchRequest.getProfileName(), sonarInfoUtils);
        String finalProjectName = getFinalProjectName(GitUtils.getRepoName(searchRequest.getRepoUrl()), sonarInfoUtils);
        // 创建项目 并且绑定profile
        try {
            sonarCommonApi.createProject(finalProjectName, projectKey, "private");
        } catch (ThemisException e) {
            if (ResultCodeConstant.SONAR_PROJECTKEY_ALREADY_EXISTS.getCode() != e.getCode()) {
                throw new RuntimeException(e);
            }
        }
        sonarCommonApi.addProjectQualityprofile(language, projectKey, finalProfileName);

        // 增加流水线项目的配置与绑定
        String pipelineProjectKey = getPipelineProjectKey(gitProjectId, sonarInfoUtils);
        String pipelineProjectName =
                getPipelineProjectName(GitUtils.getRepoName(searchRequest.getRepoUrl()), sonarInfoUtils);
        // 创建项目 并且绑定profile
        try {
            sonarCommonApi.createProject(pipelineProjectName, pipelineProjectKey, "private");
        } catch (ThemisException e) {
            if (ResultCodeConstant.SONAR_PROJECTKEY_ALREADY_EXISTS.getCode() != e.getCode()) {
                throw new RuntimeException(e);
            }
        }
        sonarCommonApi.addProjectQualityprofile(language, pipelineProjectKey, finalProfileName);
    }

    protected void afterProfileCreate(ProfileCreateRequestVo requestVo, PlatformSonarInfoUtils sonarInfoUtils) {
        String parentProfileName = requestVo.getParentProfileName();
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.CREATE_PROFILE.getType())
                .profileName(getFinalProfileName(requestVo.getProfileName(), sonarInfoUtils))
                .parentProfileName(StringUtils.isNotBlank(parentProfileName)
                                   ? getFinalProfileName(parentProfileName, sonarInfoUtils) : null)
                .language(requestVo.getLanguage())
                .build();
        KafkaProducers.sendString(topic, JSONUtils.serialize(context));
    }

    protected void afterProfileAddRule(ProfileAddRuleRequestVo requestVo, PlatformSonarInfoUtils sonarInfoUtils) {
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.PROFILE_ADD_RULE.getType())
                .profileName(getFinalProfileName(requestVo.getProfileName(), sonarInfoUtils))
                .ruleKeyList(requestVo.getRuleKeyList())
                .ruleInfos(requestVo.getRuleInfos())
                .build();
        KafkaProducers.sendString(topic, JSONUtils.serialize(context));
    }

    protected void afterProfileDeleteRule(ProfileDeleteRuleRequestVo requestVo, PlatformSonarInfoUtils sonarInfoUtils) {
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.PROFILE_DELETE_RULE.getType())
                .profileName(getFinalProfileName(requestVo.getProfileName(), sonarInfoUtils))
                .ruleKeyList(requestVo.getRuleKeyList())
                .ruleInfos(requestVo.getRuleInfos())
                .build();
        KafkaProducers.sendString(topic, JSONUtils.serialize(context));
    }

    protected void afterProjectUpdateProfile(RepoProfileUpdateRequest updateRequest,
            PlatformSonarInfoUtils sonarInfoUtils) {
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.PROJECT_UPDATE_PROFILE.getType())
                .projectKey(CheckProfileType.OFFLINE.getType().equals(updateRequest.getProfileType())
                            ? getFinalProjectKey(updateRequest.getGitProjectId(), sonarInfoUtils)
                            : getPipelineProjectKey(updateRequest.getGitProjectId(), sonarInfoUtils)
                )
                .gitProjectId(updateRequest.getGitProjectId())
                .profileName(getFinalProfileName(updateRequest.getProfileName(), sonarInfoUtils))
                .language(updateRequest.getLanguage())
                .build();
        KafkaProducers.sendString(topic, JSONUtils.serialize(context));
    }

    @Override
    public boolean judgeNeedAllScan(RepoSettingsRequest request) {
        return false;
    }

    protected void afterProfileDelete(ProfileDeleteRequestVo requestVo, PlatformSonarInfoUtils sonarInfoUtils) {
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.PROFILE_DELETE.getType())
                .profileName(getFinalProfileName(requestVo.getProfileName(), sonarInfoUtils))
                .language(requestVo.getLanguage())
                .build();
        KafkaProducers.sendString(topic, JSONUtils.serialize(context));
    }

    protected void afterIssueTransition(IssueSummary issueSummary, String transition,
            SonarClusterSimpleFactory sonarClusterSimpleFactory) {
        SonarOperations clusterOperations =
                sonarClusterSimpleFactory.getClusterOperations(issueSummary.getGitProjectId().longValue());
        clusterOperations.sonarApi().doTransition(issueSummary.getSonarIssueKey(), transition);
    }

    protected void afterProfileCopy(ProfileCopyRequestVo requestVo, PlatformSonarInfoUtils sonarInfoUtils) {
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.PROFILE_COPY.getType())
                .profileName(getFinalProfileName(requestVo.getProfileName(), sonarInfoUtils))
                .toProfileName(getFinalProfileName(requestVo.getToProfileKey(), sonarInfoUtils))
                .language(requestVo.getLanguage())
                .build();
        KafkaProducers.sendString(topic, JSONUtils.serialize(context));
    }

    public void afterChangeParentProfile(ChangeParentProfileRequest requestVo, PlatformSonarInfoUtils sonarInfoUtils) {
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.PROFILE_CHANGE_PARENT.getType())
                .language(requestVo.getLanguage())
                .profileName(getFinalProfileName(requestVo.getProfileName(), sonarInfoUtils))
                .parentProfileName(getFinalProfileName(requestVo.getParentProfileName(), sonarInfoUtils))
                .build();
        KafkaProducers.sendString(topic, JSONUtils.serialize(context));
    }
}
