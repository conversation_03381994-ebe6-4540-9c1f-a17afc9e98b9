package com.kuaishou.serveree.themis.component.service.impl;

import static com.google.common.util.concurrent.Uninterruptibles.sleepUninterruptibly;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckFile;
import com.kuaishou.serveree.themis.component.common.mappers.CheckFileMapper;
import com.kuaishou.serveree.themis.component.constant.platform.CheckFileTypeEnum;
import com.kuaishou.serveree.themis.component.service.CheckFileService;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Service
public class CheckFileServiceImpl extends ServiceImpl<CheckFileMapper, CheckFile> implements CheckFileService {

    @Autowired
    private CheckFileMapper checkFileMapper;

    private static final Integer PAGE_SIZE = 1000;

    @Override
    public CheckFile getByFileId(long repoId, long branchId, long baseId, long fileId) {
        return getOne(new QueryWrapper<CheckFile>().lambda()
                .eq(CheckFile::getCheckRepoId, repoId)
                .eq(CheckFile::getCheckRepoBranchId, branchId)
                .eq(CheckFile::getBaseId, baseId)
                .eq(CheckFile::getId, fileId));
    }

    @Override
    public List<CheckFile> listByBaseId(long repoId, long branchId, long baseId) {
        Wrapper<CheckFile> queryWrapper = new QueryWrapper<CheckFile>().lambda()
                .eq(CheckFile::getCheckRepoId, repoId)
                .eq(CheckFile::getCheckRepoBranchId, branchId)
                .eq(CheckFile::getBaseId, baseId);
        return this.list(queryWrapper);
    }

    @Override
    public List<CheckFile> listBySuffix(long repoId, long branchId, long baseId, List<String> suffix) {
        if (suffix == null || suffix.size() == 0) {
            return new ArrayList<>();
        }
        Wrapper<CheckFile> queryWrapper = new QueryWrapper<CheckFile>().lambda()
                .eq(CheckFile::getCheckRepoId, repoId)
                .eq(CheckFile::getCheckRepoBranchId, branchId)
                .eq(CheckFile::getBaseId, baseId)
                .in(CheckFile::getFileSuffix, suffix);
        return this.list(queryWrapper);
    }

    @Override
    public List<CheckFile> listByPartOfFileName(long repoId, long branchId, long baseId, String location) {
        Wrapper<CheckFile> queryWrapper = new QueryWrapper<CheckFile>().lambda()
                .eq(CheckFile::getCheckRepoId, repoId)
                .eq(CheckFile::getCheckRepoBranchId, branchId)
                .eq(CheckFile::getBaseId, baseId)
                .eq(CheckFile::getFileType, CheckFileTypeEnum.FILE.getType())
                .like(CheckFile::getLocation, location);
        return this.list(queryWrapper);
    }

    @Override
    public Map<Long, String> getFileLocationMap(CheckBase checkBase, List<Long> fileIds) {
        if (fileIds == null || fileIds.size() == 0) {
            return new HashMap<>();
        }
        // 得到指定文件的存储路径
        Wrapper<CheckFile> queryWrapper = new QueryWrapper<CheckFile>().lambda()
                .eq(CheckFile::getCheckRepoId, checkBase.getCheckRepoId())
                .eq(CheckFile::getCheckRepoBranchId, checkBase.getCheckRepoBranchId())
                .eq(CheckFile::getBaseId, checkBase.getId())
                .in(CheckFile::getId, fileIds);
        return this.list(queryWrapper).stream().collect(Collectors.toMap(CheckFile::getId, CheckFile::getLocation, (existing, replacement) -> existing));
    }

    @Override
    public List<CheckFile> listFileByFileLevel(CheckBase checkBase, int fileLevel) {
        Wrapper<CheckFile> query = new QueryWrapper<CheckFile>().lambda()
                .eq(CheckFile::getCheckRepoId, checkBase.getCheckRepoId())
                .eq(CheckFile::getCheckRepoBranchId, checkBase.getCheckRepoBranchId())
                .eq(CheckFile::getBaseId, checkBase.getId())
                .eq(CheckFile::getFileLevel, fileLevel);
        return this.list(query);
    }

    @Override
    public Map<Long, String> listFileLocationMap(long repoId, long branchId, long baseId) {
        List<CheckFile> checkFiles = listByBaseId(repoId, branchId, baseId);
        return checkFiles.stream().collect(Collectors.toMap(CheckFile::getId, CheckFile::getLocation, (existing, replacement) -> existing));
    }

    @Override
    public Map<Long, CheckFile> getFileMap(long repoId, long branchId, long baseId) {
        return listByBaseId(repoId, branchId, baseId).stream()
                .collect(Collectors.toMap(CheckFile::getId, Function.identity(), (a, b) -> a));
    }

    @Override
    public IPage<CheckFile> pageCheckRepoAndBaseId(Long checkRepoId, Long checkRepoBranchId, Long baseId, Integer page,
            Integer pageSize) {
        Wrapper<CheckFile> queryWrapper = new QueryWrapper<CheckFile>().lambda()
                .eq(CheckFile::getCheckRepoId, checkRepoId)
                .eq(CheckFile::getCheckRepoBranchId, checkRepoBranchId)
                .eq(CheckFile::getBaseId, baseId);
        return this.page(new Page<>(page, pageSize), queryWrapper);
    }

    @Override
    public void pageDelByCheckRepoAndBaseId(Long checkRepoId, Long repoBranchId, Long baseId) {
        while (true) {
            IPage<CheckFile> checkFileIPage = pageCheckRepoAndBaseId(checkRepoId, repoBranchId, baseId, 1, PAGE_SIZE);
            List<CheckFile> records = checkFileIPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            Wrapper<CheckFile> queryWrapper = new QueryWrapper<CheckFile>().lambda()
                    .eq(CheckFile::getCheckRepoId, checkRepoId)
                    .eq(CheckFile::getCheckRepoBranchId, repoBranchId)
                    .eq(CheckFile::getBaseId, baseId)
                    .in(CheckFile::getId, records.stream().map(CheckFile::getId).collect(Collectors.toList()));
            this.remove(queryWrapper);
            sleepUninterruptibly(1, TimeUnit.SECONDS);
        }
    }

    @Override
    public Integer insertBatch(Collection<CheckFile> checkFiles) {
        return checkFileMapper.insertBatchSomeColumn(checkFiles);
    }

}