package com.kuaishou.serveree.themis.component.service.check.analyze.handler;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.ThemisAnalyze;
import com.kuaishou.serveree.themis.component.common.entity.ThemisAnalyzeIssue;
import com.kuaishou.serveree.themis.component.common.entity.ThemisRule;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.analyze.AnalyzeRuleType;
import com.kuaishou.serveree.themis.component.service.ThemisAnalyzeIssueService;
import com.kuaishou.serveree.themis.component.service.ThemisAnalyzeService;
import com.kuaishou.serveree.themis.component.service.ThemisRuleService;
import com.kuaishou.serveree.themis.component.vo.request.DiffContent;
import com.kuaishou.serveree.themis.component.vo.request.QualityAnalyzeIssueListRequest;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeIssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeIssueListResponse.AnalyzeIssue;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeIssueListResponse.AnalyzeIssue.AnalyzeIssueInfo;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeIssueListResponse.AnalyzeIssue.RuleInfo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR>
 * @since 2021/7/30 3:05 下午
 */
@SuppressWarnings("DuplicatedCode")
@Service
public class CustomIssueListHandler implements CheckAnalyzeIssueListHandler {

    @Autowired
    private ThemisAnalyzeService themisAnalyzeService;

    @Autowired
    private ThemisAnalyzeIssueService themisAnalyzeIssueService;

    @Autowired
    private ThemisRuleService themisRuleService;

    @Override
    public ThemisResponse<QualityAnalyzeIssueListResponse> handle(QualityAnalyzeIssueListRequest request) {
        Integer repoId = request.getProjectId();
        String mrId = request.getMrId();
        String commitId = request.getCommitId();
        ThemisAnalyze themisAnalyze = themisAnalyzeService.getAnalyzeByMrInfo(repoId, mrId, commitId);
        if (themisAnalyze == null) {
            return ThemisResponse.fail(ResultCodeConstant.NOT_FOUND_ANALYZE);
        }
        Long analyzeId = themisAnalyze.getId();
        Long page = request.getPage();
        Long pageSize = request.getPageSize();
        Page<ThemisAnalyzeIssue> analyzeIssuePage =
                themisAnalyzeIssueService.pagingListGroupByFilePath(analyzeId, request.getRuleType(), page, pageSize);
        List<ThemisAnalyzeIssue> themisAnalyzeIssues = analyzeIssuePage.getRecords();
        if (CollectionUtils.isEmpty(themisAnalyzeIssues)) {
            return ThemisResponse.success(new QualityAnalyzeIssueListResponse(analyzeIssuePage.getTotal(),
                    page, pageSize, new ArrayList<>()));
        }
        return this.assembleData(themisAnalyzeIssues, page, pageSize, analyzeIssuePage.getTotal());
    }

    private ThemisResponse<QualityAnalyzeIssueListResponse> assembleData(List<ThemisAnalyzeIssue> themisAnalyzeIssues,
            Long page, Long pageSize, long total) {
        Map<String, ThemisRule> ruleIdMap = themisRuleService.getAllActiveRuleMap(AnalyzeRuleType.CUSTOM.getTypeCode());
        Map<String, List<ThemisAnalyzeIssue>> pathIssueListMap = themisAnalyzeIssues.stream()
                .collect(Collectors.groupingBy(ThemisAnalyzeIssue::getIllegalFile));
        List<AnalyzeIssue> analyzeIssues = Lists.newArrayList();
        // 文件分组
        pathIssueListMap.forEach((filePath, issueList) -> {
            List<AnalyzeIssueInfo> issueInfoList = Lists.newArrayList();
            Map<String, List<ThemisAnalyzeIssue>> ruleIdGroupIssueListMap =
                    issueList.stream().collect(Collectors.groupingBy(ThemisAnalyzeIssue::getRuleId));
            // 文件规则分组
            ruleIdGroupIssueListMap.forEach((ruleId, groupIssueList) -> {
                // 规则分组后文件集合
                groupIssueList.forEach(issue -> {
                    AnalyzeIssueInfo analyzeIssueInfo = AnalyzeIssueInfo.builder()
                            .ruleInfo(RuleInfo.builder().ruleId(ruleId).ruleDesc(ruleIdMap.get(ruleId).getDescription())
                                    .build())
                            .diffContent(DiffContent.builder()
                                    .lineContent(issue.getIllegalContent())
                                    .build())
                            .build();
                    issueInfoList.add(analyzeIssueInfo);
                });
            });
            AnalyzeIssue analyzeIssue = AnalyzeIssue.builder()
                    .filePath(filePath)
                    .analyzeIssueInfoList(issueInfoList)
                    .build();
            analyzeIssues.add(analyzeIssue);
        });
        QualityAnalyzeIssueListResponse analyzeIssueListResponse = new QualityAnalyzeIssueListResponse();
        analyzeIssueListResponse.setPage(page);
        analyzeIssueListResponse.setPageSize(pageSize);
        analyzeIssueListResponse.setTotal(total);
        analyzeIssueListResponse.setAnalyzeIssueList(analyzeIssues);
        return ThemisResponse.success(analyzeIssueListResponse);
    }


    @Override
    public List<AnalyzeRuleType> ruleTypes() {
        return Lists.newArrayList(AnalyzeRuleType.CUSTOM);
    }

}
