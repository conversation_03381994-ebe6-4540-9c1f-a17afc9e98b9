package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.serveree.themis.component.common.entity.MrCheckPointIssue;

/**
 * <p>
 * MR检查点Issue表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
public interface MrCheckPointIssueMapper extends BaseMapper<MrCheckPointIssue> {

    List<MrCheckPointIssue> listByProjectIdMrIdCommitIdCheckpoint(
            @Param("projectId") long projectId, @Param("mrId") long mrId, @Param("commitId")
    String commitId, @Param("checkpoint") String checkpoint);
}
