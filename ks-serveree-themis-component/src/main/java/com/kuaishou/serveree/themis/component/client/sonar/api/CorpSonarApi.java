package com.kuaishou.serveree.themis.component.client.sonar.api;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @since 2020/10/21 11:17 上午
 */
@Component
@Slf4j
public class CorpSonarApi implements SonarCommonApi {

    @Value("${sonar.url}")
    private String sonarUrl;

    @Value("${sonar.timeout}")
    private Integer timeout;

    @Value("${sonar.basic-auth}")
    private String basicAuth;

    @Override
    public String sonarUrl() {
        return sonarUrl;
    }

    @Override
    public Integer timeout() {
        return timeout;
    }

    @Override
    public String basicAuth() {
        return basicAuth;
    }

}
