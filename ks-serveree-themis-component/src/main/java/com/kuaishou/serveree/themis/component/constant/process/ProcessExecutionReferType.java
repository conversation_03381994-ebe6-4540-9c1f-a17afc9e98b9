package com.kuaishou.serveree.themis.component.constant.process;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/10/21 4:30 下午
 */
@AllArgsConstructor
public enum ProcessExecutionReferType {

    MAVEN_SONAR(1, "maven-sonar", "iconbug-yibanwenti", ProcessCheckType.K_CHECK, ""),
    CHECKSTYLE(2, "checkstyle", "iconbug-yanzhongwenti", ProcessCheckType.K_CHECK, ""),
    CUSTOM_CHECK(3, "custom-check", "iconbug-yibanwenti", ProcessCheckType.CUSTOM_CHECK, ""),
    SKY_EYE(4, "天眼扫描", "iconbug-tianyanchaxun", ProcessCheckType.THIRD_PART_CHECK, "business-front"),
    KS_CPPCHECK(5, "快手cppcheck扫描", "iconbug-tianyanchaxun", ProcessCheckType.THIRD_PART_CHECK, "ks-cppcheck"),
    K_FORMAT(6, "kFormat扫描", "iconbug-tianyanchaxun", ProcessCheckType.THIRD_PART_CHECK, "sonar-cplus"),
    SONAR_SCANNER(7, "sonar-scanner", "iconbug-yibanwenti", ProcessCheckType.K_CHECK, ""),
    IDEA_SONAR_PLUGIN(8, "idea-sonar-plugin", "iconbug-yibanwenti", ProcessCheckType.K_CHECK, ""),
    // 这个icon不清楚是怎么来的
    COVERITY(9, "coverity", "iconbug-yibanwenti", ProcessCheckType.K_CHECK, ""),
    PMD(10, "pmd", "iconbug-yibanwenti", ProcessCheckType.K_CHECK, "")
    ;

    @Getter
    private final int type;

    @Getter
    private final String desc;
    // 界面展示图标，新增类型可以新增一种图标，或者使用现有的图标
    @Getter
    private final String icon;

    @Getter
    private final ProcessCheckType checkType;

    @Getter
    private final String source;

    private static final Map<String, List<Integer>> CODE_MAP = Maps.newHashMap();

    private static final Map<Integer, String> DESC_MAP = Maps.newHashMap();

    private static final Map<String, ProcessExecutionReferType> SOURCE_MAP = Maps.newHashMap();

    private static final Map<Integer, ProcessExecutionReferType> TYPE_MAP = Maps.newHashMap();

    static {
        Arrays.stream(ProcessExecutionReferType.values()).forEach(referType -> {
            List<Integer> typeList =
                    CommonUtils.getOrCreate(referType.getCheckType().getKey(), CODE_MAP, ArrayList::new);
            typeList.add(referType.getType());
            DESC_MAP.put(referType.getType(), referType.getDesc());
            SOURCE_MAP.put(referType.getSource(), referType);
            TYPE_MAP.put(referType.getType(), referType);
        });
    }

    public static List<Integer> getTypeListByCheckType(String checkType) {
        return CODE_MAP.get(checkType);
    }

    public static String getTypeDescByCheckType(Integer checkType) {
        return DESC_MAP.get(checkType);
    }

    public static ProcessExecutionReferType getBySource(String source) {
        return SOURCE_MAP.get(source);
    }

    public static ProcessExecutionReferType getByType(Integer type) {
        return TYPE_MAP.get(type);
    }

}
