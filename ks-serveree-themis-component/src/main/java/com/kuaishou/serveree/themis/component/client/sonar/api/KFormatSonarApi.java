package com.kuaishou.serveree.themis.component.client.sonar.api;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/7/2 11:04 上午
 */
@Component
public class KFormatSonarApi implements SonarCommonApi {

    @Value("${sonar.kformat-url}")
    private String sonarUrl;

    @Value("${sonar.timeout}")
    private Integer timeout;

    @Value("${sonar.kformat-basic-auth}")
    private String basicAuth;

    @Override
    public String sonarUrl() {
        return sonarUrl;
    }

    @Override
    public Integer timeout() {
        return timeout;
    }

    @Override
    public String basicAuth() {
        return basicAuth;
    }
}
