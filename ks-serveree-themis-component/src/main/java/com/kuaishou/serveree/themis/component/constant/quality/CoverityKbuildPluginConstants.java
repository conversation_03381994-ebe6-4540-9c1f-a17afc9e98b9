package com.kuaishou.serveree.themis.component.constant.quality;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-08-05
 * kbuild coverity流水线一些插件参数
 */
public interface CoverityKbuildPluginConstants {

    // kbuild_checkout天琴插件id
    Long PIPELINE_PLUGIN_ID_CHECKOUT = 442L;
    // coverity_prepare天琴插件id
    Long PIPELINE_PLUGIN_ID_PREPARE = 1296L;
    // kbuild_cov_opt天琴插件id
    Long PIPELINE_PLUGIN_ID_BUILD_OPT = 1298L;
    // kbuild_cov_clang_opt天琴插件id
    Long PIPELINE_PLUGIN_ID_BUILD_CLANG_OPT = 1297L;

    /**
     * 仓库地址
     */
    String GIT_REPO_URL = "GIT_REPO_URL";

    /**
     * 仓库分支
     */
    String GIT_BRANCH = "GIT_BRANCH";

    /**
     * GIT_COMMIT_ID
     */
    String GIT_COMMIT_ID = "GIT_COMMIT_ID";

    /**
     * kbuild init 业务名
     */
    String KBUILD_INIT_NAME = "KBUILD_INIT_NAME";

    /**
     * 多git库依赖(注意，插件里面参数定义这个名字单词是错的)
     */
    String MULTI_GIT_URL = "mutilGitUrl";

    /**
     * BUILD文件所在路径
     */
    String SELF_BUILD_URL = "SELF_BUILD_DIR";

    /**
     * base仓库初始化分支
     */
    String BASE_TAG = "BASE_TAG";

    /**
     * 是否开启gcc10.3，baseTag是HEAD，则不支持
     */
    String ENABLE_GCC10_3 = "ENABLE_GCC10_3";

    /**
     * 是否构建opt版本（gcc）
     */
    String IS_MAKE_OPT = "IS_MAKE_OPT";

    /**
     * IS_MAKE_CLANG_OPT：是否构建clang版本
     */
    String IS_MAKE_CLANG_OPT = "IS_MAKE_CLANG_OPT";

    /**
     * build前执行shell命令
     */
    String BEFORE_BUILD_EXE_SHELL_CMD = "BEFORE_BUILD_EXE_SHELL_CMD";
}
