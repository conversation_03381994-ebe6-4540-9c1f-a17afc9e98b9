package com.kuaishou.serveree.themis.component.client.sonar.operations;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.client.sonar.api.ClusterNode3Api;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-16
 */
@Slf4j
@Component
public class ClusterNode3Operations implements SonarClusterOperations {

    @Value("${sonar.cluster.node3.login-id}")
    private String loginId;

    @Autowired
    private ClusterNode3Api clusterNode3Api;

    @Override
    public SonarCommonApi sonarApi() {
        return clusterNode3Api;
    }

    @Override
    public Integer nodeNumber() {
        return 3;
    }

    @Override
    public String loginId() {
        return loginId;
    }
}
