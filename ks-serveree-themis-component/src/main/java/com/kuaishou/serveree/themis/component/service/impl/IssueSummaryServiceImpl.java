package com.kuaishou.serveree.themis.component.service.impl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.common.mappers.IssueSummaryMapper;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.entity.issue.IssueSummaryVo;
import com.kuaishou.serveree.themis.component.entity.platform.IssueSummaryListCondition;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.DateUtils;
import com.kuaishou.serveree.themis.component.vo.request.IssueListRequest;
import com.kuaishou.serveree.themis.component.vo.request.SearchIssueRequest;

import cn.hutool.core.collection.ListUtil;
import lombok.extern.slf4j.Slf4j;


/**
 * <p>
 * 最新生效的issue表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Slf4j
@Service
public class IssueSummaryServiceImpl extends ServiceImpl<IssueSummaryMapper, IssueSummary> implements
        IssueSummaryService {

    public static final Integer PAGE_SIZE = 1000;

    @Autowired
    private IssueSummaryMapper issueSummaryMapper;
    @Resource
    private ThreadPoolExecutor issueSummarySyncExecutor;

    private static final String SPLIT_REGEX = "[,，]";

    @Override
    public IPage<IssueSummary> searchIssues(SearchIssueRequest request, PCheckBase pCheckBase) {
        // 如果指定了文件路径
        Long issueId = request.getIssueId();
        String location = null;
        if (issueId != null && issueId > 0) {
            location = getLocationById(issueId);
        }
        IPage<IssueSummary> page = new Page<>(request.getP(), request.getPs());
        LambdaQueryWrapper<IssueSummary> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(IssueSummary::getGitBranch, pCheckBase.getBranch())
                .eq(IssueSummary::getGitProjectId, pCheckBase.getProjectId())
                .eq(IssueSummary::getScanMode, ScanModeEnum.PROCESS.getCode())
                .eq(StringUtils.isNotBlank(location), IssueSummary::getLocation, location);
        if (StringUtils.isNotEmpty(request.getIssues())) {
            wrapper.and((queryWrapper) -> queryWrapper.in(IssueSummary::getIssueUniqId,
                            Stream.of(request.getIssues().split(SPLIT_REGEX)).collect(Collectors.toList()))
                    .or()
                    .in(IssueSummary::getCommonIssueUniqId,
                            Stream.of(request.getIssues().split(SPLIT_REGEX)).collect(Collectors.toList()))
                    .or()
                    .in(IssueSummary::getIssueUniqIdV2,
                            Stream.of(request.getIssues().split(SPLIT_REGEX)).collect(Collectors.toList()))
            );

        }
        if (StringUtils.isNotEmpty(request.getIssueKeys())) {
            wrapper.and((queryWrapper) -> queryWrapper.in(IssueSummary::getIssueUniqId,
                            Stream.of(request.getIssueKeys().split(SPLIT_REGEX)).collect(Collectors.toList()))
                    .or()
                    .in(IssueSummary::getCommonIssueUniqId,
                            Stream.of(request.getIssueKeys().split(SPLIT_REGEX)).collect(Collectors.toList()))
                    .or()
                    .in(IssueSummary::getIssueUniqIdV2,
                            Stream.of(request.getIssueKeys().split(SPLIT_REGEX)).collect(Collectors.toList()))
            );
        }
        if (StringUtils.isNotEmpty(request.getAuthors())) {
            wrapper.in(IssueSummary::getAuthor,
                    Stream.of(request.getAuthors().split(SPLIT_REGEX)).collect(Collectors.toList()));
        }
        if (StringUtils.isNotEmpty(request.getRules())) {
            wrapper.in(IssueSummary::getRule,
                    Stream.of(request.getRules().split(SPLIT_REGEX)).collect(Collectors.toList()));
        }
        if (StringUtils.isNotEmpty(request.getSeverities())) {
            wrapper.in(IssueSummary::getSeverity,
                    Stream.of(request.getSeverities().split(SPLIT_REGEX)).collect(Collectors.toList()));
        }
        if (StringUtils.isNotEmpty(request.getTypes())) {
            wrapper.in(IssueSummary::getType,
                    Stream.of(request.getTypes().split(SPLIT_REGEX)).collect(Collectors.toList()));
        }
        if (StringUtils.isNotEmpty(request.getStatuses())) {
            wrapper.in(IssueSummary::getStatus,
                    Stream.of(request.getStatuses().split(SPLIT_REGEX)).collect(Collectors.toList()));
        }
        wrapper.orderByAsc(IssueSummary::getLocation, IssueSummary::getStartLine);
        return page(page, wrapper);
    }

    @Override
    public void updateIssueStatus(IssueSummary issueSummary, String newStatus) {
        issueSummary.setStatus(newStatus);
        issueSummary.setGmtModified(LocalDateTime.now());
        boolean update = updateById(issueSummary);
        if (!update) {
            throw new ThemisException(ResultCodeConstant.SAVE_FAIL);
        }
    }

    @Override
    public List<IssueSummary> listOpenByProjectBranch(Integer projectId, String branch, Integer scanMode, long startTime) {
        Wrapper<IssueSummary> queryWrapper = new QueryWrapper<IssueSummary>().lambda()
                .eq(IssueSummary::getGitProjectId, projectId)
                .eq(IssueSummary::getGitBranch, branch)
                .eq(IssueSummary::getScanMode, scanMode)
                .gt(IssueSummary::getGmtCreate, startTime)
                .in(IssueSummary::getStatus,
                        Lists.newArrayList(CheckIssueStatus.OPEN.getStatus(), CheckIssueStatus.RE_OPEN.getStatus()));
        return this.list(queryWrapper);
    }

    @Override
    public List<IssueSummary> listOpenByProjectBranch(Integer projectId, String branch, Integer sacnMode) {
        Wrapper<IssueSummary> queryWrapper = new QueryWrapper<IssueSummary>().lambda()
                .eq(IssueSummary::getGitProjectId, projectId)
                .eq(IssueSummary::getGitBranch, branch)
                .eq(IssueSummary::getScanMode, sacnMode)
                .in(IssueSummary::getStatus,
                        Lists.newArrayList(CheckIssueStatus.OPEN.getStatus(), CheckIssueStatus.RE_OPEN.getStatus()));
        return this.list(queryWrapper);
    }

    @Override
    public List<IssueSummary> listOpenByProjectBranchAndIssueUniqIds(Integer projectId, String branch, Integer scanMode,
            Collection<String> issueUniqIdList) {
        if (CollectionUtils.isEmpty(issueUniqIdList)) {
            return Collections.emptyList();
        }
        Wrapper<IssueSummary> queryWrapper = new QueryWrapper<IssueSummary>().lambda()
                .eq(IssueSummary::getGitProjectId, projectId)
                .eq(IssueSummary::getGitBranch, branch)
                .eq(IssueSummary::getScanMode, scanMode)
                .in(IssueSummary::getIssueUniqId, issueUniqIdList)
                .in(IssueSummary::getStatus,
                        Lists.newArrayList(CheckIssueStatus.OPEN.getStatus(), CheckIssueStatus.RE_OPEN.getStatus()));
        return this.list(queryWrapper);
    }

    @Override
    public void updateBatchByProjectBranchUniqId(List<IssueSummary> needUpdateList) {
        if (CollectionUtils.isEmpty(needUpdateList)) {
            return;
        }
        List<IssueSummaryVo> summaryVos = Lists.newArrayList();
        for (IssueSummary issueSummary : needUpdateList) {
            IssueSummaryVo issueSummaryVo = new IssueSummaryVo();
            issueSummaryVo.setGitBranch(issueSummary.getGitBranch());
            issueSummaryVo.setGitProjectId(issueSummary.getGitProjectId());
            issueSummaryVo.setIssueUniqId(issueSummary.getIssueUniqId());
            issueSummaryVo.setIssueUniqIdV2(issueSummary.getIssueUniqIdV2());
            issueSummaryVo.setStatus(issueSummary.getStatus());
            issueSummaryVo.setIssueLink(issueSummary.getIssueLink());
            issueSummaryVo.setSonarIssueKey(StringUtils.defaultString(issueSummary.getSonarIssueKey()));
            issueSummaryVo.setGmtModified(DateUtils.getMillTimestampFromLocalDateTime(issueSummary.getGmtModified()));
            issueSummaryVo.setRepairTime(Objects.nonNull(issueSummary.getRepairTime())
                                         ? DateUtils.getMillTimestampFromLocalDateTime(issueSummary.getRepairTime()) : 0L);
            issueSummaryVo.setScanMode(issueSummary.getScanMode());
            issueSummaryVo.setSeverity(issueSummary.getSeverity());
            issueSummaryVo.setGitLink(issueSummary.getGitLink());
            issueSummaryVo.setStartLine(issueSummary.getStartLine());
            issueSummaryVo.setEndLine(issueSummary.getEndLine());
            issueSummaryVo.setStartOffset(issueSummary.getStartOffset());
            issueSummaryVo.setEndOffset(issueSummary.getEndOffset());
            issueSummaryVo.setLocation(issueSummary.getLocation());
            issueSummaryVo.setRepairType(Objects.isNull(issueSummary.getRepairType()) ? 0 : issueSummary.getRepairType());
            issueSummaryVo.setMessage(issueSummary.getMessage());
            summaryVos.add(issueSummaryVo);
        }
        // 分批更新
        CommonUtils.processInBatches(summaryVos, PAGE_SIZE, batch -> {
            issueSummaryMapper.updateBatchByProjectBranchUniqId(batch);
        });
    }

    @Override
    public List<IssueSummary> listStatisticsByUniqIds(List<String> issueUniqIds, String gitBranch) {
        if (CollectionUtils.isEmpty(issueUniqIds) || StringUtils.isEmpty(gitBranch)) {
            return Collections.emptyList();
        }
        return list(Wrappers.<IssueSummary> lambdaQuery().select(IssueSummary::getId,
                        IssueSummary::getType, IssueSummary::getStatus, IssueSummary::getSeverity,
                        IssueSummary::getIssueUniqId)
                .eq(IssueSummary::getGitBranch, gitBranch)
                .in(IssueSummary::getIssueUniqId, issueUniqIds)
                .eq(IssueSummary::getScanMode, ScanModeEnum.PROCESS.getCode())
        );
    }

    @Override
    public IssueSummary getByGitBranchUniqId(Integer gitProjectId, String gitBranch, String issueUniqId,
            Integer scanMode) {
        Wrapper<IssueSummary> queryWrapper = new QueryWrapper<IssueSummary>().lambda()
                .eq(IssueSummary::getGitProjectId, gitProjectId)
                .eq(IssueSummary::getGitBranch, gitBranch)
                .eq(IssueSummary::getIssueUniqId, issueUniqId)
                .eq(IssueSummary::getScanMode, scanMode);
        List<IssueSummary> summaryList = list(queryWrapper);
        if (CollectionUtils.isEmpty(summaryList)) {
            return null;
        }
        if (summaryList.size() > 1) {
            log.error("重复IssueSummary记录：projectId: {}, branch: {}, uniqId: {}, scanMode: {}",
                    gitProjectId, gitBranch, issueUniqId, scanMode);
        }
        return summaryList.get(0);
    }

    // CHECKSTYLE:ON

    @Override
    public Map<String, IssueSummary> sameBranchSummary(List<String> issueUniqIdList, String branch, Integer scanMode) {
        if (CollectionUtils.isEmpty(issueUniqIdList)) {
            return Collections.emptyMap();
        }
        // 所有issue，指定分支上的summary列表
        List<IssueSummary> list = list(Wrappers.<IssueSummary> lambdaQuery()
                .in(IssueSummary::getIssueUniqId, issueUniqIdList)
                .eq(IssueSummary::getGitBranch, branch)
                .eq(IssueSummary::getScanMode, scanMode));
        // 根据issue分组
        Map<String, List<IssueSummary>> issue2SummaryListMap =
                list.stream().collect(Collectors.groupingBy(IssueSummary::getIssueUniqId));
        // 每个issue取第一个summary
        return issue2SummaryListMap.entrySet().stream()
                .collect(Collectors.toMap(Entry::getKey, e -> e.getValue().get(0), (existing, replacement) -> existing));
    }


    @Override
    public Map<String, IssueSummary> sameBranchSummaryUseIssueUniqIdV2(List<String> issueUniqIdV2List, Integer gitProjectId,
            String branch, Integer scanMode) {
        if (CollectionUtils.isEmpty(issueUniqIdV2List)) {
            return Collections.emptyMap();
        }
        // 所有issue，指定分支上的summary列表
        List<IssueSummary> list = list(Wrappers.<IssueSummary> lambdaQuery()
                .in(IssueSummary::getIssueUniqIdV2, issueUniqIdV2List)
                .eq(IssueSummary::getGitProjectId, gitProjectId)
                .eq(IssueSummary::getGitBranch, branch)
                .eq(IssueSummary::getScanMode, scanMode));
        // 根据issue分组
        Map<String, List<IssueSummary>> issueV2ToSummaryListMap =
                list.stream().collect(Collectors.groupingBy(IssueSummary::getIssueUniqIdV2));
        // 每个issue取第一个summary
        return issueV2ToSummaryListMap.entrySet().stream()
                .collect(Collectors.toMap(Entry::getKey, e -> e.getValue().get(0), (existing, replacement) -> existing));
    }

    @Override
    public IPage<IssueSummary> pageByCondition(IssueSummaryListCondition condition) {
        Wrapper<IssueSummary> queryWrapper = buildQueryWrapper(condition);
        return this.page(new Page<>(condition.getPage(), condition.getPageSize()), queryWrapper);
    }

    private static Wrapper<IssueSummary> buildQueryWrapper(IssueSummaryListCondition condition) {
        List<String> severities = condition.getSeverities();
        List<String> types = condition.getTypes();
        List<String> statuses = condition.getStatuses();
        List<String> rules = condition.getRules();
        List<String> authors = condition.getAuthors();
        Collection<String> issueKeys = condition.getIssueKeys();
        LambdaQueryWrapper<IssueSummary> queryWrapper = new QueryWrapper<IssueSummary>().lambda()
                .eq(IssueSummary::getGitProjectId, condition.getGitProjectId())
                .eq(IssueSummary::getGitBranch, condition.getBranch())
                .in(CollectionUtils.isNotEmpty(types), IssueSummary::getType, types)
                .in(CollectionUtils.isNotEmpty(severities), IssueSummary::getSeverity, severities)
                .in(CollectionUtils.isNotEmpty(statuses), IssueSummary::getStatus, statuses)
                .in(CollectionUtils.isNotEmpty(rules), IssueSummary::getRule, rules)
                .in(CollectionUtils.isNotEmpty(authors), IssueSummary::getAuthor, authors)
                .eq(IssueSummary::getScanMode, condition.getScanMode())
                .eq(StringUtils.isNotBlank(condition.getLocation()), IssueSummary::getLocation, condition.getLocation());
        if (Objects.nonNull(condition.getMinGmtModified())) {
            queryWrapper.and(w -> w.gt(IssueSummary::getGmtModified,
                    DateUtils.getMillTimestampFromLocalDateTime(condition.getMinGmtModified()))
            );
        }
        if (CollectionUtils.isNotEmpty(issueKeys)) {
            queryWrapper.and((wrapper) -> wrapper
                    .in(IssueSummary::getIssueUniqId, issueKeys)
                    .or()
                    .in(IssueSummary::getCommonIssueUniqId, issueKeys)
                    .or()
                    .in(IssueSummary::getIssueUniqIdV2, issueKeys)
            );
        }
        return queryWrapper;
    }

    @Override
    public List<IssueSummary> listByCondition(IssueSummaryListCondition condition) {
        return list(buildQueryWrapper(condition));
    }

    @Override
    public List<Map<String, Object>> groupCountByCondition(IssueListRequest request, String groupColumn,
            Integer scanMode, List<String> uniqIds) {
        return groupCountByCondition(request, groupColumn, scanMode, uniqIds, Lists.newArrayList("OPEN", "RE_OPEN"));
    }

    @Override
    public List<Map<String, Object>> groupCountByCondition(IssueListRequest request, String groupColumn,
            Integer scanMode, List<String> uniqIds, List<String> status) {
        QueryWrapper<IssueSummary> wrapper = new QueryWrapper<>();
        wrapper.select(groupColumn + ",count(0) as count");
        wrapper.groupBy(groupColumn);
        wrapper.eq("git_project_id", request.getGitProjectId());
        wrapper.eq("git_branch", request.getBranch());
        if (CollectionUtils.isNotEmpty(uniqIds)) {
            wrapper.and(queryWrapper -> queryWrapper.lambda()
                    .in(IssueSummary::getIssueUniqId, uniqIds)
                    .or()
                    .in(IssueSummary::getCommonIssueUniqId, uniqIds)
                    .or()
                    .in(IssueSummary::getIssueUniqIdV2, uniqIds)
            );
        }
        if (StringUtils.isNotEmpty(request.getTypes())) {
            wrapper.in("type", Lists.newArrayList(request.getTypes().split(",")));
        }
        if (StringUtils.isNotEmpty(request.getSeverities())) {
            wrapper.in("severity", Lists.newArrayList(request.getSeverities().split(",")));
        }
        if (StringUtils.isNotEmpty(request.getRules())) {
            wrapper.in("rule", Lists.newArrayList(request.getRules().split(",")));
        }
        if (StringUtils.isNotEmpty(request.getAuthors())) {
            wrapper.in("author", Lists.newArrayList(request.getAuthors().split(",")));
        }
        wrapper.in("status", status);
        wrapper.eq("scan_mode", scanMode);
        return this.listMaps(wrapper);
    }

    @Override
    public Map<String, List<IssueSummary>> getSummaryUniqIdMapByIssueUniqIds(Collection<String> issueUniqIds) {
        if (CollectionUtils.isEmpty(issueUniqIds)) {
            return Collections.emptyMap();
        }
        Wrapper<IssueSummary> queryWrapper;
        // 使用分页查询，避免慢sql
        List<IssueSummary> issueSummaryList = Lists.newArrayListWithExpectedSize(issueUniqIds.size());
        final int pageSize = 500;
        List<List<String>> issueIdPageList = ListUtil.split(new ArrayList<>(issueUniqIds), pageSize);
        for (List<String> uniqIds : issueIdPageList) {
            queryWrapper = new QueryWrapper<IssueSummary>().lambda().in(IssueSummary::getIssueUniqId, uniqIds);
            issueSummaryList.addAll(list(queryWrapper));
        }
        return issueSummaryList.stream().collect(Collectors.groupingBy(IssueSummary::getIssueUniqId));
    }

    @Override
    public Map<String, Integer> countIssueListFacetNum(PCheckBase pCheckBase, SearchIssueRequest request) {
        return baseMapper.countFacetNum(pCheckBase.getProjectId().longValue(), pCheckBase.getBranch(),
                Stream.of(request.getIssues().split(SPLIT_REGEX)).collect(Collectors.toList()),
                Stream.of(request.getSeverities().split(SPLIT_REGEX)).collect(Collectors.toList()),
                Stream.of(request.getTypes().split(SPLIT_REGEX)).collect(Collectors.toList()),
                Stream.of(request.getStatuses().split(SPLIT_REGEX)).collect(Collectors.toList()));
    }

    @Override
    public Integer selectOpenIssueTypeCount(Integer gitProjectId, String branch, String issueType) {
        Wrapper<IssueSummary> queryWrapper = new QueryWrapper<IssueSummary>().lambda()
                .eq(IssueSummary::getGitProjectId, gitProjectId)
                .eq(IssueSummary::getGitBranch, branch)
                .in(IssueSummary::getStatus,
                        Lists.newArrayList(CheckIssueStatus.OPEN.getStatus(), CheckIssueStatus.RE_OPEN.getStatus()))
                .eq(IssueSummary::getType, issueType)
                .eq(IssueSummary::getScanMode, ScanModeEnum.OFFLINE.getCode());
        return issueSummaryMapper.selectCount(queryWrapper);
    }

    @Override
    public void deleteByGitProjectIdBranchAndScanMode(Integer gitProjectId, String branch, int scanMode) {
        Wrapper<IssueSummary> queryWrapper = new QueryWrapper<IssueSummary>().lambda()
                .eq(IssueSummary::getGitProjectId, gitProjectId)
                .eq(IssueSummary::getGitBranch, branch)
                .eq(IssueSummary::getScanMode, scanMode);
        remove(queryWrapper);
    }

    @Override
    public List<IssueSummary> listOfflineModeBetweenCreateTime(LocalDateTime startLocalDateTime,
            LocalDateTime endLocalDateTime) {
        long startTime = DateUtils.getMillTimestampFromLocalDateTime(startLocalDateTime);
        long endTime = DateUtils.getMillTimestampFromLocalDateTime(endLocalDateTime);
        Wrapper<IssueSummary> queryWrapper = new QueryWrapper<IssueSummary>().lambda()
                .between(IssueSummary::getCreateTime, startTime, endTime)
                .eq(IssueSummary::getScanMode, ScanModeEnum.OFFLINE.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public List<IssueSummary> listBySourceAnd0ReferType(String source) {
        Wrapper<IssueSummary> queryWrapper = new QueryWrapper<IssueSummary>().lambda()
                .eq(IssueSummary::getSource, source)
                .eq(IssueSummary::getExecutionReferType, 0)
                .last("limit 1000");
        return this.list(queryWrapper);
    }

    @Override
    public IPage<IssueSummary> pageByNoKdevPlatformReferType1Issue(int page, int pageSize) {
        IPage<IssueSummary> iPage = new Page<>(page, pageSize);
        Wrapper<IssueSummary> wrapper = Wrappers.<IssueSummary> lambdaQuery().select()
                .eq(IssueSummary::getExecutionReferType, 1)
                .eq(IssueSummary::getScanMode, 1)
                .ne(IssueSummary::getSonarBranch, "")
                .notLike(IssueSummary::getIssueLink, "kdev.corp.kuaishou.com")
                .orderByAsc(IssueSummary::getId);
        return page(iPage, wrapper);
    }

    @Override
    public List<IssueSummary> listByGitProjectIdAndScanMode(Integer gitProjectId, String rule, int scanModeCode) {
        Wrapper<IssueSummary> wrapper = Wrappers.<IssueSummary> lambdaQuery().select()
                .eq(IssueSummary::getGitProjectId, gitProjectId)
                .eq(IssueSummary::getScanMode, scanModeCode)
                .eq(IssueSummary::getRule, rule);
        return list(wrapper);
    }

    @Override
    public List<String> listIssueUniqIdsByGitProjectId(int gitProjectId) {
        QueryWrapper<IssueSummary> wrapper = new QueryWrapper<>();
        wrapper.select("distinct issue_uniq_id");
        wrapper.eq("git_project_id", gitProjectId);
        return list(wrapper).stream().map(IssueSummary::getIssueUniqId).collect(Collectors.toList());
    }

    @Override
    public List<Integer> getGitProjectIds() {
        QueryWrapper<IssueSummary> wrapper = new QueryWrapper<>();
        wrapper.select("distinct git_project_id");
        return list(wrapper).stream().map(IssueSummary::getGitProjectId).collect(Collectors.toList());
    }

    @Override
    public List<IssueSummary> listInCommonIssueUniqIds(Collection<String> commonIssueUniqIds) {
        if (CollectionUtils.isEmpty(commonIssueUniqIds)) {
            return Collections.emptyList();
        }
        return list(Wrappers.<IssueSummary> lambdaQuery().in(IssueSummary::getCommonIssueUniqId, commonIssueUniqIds));
    }

    @Override
    public void copyAndChangeColumn() {
        int count = count();
        int cycles = count / PAGE_SIZE;
        for (int page = 1; page <= cycles + 1; page++) {
            int finalPage = page;
            issueSummarySyncExecutor.execute(() -> {
                Page<IssueSummary> pageRecord = page(
                        new Page<>(finalPage, PAGE_SIZE),
                        Wrappers.<IssueSummary> lambdaQuery()
                                .select(IssueSummary::getId, IssueSummary::getIssueUniqId,
                                        IssueSummary::getCommonIssueUniqId,
                                        IssueSummary::getIssueUniqIdCopy,
                                        IssueSummary::getCommonIssueUniqIdCopy)
                                .orderByAsc(IssueSummary::getId)

                );
                List<IssueSummary> issueSummaryList = pageRecord.getRecords();
                if (CollectionUtils.isNotEmpty(issueSummaryList)) {
                    copyNewColumn(issueSummaryList);
                }
            });
        }
    }

    @Override
    public void swapColumnValues() {
        int count = count();
        int cycles = count / PAGE_SIZE;
        for (int page = 1; page <= cycles + 1; page++) {
            int finalPage = page;
            issueSummarySyncExecutor.execute(() -> {
                Page<IssueSummary> pageRecord = page(
                        new Page<>(finalPage, PAGE_SIZE),
                        Wrappers.<IssueSummary> lambdaQuery()
                                .select(IssueSummary::getId, IssueSummary::getIssueUniqId,
                                        IssueSummary::getCommonIssueUniqId,
                                        IssueSummary::getIssueUniqIdCopy,
                                        IssueSummary::getCommonIssueUniqIdCopy)
                                .orderByAsc(IssueSummary::getId)
                );
                List<IssueSummary> issueSummaryList = pageRecord.getRecords();
                if (CollectionUtils.isNotEmpty(issueSummaryList)) {
                    changeNewColumn(issueSummaryList);
                }
            });
        }
    }

    @Override
    public String getLocationById(Long id) {
        if (id == null || id < 1) {
            return null;
        }
        return getBaseMapper().getLocationById(id);
    }

    private void copyNewColumn(List<IssueSummary> issueSummaryList) {
        if (CollectionUtils.isEmpty(issueSummaryList)) {
            return;
        }
        for (IssueSummary issueSummary : issueSummaryList) {
            issueSummary.setCommonIssueUniqIdCopy(issueSummary.getCommonIssueUniqId());
            issueSummary.setIssueUniqIdCopy(issueSummary.getIssueUniqId());
        }
        updateBatchById(issueSummaryList);
    }

    private void changeNewColumn(List<IssueSummary> issueSummaryList) {
        if (CollectionUtils.isEmpty(issueSummaryList)) {
            return;
        }
        for (IssueSummary issueSummary : issueSummaryList) {
            issueSummary.setCommonIssueUniqId(issueSummary.getIssueUniqIdCopy());
            issueSummary.setIssueUniqId(issueSummary.getCommonIssueUniqIdCopy());
        }
        updateBatchById(issueSummaryList);
    }

}
