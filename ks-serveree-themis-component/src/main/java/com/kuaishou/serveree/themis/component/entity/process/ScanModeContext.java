package com.kuaishou.serveree.themis.component.entity.process;

import java.util.List;

import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/3/23 11:18 上午
 */
@Data
public class ScanModeContext {

    private List<PCheckIssue> pCheckIssueList;

    private PCheckBase pCheckBase;

    private PCheckExecution pCheckExecution;

    private String kimNoticeUrl;

}
