package com.kuaishou.serveree.themis.component.client.sonar.api;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/8/4 3:03 下午
 */
@Component
public class MetricSonarApi implements SonarCommonApi {

    @Value("${sonar.metric-url}")
    private String sonarUrl;

    @Value("${sonar.timeout}")
    private Integer timeout;

    @Value("${sonar.metric-basic-auth}")
    private String basicAuth;

    @Override
    public String sonarUrl() {
        return sonarUrl;
    }

    @Override
    public Integer timeout() {
        return timeout;
    }

    @Override
    public String basicAuth() {
        return basicAuth;
    }

}
