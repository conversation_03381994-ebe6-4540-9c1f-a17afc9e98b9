package com.kuaishou.serveree.themis.component.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-12
 */
@Data
@TableName(value = "sonar_hit_cache_record")
public class SonarHitCacheRecordPO {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long gitProjectId;

    private Long currentKspBuildId;

    private Long currentKspPipelineId;

    private Long cacheKspBuildId;

    private Long cacheKspPipelineId;

    private Boolean hitCache;

    //补充信息json格式
    private String ext;

    private Long jobDuration;

    private String creator;         // 创建人

    private Long createdTime;        // 创建时间

    private String updater;         // 最后修改人

    private Long updatedTime;        // 最后修改时间

}
