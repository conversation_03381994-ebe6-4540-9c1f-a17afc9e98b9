package com.kuaishou.serveree.themis.component.config.datasource;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.naming.NamingException;
import javax.sql.DataSource;

import org.apache.shardingsphere.infra.exception.ShardingSphereException;
import org.apache.shardingsphere.sharding.support.InlineExpressionParser;
import org.apache.shardingsphere.spring.boot.datasource.prop.impl.DataSourcePropertiesSetterHolder;
import org.apache.shardingsphere.spring.boot.util.DataSourceUtil;
import org.apache.shardingsphere.spring.boot.util.PropertyUtil;
import org.springframework.core.env.Environment;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.jndi.JndiObjectFactoryBean;
import org.springframework.util.StringUtils;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * Data source map setter.
 *
 * 拷贝自 org.apache.shardingsphere.spring.boot.datasource.DataSourceMapSetter
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ThemisDataSourceMapSetter {

    private static final String PREFIX = "spring.shardingsphere.datasource.";

    private static final String DATA_SOURCE_NAME = "name";

    private static final String DATA_SOURCE_NAMES = "names";

    private static final String DATA_SOURCE_TYPE = "type";

    private static final String JNDI_NAME = "jndi-name";


    /**
     * 数据源账号密码Kconf配置
     */
    public static final Kconf<HashMap<String, Map<String, String>>> KCONF_DATASOURCE_CONFIG =
            Kconfs.ofMapMap("qa.themis.dataSourceConfigMap", Maps.newHashMap(),
                    String.class, String.class, String.class).build();

    private static final String DATA_SOURCE_USERNAME = "username";
    private static final String DATA_SOURCE_PASSWORD = "password";

    /**
     * Get data source map.
     *
     * @param environment spring boot environment
     * @return data source map
     */
    public static Map<String, DataSource> getDataSourceMap(final Environment environment) {

        Map<String, DataSource> result = new LinkedHashMap<>();
        for (String each : getDataSourceNames(environment)) {
            try {
                result.put(each, getDataSource(environment, each));
            } catch (final ReflectiveOperationException ex) {
                throw new ShardingSphereException("Can't find data source type.", ex);
            } catch (final NamingException ex) {
                throw new ShardingSphereException("Can't find JNDI data source.", ex);
            }
        }
        return result;
    }

    private static List<String> getDataSourceNames(final Environment environment) {
        StandardEnvironment standardEnv = (StandardEnvironment) environment;
        standardEnv.setIgnoreUnresolvableNestedPlaceholders(true);
        String dataSourceNames = standardEnv.getProperty(PREFIX + DATA_SOURCE_NAME);
        if (StringUtils.isEmpty(dataSourceNames)) {
            dataSourceNames = standardEnv.getProperty(PREFIX + DATA_SOURCE_NAMES);
        }
        return new InlineExpressionParser(dataSourceNames).splitAndEvaluate();
    }

    @SuppressWarnings("unchecked")
    private static DataSource getDataSource(final Environment environment, final String dataSourceName)
            throws ReflectiveOperationException, NamingException {
        Map<String, Object> dataSourceProps =
                PropertyUtil.handle(environment, String.join("", PREFIX, dataSourceName), Map.class);
        Preconditions.checkState(!dataSourceProps.isEmpty(), "Wrong datasource [%s] properties.", dataSourceName);
        // 补充 username 和 password
        HashMap<String, Map<String, String>> configMap = KCONF_DATASOURCE_CONFIG.get();
        dataSourceProps.put(DATA_SOURCE_USERNAME, configMap.getOrDefault(dataSourceName, new HashMap<>()).get(DATA_SOURCE_USERNAME));
        dataSourceProps.put(DATA_SOURCE_PASSWORD, configMap.getOrDefault(dataSourceName, new HashMap<>()).get(DATA_SOURCE_PASSWORD));
        if (dataSourceProps.containsKey(JNDI_NAME)) {
            return getJNDIDataSource(dataSourceProps.get(JNDI_NAME).toString());
        }
        DataSource result =
                DataSourceUtil.getDataSource(dataSourceProps.get(DATA_SOURCE_TYPE).toString(), dataSourceProps);
        DataSourcePropertiesSetterHolder.getDataSourcePropertiesSetterByType(
                dataSourceProps.get(DATA_SOURCE_TYPE).toString()).ifPresent(
                propsSetter -> propsSetter.propertiesSet(environment, PREFIX, dataSourceName, result));
        return result;
    }

    private static DataSource getJNDIDataSource(final String jndiName) throws NamingException {
        JndiObjectFactoryBean bean = new JndiObjectFactoryBean();
        bean.setResourceRef(true);
        bean.setJndiName(jndiName);
        bean.setProxyInterface(DataSource.class);
        bean.afterPropertiesSet();
        return (DataSource) bean.getObject();
    }
}