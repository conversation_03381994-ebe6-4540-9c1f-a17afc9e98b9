package com.kuaishou.serveree.themis.component.client.ares;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.common.entity.KsUserInfo;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.entity.ares.AresMessageEntity;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.response.KsUserInfoResponse;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/10/12 6:11 下午
 */
@Component
@Slf4j
public class AresApi {

    @Value("${ares.kim-notice.app-id}")
    private String appId;

    @Value("${ares.url}")
    private String url;

    @Value("${ares.timeout}")
    private Integer timeout;

    @Value("${ares.auth-token}")
    private String authToken;

    // 接口调用限制，每次最多查100个
    private static final int USER_INFO_BATCH_SIZE_LIMIT = 100;

    public String sendKimNotice(AresMessageEntity aresMessageEntity) {
        String requestUrl = url + "/api/ares/notification/text";
        aresMessageEntity.setAppId(appId);
        HttpResponse httpResponse = HttpRequest
                .post(requestUrl)
                .body(JSONUtils.serialize(aresMessageEntity))
                .timeout(timeout)
                .execute();
        if (!httpResponse.isOk()) {
            return "调用ares发送kim通知失败，" + httpResponse;
        }
        return httpResponse.body();
    }

    public List<KsUserInfo> batchGetUserInfo(Collection<String> userNames) {
        List<KsUserInfo> userInfoList = Lists.newArrayList();
        // ares接口限制，分批处理
        CommonUtils.processInBatches(userNames, USER_INFO_BATCH_SIZE_LIMIT, list -> {
            List<KsUserInfo> ksUserInfos = internalBatchGetUserInfo(list);
            if (CollectionUtils.isNotEmpty(ksUserInfos)) {
                userInfoList.addAll(ksUserInfos);
            }
        });
        return userInfoList;
    }

    private List<KsUserInfo> internalBatchGetUserInfo(Collection<String> userNames) {
        String requestUrl = url + "/api/ares/permission/outer/direct/users/names";
        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("userNames", String.join(",", userNames));
        HttpResponse httpResponse = HttpRequest
                .get(requestUrl)
                .form(reqMap)
                .timeout(timeout)
                .header("Authentication", authToken)
                .execute();
        if (!httpResponse.isOk()) {
            throw new ThemisException(-1, "调用ares获取用户信息失败，" + httpResponse);
        }
        String body = httpResponse.body();
        if (StringUtils.isEmpty(body)) {
            throw new ThemisException(-1, "调用ares获取用户信息返回为空");
        }
        KsUserInfoResponse infoResponse = JSONUtils.deserialize(body, new TypeReference<KsUserInfoResponse>() {
        });
        // 判空
        if (Objects.isNull(infoResponse) || Objects.isNull(infoResponse.getData())) {
            return Collections.emptyList();
        }
        return infoResponse.getData().getUserList();
    }
}
