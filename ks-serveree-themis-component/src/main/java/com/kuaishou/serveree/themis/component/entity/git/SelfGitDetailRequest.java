package com.kuaishou.serveree.themis.component.entity.git;

/**
 * <AUTHOR>
 * @since 2022/8/29 2:57 PM
 */
public class SelfGitDetailRequest {

    private Integer gitProjectId;
    private Boolean statistics;

    public SelfGitDetailRequest() {
    }

    private SelfGitDetailRequest(Builder builder) {
        setGitProjectId(builder.gitProjectId);
        setStatistics(builder.statistics);
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public Integer getGitProjectId() {
        return gitProjectId;
    }

    public void setGitProjectId(Integer gitProjectId) {
        this.gitProjectId = gitProjectId;
    }

    public Boolean getStatistics() {
        return statistics;
    }

    public void setStatistics(Boolean statistics) {
        this.statistics = statistics;
    }

    public static final class Builder {
        private Integer gitProjectId;
        private Boolean statistics;

        private Builder() {
        }

        public Builder gitProjectId(Integer val) {
            gitProjectId = val;
            return this;
        }

        public Builder statistics(Boolean val) {
            statistics = val;
            return this;
        }

        public SelfGitDetailRequest build() {
            return new SelfGitDetailRequest(this);
        }
    }
}
