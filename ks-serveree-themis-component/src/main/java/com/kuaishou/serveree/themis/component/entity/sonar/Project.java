package com.kuaishou.serveree.themis.component.entity.sonar;

import java.util.List;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/21 3:19 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Project {

    private String key;

    private String name;

    private String url;

    private List<String> tags;

    private String visibility;

    private String analysisDate;

    private Measures measures;

    private String qualifier;

    // 以下为电商特需 定制化开发
    /**
     * 团队
     */
    private String projectTeam = StringUtils.EMPTY;

    /**
     * 项目描述
     */
    private String projectDescription = StringUtils.EMPTY;

    /**
     * 项目git地址
     */
    private String projectGitRepoUrl = StringUtils.EMPTY;

    /**
     * 项目总数 逻辑匹配
     */
    private Integer totalCount;
}
