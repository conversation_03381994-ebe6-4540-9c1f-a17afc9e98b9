package com.kuaishou.serveree.themis.component.entity.sonar.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-05-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MeasureComponentTreeRequest extends SonarBaseRequest {
    private String additionalFields;
    private String asc;
    private String component;
    private String metricKeys;
    private int metricPeriodSort;
    private String metricSort;
    private String metricSortFilter;
    private int page;
    private int pageSize;
    private String q;
    private String qualifiers;
    private String s;
    private String strategy;

    private String target;
    private String branch;
}
