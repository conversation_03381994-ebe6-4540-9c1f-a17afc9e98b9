package com.kuaishou.serveree.themis.component.service.kdev.impl;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.INVALID_PARAMS;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.MR_STUCK_SPONSOR_CODE_SCAN_FAIL;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.OPEN_API_KDEV_PIPELINE_CREATE_FAIL;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.OPEN_API_KDEV_PIPELINE_TRIGGER_FAIL;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.UNSUPPORTED_LANGUAGE;
import static com.kuaishou.serveree.themis.component.constant.platform.PlatformLanguageEnum.JAVA;
import static com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant.PLATFORM_REPO_MR_STUCK_PIPELINE_PREFIX;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.phantomthief.util.MoreFunctions;
import com.google.common.base.Functions;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.MrCheckPointResult;
import com.kuaishou.serveree.themis.component.common.entity.MrStuckPipeline;
import com.kuaishou.serveree.themis.component.common.entity.MrStuckRecord;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.SonarPipelineMeasure;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.kdev.KdevPipelineJobStatusEnum;
import com.kuaishou.serveree.themis.component.constant.kdev.MrCheckpointEnum;
import com.kuaishou.serveree.themis.component.constant.kdev.MrStuckPointResultStatus;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.entity.issue.MrCheckpointIssueBo;
import com.kuaishou.serveree.themis.component.entity.kdev.AbstractOpenapiRequestCreator;
import com.kuaishou.serveree.themis.component.entity.kdev.KdevResponse;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineCreateRequest;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineTriggerRequest;
import com.kuaishou.serveree.themis.component.entity.mr.MrStuckResultData;
import com.kuaishou.serveree.themis.component.entity.platform.IssueSummaryListCondition;
import com.kuaishou.serveree.themis.component.entity.plugin.changedFiles.ChangedFilesResponse.ChangedFile;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoLanguageService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryBaseService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.MrStuckPipelineService;
import com.kuaishou.serveree.themis.component.service.MrStuckRecordService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PCheckExecutionService;
import com.kuaishou.serveree.themis.component.service.PCheckIssueService;
import com.kuaishou.serveree.themis.component.service.SonarIssueService;
import com.kuaishou.serveree.themis.component.service.SonarPipelineMeasureService;
import com.kuaishou.serveree.themis.component.service.kdev.MrCheckpointBo;
import com.kuaishou.serveree.themis.component.service.kdev.MrCheckpointTemplateService;
import com.kuaishou.serveree.themis.component.service.kdev.MrStuckPointService;
import com.kuaishou.serveree.themis.component.service.openapi.KdevOpenApi;
import com.kuaishou.serveree.themis.component.service.openapi.KdevOpenApi.PipelineCreateResponse;
import com.kuaishou.serveree.themis.component.service.openapi.KdevOpenApi.PipelineTriggerResponse;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoService;
import com.kuaishou.serveree.themis.component.service.platform.notice.CheckNoticeActionService;
import com.kuaishou.serveree.themis.component.service.plugin.SonarPluginService;
import com.kuaishou.serveree.themis.component.utils.IssueUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.KdevPipelineCallbackRequest.KdevPipelineBuildInfo;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrCheckpointResultRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckGetOrCreateProjectRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckIssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.response.kdev.MrCheckpointResultVo;
import com.kuaishou.serveree.themis.component.vo.response.kdev.MrCheckpointSponsorResponse;
import com.kuaishou.serveree.themis.component.vo.response.kdev.MrStuckRepoDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SonarIssueVo;

import cn.hutool.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-16
 */
@Slf4j
@Service
public class MrCheckpointCodeScanService extends MrCheckpointTemplateService implements MrStuckPointService {

    private final CheckRepoService checkRepoService;
    private final MrStuckPipelineService mrStuckPipelineService;
    private final MrStuckRecordService mrStuckRecordService;
    private final KdevOpenApi kdevOpenApi;
    private final PlatformRepoService platformRepoService;
    private final KsRedisLock redisLock;
    private final SonarIssueService sonarIssueService;
    private final PCheckBaseService pCheckBaseService;
    private final PCheckIssueService pCheckIssueService;
    private final IssueSummaryBaseService issueSummaryBaseService;
    private final IssueSummaryService issueSummaryService;
    private final CheckRepoBranchService checkRepoBranchService;
    private final List<AbstractOpenapiRequestCreator> openapiRequestCreatorList;
    private final SonarPipelineMeasureService pipelineMeasureService;
    private final CheckNoticeActionService checkNoticeActionService;
    private final GitOperations gitOperations;
    private final CheckRepoLanguageService checkRepoLanguageService;
    private final SonarPluginService sonarPluginService;
    private final PCheckExecutionService pCheckExecutionService;

    @Value("${kdev.url}")
    private String kdevUrl;

    // CHECKSTYLE:OFF
    public MrCheckpointCodeScanService(CheckRepoService checkRepoService, MrStuckPipelineService mrStuckPipelineService,
            MrStuckRecordService mrStuckRecordService, KdevOpenApi kdevOpenApi,
            PlatformRepoService platformRepoService, KsRedisLock redisLock, SonarIssueService sonarIssueService,
            PCheckBaseService pCheckBaseService, PCheckIssueService pCheckIssueService,
            IssueSummaryBaseService issueSummaryBaseService, IssueSummaryService issueSummaryService,
            CheckRepoBranchService checkRepoBranchService,
            List<AbstractOpenapiRequestCreator> openapiRequestCreatorList,
            SonarPipelineMeasureService pipelineMeasureService, CheckNoticeActionService checkNoticeActionService,
            GitOperations gitOperations, CheckRepoLanguageService checkRepoLanguageService,
            SonarPluginService sonarPluginService, PCheckExecutionService pCheckExecutionService) {
        this.checkRepoService = checkRepoService;
        this.mrStuckPipelineService = mrStuckPipelineService;
        this.mrStuckRecordService = mrStuckRecordService;
        this.kdevOpenApi = kdevOpenApi;
        this.platformRepoService = platformRepoService;
        this.redisLock = redisLock;
        this.sonarIssueService = sonarIssueService;
        this.pCheckBaseService = pCheckBaseService;
        this.pCheckIssueService = pCheckIssueService;
        this.issueSummaryBaseService = issueSummaryBaseService;
        this.issueSummaryService = issueSummaryService;
        this.checkRepoBranchService = checkRepoBranchService;
        this.openapiRequestCreatorList = openapiRequestCreatorList;
        this.pipelineMeasureService = pipelineMeasureService;
        this.checkNoticeActionService = checkNoticeActionService;
        this.gitOperations = gitOperations;
        this.checkRepoLanguageService = checkRepoLanguageService;
        this.sonarPluginService = sonarPluginService;
        this.pCheckExecutionService = pCheckExecutionService;
    }
    // CHECKSTYLE:ON

    /**
     * 发起mr卡点流水线
     */
    @Transactional
    @Override
    public MrCheckpointSponsorResponse triggerCheck(MrCheckpointBo request, MrCheckPointResult resultRecord) {
        // 参数检查
        checkParam(request);
        // 获取diff列表
        List<ChangedFile> changedFiles = sonarPluginService.getCachedMrDiffList(
                request.getGitProjectId(), request.getMrId(), request.getCommitId()
        );
        // 获取diff失败
        if (Objects.isNull(changedFiles)) {
            log.error("[发起MR代码扫描检查失败]获取MR增量文件列表失败，request is {}", request);
            throw new ThemisException(MR_STUCK_SPONSOR_CODE_SCAN_FAIL.getCode(), "获取MR变更列表失败");
        }
        // 拿到对应语言的流水线请求创建器
        AbstractOpenapiRequestCreator openapiRequestCreator =
                getOpenapiRequestCreatorByLanguage(request.getRepoLanguage());
        // 可跳过扫描
        if (openapiRequestCreator.canSkipScan(changedFiles)) {
            // 保存结果
            saveCheckResult(request, Collections.emptyList());
            // 返回默认
            return MrCheckpointSponsorResponse.buildResp(checkpoint(), MrStuckPointResultStatus.SUCCESS);
        }
        // 若未创建项目，则默认创建
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(request.getGitProjectId());
        if (Objects.isNull(checkRepo)) {
            checkRepo =
                    platformRepoService.createProjectDefault(request.getGitProjectId(), request.getRepoLanguage());
        }
        // 若未创建流水线，则默认创建
        MrStuckPipeline stuckPipeline = mrStuckPipelineService.getByGitProjectId(request.getGitProjectId());
        if (Objects.isNull(stuckPipeline)) {
            stuckPipeline = createMrStuckPipeline(openapiRequestCreator, checkRepo);
            // 等待kdev主从同步
            MoreFunctions.runCatching(() -> TimeUnit.MILLISECONDS.sleep(300));
        }
        // 触发流水线执行
        PipelineTriggerResponse triggerResponse =
                triggerKdevPipeline(openapiRequestCreator, request, stuckPipeline.getKdevPipelineId(), changedFiles);
        // 保存数据库
        savePipelineExecuteRecord(request, triggerResponse);
        // 准备执行记录
        // 触发成功 保存流水线信息
        mrCheckPointResultService.updatePipelineInfoById(resultRecord.getId(), triggerResponse.getPipelineId(), triggerResponse.getId(), triggerResponse.getUrl());
        // 返回结果
        return MrCheckpointSponsorResponse.builder()
                .checkpointName(checkpoint())
                .kdevPipelineId(triggerResponse.getPipelineId())
                .kdevBuildId(triggerResponse.getId())
                .url(triggerResponse.getUrl())
                .status(MrStuckPointResultStatus.PROCESSING.name())
                .build();
    }

    @Override
    protected MrCheckpointIssueBo doIssueTransition(MrStuckIssueTransitionRequest request, String userName, MrCheckpointIssueBo issue) {
        // 查询构建记录
        MrStuckRecord mrStuckRecord =
                mrStuckRecordService.getLatestValidRecord(request.getGitProjectId().intValue(), request.getMrId().intValue(), request.getCommitId());
        // issue 打标
        SonarIssueVo sonarIssueVo = issueTransition(request, mrStuckRecord, userName);
        // 直接使用扫描表中的最新状态
        issue.setStatus(sonarIssueVo.getStatus());
        issue.setGmtModified(LocalDateTime.now());
        issue.setOperateTime(LocalDateTime.now());
        issue.setOperator(userName);
        return issue;
    }

    /**
     * 对某一次MR卡点扫描结果页的issue进行标注
     */
    private SonarIssueVo issueTransition(MrStuckIssueTransitionRequest request, MrStuckRecord mrStuckRecord, String userName) {
        // 构造issue transition 请求
        IssueTransitionRequest issueTransitionRequest = IssueTransitionRequest
                .builder()
                .transition(request.getTransition()).issueId(request.getIssueId())
                .kspBuildId(0L).executionReferType(ProcessExecutionReferType.MAVEN_SONAR.getType())
                .build();
        if (Objects.isNull(mrStuckRecord)) {
            // 没有有效卡点记录，直接打标，正常不会走到这里
            return sonarIssueService.issueTransition(issueTransitionRequest, userName);
        }
        // 填充额外信息
        issueTransitionRequest.setKspBuildId(mrStuckRecord.getKspBuildId());
        issueTransitionRequest.setExecutionReferType(getExecutionReferTypeByKspBuildId(mrStuckRecord.getKspBuildId()));
        // issue 打标
        return sonarIssueService.issueTransition(issueTransitionRequest, userName);
    }

    /**
     * 拿到本次扫描的扫描器类型
     */
    private int getExecutionReferTypeByKspBuildId(Long kspBuildId) {
        if (Objects.isNull(kspBuildId) || kspBuildId < 1) {
            // 默认
            return ProcessExecutionReferType.MAVEN_SONAR.getType();
        }
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(kspBuildId);
        if (Objects.isNull(pCheckBase)) {
            // 默认
            return ProcessExecutionReferType.MAVEN_SONAR.getType();
        }
        // 正常情况下 这个list只会有1个值
        List<PCheckExecution> checkExecutionList = pCheckExecutionService.listByPBaseId(pCheckBase.getId());
        if (CollectionUtils.isEmpty(checkExecutionList)) {
            // 默认
            return ProcessExecutionReferType.MAVEN_SONAR.getType();
        }
        ProcessExecutionReferType referType =
                ProcessExecutionReferType.getByType(checkExecutionList.get(0).getReferType());
        return Objects.nonNull(referType) ? referType.getType() : ProcessExecutionReferType.MAVEN_SONAR.getType();
    }

    /**
     * 获取mr卡点的issue信息（给前端）
     * @param request
     * @return
     */
    @Override
    public MrCheckpointResultVo getCheckpointResult(MrCheckpointResultRequest request) {
        // 查快照表
        MrCheckPointResult resultRecord =
                getMrCheckPointResultService().getByProjectIdBranchCommitIdCheckpoint(request.getGitProjectId(),
                        request.getMrId(), request.getCommitId(), request.getCheckpointName());
        // 快照表不存在
        if (Objects.isNull(resultRecord)) {
            // 初始化一条记录
            initMrCheckpointResult(request);
            // 从扫描表取，构造数据
            buildMrStuckResultData(request.getGitProjectId().intValue(), request.getMrId().intValue(), request.getCommitId());
        }
        return super.getCheckpointResult(request);
    }

    private void initMrCheckpointResult(MrCheckpointResultRequest request) {
        MrCheckpointBo mrCheckpointBo = new MrCheckpointBo();
        mrCheckpointBo.setCheckpointName(checkpoint());
        mrCheckpointBo.setGitProjectId(request.getGitProjectId().intValue());
        mrCheckpointBo.setMrId(request.getMrId().intValue());
        mrCheckpointBo.setCommitId(request.getCommitId());
        super.initCheckResult(mrCheckpointBo);
    }

    /**
     * 构造mr卡点详细结果
     * @param gitProjectId
     * @param mrId
     * @param commitId
     * @return
     */
    public List<MrCheckpointIssueBo> buildMrStuckResultData(Integer gitProjectId, Integer mrId, String commitId) {
        // 查询构建记录
        MrStuckRecord mrStuckRecord =
                mrStuckRecordService.getLatestValidRecord(gitProjectId, mrId, commitId);
        if (Objects.isNull(mrStuckRecord)) {
            log.warn("[获取MR卡点结果失败]不存在有效的MR卡点流水线记录，gitProjectId:[{}]，mrId:[{}]，commitId:[{}]",
                    gitProjectId, mrId, commitId);
            return null;
        }
        return buildMrStuckResultData(mrStuckRecord);
    }

    /**
     * 根据mr卡点流水线执行记录 构造卡点详细结果
     * @param mrStuckRecord
     * @return
     */
    private List<MrCheckpointIssueBo> buildMrStuckResultData(MrStuckRecord mrStuckRecord) {
        if (Objects.isNull(mrStuckRecord)) {
            return null;
        }
        // 还在执行 （kdev的枚举）
        if (KdevPipelineJobStatusEnum.RUNNING.getCode() == mrStuckRecord.getStatus()) {
            return null;
        }
        // 还在执行，kdev流水线还未回调，未补偿
        Long kspBuildId = mrStuckRecord.getKspBuildId();
        if (Objects.isNull(kspBuildId) || kspBuildId < 1) {
            return null;
        }
        // 流水线执行失败（回调，状态失败）（kdev的枚举）
        if (KdevPipelineJobStatusEnum.FAIL.getCode() == mrStuckRecord.getStatus()) {
            // 更新检查结果并同步CR
            saveCheckResult(mrStuckRecord.getGitProjectId(), mrStuckRecord.getMrId(), mrStuckRecord.getCommitId(), null);
            return null;
        }
        // 还在执行，插件未上报结果
        SonarPipelineMeasure reportedMeasure = pipelineMeasureService.getByKspBuildId(kspBuildId);
        if (Objects.isNull(reportedMeasure)) {
            return null;
        }
        // 查询本次所有issue
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(kspBuildId);
        List<MrCheckpointIssueBo> issueList = getMrCodeScanIssues(mrStuckRecord, pCheckBase);
        // 更新检查结果并同步CR
        saveCheckResult(mrStuckRecord.getGitProjectId(), mrStuckRecord.getMrId(), mrStuckRecord.getCommitId(), issueList);
        return issueList;
    }

    /**
     * 查询本次mr卡点扫描出的所有问题
     */
    private List<MrCheckpointIssueBo> getMrCodeScanIssues(MrStuckRecord mrStuckRecord, PCheckBase pCheckBase) {
        // 查询本次扫描出的所有问题
        // 本次卡点issue
        List<PCheckIssue> issueList = pCheckIssueService.listByPBaseIdAndType(pCheckBase.getId(),
                ProcessExecutionReferType.MAVEN_SONAR.getType());
        // 空集合，不用再查数据库
        if (CollectionUtils.isEmpty(issueList)) {
            return Collections.emptyList();
        }
        // 查询summary表并转为vo
        return getAndConvertIssue(pCheckBase, issueList);
    }

    /**
     * 查询issue summary 并转为 SonarIssueVo
     */
    private List<MrCheckpointIssueBo> getAndConvertIssue(PCheckBase pCheckBase, List<PCheckIssue> issueList) {
        // 是否卡点
        Map<String, Boolean> validStuckMap =
                issueList.stream().collect(Collectors.toMap(IssueUtils::getIssueUniqkey, PCheckIssue::getValidStuck, (a, b) -> a));
        // 查询issue summary记录
        Map<String, IssueSummary> summaryMap = issueSummaryService.listByCondition(IssueSummaryListCondition
                        .builder()
                        .gitProjectId(pCheckBase.getProjectId())
                        .branch(pCheckBase.getBranch())
                        .scanMode(ScanModeEnum.PROCESS.getCode())
                        .issueKeys(validStuckMap.keySet())
                        .build())
                .stream().collect(Collectors.toMap(IssueUtils::getIssueUniqkey, Functions.identity(), (a, b) -> a));
        // 得到最后修改者
        Map<String, String> modifierMap =
                issueSummaryBaseService.getModifierByIssueUniqKeys(summaryMap.keySet());
        // 转换返回结构
        List<MrCheckpointIssueBo> mrCheckpointIssueBoList = new ArrayList<>();
        for (IssueSummary issueSummary : summaryMap.values()) {
            MrCheckpointIssueBo issue = new MrCheckpointIssueBo();
            issue.setCheckpointName(checkpoint());
            issue.setIssueId(issueSummary.getId());
            issue.setIssueUniqId(issueSummary.getIssueUniqId());
            issue.setRule(issueSummary.getRule());
            issue.setSeverity(issueSummary.getSeverity());
            issue.setType(issueSummary.getType());
            issue.setStuck(Boolean.TRUE.equals(validStuckMap.get(IssueUtils.getIssueUniqkey(issueSummary))));
            issue.setStatus(issueSummary.getStatus());
            issue.setMessage(issueSummary.getMessage());
            issue.setLocation(issueSummary.getLocation());
            issue.setStartLine(issueSummary.getStartLine());
            issue.setEndLine(issueSummary.getEndLine());
            issue.setStartOffset(issueSummary.getStartOffset());
            issue.setEndOffset(issueSummary.getEndOffset());
            issue.setAuthor(issueSummary.getAuthor());
            issue.setOperator(modifierMap.get(IssueUtils.getIssueUniqkey(issueSummary)));
            issue.setOperateTime(issueSummary.getGmtModified());
            issue.setGmtCreate(issueSummary.getGmtCreate());
            issue.setGmtModified(issueSummary.getGmtModified());
            mrCheckpointIssueBoList.add(issue);
        }
        return mrCheckpointIssueBoList;
    }

    /**
     * 获取项目地址或创建项目再获取地址
     * @param request
     * @return
     */
    @Override
    public MrStuckRepoDetailResponse getOrCreateProject(MrStuckGetOrCreateProjectRequest request) {
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(request.getGitProjectId());
        // 数据库中不存在
        if (Objects.isNull(checkRepo)) {
            // 查询gitlab
            if (!gitOperations.isJavaProject(request.getGitProjectId())) {
                throw new ThemisException(INVALID_PARAMS.getCode(), "暂时只支持Java项目");
            }
            // 创建默认项目
            checkRepo =
                    platformRepoService.createProjectDefault(request.getGitProjectId(), "java");
        }
        // 数据库存在
        if (!checkRepoLanguageService.isJavaProject(checkRepo.getId())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "暂时只支持Java项目");
        }
        CheckRepoBranch branch = checkRepoBranchService.getOfflineScanBranchByRepoId(checkRepo.getId());
        return MrStuckRepoDetailResponse.builder()
                .repoDetailUrl(
                        String.format(kdevUrl + "/web/codescan/project/detail?gitProjectId=%s&branch=%s&open=true",
                        checkRepo.getGitProjectId(), branch.getBranchName())
                )
                .build();
    }

    /**
     * 结束流水线运行（收到回调、主动获取到执行结果）
     */
    @Transactional
    @Override
    public void finishKdevPipelineExecute(KdevPipelineBuildInfo kdevPipelineBuildInfo) {
        // 更新kdev流水线执行记录
        MrStuckRecord mrStuckRecord =
                mrStuckRecordService.getByKdevPipelineIdAndKdevBuildId(kdevPipelineBuildInfo.getPipelineId(),
                        kdevPipelineBuildInfo.getId());
        // 已经是结束态【成功、失败、取消】
        if (mrStuckRecord.getStatus() != KdevPipelineJobStatusEnum.RUNNING.getCode()) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        mrStuckRecord.setKspBuildId(kdevPipelineBuildInfo.getKspBuildId());
        mrStuckRecord.setGmtModified(now);
        mrStuckRecord.setStatus(kdevPipelineBuildInfo.getStatus());
        mrStuckRecord.setStatusDesc(kdevPipelineBuildInfo.getStatusDesc());
        mrStuckRecord.setFailReason(kdevPipelineBuildInfo.getFailReason());
        mrStuckRecordService.updateById(mrStuckRecord);
        // 保存卡点结果
        List<MrCheckpointIssueBo> issueList = buildMrStuckResultData(mrStuckRecord);
        // 构造最新数据，通知mr创建者
        MrCheckPointResult checkResult =
                mrCheckPointResultService.getByProjectIdBranchCommitIdCheckpoint(
                        Long.valueOf(mrStuckRecord.getGitProjectId()), Long.valueOf(mrStuckRecord.getMrId()),
                        mrStuckRecord.getCommitId(), checkpoint());
        MoreFunctions.runCatching(() -> checkNoticeActionService.sendMrStuckResultNotice(
                MrStuckResultData.builder()
                        .projectId(mrStuckRecord.getGitProjectId())
                        .mrId(mrStuckRecord.getMrId())
                        .commitId(mrStuckRecord.getCommitId())
                        .status(checkResult.getStatus())
                        .kdevPipelineId(mrStuckRecord.getKdevPipelineId())
                        .kdevBuildId(mrStuckRecord.getKdevBuildId())
                        .kdevBuildUrl(mrStuckRecord.getKdevBuildUrl())
                        .mrTitle(mrStuckRecord.getMrTitle())
                        .mrUrl(mrStuckRecord.getMrUrl())
                        .mrCreator(mrStuckRecord.getMrCreator())
                        .issues(issueList)
                        .build())
        );
    }

    /**
     * 保存kdev流水线执行记录
     */
    private void savePipelineExecuteRecord(MrCheckpointBo request, PipelineTriggerResponse triggerResponse) {
        MrStuckRecord mrStuckRecord = new MrStuckRecord();
        LocalDateTime now = LocalDateTime.now();
        mrStuckRecord.setGitProjectId(request.getGitProjectId());
        mrStuckRecord.setSourceBranch(request.getSourceBranch());
        mrStuckRecord.setTargetBranch(request.getTargetBranch());
        mrStuckRecord.setMrId(request.getMrId());
        mrStuckRecord.setCommitId(request.getCommitId());
        mrStuckRecord.setStuckSeverity(request.getStuckSeverity());
        mrStuckRecord.setOnlyDiff(request.isOnlyDiff());
        mrStuckRecord.setKdevPipelineId(triggerResponse.getPipelineId());
        mrStuckRecord.setKdevBuildId(triggerResponse.getId());
        mrStuckRecord.setKdevBuildUrl(triggerResponse.getUrl());
        mrStuckRecord.setStatus(KdevPipelineJobStatusEnum.RUNNING.getCode());
        mrStuckRecord.setStatusDesc(KdevPipelineJobStatusEnum.RUNNING.getDesc());
        mrStuckRecord.setMrCreator(request.getMrCreator());
        mrStuckRecord.setMrTitle(request.getMrTitle());
        mrStuckRecord.setMrUrl(request.getMrUrl());
        mrStuckRecord.setGmtCreate(now);
        mrStuckRecord.setGmtModified(now);
        mrStuckRecordService.save(mrStuckRecord);
    }

    /**
     * 触发kdev流水线执行
     */
    private PipelineTriggerResponse triggerKdevPipeline(AbstractOpenapiRequestCreator openapiRequestCreator,
            MrCheckpointBo request, long kdevPipelineId, List<ChangedFile> changedFiles) {
        // 取消之前已在运行的流水线
        stopPipelineAlreadyRunning(openapiRequestCreator, request.getGitProjectId(), request.getMrId());
        // 构造触发请求
        PipelineTriggerRequest triggerRequest = openapiRequestCreator.buildPipelineTriggerRequest(request, kdevPipelineId, changedFiles);
        KdevResponse<PipelineTriggerResponse> response = null;
        try {
            log.info("触发kdev流水线，request is : {}", JSONUtils.serialize(triggerRequest));
            response = kdevOpenApi.pipelineTrigger(triggerRequest).execute().body();
        } catch (IOException e) {
            log.error("BBB触发kdev流水线失败，response is : {}", JSONUtils.serialize(response));
            throw new ThemisException(OPEN_API_KDEV_PIPELINE_TRIGGER_FAIL);
        }
        if (Objects.isNull(response) || HttpStatus.HTTP_OK != response.getStatus()) {
            log.error(String.format("触发kdev流水线失败，response is : %s", JSONUtils.serialize(response)));
            throw new ThemisException(OPEN_API_KDEV_PIPELINE_TRIGGER_FAIL);
        }
        return response.getData();
    }

    /**
     * 停止同一个mr正在运行的流水线
     * @param openapiRequestCreator
     * @param gitProjectId
     * @param mrId
     */
    private void stopPipelineAlreadyRunning(AbstractOpenapiRequestCreator openapiRequestCreator, Integer gitProjectId, Integer mrId) {
        List<MrStuckRecord> runningRecords =
                mrStuckRecordService.listRunningRecordsByProjectIdAndMrId(gitProjectId, mrId);
        if (CollectionUtils.isEmpty(runningRecords)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        // 正常只有一个
        for (MrStuckRecord runningRecord : runningRecords) {
            try {
                KdevResponse cancelResponse = kdevOpenApi.pipelineCancel(
                            openapiRequestCreator.buildPipelineCancelRequest(
                                    runningRecord.getKdevPipelineId(), runningRecord.getKdevBuildId()
                            )
                        ).execute().body();
                if (Objects.isNull(cancelResponse) || HttpStatus.HTTP_OK != cancelResponse.getStatus()) {
                    log.warn("openapi取消kdev流水线失败，pipelineId: {}, buildId : {}, response is {}",
                            runningRecord.getKdevPipelineId(), runningRecord.getKdevBuildId(), cancelResponse);
                }
            } catch (IOException e) {
                log.error(String.format("openapi取消kdev流水线失败，pipelineId: %s, buildId : %s",
                        runningRecord.getKdevPipelineId(), runningRecord.getKdevBuildId()), e);
            }
            runningRecord.setStatus(KdevPipelineJobStatusEnum.CANCEL.getCode());
            runningRecord.setStatusDesc(KdevPipelineJobStatusEnum.CANCEL.getDesc());
            runningRecord.setGmtModified(now);
        }
        // 批量更改执行状态为 已取消
        mrStuckRecordService.updateBatchById(runningRecords);
    }


    /**
     * 创建用于MR卡点的流水线
     * @param openapiRequestCreator
     * @param checkRepo
     * @return
     */
    private MrStuckPipeline createMrStuckPipeline(AbstractOpenapiRequestCreator openapiRequestCreator, CheckRepo checkRepo) {
        Integer gitProjectId = checkRepo.getGitProjectId();
        final String redisKey = PLATFORM_REPO_MR_STUCK_PIPELINE_PREFIX + gitProjectId;
        KdevResponse<PipelineCreateResponse> response;
        try {
            // 互斥
            redisLock.spinLock(redisKey);
            MrStuckPipeline stuckPipeline = mrStuckPipelineService.getByGitProjectId(gitProjectId);
            if (Objects.nonNull(stuckPipeline)) {
                return stuckPipeline;
            }
            // openapi创建
            PipelineCreateRequest createRequest = openapiRequestCreator.buildPipelineCreateRequest(gitProjectId);
            log.info("openapi创建kdev流水线，request is {}", JSONUtils.serialize(createRequest));
            response = kdevOpenApi.pipelineCreate(createRequest).execute().body();
            if (Objects.isNull(response) || HttpStatus.HTTP_OK != response.getStatus()) {
                log.error(String.format("openapi创建kdev流水线失败，response is %s", JSONUtils.serialize(response)));
                throw new ThemisException(OPEN_API_KDEV_PIPELINE_CREATE_FAIL);
            }
            // 保存数据库
            return saveMrStuckPipeline(checkRepo, response.getData());
        } catch (IOException e) {
            log.error("openpai创建kdev流水线失败", e);
            throw new ThemisException(OPEN_API_KDEV_PIPELINE_CREATE_FAIL);
        } finally {
            redisLock.unlock(redisKey);
        }
    }

    /**
     * 保存项目用于mr卡点的流水线信息
     */
    private MrStuckPipeline saveMrStuckPipeline(CheckRepo checkRepo, PipelineCreateResponse response) {
        LocalDateTime now = LocalDateTime.now();
        MrStuckPipeline stuckPipeline = new MrStuckPipeline();
        stuckPipeline.setKdevPipelineId(response.getPipelineId());
        stuckPipeline.setGitProjectId(checkRepo.getGitProjectId());
        stuckPipeline.setCheckRepoId(checkRepo.getId());
        stuckPipeline.setGmtCreate(now);
        stuckPipeline.setGmtModified(now);
        stuckPipeline.setDeleted(false);
        mrStuckPipelineService.save(stuckPipeline);
        return stuckPipeline;
    }

    /**
     * 请求参数校验
     */
    private void checkParam(MrCheckpointBo checkpointBo) {
        String repoLanguage = checkpointBo.getRepoLanguage();
        String stuckSeverity = checkpointBo.getStuckSeverity();
        if (!JAVA.getName().equals(repoLanguage)) {
            throw new ThemisException(UNSUPPORTED_LANGUAGE);
        }
        if (Objects.isNull(CheckIssueSeverity.getByDesc(stuckSeverity))) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "stuckSeverity可选值为['阻断','严重','主要']");
        }
    }


    /**
     * 得到不同语言的 KdevOpenapi 请求构造器
     * @param language
     * @return
     */
    private AbstractOpenapiRequestCreator getOpenapiRequestCreatorByLanguage(String language) {
        return openapiRequestCreatorList.stream()
                .filter(c -> c.supportLanguage(language))
                .findAny()
                .orElseThrow(() -> new ThemisException(UNSUPPORTED_LANGUAGE));
    }

    @Override
    protected String checkpoint() {
        return MrCheckpointEnum.CodeScan.name();
    }

    @Override
    protected void beforeIssueTransitionCheck(MrStuckIssueTransitionRequest request, String userName) {

    }

}
