package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/27 11:46 上午
 */
@AllArgsConstructor
public enum RepoStarActionType {

    STAR(1, "关注"),
    UN_STAR(2, "取消关注"),
    ;

    @Getter
    private final int type;
    @Getter
    private final String desc;

    private static final Map<Integer, RepoStarActionType> TYPE_MAP = Maps.newHashMap();

    static {
        for (RepoStarActionType repoStarActionType : RepoStarActionType.values()) {
            TYPE_MAP.put(repoStarActionType.getType(), repoStarActionType);
        }
    }

    public static RepoStarActionType getByType(Integer actionType) {
        return TYPE_MAP.get(actionType);
    }
}
