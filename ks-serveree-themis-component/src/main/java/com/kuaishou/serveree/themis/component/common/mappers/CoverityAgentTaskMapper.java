package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.serveree.themis.component.common.entity.CoverityAgentTask;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
@Mapper
@Repository
public interface CoverityAgentTaskMapper extends BaseMapper<CoverityAgentTask> {

    @Update("update coverity_agent_task set status = 'EXECUTING', agent_id = #{agentId}, execute_start = #{currentTime},"
            + " execute_end = 0, gmt_modified = #{currentTime}, heartbeat_time = #{currentTime} "
            + "where id = #{taskId} and status = 'WAITING' and agent_id = 0 and execute_start=0")
    int lockTaskToRun(@Param("taskId") long taskId, @Param("agentId") long agentId, @Param("currentTime") long currentTime);

    @Select("select * from coverity_agent_task where status = #{status} order by priority desc, gmt_create limit #{limit}")
    List<CoverityAgentTask> listByStatusOrderByPriorityAndCreateTime(@Param("status") String status, @Param("limit") int limit);
}
