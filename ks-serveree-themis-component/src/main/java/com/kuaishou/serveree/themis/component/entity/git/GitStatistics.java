package com.kuaishou.serveree.themis.component.entity.git;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/8/29 2:40 PM
 * 大小是byte
 */
@Data
public class GitStatistics {

    @JsonProperty("commit_count")
    private Long commitCount;
    @JsonProperty("storage_size")
    private Long storageSize;
    @JsonProperty("repository_size")
    private Long repositorySize;
    @JsonProperty("wiki_size")
    private Long wikiSize;
    @JsonProperty("lfs_objects_size")
    private Long lfsObjectsSize;
    @JsonProperty("job_artifacts_size")
    private Long jobArtifactsSize;
    @JsonProperty("snippets_size")
    private Long snippetsSize;
    @JsonProperty("packages_size")
    private Long packagesSize;

}
