package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.kuaishou.serveree.themis.component.common.entity.IssueSummaryBase;
import com.kuaishou.serveree.themis.component.config.mybatis.RootMapper;

/**
 * IssueSummaryBase Mapper 接口
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-12
 */
@Mapper
@Repository
public interface IssueSummaryBaseMapper extends RootMapper<IssueSummaryBase> {

    @Select("select issue_uniq_id from issue_summary_base where issue_uniq_id in (#{issueUniqIds})")
    List<String> listByIssueUniqIds(@Param("issueUniqIds") String issueUniqIds);

}
