package com.kuaishou.serveree.themis.component.entity.plugin;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2022/1/18 5:59 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BannedConfig {

    /**
     * 禁止import集合
     */
    private List<String> importList;
    /**
     * 进行注解集合
     */
    private List<String> annotationList;
    /**
     * 禁止catch的异常集合
     */
    private List<String> catchList;

}
