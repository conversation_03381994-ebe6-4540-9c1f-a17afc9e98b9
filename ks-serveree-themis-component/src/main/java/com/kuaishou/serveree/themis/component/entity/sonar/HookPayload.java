package com.kuaishou.serveree.themis.component.entity.sonar;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/21 3:22 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HookPayload {

    private String serverUrl;
    private String taskId;
    private String status;
    private String analysedAt;
    private String revision;
    private String changedAt;
    private Project project;
    private Branch branch;
    private QualityGate qualityGate;
    private HookProperties properties;

}
