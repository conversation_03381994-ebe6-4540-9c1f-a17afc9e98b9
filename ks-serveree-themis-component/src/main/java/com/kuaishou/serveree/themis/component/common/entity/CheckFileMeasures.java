package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckFileMeasures对象", description = "")
public class CheckFileMeasures implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "check_repo表主键")
    private Long checkRepoId;

    @ApiModelProperty(value = "check_repo_branch表主键")
    private Long checkRepoBranchId;

    @ApiModelProperty(value = "check_base表主键id")
    private Long baseId;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "check_file的主键")
    private Long fileId;

    @ApiModelProperty(value = "度量key")
    private String metricKey;

    @ApiModelProperty(value = "度量value")
    private String metricValue;


}
