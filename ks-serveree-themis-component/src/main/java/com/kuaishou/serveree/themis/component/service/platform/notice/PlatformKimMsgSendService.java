package com.kuaishou.serveree.themis.component.service.platform.notice;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.client.ares.AresApi;
import com.kuaishou.serveree.themis.component.entity.ares.AresMessageEntity;
import com.kuaishou.serveree.themis.component.entity.ares.RobotParam;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-12
 *
 * <a href="https://docs.corp.kuaishou.com/k/home/<USER>/fcAD_BqSz3ktTKV6Lt9fNzhK6">...</a>
 */
@Service
@Slf4j
public class PlatformKimMsgSendService {
    @Resource
    private AresApi aresApi;

    public void sendTextMsg(Set<String> users, String msg) {
        if (StringUtils.isBlank(msg) || CollectionUtils.isEmpty(users)) {
            return;
        }
        AresMessageEntity aresMessageEntity = AresMessageEntity.builder()
                .msgTypes(Lists.newArrayList(MsgTypeEnum.KIM_TEXT.getType()))
                .templateId(0)
                .text(msg)
                .userNames(Lists.newArrayList(users))
                .build();
        aresApi.sendKimNotice(aresMessageEntity);
    }

    private void sendMarkdownMsg(Set<String> users, String msg) {
        if (StringUtils.isBlank(msg) || CollectionUtils.isEmpty(users)) {
            return;
        }
        AresMessageEntity aresMessageEntity = AresMessageEntity.builder()
                .msgTypes(Lists.newArrayList(MsgTypeEnum.KIM_MARKDOWN.getType()))
                .templateId(0)
                .text(msg)
                .userNames(Lists.newArrayList(users))
                .build();
        aresApi.sendKimNotice(aresMessageEntity);
    }

    private void sendMarkdownMsgToKimRobot(String robotWebhook, String msg, Set<String> mentionedUsers) {
        if (StringUtils.isBlank(robotWebhook) || StringUtils.isBlank(msg)) {
            return;
        }
        List<String> robotIds = CommonUtils.parseKimRobotIds(robotWebhook);
        if (CollectionUtils.isEmpty(robotIds)) {
            log.error("发送消息到kim群机器人失败，无效的机器人地址：{}", robotWebhook);
            return;
        }
        AresMessageEntity aresMessageEntity = AresMessageEntity.builder()
                .msgTypes(Lists.newArrayList(MsgTypeEnum.KIM_ROBOT.getType()))
                .templateId(0)
                .text(msg)
                .userNames(List.of(""))     // 不要改成空集合，否则会发送失败
                .robotParam(RobotParam.builder()
                        .msgType("markdown")    // 固定
                        .robotIds(robotIds)
                        .mentionedUserIdList(new ArrayList<>(mentionedUsers))
                        .build())
                .build();
        aresApi.sendKimNotice(aresMessageEntity);
    }

    public void sendMarkdownMsgToKimRobotAndUsers(String robotWebhook, String msg, Set<String> users) {
        sendMarkdownMsgToKimRobotAndUsers(robotWebhook, msg, users, Collections.emptySet());
    }

    public void sendMarkdownMsgToKimRobotAndUsers(String robotWebhook, String msg, Set<String> users, Set<String> mentionedUsers) {
        if (StringUtils.isBlank(msg)) {
            return;
        }
        // 空的机器人，则 发给用户
        if (StringUtils.isBlank(robotWebhook)) {
            sendMarkdownMsg(users, msg);
            return;
        }
        // 空的用户，则发给机器人
        if (CollectionUtils.isEmpty(users)) {
            sendMarkdownMsgToKimRobot(robotWebhook, msg, mentionedUsers);
        }
        // 通知发给用户和机器人
        List<String> robotIds = CommonUtils.parseKimRobotIds(robotWebhook);
        if (CollectionUtils.isEmpty(robotIds)) {
            log.error("发送消息到kim群机器人失败，无效的机器人地址：{}", robotWebhook);
            return;
        }
        AresMessageEntity aresMessageEntity = AresMessageEntity.builder()
                .msgTypes(Lists.newArrayList(MsgTypeEnum.KIM_MARKDOWN.getType(), MsgTypeEnum.KIM_ROBOT.getType()))
                .templateId(0)
                .text(msg)
                .userNames(Lists.newArrayList(users))
                .robotParam(RobotParam.builder()
                        .msgType("markdown")    // 固定
                        .robotIds(robotIds)
                        .mentionedUserIdList(new ArrayList<>(mentionedUsers))
                        .build())
                .build();
        aresApi.sendKimNotice(aresMessageEntity);
    }

    @AllArgsConstructor
    @Getter
    enum MsgTypeEnum {
        KIM_TEXT(0, "KIM_TEXT"),
        SMS(1, "SMS"),
        EMAIL(2, "邮件"),
        TEL(3, "电话"),
        PLATFORM(4, "PLATFORM_KAFKA"),
        KIM_CARD(5, "KIM_CARD"),
        KIM_ROBOT(6, "KIM机器人"),
        KIM_MARKDOWN(7, "KIM_MARKDOWN"),
        ;
        private final int type;
        private final String desc;
    }
}