package com.kuaishou.serveree.themis.component.constant.sonar;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-25
 */
@AllArgsConstructor
@Getter
public enum SonarMetricsEnum {
    DUPLICATED_LINES("duplicated_lines", "重复行数"),
    DUPLICATED_LINES_DENSITY("duplicated_lines_density", "重复行密度"),
    DUPLICATED_BLOCKS("duplicated_blocks", "重复块"),
    DUPLICATED_FILES("duplicated_files", "重复文件"),
    NCLOC("ncloc", "代码行数"),
    LINES("lines", "行数"),
    STATEMENTS("statements", "语句"),
    FUNCTIONS("functions", "方法"),
    CLASSES("classes", "类"),
    FILES("files", "文件"),
    COMMENT_LINES_DENSITY("comment_lines_density", "注释行密度"),
    COMMENT_LINES("comment_lines", "注释行"),
    GENERATED_NCLOC("generated_ncloc", "生成的代码行数"),
    GENERATED_LINES("generated_lines", "生成的行数"),
    COMPLEXITY("complexity", "圈复杂度"),
    COGNITIVE_COMPLEXITY("cognitive_complexity", "认知复杂度");

    private static final Map<String, SonarMetricsEnum> KEY_METRIC_MAP = Maps.newHashMap();

    static {
        Arrays.stream(SonarMetricsEnum.values()).forEach(o -> KEY_METRIC_MAP.put(o.getKey(), o));
    }

    public static SonarMetricsEnum getByKey(String key) {
        if (StringUtils.isEmpty(key)) {
            return null;
        }
        return KEY_METRIC_MAP.get(key);
    }

    public static List<String> getAllMetricKeys() {
        return new ArrayList<>(KEY_METRIC_MAP.keySet());
    }

    private final String key;
    private final String des;

    public String getKey() {
        return key;
    }

    public String getDes() {
        return des;
    }
}
