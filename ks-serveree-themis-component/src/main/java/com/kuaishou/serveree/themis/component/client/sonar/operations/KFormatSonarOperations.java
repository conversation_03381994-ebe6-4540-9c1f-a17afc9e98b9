package com.kuaishou.serveree.themis.component.client.sonar.operations;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.client.sonar.api.KFormatSonarApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;

/**
 * <AUTHOR>
 * @since 2021/7/14 3:24 下午
 */
@Component
public class KFormatSonarOperations implements SonarOperations {

    @Autowired
    private KFormatSonarApi kFormatSonarApi;

    @Override
    public SonarCommonApi sonarApi() {
        return kFormatSonarApi;
    }

}
