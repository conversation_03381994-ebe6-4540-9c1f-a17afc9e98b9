package com.kuaishou.serveree.themis.component.common.exception;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import cn.hutool.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;

@SuppressWarnings("rawtypes")
@ControllerAdvice
@Slf4j
public class ApiExceptionHandler {

    @ExceptionHandler(value = Exception.class)
    @ResponseBody
    public ThemisResponse handle(Exception e) {
        ThemisResponse themisResponse = new ThemisResponse();
        if (e instanceof ThemisException) {
            ThemisException apiException = (ThemisException) e;
            return new ThemisResponse(apiException);
        } else if (e instanceof HttpRequestMethodNotSupportedException) {
            // http method 错误
            HttpRequestMethodNotSupportedException httpRequestMethodNotSupportedException =
                    (HttpRequestMethodNotSupportedException) e;
            themisResponse.setStatus(-1);
            themisResponse.setMessage(
                    httpRequestMethodNotSupportedException.getMessage() + ",support method:"
                            + httpRequestMethodNotSupportedException.getSupportedHttpMethods()
            );
            return themisResponse;
        } else if (e instanceof BindException) {
            // 参数绑定错误
            BindException bindException = (BindException) e;
            themisResponse.setStatus(-1);
            String msg = "param type error";
            if (!CollectionUtils.isEmpty(bindException.getAllErrors())) {
                FieldError fieldError = bindException.getFieldErrors().get(0);
                msg = "param " + fieldError.getObjectName() + " field "
                        + fieldError.getField() + " type error";
            }
            themisResponse.setMessage(msg);
            return themisResponse;
        } else if (e instanceof MethodArgumentTypeMismatchException) {
            // 参数类型错误
            MethodArgumentTypeMismatchException methodArgumentTypeMismatchException =
                    (MethodArgumentTypeMismatchException) e;
            themisResponse.setStatus(-1);
            String msg = "param " + methodArgumentTypeMismatchException.getName()
                    + " type error, required type is "
                    + methodArgumentTypeMismatchException.getRequiredType().getSimpleName();
            themisResponse.setMessage(msg);
            return themisResponse;
        } else if (e instanceof MethodArgumentNotValidException) {
            // 参数校验错误
            MethodArgumentNotValidException notValidException = (MethodArgumentNotValidException) e;
            BindingResult bindingResult = notValidException.getBindingResult();
            String msg = "invalid request arguments";
            if (!CollectionUtils.isEmpty(bindingResult.getAllErrors())) {
                msg = bindingResult.getFieldErrors().get(0).getDefaultMessage();
            }
            return ThemisResponse.fail(HttpStatus.HTTP_BAD_REQUEST, msg);
        } else {
            // 未知错误
            log.error("an unknown exception occurred", e);
            themisResponse.setStatus(-1);
            themisResponse.setMessage("系统未知错误");
            return themisResponse;
        }
    }
}
