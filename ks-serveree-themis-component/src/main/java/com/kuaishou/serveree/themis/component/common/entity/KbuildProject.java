package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "KbuildProject对象", description = "")
public class KbuildProject implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键id")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "git仓库地址")
    private String repoUrl;

    @ApiModelProperty(value = "git仓库分支")
    private String branch;

    @ApiModelProperty(value = "构建文件所处相对路径")
    private String buildPath;

    @ApiModelProperty(value = "流水线参数json")
    private String buildParams;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "是否已删除")
    private Boolean deleted;


}
