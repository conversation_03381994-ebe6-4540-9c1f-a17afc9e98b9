package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Paging;
import com.kuaishou.serveree.themis.component.entity.sonar.User;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/06/15 3:21 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UsersResp {

    private Paging paging;

    private List<User> users;

}
