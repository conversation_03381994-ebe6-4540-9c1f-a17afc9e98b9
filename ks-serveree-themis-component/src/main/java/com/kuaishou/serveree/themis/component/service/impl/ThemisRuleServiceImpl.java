package com.kuaishou.serveree.themis.component.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.common.entity.ThemisRule;
import com.kuaishou.serveree.themis.component.common.mappers.ThemisRuleMapper;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.analyze.AnalyzeRuleLevel;
import com.kuaishou.serveree.themis.component.constant.analyze.AnalyzeRuleType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckPluginInputConstants;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.service.ThemisRuleService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-17
 */
@Service
public class ThemisRuleServiceImpl extends ServiceImpl<ThemisRuleMapper, ThemisRule> implements ThemisRuleService {

    @Autowired
    private KsRedisClient ksRedisClient;

    @Override
    public Integer getEffectiveVersion(Integer type) {
        List<ThemisRule> list = this.getRuleListByRuleType(type);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        return list.get(0).getVersion();
    }

    @Override
    public Map<String, ThemisRule> getAllActiveRuleMap(Integer ruleType) {
        if (ruleType == null) {
            return Collections.emptyMap();
        }
        String redisKey = KsRedisPrefixConstant.RULE_TYPE_RULE_MAP_PREFIX + ruleType;
        String themisRuleMapStr = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(themisRuleMapStr)) {
            return JSONUtils.deserializeMap(themisRuleMapStr, String.class, ThemisRule.class);
        }
        Wrapper<ThemisRule> queryWrapper = new QueryWrapper<ThemisRule>().lambda()
                .eq(ThemisRule::getType, ruleType)
                .eq(ThemisRule::getDeleted, false);
        List<ThemisRule> ruleTypeList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(ruleTypeList)) {
            return Collections.emptyMap();
        }
        Map<String, ThemisRule> ruleIdRuleMap = ruleTypeList.stream()
                .collect(Collectors.toMap(ThemisRule::getRuleId, Function.identity(), (existing, replacement) -> existing));
        ksRedisClient.sync().setex(redisKey, DateTimeConstants.SECONDS_PER_DAY, JSONUtils.serialize(ruleIdRuleMap));
        return ruleIdRuleMap;
    }

    @Override
    public String getRuleIdListByRuleTypeLevel(Map<Integer, String> selectedRuleTypeLevelMap) {
        if (MapUtils.isEmpty(selectedRuleTypeLevelMap)) {
            return StringUtils.EMPTY;
        }
        Map<String, String> themisRuleMap;
        String redisKey = KsRedisPrefixConstant.CHECK_RULE_ALL_MAP;
        String themisRule = ksRedisClient.sync().get(redisKey);
        if (!StringUtils.isEmpty(themisRule)) {
            themisRuleMap = JSONUtils.deserializeMap(themisRule, String.class, String.class);
        } else {
            Wrapper<ThemisRule> queryWrapper =
                    new QueryWrapper<ThemisRule>().lambda().eq(ThemisRule::getDeleted, false);
            themisRuleMap = this.list(queryWrapper).stream()
                    .collect(Collectors.groupingBy(rule -> rule.getType() + ":" + rule.getLevel(),
                            Collectors.mapping(ThemisRule::getRuleId, Collectors.joining(","))));
            if (MapUtils.isNotEmpty(themisRuleMap)) {
                ksRedisClient.sync().setex(redisKey, DateTimeConstants.SECONDS_PER_DAY,
                        JSONUtils.serialize(themisRuleMap));
            }
        }
        List<String> needSelectedKeyList = Lists.newArrayList();
        for (Entry<Integer, String> entry : selectedRuleTypeLevelMap.entrySet()) {
            Integer ruleType = entry.getKey();
            String ruleLevel = entry.getValue();
            if (AnalyzeRuleLevel.SERIOUS.getLevel().equals(ruleLevel)) {
                needSelectedKeyList.add(ruleType + ":" + ruleLevel);
            } else if (AnalyzeRuleLevel.MAJOR.getLevel().equals(ruleLevel)) {
                needSelectedKeyList.add(ruleType + ":" + AnalyzeRuleLevel.SERIOUS.getLevel());
                needSelectedKeyList.add(ruleType + ":" + ruleLevel);
            } else {
                needSelectedKeyList.add(ruleType + ":" + AnalyzeRuleLevel.SERIOUS.getLevel());
                needSelectedKeyList.add(ruleType + ":" + AnalyzeRuleLevel.MAJOR.getLevel());
                needSelectedKeyList.add(ruleType + ":" + ruleLevel);
            }
        }
        List<String> ruleIdList = Lists.newArrayList();
        for (String ruleMapKey : needSelectedKeyList) {
            String ruleIdStr = themisRuleMap.get(ruleMapKey);
            if (StringUtils.isNotEmpty(ruleIdStr)) {
                ruleIdList.add(ruleIdStr);
            }
        }
        return StringUtils.join(ruleIdList, ",");
    }

    @Override
    public String getRuleIdListByInputParams(String inputParams) {
        Map<String, String> paramsMap = JSONUtils.deserializeMap(inputParams, String.class, String.class);
        Map<Integer, String> typeLevelMap = Maps.newHashMap();
        String ciCheckLevel = paramsMap.get(CheckPluginInputConstants.CI_CHECK_LEVEL);
        typeLevelMap.put(AnalyzeRuleType.CICHECK.getTypeCode(), ciCheckLevel);
        String checkstyleLevel = paramsMap.get(CheckPluginInputConstants.CHECKSTYLE_LEVEL);
        typeLevelMap.put(AnalyzeRuleType.CHECKSTYLE.getTypeCode(), checkstyleLevel);
        String kCheckLevel = paramsMap.get(CheckPluginInputConstants.KCHECK_LEVEL);
        typeLevelMap.put(AnalyzeRuleType.K_CHECK.getTypeCode(), kCheckLevel);
        return this.getRuleIdListByRuleTypeLevel(typeLevelMap);
    }

    @Override
    public String getMvnArgsByInputParams(String inputParams) {
        Map<String, String> paramsMap = JSONUtils.deserializeMap(inputParams, String.class, String.class);
        StringBuilder mvnArgsBuilder = new StringBuilder();
        String ciCheckSwitch = paramsMap.get(CheckPluginInputConstants.CI_CHECK_SWITCH);
        if ("是".equals(ciCheckSwitch)) {
            // 这里默认跳过spring-init检查 并打开上报开关
            mvnArgsBuilder.append(" -Dcicheck.skip=false -Dcicheck.skipSpringInitCheck=true -Dcicheck.report=true");
        }
        String checkstyleSwitch = paramsMap.get(CheckPluginInputConstants.CHECKSTYLE_SWITCH);
        if ("否".equals(checkstyleSwitch)) {
            mvnArgsBuilder.append(" -Dcheckstyle.skip=true");
        } else {
            mvnArgsBuilder.append(" -Dcheckstyle.result.report=true");
        }
        // k-check默认打开
        mvnArgsBuilder.append(" -Dproto.check.report=true "
                + "-Ddeprecated.check.skip=false "
                + "-Ddeprecated.check.report=true "
                + "-Djava.enforcer.report=true "
                + "-Ddependency.rely.skip=false "
                + "-Ddependency.rely.report=true "
                + "-Dserveree.isLocal=false ");
        return mvnArgsBuilder.toString();
    }

    @Override
    public Map<Integer, String> getRuleTypeLevelByInputParams(String inputParams) {
        Map<String, String> paramsMap = JSONUtils.deserializeMap(inputParams, String.class, String.class);
        Map<Integer, String> ruleTypeLevelMap = Maps.newHashMap();
        String ciCheckSwitch = paramsMap.get(CheckPluginInputConstants.CI_CHECK_SWITCH);
        if ("否".equals(ciCheckSwitch)) {
            ruleTypeLevelMap.put(AnalyzeRuleType.CICHECK.getTypeCode(), AnalyzeRuleLevel.NO_CHECK.getLevel());
        } else {
            String ciCheckLevel = paramsMap.get(CheckPluginInputConstants.CI_CHECK_LEVEL);
            ruleTypeLevelMap.put(AnalyzeRuleType.CICHECK.getTypeCode(), ciCheckLevel);
        }
        String checkstyleSwitch = paramsMap.get(CheckPluginInputConstants.CHECKSTYLE_SWITCH);
        if ("否".equals(checkstyleSwitch)) {
            ruleTypeLevelMap.put(AnalyzeRuleType.CHECKSTYLE.getTypeCode(), AnalyzeRuleLevel.NO_CHECK.getLevel());
        } else {
            String checkstyleLevel = paramsMap.get(CheckPluginInputConstants.CHECKSTYLE_LEVEL);
            ruleTypeLevelMap.put(AnalyzeRuleType.CHECKSTYLE.getTypeCode(), checkstyleLevel);
        }
        String kcheckLevel = paramsMap.get(CheckPluginInputConstants.KCHECK_LEVEL);
        ruleTypeLevelMap.put(AnalyzeRuleType.K_CHECK.getTypeCode(), kcheckLevel);
        return ruleTypeLevelMap;
    }

    @Override
    public List<ThemisRule> getRuleListByRuleType(Integer type) {
        Wrapper<ThemisRule> queryWrapper = new QueryWrapper<ThemisRule>().lambda()
                .eq(type != null, ThemisRule::getType, type)
                .eq(ThemisRule::getDeleted, false);
        return this.list(queryWrapper);
    }

    @Override
    public ThemisRule findByRuleId(String ruleId) {
        return getOne(new QueryWrapper<ThemisRule>().lambda()
                .eq(ThemisRule::getRuleId, ruleId));
    }
}
