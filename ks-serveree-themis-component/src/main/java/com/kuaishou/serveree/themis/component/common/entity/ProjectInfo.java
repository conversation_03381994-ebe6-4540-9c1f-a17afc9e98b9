package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 项目信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProjectInfo对象", description = "项目信息表")
public class ProjectInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "仓库repo地址")
    private String projectRepoUrl;

    @ApiModelProperty(value = "项目描述")
    private String projectDescription;

    @ApiModelProperty(value = "团队名称")
    private String teamName;

    @ApiModelProperty(value = "创建时间")
    private Long createdTime;

    @ApiModelProperty(value = "修改时间")
    private Long updatedTime;


}
