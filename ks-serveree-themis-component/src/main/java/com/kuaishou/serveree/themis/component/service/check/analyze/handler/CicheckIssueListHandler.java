package com.kuaishou.serveree.themis.component.service.check.analyze.handler;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.ThemisAnalyze;
import com.kuaishou.serveree.themis.component.common.entity.ThemisAnalyzeIssue;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.analyze.AnalyzeRuleType;
import com.kuaishou.serveree.themis.component.service.ThemisAnalyzeIssueService;
import com.kuaishou.serveree.themis.component.service.ThemisAnalyzeService;
import com.kuaishou.serveree.themis.component.vo.request.QualityAnalyzeIssueListRequest;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeIssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeIssueListResponse.AnalyzeIssue;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeIssueListResponse.AnalyzeIssue.RuleInfo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR>
 * @since 2021/7/30 2:40 下午
 */
@Service
@SuppressWarnings({"DuplicatedCode"})
public class CicheckIssueListHandler implements CheckAnalyzeIssueListHandler {

    @Autowired
    private ThemisAnalyzeIssueService themisAnalyzeIssueService;

    @Autowired
    private ThemisAnalyzeService themisAnalyzeService;

    @Override
    public ThemisResponse<QualityAnalyzeIssueListResponse> handle(QualityAnalyzeIssueListRequest request) {
        Long buildId = request.getBuildId();
        if (buildId == null || buildId < 0) {
            return ThemisResponse.fail(ResultCodeConstant.INVALID_PARAMS.getCode(),
                    ResultCodeConstant.INVALID_PARAMS.getMessage() + "buildId");
        }
        ThemisAnalyze themisAnalyze = themisAnalyzeService.getAnalyzeByBuildId(buildId);
        if (themisAnalyze == null) {
            return ThemisResponse.fail(ResultCodeConstant.NOT_FOUND_ANALYZE);
        }
        Long analyzeId = themisAnalyze.getId();
        Long page = request.getPage();
        Long pageSize = request.getPageSize();
        Integer ruleType = request.getRuleType();
        // 这个理论上只会有一页
        List<ThemisAnalyzeIssue> analyzeIssueList =
                themisAnalyzeIssueService.getIssueListByAnalyzeIdRuleType(analyzeId, ruleType);
        if (CollectionUtils.isEmpty(analyzeIssueList)) {
            return ThemisResponse.success(QualityAnalyzeIssueListResponse.builder()
                    .page(page)
                    .pageSize(pageSize)
                    .total(NumberUtils.LONG_ZERO)
                    .analyzeIssueList(Lists.newArrayList())
                    .build());
        }
        // 根据ruleId做聚合
        Map<String, List<ThemisAnalyzeIssue>> ruleIdGroupMap = analyzeIssueList.stream()
                .collect(Collectors.groupingBy(ThemisAnalyzeIssue::getRuleId, Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> o.getRuleId()
                                + "#" + o.getIllegalFile() + "#" + o.getIllegalDependency() + "#" + o.getIllegalLineNo()
                                + "#" + o.getIllegalColumnNo()))), ArrayList::new)));
        List<AnalyzeIssue> analyzeIssues = Lists.newArrayList();
        ruleIdGroupMap.forEach((ruleId, issueList) -> {
            AnalyzeIssue analyzeIssue = new AnalyzeIssue();
            ThemisAnalyzeIssue themisAnalyzeIssue = issueList.get(0);
            analyzeIssue.setRuleInfo(RuleInfo.builder()
                    .ruleId(ruleId)
                    .ruleLevel(themisAnalyzeIssue.getRuleLevel())
                    .ruleDesc(themisAnalyzeIssue.getRuleDescription())
                    .standardType(themisAnalyzeIssue.getStandardType())
                    .build());
            StringBuilder sb = new StringBuilder();
            issueList.forEach(issue -> {
                sb.append("\n");
                sb.append(issue.getIllegalErrorMsg());
            });
            analyzeIssue.setStackTrace(sb.toString());
            analyzeIssues.add(analyzeIssue);
        });
        return ThemisResponse.success(QualityAnalyzeIssueListResponse.builder()
                .analyzeIssueList(analyzeIssues)
                .page(page)
                .pageSize(pageSize)
                .total((long) analyzeIssues.size())
                .build());
    }

    @Override
    public List<AnalyzeRuleType> ruleTypes() {
        return Lists.newArrayList(AnalyzeRuleType.CICHECK);
    }
}
