package com.kuaishou.serveree.themis.component.config;

import static freemarker.template.Configuration.VERSION_2_3_28;

import java.sql.Time;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Locale;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import freemarker.template.DefaultObjectWrapper;
import freemarker.template.SimpleDate;
import freemarker.template.TemplateModel;
import freemarker.template.TemplateModelException;
import freemarker.template.Version;
// CHECKSTYLE:ON

@Configuration
public class FreemarkerConfig {

    @Bean
    public freemarker.template.Configuration freemarkerConfiguration() {
        freemarker.template.Configuration config = new freemarker.template.Configuration(VERSION_2_3_28);
        config.setLocale(Locale.CHINESE);
        config.setClassLoaderForTemplateLoading(this.getClass().getClassLoader(), "");
        config.setDefaultEncoding("UTF-8");
        config.setNumberFormat("computer");
        config.setDateFormat("yyyy-MM-dd");
        config.setTimeFormat("HH:mm:ss");
        config.setDateTimeFormat("yyyy-MM-dd HH:mm:ss");
        DefaultObjectWrapper wrapper = new CustomAppObjectWrapper(VERSION_2_3_28);
        wrapper.setExposeFields(true);
        config.setObjectWrapper(wrapper);
        return config;
    }

    private static class CustomAppObjectWrapper extends DefaultObjectWrapper {

        public CustomAppObjectWrapper(Version incompatibleImprovements) {
            super(incompatibleImprovements);
        }

        @Override
        protected TemplateModel handleUnknownType(final Object obj) throws TemplateModelException {
            if (obj instanceof LocalDateTime) {
                Timestamp timestamp = Timestamp.valueOf((LocalDateTime) obj);
                return new SimpleDate(timestamp);
            }
            if (obj instanceof LocalTime) {
                Time time = Time.valueOf((LocalTime) obj);
                return new SimpleDate(time);
            }
            return super.handleUnknownType(obj);
        }

    }
}
