package com.kuaishou.serveree.themis.component.client.compile;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.entity.compile.CompileApiRsp;
import com.kuaishou.serveree.themis.component.entity.compile.DependencyKimNoticeRequest;
import com.kuaishou.serveree.themis.component.entity.compile.InvalidDependencyItem;
import com.kuaishou.serveree.themis.component.entity.compile.ReleaseRootPomVersionCheckRsp;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/11/24 9:25 AM
 */
@Component
@Slf4j
public class CompileApi {

    @Value("${compile.url}")
    private String compileUrl;

    @Value("${kdev.timeout}")
    private int timeout;

    public void sendDependencyKimNotice(DependencyKimNoticeRequest kimNoticeRequest) {
        String requestUrl = compileUrl + "/api/pipeline/compile/internal/send/dependency/check/result";
        String body = HttpRequest
                .post(requestUrl)
                .body(JSONUtils.serialize(kimNoticeRequest))
                .timeout(timeout)
                .execute()
                .body();
        if (StringUtils.isEmpty(body)) {
            throw new ThemisException(-1, "依赖规范kim发送接口返回为空");
        }
        Map<String, Object> stringObjectMap = JSONUtils.deserializeMap(body, String.class, Object.class);
        if (MapUtils.isEmpty(stringObjectMap)) {
            throw new ThemisException(-1, "依赖规范kim发送接口解析为空，body is " + body);
        }
        Integer status = (Integer) stringObjectMap.get("status");
        if (status == null || status != HttpStatus.HTTP_OK) {
            throw new ThemisException(-1, "依赖规范kim发送接口返回为false，body is " + body);
        } else {
            log.info("send dependency rule kim notice success, request is {}", JSONUtils.serialize(kimNoticeRequest));
        }
    }

    public ReleaseRootPomVersionCheckRsp checkReleaseRootPomVersion(Integer gitProjectId, String branch, String commitId, String pomPath) {
        Map<String, Object> reqMap = Map.of("gitProjectId", gitProjectId, "branch", branch, "commitId", commitId, "pomPath", pomPath);
        String url = compileUrl + "/api/pipeline/compile/internal/rootpom/release/version/check";
        HttpRequest httpRequest = HttpRequest
                .post(url)
                .form(reqMap)
                .timeout(timeout);
        try (HttpResponse response = httpRequest.execute()) {
            if (!response.isOk()) {
                log.error("调用编译服务接口检查RootPom版本失败：url: {}, req: {}, resp: {}", url, reqMap, response);
                throw new ThemisException(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR.value(), "ReleaseRootPom检查失败");
            }
            CompileApiRsp<ReleaseRootPomVersionCheckRsp> deserialize = JSONUtils.deserialize(response.body(),
                    new TypeReference<CompileApiRsp<ReleaseRootPomVersionCheckRsp>>() {
                    });
            if (!CompileApiRsp.success(deserialize) || Objects.isNull(deserialize.getData())) {
                log.error("调用编译服务接口检查RootPom版本失败：url: {}, req: {}, resp: {}", url, reqMap, response);
                throw new ThemisException(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR.value(), "ReleaseRootPom检查失败");
            }
            return deserialize.getData();
        } catch (Exception e) {
            log.error("调用编译服务接口检查RootPom版本失败：url: {}, req: {}", url, reqMap, e);
            throw new RuntimeException(e);
        }
    }


    public List<InvalidDependencyItem> checkSnapshotDependency(Integer gitProjectId, String branch, String commitId, String pomPath) {
        Map<String, Object> reqMap = Map.of("gitProjectId", gitProjectId, "branch", branch, "commitId", commitId, "pomPath", pomPath);
        String url = compileUrl + "/api/pipeline/compile/internal/pom/dependency/snapshot/check";
        HttpRequest httpRequest = HttpRequest
                .post(url)
                .form(reqMap)
                .timeout(timeout);
        try (HttpResponse response = httpRequest.execute()) {
            if (!response.isOk()) {
                log.error("调用编译服务接口检查SNAPSHOT依赖失败：url: {}, req: {}, resp: {}", url, reqMap, response);
                throw new ThemisException(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR.value(), "SNAPSHOT依赖检查失败");
            }
            CompileApiRsp<List<InvalidDependencyItem>> deserialize = JSONUtils.deserialize(response.body(),
                    new TypeReference<CompileApiRsp<List<InvalidDependencyItem>>>() {
                    });
            if (!CompileApiRsp.success(deserialize) || Objects.isNull(deserialize.getData())) {
                log.error("调用编译服务接口检查SNAPSHOT依赖失败：url: {}, req: {}, resp: {}", url, reqMap, response);
                throw new ThemisException(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR.value(), "SNAPSHOT依赖检查失败");
            }
            return deserialize.getData();
        } catch (Exception e) {
            log.error("调用编译服务接口检查SNAPSHOT依赖失败：url: {}, req: {}", url, reqMap, e);
            throw new RuntimeException(e);
        }
    }

}
