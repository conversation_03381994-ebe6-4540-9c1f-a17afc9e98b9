package com.kuaishou.serveree.themis.component.service.sonar.mode;


import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.github.phantomthief.util.MoreFunctions;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.client.ares.AresApi;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.SonarPipelineMeasure;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckModeEnum;
import com.kuaishou.serveree.themis.component.entity.ares.AresMessageEntity;
import com.kuaishou.serveree.themis.component.entity.kafka.ScanIssueSummaryNotify;
import com.kuaishou.serveree.themis.component.entity.process.ScanModeContext;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarPipelineMeasureElement;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PCheckExecutionService;
import com.kuaishou.serveree.themis.component.service.SonarPipelineMeasureService;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.IssueUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.extra.spring.SpringUtil;

/**
 * <AUTHOR>
 * @since 2022/3/23 11:15 上午
 */
public interface ScanModeService {

    Logger LOGGER = LoggerFactory.getLogger(ScanModeService.class);

    void preAction(ScanModeContext scanModeContext);

    void postAction(ScanModeContext scanModeContext);

    void afterAction(ScanModeContext scanModeContext);

    boolean incrementMode();

    /**
     * 获取相同条件的最近一次扫描记录
     */
    default PCheckBase getLatestRecordWithSameCondition(PCheckBase pCheckBase, PCheckExecution execution) {
        // 查找同分支、同模块、所有实际执行成功的记录
        List<PCheckBase> pCheckBases = SpringUtil.getBean(PCheckBaseService.class).listAllByRepoBranchModulesBase(pCheckBase);
        if (CollectionUtils.isEmpty(pCheckBases)) {
            return null;
        }
        Map<Long, PCheckBase> pCheckBaseMap = pCheckBases.stream().collect(Collectors.toMap(PCheckBase::getId, Function.identity(), (existing, replacement) -> existing));
        // 过滤同扫描器、同增量/全量模式，取最近一次
        PCheckExecution latestExecution = SpringUtil.getBean(PCheckExecutionService.class)
                .getLatestExecution(pCheckBaseMap.keySet(), execution.getReferType(), execution.getIncrementMode());
        return Objects.isNull(latestExecution) ? null : pCheckBaseMap.get(latestExecution.getPBaseId());
    }

    default void sendKimNotice(ScanModeContext scanModeContext) {
        SonarPipelineMeasureService sonarPipelineMeasureService = SpringUtil.getBean(SonarPipelineMeasureService.class);
        PCheckBase pCheckBase = scanModeContext.getPCheckBase();
        PCheckExecution execution = scanModeContext.getPCheckExecution();
        String kimNoticeUrl = String.format(scanModeContext.getKimNoticeUrl(), execution.getReferType(), pCheckBase.getKspBuildId());
        Long kspBuildId = pCheckBase.getKspBuildId();
        SonarPipelineMeasure sonarPipelineMeasure = sonarPipelineMeasureService.getByKspBuildId(kspBuildId);
        AresMessageEntity aresMessageEntity =
                getMarkDownMessageAresMessageEntity(sonarPipelineMeasure, pCheckBase, kimNoticeUrl);
        MoreFunctions.runCatching(() -> {
            String sendResult = SpringUtil.getBean(AresApi.class).sendKimNotice(aresMessageEntity);
            LOGGER.info("buildId is {},ScanModeService send kim result is {}", kspBuildId, sendResult);
        });
    }

    default AresMessageEntity getMarkDownMessageAresMessageEntity(SonarPipelineMeasure sonarPipelineMeasure,
            PCheckBase pCheckBase, String kimNoticeUrl) {
        StringBuilder sb = new StringBuilder("### MavenScannerNew通知").append("\n");
        sb.append("项目: ").append(GitUtils.getRepoName(pCheckBase.getRepoUrl())).append("\n");
        sb.append("触发人: ").append(pCheckBase.getSponsor()).append("\n");
        sb.append(getResultDetail(sonarPipelineMeasure)).append("\n");
        sb.append("[跳转链接](").append(kimNoticeUrl).append(")");
        return AresMessageEntity.builder()
                .msgTypes(Lists.newArrayList(7))
                .templateId(0)
                .text(sb.toString())
                .userNames(Lists.newArrayList(pCheckBase.getSponsor()))
                .build();
    }

    default String getResultDetail(SonarPipelineMeasure sonarPipelineMeasure) {
        StringBuilder sb = new StringBuilder();
        String bugMessage = getTypeMessage("BUG", sonarPipelineMeasure.getBugMeasure());
        if (StringUtils.isNotEmpty(bugMessage)) {
            sb.append(bugMessage).append("\n");
        }
        String vulnerabilityMessage = getTypeMessage("漏洞", sonarPipelineMeasure.getVulnerabilityMeasure());
        if (StringUtils.isNotEmpty(vulnerabilityMessage)) {
            sb.append(vulnerabilityMessage);
        }
        return sb.toString();
    }

    default String getTypeMessage(String type, String typeMeasure) {
        SonarPipelineMeasureElement typeMeasureElement =
                JSONUtils.deserialize(typeMeasure, SonarPipelineMeasureElement.class);
        int blockerCount = typeMeasureElement.getBlockerCount();
        int criticalCount = typeMeasureElement.getCriticalCount();
        int majorCount = typeMeasureElement.getMajorCount();
        if (blockerCount > 0 || majorCount > 0 || criticalCount > 0) {
            return type + ":" + "阻断级别" + blockerCount + "个，"
                    + "严重级别" + criticalCount + "个，"
                    + "主要级别" + majorCount + "个。";
        }
        return null;
    }

    default void fillUpSonarBranch(List<IssueSummary> summaryList, PCheckBase pCheckBase,
            List<PCheckIssue> pCheckIssueList) {
        Map<String, PCheckIssue> uniqIdPCheckMap = pCheckIssueList.stream()
                .collect(Collectors.toMap(PCheckIssue::getIssueUniqId, Function.identity(), (a, b) -> a));
        for (IssueSummary issueSummary : summaryList) {
            if (StringUtils.isEmpty(issueSummary.getSonarBranch())) {
                issueSummary.setSonarBranch(pCheckBase.getSonarBranch());
            }
            PCheckIssue pCheckIssue = uniqIdPCheckMap.get(issueSummary.getIssueUniqId());
            if (pCheckIssue != null) {
                issueSummary.setSeverity(pCheckIssue.getSeverity());
            }
            if (StringUtils.isEmpty(issueSummary.getCommonIssueUniqId())) {
                issueSummary.setCommonIssueUniqId(IssueUtils.genIssueUniqId(issueSummary));
            }
            issueSummary.setScanMode(ScanModeEnum.PROCESS.getCode());
        }
    }

    default ScanIssueSummaryNotify fillUpSummaryNotify(ScanModeContext scanModeContext) {
        PCheckBase pCheckBase = scanModeContext.getPCheckBase();
        return ScanIssueSummaryNotify.builder()
                .buildId(pCheckBase.getKspBuildId())
                .projectId(pCheckBase.getProjectId())
                .branch(pCheckBase.getBranch())
                .repoUrl(pCheckBase.getRepoUrl())
                .scanMode(ScanModeEnum.PROCESS.getCode())
                .checkMode(CheckModeEnum.COVER_MODE.getMode())
                .pBaseId(pCheckBase.getId())
                .build();
    }

}
