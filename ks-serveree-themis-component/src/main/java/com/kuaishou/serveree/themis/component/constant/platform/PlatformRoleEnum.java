package com.kuaishou.serveree.themis.component.constant.platform;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/9/4 1:50 下午
 */
@AllArgsConstructor
@Getter
public enum PlatformRoleEnum {

    PROFILE_EDITOR("profile_editor", "规则集编辑者"),
    PROJECT_PROFILE_EDITOR("project_profile_editor", "项目规则集编辑者"),
    OFFLINE_SCAN_SCHEDULER("offline_scan_scheduler", "离线扫描调度者"),
    ;

    private final String key;
    private final String desc;

}