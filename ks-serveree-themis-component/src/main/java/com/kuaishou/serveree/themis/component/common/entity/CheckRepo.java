package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 仓库数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckRepo对象", description = "仓库数据表")
public class CheckRepo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Integer gitProjectId;

    @ApiModelProperty(value = "repo地址")
    private String repoUrl;

    @ApiModelProperty(value = "repo的类型")
    private Integer repoType;

    @ApiModelProperty(value = "sonar的projectKey")
    private String sonarKey;

    @ApiModelProperty(value = "sonar的projectName")
    private String sonarName;

    private String source;

    @ApiModelProperty(value = "所属的group")
    private String groupPath;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    private String creator;
    private String updater;
    private Integer version;

    @ApiModelProperty(value = "删除标记")
    private Boolean deleted;

    @ApiModelProperty(value = "是否使用kbuild")
    private Boolean useKbuild;
}
