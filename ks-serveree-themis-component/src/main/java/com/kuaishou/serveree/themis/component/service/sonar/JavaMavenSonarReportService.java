package com.kuaishou.serveree.themis.component.service.sonar;

import static com.google.common.base.CaseFormat.LOWER_CAMEL;
import static com.google.common.base.CaseFormat.LOWER_UNDERSCORE;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.maven.model.Model;
import org.apache.maven.model.Parent;
import org.apache.maven.model.io.xpp3.MavenXpp3Reader;
import org.gitlab.api.GitlabAPI;
import org.gitlab.api.models.GitlabProject;
import org.gitlab.api.models.GitlabRepositoryFile;
import org.gitlab.api.models.GitlabRepositoryTree;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.git.SelfGitApi;
import com.kuaishou.serveree.themis.component.client.sonar.operations.CorpSonarOperations;
import com.kuaishou.serveree.themis.component.common.entity.ProjectInfo;
import com.kuaishou.serveree.themis.component.common.entity.ProjectTeam;
import com.kuaishou.serveree.themis.component.entity.sonar.Measure;
import com.kuaishou.serveree.themis.component.entity.sonar.Measures;
import com.kuaishou.serveree.themis.component.entity.sonar.Project;
import com.kuaishou.serveree.themis.component.entity.sonar.ReportData;
import com.kuaishou.serveree.themis.component.entity.sonar.TagGroup;
import com.kuaishou.serveree.themis.component.service.ProjectInfoService;
import com.kuaishou.serveree.themis.component.service.ProjectTeamService;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/11/2 10:19 上午
 */
@Slf4j
@Component
public class JavaMavenSonarReportService {

    public static final Kconf<Map<String, String>> GIT_ID_REPORT_EMAILS_MAP = Kconfs
            .ofStringMap("qa.themis.serverSonarEmailReports", Maps.newHashMap())
            .build();

    public static final Kconf<HashMap<String, List<String>>> GIT_ID_REPORT_EXCLUDE_MAP = Kconfs
            .ofListMap("qa.themis.serverSonarEmailReportsExclude", Maps.newHashMap(), String.class, String.class)
            .build();

    @Autowired
    private CorpSonarOperations corpSonarOperations;

    @Resource
    private Configuration freemarkerConfiguration;

    @Resource
    private JavaMailSender javaMailSender;

    @Autowired
    private GitlabAPI gitlabApi;

    @Autowired
    private SelfGitApi selfGitApi;

    @Autowired
    private GitOperations gitOperations;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private ProjectTeamService projectTeamService;

    private static final Integer DS_GIT_GROUP_ID = 2428;

    /**
     * 生成报告
     */
    public void generateSonarReport() {
        DateTime dt = new DateTime().minusWeeks(1);
        DateTime weekBeginDate = dt.minusDays(dt.getDayOfWeek() - 1).withTimeAtStartOfDay();
        DateTime weekEndDate = weekBeginDate.plusDays(DateTimeConstants.DAYS_PER_WEEK).minusSeconds(1);

        String pattern = "yyyy-MM-dd";
        String caption = weekBeginDate.toString(pattern) + " - " + weekEndDate.toString(pattern);

        // 为每一个配置的报告做发送逻辑
        GIT_ID_REPORT_EMAILS_MAP.get().forEach((gitGroupId, emailsPersons) -> {
            List<String> excludeProjectName = GIT_ID_REPORT_EXCLUDE_MAP.get().get(gitGroupId);
            FilterTagGroup tagGroups =
                    getTagGroups(weekBeginDate, weekEndDate, Integer.valueOf(gitGroupId), excludeProjectName);
            Map<String, Object> dataModel = new HashMap<>();
            dataModel.put("caption", caption);
            dataModel.put("tagGroups", tagGroups.getTagGroupList());
            dataModel.put("teamGroups", tagGroups.getTeamGroupList());
            dataModel.put("noScanGroups", tagGroups.getNoScanGroupList());
            String content = null;
            try {
                Template tpl;
                switch (gitGroupId) {
                    case "2428":
                        tpl = freemarkerConfiguration.getTemplate("report_template.ftl");
                        break;
                    default:
                        tpl = freemarkerConfiguration.getTemplate("report_template_common.ftl");
                        break;
                }
                StringWriter writer = new StringWriter();
                tpl.process(dataModel, writer);
                content = writer.toString();
            } catch (IOException | TemplateException e) {
                log.error("generate sonar report error!", e);
            }
            String subject;
            switch (gitGroupId) {
                case "2428":
                    subject = "【电商项目Sonar代码质量报表】" + caption;
                    break;
                case "262":
                    subject = "【商业化业务研发代码质量周报】" + caption;
                    break;
                default:
                    subject = "【Sonar代码质量报表】" + caption;
                    break;
            }
            if (StringUtils.isNotEmpty(emailsPersons) && !StringUtils.isEmpty(content)) {
                try {
                    MimeMessage mimeMessage = javaMailSender.createMimeMessage();
                    MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
                    helper.setSubject(subject);
                    helper.setText(content, true);
                    helper.setTo(emailsPersons.split(","));
                    helper.setFrom("<EMAIL>");
                    helper.setSentDate(new Date());
                    javaMailSender.send(mimeMessage);
                } catch (Exception e) {
                    log.error("spring mail sender send email error", e);
                }
            } else {
                log.warn("generate sonar report fail, content is empty");
            }
        });
    }

    public FilterAllProject fetchAllProjects(DateTime weekBeginDate, DateTime weekEndDate, Integer gitGroupId,
            List<String> excludeProjectNames) {
        // 获取指标的key
        String metricKey = Arrays.stream(Measure.class.getDeclaredFields())
                .map(Field::getName)
                .map(LOWER_CAMEL.converterTo(LOWER_UNDERSCORE)::convert)
                .collect(joining(","));

        String pattern = "yyyy-MM-dd'T'HH:mm:ssZ";
        String weekEnd = weekEndDate.toString(pattern);
        String preWeekEnd = weekBeginDate.minusSeconds(1).toString(pattern);

        List<Project> allProjects = corpSonarOperations.getAllProjects();
        if (CollectionUtils.isNotEmpty(excludeProjectNames)) {
            allProjects =
                    allProjects.stream().filter(sonarProject -> !excludeProjectNames.contains(sonarProject.getName()))
                            .collect(toList());
        }
        FilterProject filterProject = filterProject(allProjects, gitGroupId);
        allProjects = filterProject.getScanProjectList();
        List<Project> noScanProjectList = filterProject.noScanProjectList;

        for (Project project : allProjects) {
            try {
                if (StringUtils.isEmpty(project.getAnalysisDate())) {
                    log.warn("{} dont have analysisDate,ignore", project.getKey());
                    noScanProjectList.add(project);
                    continue;
                }

                DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(pattern);
                DateTime analysisDate = DateTime.parse(project.getAnalysisDate(), dateTimeFormatter);

                // 找到最接近endTime的之后的分析日期
                String curWeekAnalysisDate;
                if (analysisDate.isAfter(weekEndDate.getMillis())) {
                    curWeekAnalysisDate = corpSonarOperations.getAnalysesDate(project.getKey(), null, weekEnd);
                } else {
                    curWeekAnalysisDate = project.getAnalysisDate();
                }
                // 没有分析过的直接忽略
                if (StringUtils.isEmpty(curWeekAnalysisDate)) {
                    log.warn("project key is {} ,curWeekAnalysisDate == null ignore", project.getKey());
                    noScanProjectList.add(project);
                    continue;
                }

                Measure weekMeasure = corpSonarOperations.getHistoryMeasure(project.getKey(), metricKey,
                        curWeekAnalysisDate, curWeekAnalysisDate);
                // 找到最接近的startTime的之前的分析日期
                String preWeekAnalysisDate;
                if (analysisDate.isBefore(weekBeginDate.getMillis())) {
                    preWeekAnalysisDate = project.getAnalysisDate();
                } else {
                    preWeekAnalysisDate = corpSonarOperations.getAnalysesDate(project.getKey(), null, preWeekEnd);
                }
                Measure preWeekMeasure;
                if (StringUtils.isEmpty(preWeekAnalysisDate) || preWeekAnalysisDate.equals(curWeekAnalysisDate)) {
                    preWeekMeasure = weekMeasure;
                } else {
                    preWeekMeasure = corpSonarOperations.getHistoryMeasure(project.getKey(), metricKey,
                            preWeekAnalysisDate, preWeekAnalysisDate);
                }
                project.setMeasures(new Measures(weekMeasure, preWeekMeasure));
            } catch (Exception e) {
                log.error("fetch measure error: {}", project.getName(), e);
            }
        }
        List<Project> filterProjectList = allProjects.stream().filter(this::filterProject).collect(toList());
        return new FilterAllProject(filterProjectList, noScanProjectList);
    }

    /**
     * 过滤一些项目
     */
    private FilterProject filterProject(List<Project> allProjects, Integer gitGroupId) {

        // 查询出项目详情表所有的数据
        List<ProjectInfo> projectInfoList = projectInfoService.getAllProjectInfo();
        List<ProjectTeam> projectTeams = projectTeamService.listAllProjectTeam();
        Map<String, ProjectTeam> teamPathProjectMap =
                projectTeams.stream().collect(Collectors.toMap(ProjectTeam::getGitPath, Function.identity(), (existing, replacement) -> existing));
        Map<String, ProjectInfo> gitUrlProjectInfoMap = projectInfoList.stream()
                .collect(Collectors.toMap(ProjectInfo::getProjectRepoUrl, Function.identity(), (existing, replacement) -> existing));

        List<GitlabProject> groupProjects = this.getAllGitProjectsWithSubGroups(gitGroupId);
        // 过滤非java maven项目直接
        List<GitlabProject> gitlabProjects = groupProjects.stream().filter(this::isJavaAndMaven)
                .collect(Collectors.toList());
        // 获取java maven项目的key与repoUrl数据
        List<ProjectGitInfo> javaMavenProjectTag = this.getJavaMavenProjectTag(gitlabProjects);

        // key为groupId:artifactId的map
        Map<String, List<ProjectGitInfo>> keyRepoMap = javaMavenProjectTag.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.groupingBy(ProjectGitInfo::getKey));
        Set<String> keySet = keyRepoMap.keySet();
        boolean isMall = DS_GIT_GROUP_ID.equals(gitGroupId);
        // 已扫描项目过滤
        List<Project> scanProjectList = allProjects.stream().filter(sonarProject -> {
            String key = sonarProject.getKey();
            if (keySet.contains(key)) {
                // 这里是单独为电商开发的分team逻辑
                if (isMall) {
                    List<ProjectGitInfo> projectGitInfos = keyRepoMap.get(key);
                    if (CollectionUtils.isNotEmpty(projectGitInfos)) {
                        ProjectGitInfo projectGitInfo = projectGitInfos.get(0);
                        sonarProject.setProjectGitRepoUrl(projectGitInfo.getGitRepoWebUrl());
                        sonarProject.setProjectDescription(StringUtils.isEmpty(projectGitInfo.getGitDescription())
                                                           ? "" : projectGitInfo.getGitDescription());
                        // 先以gitPath路径匹配team名称
                        // 先以全路径来匹配
                        // 封装详情数据
                        ProjectInfo projectInfo = gitUrlProjectInfoMap.get(projectGitInfo.getGitRepoWebUrl());
                        if (projectInfo != null) {
                            // 这里只判断null
                            if (projectInfo.getTeamName() != null) {
                                sonarProject.setProjectTeam(projectInfo.getTeamName());
                            }
                        } else {
                            String replace = projectGitInfo.getPathWithNamespace().replace("plateco-dev/", "");
                            String secondPath = replace.split("/")[0];
                            ProjectTeam projectTeam = teamPathProjectMap.get(secondPath);
                            if (projectTeam != null) {
                                sonarProject.setProjectTeam(projectTeam.getTeamName());
                            }
                        }
                    }
                }
                return true;
            }
            return false;
        }).collect(toList());

        List<String> webUrlList = scanProjectList.stream().map(Project::getProjectGitRepoUrl).collect(toList());
        // 未扫描项目分组
        List<Project> noScanProjectList = groupProjects.stream()
                .filter(project -> !webUrlList.contains(project.getWebUrl()))
                .map(gitlabProject -> {
                    Project project = new Project();
                    project.setProjectGitRepoUrl(gitlabProject.getWebUrl());
                    // 这里是单独为电商开发的逻辑
                    if (isMall) {
                        ProjectInfo projectInfo = gitUrlProjectInfoMap.get(gitlabProject.getWebUrl());
                        if (projectInfo != null) {
                            // 这里只判断null
                            if (projectInfo.getTeamName() != null) {
                                project.setProjectTeam(projectInfo.getTeamName());
                            }
                            // 这里只判断null
                            if (projectInfo.getProjectDescription() != null) {
                                project.setProjectDescription(projectInfo.getProjectDescription());
                            }
                        }
                    }
                    return project;
                }).collect(toList());
        return new FilterProject(noScanProjectList, scanProjectList);
    }

    private List<GitlabProject> getAllGitProjectsWithSubGroups(Integer gitGroupId) {
        List<Integer> allSubGroupIds = selfGitApi.listAllSubGroupIds(gitGroupId);
        allSubGroupIds.add(gitGroupId);
        List<GitlabProject> allProjects = Lists.newArrayList();
        allSubGroupIds.forEach(groupId -> {
            List<GitlabProject> groupProjects = gitlabApi.getGroupProjects(groupId);
            if (CollectionUtils.isNotEmpty(groupProjects)) {
                allProjects.addAll(groupProjects);
            }
        });
        return allProjects;
    }

    /**
     * 获取java maven的一些相关信息
     */
    private List<ProjectGitInfo> getJavaMavenProjectTag(List<GitlabProject> gitlabProjects) {
        return gitlabProjects.stream().map(gitlabProject -> {
            try {
                GitlabRepositoryFile file = gitlabApi.getRepositoryFile(gitlabProject, "pom.xml", "master");
                if (file != null && file.getContent() != null) {
                    String pomStr = new String(Base64Utils.decodeFromString(file.getContent()), StandardCharsets.UTF_8);
                    InputStream inputStream = new ByteArrayInputStream(pomStr.getBytes());
                    MavenXpp3Reader reader = new MavenXpp3Reader();
                    try {
                        ProjectGitInfo projectGitInfo = new ProjectGitInfo();
                        Model model = reader.read(inputStream);
                        String artifactId = model.getArtifactId();
                        String groupId = model.getGroupId();
                        if (StringUtils.isEmpty(groupId)) {
                            Parent parent = model.getParent();
                            groupId = parent.getGroupId();
                        }
                        projectGitInfo.setKey(groupId + ":" + artifactId);
                        projectGitInfo.setGitRepoWebUrl(gitlabProject.getWebUrl());
                        projectGitInfo.setGitDescription(gitlabProject.getDescription());
                        projectGitInfo.setPathWithNamespace(gitlabProject.getPathWithNamespace());
                        return projectGitInfo;
                    } catch (Exception e) {
                        log.error("gitlabApi getRepositoryFile jiexi java project get pom error!", e);
                    }
                }
                return null;
            } catch (Throwable e) {
                log.error("gitlabApi getRepositoryFile error!,gitlab id is {}", gitlabProject.getId(), e);
                return null;
            }
        }).collect(Collectors.toList());
    }

    private boolean isJavaAndMaven(GitlabProject gitlabProject) {
        try {
            boolean javaProject = gitOperations.isJavaProject(gitlabProject.getId());
            if (!javaProject) {
                return false;
            }
        } catch (Throwable e) {
            return false;
        }
        try {
            List<GitlabRepositoryTree> treeList = gitlabApi.getRepositoryTree(gitlabProject, "/", "master", false);
            boolean hasPomXml = treeList.stream().anyMatch(tree -> tree.getName().equals("pom.xml"));
            if (!hasPomXml) {
                return false;
            }
        } catch (Throwable e) {
            log.error("gitlabApi getRepositoryTree error, projectId is {}", gitlabProject.getId(), e);
            return false;
        }
        return true;
    }

    private FilterTagGroup getTagGroups(DateTime weekBeginDate, DateTime weekEndDate, Integer gitGroupId,
            List<String> excludeProjectName) {
        String pattern = "yyyy-MM-dd'T'HH:mm:ssZ";
        String weekBegin = weekBeginDate.toString(pattern);
        String weekEnd = weekEndDate.toString(pattern);

        FilterAllProject filterAllProject =
                fetchAllProjects(weekBeginDate, weekEndDate, gitGroupId, excludeProjectName);
        List<Project> projects = filterAllProject.getAfterFilterAllProject();
        List<Project> noScanProjectList = filterAllProject.getNoScanProjectList();

        List<TagGroup> scanGroupList = getTagGroupData(projects, weekBegin, weekEnd);
        List<TagGroup> noScanGroupList = getTagGroupData(noScanProjectList, StringUtils.EMPTY, StringUtils.EMPTY);
        List<TagGroup> teamGroupList = getTeamGroupData(scanGroupList);
        return new FilterTagGroup(scanGroupList, noScanGroupList, teamGroupList);
    }

    private List<TagGroup> getTeamGroupData(List<TagGroup> scanGroupList) {
        List<TagGroup> teamGroup = Lists.newArrayList();
        for (TagGroup tagGroup : scanGroupList) {
            String teamName = tagGroup.getTag();
            if (StringUtils.isEmpty(teamName)) {
                log.warn("teamName is empty skip this data, tagGroup is {}", tagGroup);
                continue;
            }
            int newUnResolvedBugs = 0;
            int newUnResolvedVulnerability = 0;

            int cncloc = 0;
            int cbugs = 0;
            int cviolations = 0;
            int ccriticalViolations = 0;
            int cvulnerabilities = 0;
            int cduplicatedBlocks = 0;

            int dncloc = 0;
            int dbugs = 0;
            int dviolations = 0;
            int dcriticalViolations = 0;
            int dvulnerabilities = 0;
            int dduplicatedBlocks = 0;
            List<ReportData> reportDatas = tagGroup.getReportDatas();
            for (ReportData reportData : reportDatas) {
                newUnResolvedBugs += reportData.getNewUnResolvedBugs();
                newUnResolvedVulnerability += reportData.getNewUnResolvedVulnerability();
                Measure currentMeasure = reportData.getProject().getMeasures().getCurrent();
                cncloc += currentMeasure.getNcloc();
                cbugs += currentMeasure.getBugs();
                cviolations += currentMeasure.getViolations();
                ccriticalViolations += currentMeasure.getCriticalViolations();
                cvulnerabilities += currentMeasure.getVulnerabilities();
                cduplicatedBlocks += currentMeasure.getDuplicatedBlocks();
                Measure deltaMeasure = reportData.getProject().getMeasures().getDelta();
                dncloc += deltaMeasure.getNcloc();
                dbugs += deltaMeasure.getBugs();
                dviolations += deltaMeasure.getViolations();
                dcriticalViolations += deltaMeasure.getCriticalViolations();
                dvulnerabilities += deltaMeasure.getVulnerabilities();
                dduplicatedBlocks += deltaMeasure.getDuplicatedBlocks();
            }
            Measure cuMeasure = new Measure();
            cuMeasure.setNcloc(cncloc);
            cuMeasure.setBugs(cbugs);
            cuMeasure.setViolations(cviolations);
            cuMeasure.setCriticalViolations(ccriticalViolations);
            cuMeasure.setVulnerabilities(cvulnerabilities);
            cuMeasure.setDuplicatedBlocks(cduplicatedBlocks);

            Measure duMeasure = new Measure();
            duMeasure.setNcloc(dncloc);
            duMeasure.setBugs(dbugs);
            duMeasure.setViolations(dviolations);
            duMeasure.setCriticalViolations(dcriticalViolations);
            duMeasure.setVulnerabilities(dvulnerabilities);
            duMeasure.setDuplicatedBlocks(dduplicatedBlocks);

            TagGroup teamTagGroup = new TagGroup();
            teamTagGroup.setTag(teamName);
            ReportData reportData = new ReportData();
            reportData.setNewUnResolvedVulnerability(newUnResolvedVulnerability);
            reportData.setNewUnResolvedBugs(newUnResolvedBugs);
            Project project = new Project();
            project.setProjectTeam(teamName);
            project.setTotalCount(reportDatas.size());
            project.setMeasures(new Measures(cuMeasure, duMeasure));
            reportData.setProject(project);
            teamTagGroup.setReportDatas(Collections.singletonList(reportData));
            teamGroup.add(teamTagGroup);
        }
        return teamGroup;
    }

    private List<TagGroup> getTagGroupData(List<Project> projects, String weekBegin, String weekEnd) {

        Map<String, List<Project>> teamsMap = new HashMap<>();
        for (Project project : projects) {
            String projectTeam = project.getProjectTeam();
            if (projectTeam == null) {
                projectTeam = StringUtils.EMPTY;
            }
            List<Project> teamList = teamsMap.get(projectTeam);
            if (teamList == null) {
                teamList = new ArrayList<>();
            }
            teamList.add(project);
            teamsMap.put(projectTeam, teamList);
        }

        Comparator<Project> projectComparator = Comparator
                .comparing(p -> p.getMeasures().getCurrent().getBugs(), Comparator.reverseOrder());

        List<String> teamList = teamsMap.keySet().stream()
                .sorted((x, y) -> {
                    if (StringUtils.isNotEmpty(x) && StringUtils.isNotEmpty(y)) {
                        return x.compareTo(y);
                    } else {
                        return StringUtils.isEmpty(x) ? 1 : -1;
                    }
                })
                .collect(Collectors.toList());

        if (StringUtils.isEmpty(weekBegin) || StringUtils.isEmpty(weekEnd)) {
            return teamList.stream().map(team -> {
                        List<Project> teamProjects = teamsMap.get(team);
                        List<ReportData> reportDataList =
                                teamProjects.stream().map(project -> new ReportData(project, 0, 0)).collect(toList());
                        return new TagGroup(team, reportDataList);
                    }
            ).collect(toList());
        } else {
            return teamList.stream().map(team -> {
                        List<Project> teamProjects = teamsMap.get(team);
                        List<ReportData> teamReportData = teamProjects.stream()
                                .sorted(projectComparator)
                                .map(p -> getReportData(p, weekBegin, weekEnd))
                                .collect(toList());
                        return new TagGroup(team, teamReportData);
                    }
            ).collect(toList());
        }
    }

    private ReportData getReportData(Project project, String weekBegin, String weekEnd) {
        int curWeekNewBugs = corpSonarOperations.getBugsCount(project.getKey(), weekBegin, weekEnd, null);
        int curWeekNewResolvedBugs = corpSonarOperations.getBugsCount(project.getKey(), weekBegin, weekEnd, true);

        int curWeekNewVulnerability = corpSonarOperations.getIssuesCount(project.getKey(), weekBegin, weekEnd,
                "VULNERABILITY", null);
        int curWeekNewResolvedVulnerability = corpSonarOperations.getIssuesCount(project.getKey(), weekBegin, weekEnd,
                "VULNERABILITY", true);

        int newUnResolvedBugs = curWeekNewBugs - curWeekNewResolvedBugs;
        int newUnResolvedVulnerability = curWeekNewVulnerability - curWeekNewResolvedVulnerability;

        ReportData reportData = new ReportData();
        reportData.setProject(project);
        reportData.setNewUnResolvedBugs(newUnResolvedBugs);
        reportData.setNewUnResolvedVulnerability(newUnResolvedVulnerability);

        return reportData;
    }

    private boolean filterProject(Project project) {
        return Optional.ofNullable(project.getMeasures()).map(Measures::getCurrent).orElse(null) != null;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class ProjectGitInfo {
        private String key;
        private String gitRepoWebUrl;
        private String gitDescription;
        private String pathWithNamespace;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class FilterProject {
        private List<Project> noScanProjectList;
        private List<Project> scanProjectList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class FilterAllProject {
        private List<Project> afterFilterAllProject;
        private List<Project> noScanProjectList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class FilterTagGroup {
        private List<TagGroup> tagGroupList;
        private List<TagGroup> noScanGroupList;
        private List<TagGroup> teamGroupList;
    }

}
