package com.kuaishou.serveree.themis.component.constant.analyze;

import java.util.Arrays;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/1/8 3:52 下午
 */
@AllArgsConstructor
public enum AnalyzeRuleType {

    CICHECK(1, "S", "容器检查", " -Dcicheck.skip=false "
            + "-Dcicheck.skipSpringInitCheck=true ", " -Dcicheck.report=true ", " -Dcicheck.report=false "),
    CHECKSTYLE(2, "C", "checkstyle检查", "", " -Dcheckstyle.result.report=true ", " -Dcheckstyle.result.report=false "),
    K_CHECK(3, "K", "快手检查", ""
            + "-Ddeprecated.check.skip=false " + "-Ddependency.rely.skip=false ",
            " -Dproto.check.report=true -Djava.enforcer.report=true -Ddeprecated.check.report=true -Ddependency.rely"
                    + ".report=true ",
            " -Dproto.check.report=false -Djava.enforcer.report=false -Ddeprecated.check.report=false -Ddependency"
                    + ".rely.report=false "),
    CUSTOM(4, "Z", "用户自定义检查", "", "", ""),
    ;

    @Getter
    private Integer typeCode;

    @Getter
    private String ruleIdPrefix;

    @Getter
    private String description;

    @Getter
    private String analyzeMvnArgs;

    @Getter
    private String reportArgs;

    @Getter
    private String errorArgs;

    private static final Map<String, AnalyzeRuleType> CODE_MAP = Maps.newHashMap();

    static {
        Arrays.stream(AnalyzeRuleType.values()).forEach(analyzeRuleType -> {
            CODE_MAP.put(analyzeRuleType.getRuleIdPrefix(), analyzeRuleType);
        });
    }

    public static Integer getRuleTypeByPrefix(String prefix) {
        if (StringUtils.isEmpty(prefix)) {
            return 0;
        }
        return CODE_MAP.get(prefix).getTypeCode();
    }

}
