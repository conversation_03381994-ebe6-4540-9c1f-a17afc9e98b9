package com.kuaishou.serveree.themis.component.constant.platform;

import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-04-16
 */
@Getter
public enum IssueRepairType {

    CODE_UPDATED(1, "代码变动"),
    PROFILE_UPDATED(2, "规则集变动");

    private final int type;
    private final String desc;

    IssueRepairType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
