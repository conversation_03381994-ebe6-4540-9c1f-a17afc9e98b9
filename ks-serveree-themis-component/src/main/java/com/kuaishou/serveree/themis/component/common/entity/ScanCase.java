package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ScanCase对象", description = "")
public class ScanCase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "匹配字符串")
    private String caseStr;

    @ApiModelProperty(value = "扩展字段")
    private String extendStr;

    @ApiModelProperty(value = "匹配类型")
    private Integer caseType;

    public ScanCase(String caseStr, String extendStr, Integer caseType) {
        this.caseStr = caseStr;
        this.extendStr = extendStr;
        this.caseType = caseType;
    }

    public ScanCase() {
    }

    public ScanCase(Long id, LocalDateTime gmtCreate, LocalDateTime gmtModified, String caseStr, String extendStr,
            Integer caseType) {
        this.id = id;
        this.gmtCreate = gmtCreate;
        this.gmtModified = gmtModified;
        this.caseStr = caseStr;
        this.extendStr = extendStr;
        this.caseType = caseType;
    }
}
