package com.kuaishou.serveree.themis.component.constant.team;

import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-27 14:49
 **/
@Getter
public enum TeamIssueRequiredFieldEnum {
    COMMERCE_BU_REQUIRED_FIELD(TeamIssueBuEnum.COMMERCE_BU_SONAR, Maps.newHashMap()),
    API_MANAGE_BU_REQUIRED_FIELD(TeamIssueBuEnum.API_MANAGE_BU, Maps.newHashMap()),
    TEAM_GRAY_VERIFY_REQUIRED_FIELD(TeamIssueBuEnum.TEAM_GRAY_VERIFY, Maps.newHashMap()),
    COMMON_TEAM_REQUIRED_FIELD(TeamIssueBuEnum.COMMON_TEAM, Maps.newHashMap()),
    ;

    public static final String P2_PRIORITY = "65"; // P2优先级
    public static final String API_MANAGE_BU_BUG_FIND_STATE = "7"; // 功能测试
    /**
     * sonar发现问题阶段
     */
    public static final String SONAR_FIND_STAGE = "449869";


    static {
        API_MANAGE_BU_REQUIRED_FIELD.getRequired()
                .put("assignee", Lists.newArrayList(TeamIssueBuEnum.API_MANAGE_BU.getDefaultOperator()));
        API_MANAGE_BU_REQUIRED_FIELD.getRequired()
                .put("reporter", Lists.newArrayList(TeamIssueBuEnum.API_MANAGE_BU.getDefaultOperator()));
        API_MANAGE_BU_REQUIRED_FIELD.getRequired().put("priority", Lists.newArrayList(P2_PRIORITY));
        API_MANAGE_BU_REQUIRED_FIELD.getRequired()
                .put("bugFindStage", Lists.newArrayList(API_MANAGE_BU_BUG_FIND_STATE));

        TEAM_GRAY_VERIFY_REQUIRED_FIELD.getRequired()
                .put("assignee", Lists.newArrayList(TeamIssueBuEnum.TEAM_GRAY_VERIFY.getDefaultOperator()));
        TEAM_GRAY_VERIFY_REQUIRED_FIELD.getRequired()
                .put("reporter", Lists.newArrayList(TeamIssueBuEnum.TEAM_GRAY_VERIFY.getDefaultOperator()));
        TEAM_GRAY_VERIFY_REQUIRED_FIELD.getRequired().put("priority", Lists.newArrayList(P2_PRIORITY));
        TEAM_GRAY_VERIFY_REQUIRED_FIELD.getRequired()
                .put("bugFindStage", Lists.newArrayList(API_MANAGE_BU_BUG_FIND_STATE));

        COMMON_TEAM_REQUIRED_FIELD.getRequired().put("assignee", Lists.newArrayList());
        COMMON_TEAM_REQUIRED_FIELD.getRequired().put("reporter", Lists.newArrayList());
        COMMON_TEAM_REQUIRED_FIELD.getRequired().put("priority", Lists.newArrayList(P2_PRIORITY));
        COMMON_TEAM_REQUIRED_FIELD.getRequired().put("bugFindStage", Lists.newArrayList(API_MANAGE_BU_BUG_FIND_STATE));

        COMMERCE_BU_REQUIRED_FIELD.getRequired().put("assignee", Lists.newArrayList());
        COMMERCE_BU_REQUIRED_FIELD.getRequired().put("reporter", Lists.newArrayList());
        COMMERCE_BU_REQUIRED_FIELD.getRequired().put("priority", Lists.newArrayList(P2_PRIORITY));
        COMMON_TEAM_REQUIRED_FIELD.getRequired().put("F54865", Lists.newArrayList(SONAR_FIND_STAGE));
    }

    private final TeamIssueBuEnum issueBuEnum;
    private final Map<String, List<String>> required;

    TeamIssueRequiredFieldEnum(TeamIssueBuEnum issueBuEnum, Map<String, List<String>> required) {
        this.issueBuEnum = issueBuEnum;
        this.required = required;
    }
}
