package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ComplexityFile对象", description = "")
public class ComplexityFile implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "所属仓库复杂度表id")
    private Long repositoryId;

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "圈复杂度值")
    private Integer cyclomaticComplexity;

    @ApiModelProperty(value = "认知复杂度值")
    private Integer cognitiveComplexity;
}
