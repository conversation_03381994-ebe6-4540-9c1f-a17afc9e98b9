package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-25
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckReportContext对象", description = "")
public class CheckReportContext implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "check_repo_id")
    private Long checkRepoId;

    @ApiModelProperty(value = "check_repo_branch_id")
    private Long checkRepoBranchId;

    @ApiModelProperty(value = "扫描主表id")
    private Long baseId;

    @ApiModelProperty(value = "check_execution的主键id")
    private Long checkExecutionId;

    @ApiModelProperty(value = "上报数据上下文json结构")
    private String context;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;



}
