package com.kuaishou.serveree.themis.component.constant.analyze;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/8/12 4:59 下午
 */
@AllArgsConstructor
public enum CheckBaseTriggerType {

    UN_KNOW(0, "未知"),
    MR(1, "merge request"),
    PIPELINE(2, "流水线"),
    METRIC(3, "度量"),
    THIRD_API(4, "第三方接口调用");

    @Getter
    private Integer code;

    @Getter
    private String desc;

}
