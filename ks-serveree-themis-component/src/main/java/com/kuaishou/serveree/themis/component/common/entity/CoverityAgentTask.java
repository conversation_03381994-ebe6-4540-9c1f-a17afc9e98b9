package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CoverityAgentTask对象", description = "")
public class CoverityAgentTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "执行cov-build的流水线构建id")
    private Long kspBuildId;

    @ApiModelProperty(value = "执行cov-build的流水线id")
    private Long kspPipelineId;

    @ApiModelProperty(value = "cov-build-dir下载地址")
    private String covDownloadUrl;

    @ApiModelProperty(value = "任务优先级,值越大越优先")
    private Integer priority;

    @ApiModelProperty(value = "开始执行时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime executeStart;

    @ApiModelProperty(value = "执行结束时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime executeEnd;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "更新时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "执行此任务的agentId")
    private Long agentId;

    @ApiModelProperty(value = "任务状态")
    private String status;

    @ApiModelProperty(value = "任务结果")
    private String result;

    @ApiModelProperty(value = "agent最近一次上报状态时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime heartbeatTime;

    private String taskType;
}
