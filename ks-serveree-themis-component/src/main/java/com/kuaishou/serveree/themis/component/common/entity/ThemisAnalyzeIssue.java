package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-08
 */
@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ThemisAnalyzeIssue对象", description = "")
public class ThemisAnalyzeIssue implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "分析id")
    private Long analyzeId;

    @ApiModelProperty(value = "规范类型 0.其他 1.后端开发规范 2.依赖规范")
    private Integer standardType;

    @ApiModelProperty(value = "规则id")
    private String ruleId;

    @ApiModelProperty(value = "整体展示分类 1.cicheck 2.checkstyle 3.k-check 4.custom")
    private Integer ruleType;

    @ApiModelProperty(value = "规则类型")
    private String ruleLevel;

    @ApiModelProperty(value = "违反的规则描述")
    private String ruleDescription;

    @ApiModelProperty(value = "违规文件集合")
    private String illegalFile;

    @ApiModelProperty(value = "违反规则的依赖")
    private String illegalDependency;

    @ApiModelProperty(value = "违反规则的报错信息")
    private String illegalErrorMsg;

    @ApiModelProperty(value = "违反的内容")
    private String illegalContent;

    @ApiModelProperty(value = "违反的行号")
    private Integer illegalLineNo;

    @ApiModelProperty(value = "违反的列号")
    private Integer illegalColumnNo;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createdTime;

}
