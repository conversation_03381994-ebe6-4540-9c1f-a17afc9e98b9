package com.kuaishou.serveree.themis.component.constant.team;

import lombok.Getter;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-28 16:03
 **/
@Getter
public enum TeamIssueRecordBuildStatus {

    SUCCESS(0, "创建成功"),
    FAILED(1, "创建失败");
    private final int code;
    private final String desc;

    TeamIssueRecordBuildStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
