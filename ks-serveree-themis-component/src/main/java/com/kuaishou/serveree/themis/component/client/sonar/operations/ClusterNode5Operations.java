package com.kuaishou.serveree.themis.component.client.sonar.operations;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.client.sonar.api.ClusterNode5Api;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-16
 */
@Slf4j
@Component
public class ClusterNode5Operations implements SonarClusterOperations {

    @Value("${sonar.cluster.node5.login-id}")
    private String loginId;

    @Autowired
    private ClusterNode5Api clusterNode5Api;

    @Override
    public SonarCommonApi sonarApi() {
        return clusterNode5Api;
    }

    @Override
    public Integer nodeNumber() {
        return 5;
    }

    @Override
    public String loginId() {
        return loginId;
    }

    @Override
    public boolean supportJdk17() {
        return true;
    }
}
