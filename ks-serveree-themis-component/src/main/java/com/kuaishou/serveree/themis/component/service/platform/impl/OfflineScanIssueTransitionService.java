package com.kuaishou.serveree.themis.component.service.platform.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranchProfile;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.platform.scan.ScannerHelper;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-10
 */
@Service
public class OfflineScanIssueTransitionService extends AbstractIssueTransitionService {

    @Autowired
    private CheckRepoBranchService checkRepoBranchService;
    @Autowired
    private CheckRepoBranchProfileService checkRepoBranchProfileService;
    @Autowired
    private CheckMeasuresService checkMeasuresService;
    @Autowired
    private ScannerHelper scannerHelper;
    @Autowired
    private CheckRepoService checkRepoService;

    @Override
    protected ScanModeEnum scanMode() {
        return ScanModeEnum.OFFLINE;
    }

    @Override
    protected void afterTransition(IssueTransitionRequest request, IssueSummary issueSummary) {
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(request.getGitProjectId());
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.getByRepoIdBranch(checkRepo.getId(), request.getGitBranch());
        CheckRepoBranchProfile checkRepoBranchProfile =
                checkRepoBranchProfileService.getByBranchIdOffline(checkRepoBranch.getId());
        checkMeasuresService.changeMeasuresValue(checkRepoBranchProfile, checkRepoBranch, issueSummary);
        scannerHelper.afterIssueTransition(issueSummary, checkRepoBranchProfile.getScanner(), request.getTransition());
    }
}
