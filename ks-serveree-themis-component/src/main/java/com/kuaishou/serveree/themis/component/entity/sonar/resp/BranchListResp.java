package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-11-30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BranchListResp {
    private List<BranchInfo> branches;

    @Data
    public static class BranchInfo {
        private String name;
        private Boolean isMain;
        private String type;
        private String mergeBranch;
        //    private Object status;
        private Date analysisDate;
    }
}
