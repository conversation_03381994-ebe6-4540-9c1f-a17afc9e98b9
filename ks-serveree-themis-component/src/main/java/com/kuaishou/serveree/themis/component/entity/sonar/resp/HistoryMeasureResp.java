package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.History;
import com.kuaishou.serveree.themis.component.entity.sonar.Paging;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/21 4:54 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HistoryMeasureResp {

    private Paging paging;
    private List<HistoryMeasures> measures;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HistoryMeasures {
        private String metric;
        private List<History> history;
    }

}
