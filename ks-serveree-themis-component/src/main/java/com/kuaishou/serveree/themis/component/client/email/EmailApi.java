package com.kuaishou.serveree.themis.component.client.email;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;

import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/10/15 4:02 下午
 */
@Component
@Slf4j
public class EmailApi {

    @Resource
    private JavaMailSender javaMailSender;

    public void send(String subject, String content, List<String> persons) {
        try {
            MimeMessage mimeMessage = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
            helper.setSubject(subject);
            helper.setText(content, true);
            helper.setTo(persons.toArray(new String[0]));
            helper.setFrom("<EMAIL>");
            helper.setSentDate(new Date());
            javaMailSender.send(mimeMessage);
        } catch (Exception e) {
            log.error("spring mail sender send email error", e);
        }
    }

}
