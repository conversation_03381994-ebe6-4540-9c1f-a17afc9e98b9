package com.kuaishou.serveree.themis.component.common.exception;

import java.util.Objects;

import com.kuaishou.serveree.themis.component.service.openapi.TeamOpenApi.ApiResponse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-24 15:31
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamTaskException extends RuntimeException {
    private int code;
    private String message;

    public TeamTaskException(ApiResponse response) {
        if (Objects.isNull(response)) {
            this.code = 0;
            this.message = "api response is null!";
        } else {
            this.code = response.getCode();
            this.message = this.subBuildMsg(response.getMessage());
        }
    }

    public TeamTaskException(Throwable throwable) {
        super(throwable);
    }

    /**
     * 截取信息的前255，方法堆栈信息过大导致sql超越长度
     */
    private String subBuildMsg(String message) {
        return message.substring(0, Math.min(message.length(), 255));
    }
}
