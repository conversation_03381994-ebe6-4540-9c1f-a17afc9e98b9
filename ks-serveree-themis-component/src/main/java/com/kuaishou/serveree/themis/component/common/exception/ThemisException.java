package com.kuaishou.serveree.themis.component.common.exception;

import org.apache.commons.lang3.StringUtils;

import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/28 2:45 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ThemisException extends RuntimeException {

    private int code;

    private String message;

    public ThemisException(ResultCodeConstant resultCodeConstant, Object... args) {
        this.code = resultCodeConstant.getCode();
        if (StringUtils.isNotEmpty(resultCodeConstant.getMessage())
            && resultCodeConstant.getMessage().contains("%s")) {
            this.message = String.format(resultCodeConstant.getMessage(), args);
        } else {
            this.message = resultCodeConstant.getMessage();
        }
    }

    public ThemisException(Throwable cause) {
        super(cause);
    }
}
