package com.kuaishou.serveree.themis.component.service.impl;

import static com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.PAGE_SIZE;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.phantomthief.util.MoreFunctions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummaryBase;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.common.mappers.IssueSummaryBaseMapper;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.service.IssueSummaryBaseService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.IssueUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * IssueSummaryBase 表 服务类
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-12
 */
@Slf4j
@Service
public class IssueSummaryBaseServiceImpl extends ServiceImpl<IssueSummaryBaseMapper, IssueSummaryBase> implements
        IssueSummaryBaseService {

    @Autowired
    private IssueSummaryBaseMapper issueSummaryBaseMapper;


    @Autowired
    private IssueSummaryService issueSummaryService;

    @Resource
    private ThreadPoolExecutor fillUpCommonUniqIdExecutor;

    @Resource
    private ThreadPoolExecutor issueSummaryBaseSyncExecutor;

    @Override
    public Map<String, String> getModifierByIssueUniqKeys(Collection<String> issueUniqKeyList) {
        if (issueUniqKeyList.isEmpty()) {
            return Collections.emptyMap();
        }
        List<IssueSummaryBase> list =
                list(Wrappers.<IssueSummaryBase> lambdaQuery().in(IssueSummaryBase::getIssueUniqId, issueUniqKeyList)
                        .or().in(IssueSummaryBase::getIssueUniqIdV2, issueUniqKeyList));
        return list.stream().collect(Collectors.toMap(IssueUtils::getIssueUniqkey, IssueSummaryBase::getModifier, (a, b) -> a));
    }

    @Override
    public String getEffectStatus(IssueSummaryBase issueSummaryBase) {
        if (issueSummaryBase == null || StringUtils.isBlank(issueSummaryBase.getStatus())) {
            return null;
        }
        List<String> adaptStatusList = Lists.newArrayList(CheckIssueStatus.CLOSED.getStatus(),
                CheckIssueStatus.TO_REVIEW.getStatus());
        // 如果是 closed 或 to_review 就返回此状态
        if (adaptStatusList.contains(issueSummaryBase.getStatus())) {
            return issueSummaryBase.getStatus();
        }
        return null;
    }

    @Override
    public void pageFillCommonIssueUniqId() {

        int count = count();
        int cycles = count / PAGE_SIZE;
        for (int page = 1; page <= cycles + 1; page++) {
            int finalPage = page;
            fillUpCommonUniqIdExecutor.execute(() -> {
                Page<IssueSummaryBase> pageRecord = page(
                        new Page<>(finalPage, PAGE_SIZE),
                        Wrappers.<IssueSummaryBase> lambdaQuery().orderByAsc(IssueSummaryBase::getId)
                );
                List<IssueSummaryBase> issueSummaryBases = pageRecord.getRecords();
                if (CollectionUtils.isNotEmpty(issueSummaryBases)) {
                    fillAndSaveBases(issueSummaryBases);
                }
            });
        }

    }

    private void fillAndSaveBases(List<IssueSummaryBase> issueSummaryBases) {
        for (IssueSummaryBase issueSummaryBase : issueSummaryBases) {
            String commonIssueUniqId = IssueUtils.genIssueUniqId(issueSummaryBase);
            issueSummaryBase.setCommonIssueUniqId(commonIssueUniqId);
        }
        issueSummaryBaseMapper.updateBatch(issueSummaryBases);
    }


    @Override
    public List<IssueSummaryBase> listIssueSummaryBaseInCommonIssueUniqIds(Collection<String> commonIssueUniqIds) {
        if (CollectionUtils.isEmpty(commonIssueUniqIds)) {
            return Collections.emptyList();
        }
        return this.list(
                Wrappers.<IssueSummaryBase> lambdaQuery().in(IssueSummaryBase::getCommonIssueUniqId, commonIssueUniqIds)
        );
    }

    @Override
    @Transactional
    public void updateIssueStatusByUniqKey(String issueUniqKey, String newStatus, String userName) {
        List<IssueSummaryBase> summaryBaseList = list(Wrappers.<IssueSummaryBase> lambdaQuery()
                .eq(IssueSummaryBase::getIssueUniqId, issueUniqKey)
                .or()
                .eq(IssueSummaryBase::getIssueUniqIdV2, issueUniqKey));
        if (CollectionUtils.isEmpty(summaryBaseList)) {
            log.error("更新Issue状态失败，IssueSummaryBase表中不存在的Issue，key：{}", issueUniqKey);
            throw new ThemisException(ResultCodeConstant.ISSUE_NOT_EXIST);
        }
        // 先打个error日志，手动查库排查问题
        if (summaryBaseList.size() > 1) {
            log.error("查询到多条IssueSummaryBase记录，issueKey：{}", issueUniqKey);
        }
        // 取第一个
        IssueSummaryBase issueSummaryBase = summaryBaseList.get(0);
        // issue手动标注只可能是 打开、重开 -> 误判、不会修复 或 误判、不会修复 --> 打开
        // 如果issue终态已经是解决，那就不做变动了，除非下次重新扫描出来再变为重开
        if (CheckIssueStatus.RESOLVED.getStatus().equals(issueSummaryBase.getStatus())) {
            // 打个error日志提醒一下
            log.error("更新SummaryBase状态失败，已经是RESOLVED，本次操作：issueKey: {}, toStatus: {}", issueUniqKey, newStatus);
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        issueSummaryBase.setGmtModified(now);
        issueSummaryBase.setStatus(newStatus);
        issueSummaryBase.setModifier(userName);
        this.saveOrUpdate(issueSummaryBase);
    }

    @Override
    public Map<String, IssueSummaryBase> aggragateIssueUniqIdSummaryBaseMap(int gitProjectId) {
        // 重新查
        return list(Wrappers.<IssueSummaryBase> lambdaQuery()
                .eq(IssueSummaryBase::getGitProjectId, gitProjectId)).stream()
                .collect(Collectors.toMap(IssueSummaryBase::getIssueUniqId, Function.identity(), (existing, replacement) -> existing));
    }

    @Override
    public Map<String, IssueSummaryBase> aggragateIssueUniqIdSummaryBaseMap(Collection<String> issueUniqIdList) {
        return listSummaryBaseByIssueUniqIds(issueUniqIdList).stream()
                .collect(Collectors.toMap(IssueSummaryBase::getIssueUniqId, Function.identity(), (a, b) -> a));
    }

    @Override
    public Map<String, IssueSummaryBase> aggragateSummaryBaseMapByIssueUniqKeys(Collection<String> issueUniqKeyList) {
        if (CollectionUtils.isEmpty(issueUniqKeyList)) {
            return Collections.emptyMap();
        }
        return list(Wrappers.<IssueSummaryBase> lambdaQuery()
                .in(IssueSummaryBase::getIssueUniqId, issueUniqKeyList)
                .or()
                .in(IssueSummaryBase::getIssueUniqIdV2, issueUniqKeyList)).stream()
                .collect(Collectors.toMap(IssueUtils::getIssueUniqkey, Function.identity(), (a, b) -> a));
    }

    @Override
    public Map<String, IssueSummaryBase> aggragateIssueUniqIdV2SummaryBaseMap(Collection<String> issueUniqIdV2List) {
        if (CollectionUtils.isEmpty(issueUniqIdV2List)) {
            return Collections.emptyMap();
        }
        return list(Wrappers.<IssueSummaryBase> lambdaQuery().in(IssueSummaryBase::getIssueUniqIdV2, issueUniqIdV2List)).stream()
                .collect(Collectors.toMap(IssueSummaryBase::getIssueUniqIdV2, Function.identity(), (a, b) -> a));
    }

    @Override
    public void syncIssueFromIssueSummary(int gitProjectId) {
        // 此项目所有 issue uniq ids
        List<String> issueUniqIdList = issueSummaryService.listIssueUniqIdsByGitProjectId(gitProjectId);
        // summary base 表中已经存在的 issue
        List<String> existIssueUniqIds = getExistIssueUniqIds(issueUniqIdList);
        // summary base 表中缺失的 issue
        Collection<String> missedIssueUniqIds = CollectionUtil.subtract(issueUniqIdList, existIssueUniqIds);
        if (CollectionUtils.isEmpty(missedIssueUniqIds)) {
            return;
        }
        // 查询summary表中这些缺失的issue的记录
        Map<String, List<IssueSummary>> issueUniqId2SummaryListMap =
                issueSummaryService.getSummaryUniqIdMapByIssueUniqIds(missedIssueUniqIds);
        // 构造 summary base 记录并保存
        LocalDateTime now = LocalDateTime.now();
        List<IssueSummaryBase> needSaveList = issueUniqId2SummaryListMap.values().stream()
                .map(summaryList -> createByIssueSummaryList(summaryList, now))
                .collect(Collectors.toList());
        MoreFunctions.runCatching(() -> saveBatch(needSaveList));
    }

    @Transactional
    @Override
    public void batchCreateOrUpdate(Collection<IssueSummary> latestIssueSummaryList) {
        if (CollectionUtils.isEmpty(latestIssueSummaryList)) {
            return;
        }
        // 有新key用新key，否则用旧key
        Map<Boolean, List<IssueSummary>> map = latestIssueSummaryList.stream()
                .collect(Collectors.groupingBy(s -> StringUtils.isNotBlank(s.getIssueUniqIdV2())));
        List<IssueSummary> summaryListUseUniqIdV2 = map.get(Boolean.TRUE);
        List<IssueSummary> summaryListUseUniqId = map.getOrDefault(Boolean.FALSE, new ArrayList<>());
        List<IssueSummaryBase> needSaveList = Lists.newArrayList();
        List<IssueSummaryBase> needUpdateList = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        // 先处理使用新key的
        if (CollectionUtils.isNotEmpty(summaryListUseUniqIdV2)) {
            // 转成map
            Map<String, IssueSummary> uniqIdV2IssueSummaryMap = summaryListUseUniqIdV2.stream()
                    .collect(Collectors.toMap(IssueSummary::getIssueUniqIdV2, Function.identity(), (a, b) -> a));
            // 查询base表记录
            Map<String, IssueSummaryBase> uniqIdV2SummaryBaseMap =
                    aggragateIssueUniqIdV2SummaryBaseMap(uniqIdV2IssueSummaryMap.keySet());
            // 若用新key判断base表记录不存在，则仍然需要使用旧key再次判断。否则需要更新
            for (IssueSummary issueSummary : summaryListUseUniqIdV2) {
                IssueSummaryBase summaryBase = uniqIdV2SummaryBaseMap.get(issueSummary.getIssueUniqIdV2());
                if (Objects.isNull(summaryBase)) {
                    // 需要用旧key再次判读
                    summaryListUseUniqId.add(issueSummary);
                    continue;
                }
                // 需要更新
                updateSummaryBaseByIssueSummary(needUpdateList, now, issueSummary, summaryBase);
            }
        }
        // 再处理使用旧key的
        if (CollectionUtils.isNotEmpty(summaryListUseUniqId)) {
            Map<String, IssueSummary> uniqIdIssueSummaryMap = summaryListUseUniqId.stream()
                    .collect(Collectors.toMap(IssueSummary::getIssueUniqId, Function.identity(), (a, b) -> a));
            // 已经存在的base表记录
            Map<String, IssueSummaryBase> uniqIdSummaryBaseMap =
                    aggragateIssueUniqIdSummaryBaseMap(uniqIdIssueSummaryMap.keySet());
            saveOrUpdateSummaryBase(uniqIdIssueSummaryMap, uniqIdSummaryBaseMap, needSaveList, needUpdateList, now);
        }
        if (CollectionUtils.isNotEmpty(needSaveList)) {
            try {
                saveBatch(needSaveList);
            } catch (DuplicateKeyException e) {
                log.error(
                        String.format("BatchSave Error, issueSummaryMap is %s, needSaveList is %s",
                        JSONUtils.serialize(map), needSaveList),
                        e
                );
                throw e;
            }
        }
        // 分批更新
        CommonUtils.processInBatches(needUpdateList, PAGE_SIZE, this::updateBatchById);
    }

    private void updateSummaryBaseByIssueSummary(List<IssueSummaryBase> needUpdateList, LocalDateTime now, IssueSummary issueSummary,
            IssueSummaryBase summaryBase) {
        // 有变更再更新
        if (!StringUtils.equals(issueSummary.getStatus(), summaryBase.getStatus())
                || !StringUtils.equals(issueSummary.getSeverity(), summaryBase.getSeverity())
                || !StringUtils.equals(issueSummary.getIssueUniqIdV2(), summaryBase.getIssueUniqIdV2())) {
            summaryBase.setStatus(issueSummary.getStatus());
            summaryBase.setSeverity(issueSummary.getSeverity());
            summaryBase.setIssueUniqIdV2(issueSummary.getIssueUniqIdV2());
            summaryBase.setRepairTime(issueSummary.getRepairTime());
            summaryBase.setRepairType(issueSummary.getRepairType());
            summaryBase.setGmtModified(now);
            needUpdateList.add(summaryBase);
        }
    }

    private void saveOrUpdateSummaryBase(Map<String, IssueSummary> issueSummaryMap,
            Map<String, IssueSummaryBase> summaryBaseMap, List<IssueSummaryBase> needSaveList,
            List<IssueSummaryBase> needUpdateList, LocalDateTime now) {
        for (Entry<String, IssueSummary> entry : issueSummaryMap.entrySet()) {
            String issueUniqId = entry.getKey();
            IssueSummary issueSummary = entry.getValue();
            IssueSummaryBase summaryBase = summaryBaseMap.get(issueUniqId);
            if (Objects.nonNull(summaryBase)) {
                updateSummaryBaseByIssueSummary(needUpdateList, now, issueSummary, summaryBase);
            } else {
                // 不存在，新建
                summaryBase = new IssueSummaryBase();
                BeanUtil.copyProperties(issueSummary, summaryBase);
                summaryBase.setId(null);
                summaryBase.setGmtModified(now);
                needSaveList.add(summaryBase);
            }
        }
    }

    private IssueSummaryBase createByIssueSummaryList(List<IssueSummary> issueSummaryList, LocalDateTime now) {
        // 拿到某个 issue 在 离线 和 流水下 两种模式下的 summary 列表
        Map<Integer, List<IssueSummary>> scanModeIssueSummaryListMap =
                issueSummaryList.stream().collect(Collectors.groupingBy(IssueSummary::getScanMode));
        // 在两种模式下的最新 summary
        List<IssueSummary> scanModeLatestIssueSummary = scanModeIssueSummaryListMap.values().stream()
                .map(summaryList -> summaryList.stream()
                        .max(Comparator.comparing(IssueSummary::getGmtModified)).get())
                .collect(Collectors.toList());
        // 选择状态为 closed 或 to_review 或 最新 的 summary 创建 summary base
        Set<String> adaptStatus =
                Sets.newHashSet(CheckIssueStatus.CLOSED.getStatus(), CheckIssueStatus.TO_REVIEW.getStatus());
        IssueSummary adaptIssueSummary = scanModeLatestIssueSummary.get(0);
        for (IssueSummary summary : scanModeLatestIssueSummary) {
            // 如果是这两状态 那么直接更新为此状态
            if (adaptStatus.contains(summary.getStatus())) {
                adaptIssueSummary = summary;
                break;
            }
            if (summary.getGmtModified().isAfter(adaptIssueSummary.getGmtModified())) {
                adaptIssueSummary = summary;
            }
        }
        // 创建 summary base
        IssueSummaryBase issueSummaryBase = new IssueSummaryBase();
        BeanUtil.copyProperties(adaptIssueSummary, issueSummaryBase);
        issueSummaryBase.setId(null);
        issueSummaryBase.setGmtCreate(now);
        issueSummaryBase.setGmtModified(now);

        return issueSummaryBase;
    }

    private List<String> getExistIssueUniqIds(Collection<String> issueUniqIdList) {
        if (CollectionUtils.isEmpty(issueUniqIdList)) {
            return Collections.emptyList();
        }
        return this.baseMapper.listByIssueUniqIds(StringUtils.join(issueUniqIdList, ","));
    }

    @Override
    public List<IssueSummaryBase> listSummaryBaseByIssueUniqIds(Collection<String> issueUniqIdList) {
        if (CollectionUtils.isEmpty(issueUniqIdList)) {
            return Collections.emptyList();
        }
        return this.list(
                Wrappers.<IssueSummaryBase> lambdaQuery().in(IssueSummaryBase::getIssueUniqId, issueUniqIdList)
        );
    }

    @Override
    public void copyAndChangeColumn() {
        int count = count();
        int cycles = count / PAGE_SIZE;
        for (int page = 1; page <= cycles + 1; page++) {
            int finalPage = page;
            issueSummaryBaseSyncExecutor.execute(() -> {
                Page<IssueSummaryBase> pageRecord = page(
                        new Page<>(finalPage, PAGE_SIZE),
                        Wrappers.<IssueSummaryBase> lambdaQuery()
                                .select(IssueSummaryBase::getId, IssueSummaryBase::getIssueUniqId,
                                        IssueSummaryBase::getCommonIssueUniqId,
                                        IssueSummaryBase::getIssueUniqIdCopy,
                                        IssueSummaryBase::getCommonIssueUniqIdCopy).orderByAsc(IssueSummaryBase::getId)
                );
                List<IssueSummaryBase> issueSummaryBases = pageRecord.getRecords();
                if (CollectionUtils.isNotEmpty(issueSummaryBases)) {
                    copyNewColumn(issueSummaryBases);
                }
            });
        }
    }

    @Override
    public void swapColumnValues() {
        int count = count();
        int cycles = count / PAGE_SIZE;
        for (int page = 1; page <= cycles + 1; page++) {
            int finalPage = page;
            issueSummaryBaseSyncExecutor.execute(() -> {
                Page<IssueSummaryBase> pageRecord = page(
                        new Page<>(finalPage, PAGE_SIZE),
                        Wrappers.<IssueSummaryBase> lambdaQuery()
                                .select(IssueSummaryBase::getId, IssueSummaryBase::getIssueUniqId,
                                        IssueSummaryBase::getCommonIssueUniqId,
                                        IssueSummaryBase::getIssueUniqIdCopy,
                                        IssueSummaryBase::getCommonIssueUniqIdCopy).orderByAsc(IssueSummaryBase::getId)
                );
                List<IssueSummaryBase> issueSummaryBases = pageRecord.getRecords();
                if (CollectionUtils.isNotEmpty(issueSummaryBases)) {
                    changeNewColumn(issueSummaryBases);
                }
            });
        }
    }

    private void copyNewColumn(List<IssueSummaryBase> issueSummaryBases) {
        if (CollectionUtils.isEmpty(issueSummaryBases)) {
            return;
        }
        for (IssueSummaryBase issueSummaryBase : issueSummaryBases) {
            issueSummaryBase.setCommonIssueUniqIdCopy(issueSummaryBase.getCommonIssueUniqId());
            issueSummaryBase.setIssueUniqIdCopy(issueSummaryBase.getIssueUniqId());
        }
        updateBatchById(issueSummaryBases);
    }

    private void changeNewColumn(List<IssueSummaryBase> issueSummaryBases) {
        if (CollectionUtils.isEmpty(issueSummaryBases)) {
            return;
        }
        for (IssueSummaryBase issueSummaryBase : issueSummaryBases) {
            issueSummaryBase.setCommonIssueUniqId(issueSummaryBase.getIssueUniqIdCopy());
            issueSummaryBase.setIssueUniqId(issueSummaryBase.getCommonIssueUniqIdCopy());
        }

        for (IssueSummaryBase issueSummaryBase : issueSummaryBases) {
            MoreFunctions.runCatching(() -> {
                updateById(issueSummaryBase);
            });
        }

    }

}
