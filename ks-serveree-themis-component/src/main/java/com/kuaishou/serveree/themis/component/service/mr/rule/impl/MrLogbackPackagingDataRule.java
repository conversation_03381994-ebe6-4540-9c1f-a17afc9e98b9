package com.kuaishou.serveree.themis.component.service.mr.rule.impl;

import java.io.ByteArrayInputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.annotation.Resource;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;

import org.apache.commons.compress.utils.Lists;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.xml.sax.Attributes;
import org.xml.sax.Locator;
import org.xml.sax.helpers.DefaultHandler;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.kdev.MrCheckpointEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueType;
import com.kuaishou.serveree.themis.component.entity.issue.MrCheckpointIssueBo;
import com.kuaishou.serveree.themis.component.service.kdev.MrCheckpointBo;
import com.kuaishou.serveree.themis.component.service.mr.rule.MrCheckpointRule;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-15
 */
@Slf4j
@Component
public class MrLogbackPackagingDataRule implements MrCheckpointRule {

    @Resource
    private GitOperations gitOperations;

    private static final String MSG = "logback配置packagingData=true可能导致业务线程被打满";

    @Getter
    public static class LineNumberHandler extends DefaultHandler {
        private Locator locator;
        private final List<Integer> lines = new ArrayList<>();

        @Override
        public void setDocumentLocator(Locator locator) {
            this.locator = locator;
        }

        @Override
        public void startElement(String uri, String localName, String qName, Attributes attributes) {
            for (int i = 0; i < attributes.getLength(); i++) {
                if ("packagingData".equals(attributes.getQName(i)) && "true".equals(attributes.getValue(i))) {
                    lines.add(locator.getLineNumber());
                    break;
                }
            }
        }
    }

    @Override
    public List<MrCheckpointIssueBo> check(MrCheckpointBo checkpointBo, List<String> filePaths) {
        if (!support(checkpointBo.getCheckpointName())) {
            return Collections.emptyList();
        }
        Map<String, List<Integer>> fileLinesMap = Maps.newHashMap();
        try {
            SAXParserFactory factory = SAXParserFactory.newInstance();
            SAXParser saxParser = factory.newSAXParser();
            for (String path : filePaths) {
                // 获取文件内容
                String content = gitOperations.getCachedRawContent(
                        checkpointBo.getGitProjectId(), checkpointBo.getCommitId(), path);
                LineNumberHandler handler = new LineNumberHandler();
                saxParser.parse(new ByteArrayInputStream(content.getBytes()), handler);
                List<Integer> lines = handler.getLines();
                if (lines.isEmpty()) {
                    continue;
                }
                fileLinesMap.put(path, lines);
            }
        } catch (Exception e) {
            log.error("解析logback.xml失败, params: {}, fileList: {}", checkpointBo, filePaths);
            throw new ThemisException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "解析logback.xml失败");
        }
        // 汇总issue
        List<MrCheckpointIssueBo> mrCheckpointIssueBoList = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        for (Entry<String, List<Integer>> e : fileLinesMap.entrySet()) {
            String filePath = e.getKey();
            List<Integer> lineNumbers = e.getValue();
            for (Integer lineNumber : lineNumbers) {
                MrCheckpointIssueBo issue = new MrCheckpointIssueBo();
                issue.setCheckpointName(checkpointBo.getCheckpointName());
                issue.setRule(ruleKey());
                issue.setMessage(MSG);
                issue.setType(CheckIssueType.BUG.getType());
                issue.setSeverity(CheckIssueSeverity.SERIOUS.getKey());
                issue.setStatus(CheckIssueStatus.OPEN.getStatus());
                issue.setStuck(true);
                issue.setLocation(filePath);
                issue.setStartLine(lineNumber);
                issue.setEndLine(lineNumber);
                issue.setStartOffset(0);
                issue.setEndOffset(0);
                issue.setOperator("");
                issue.setGmtCreate(now);
                issue.setGmtModified(now);
                issue.setIssueUniqId(issue.generateUniqId(checkpointBo.getGitProjectId(), checkpointBo.getCheckpointName()));
                issue.setIssueId((long) (issue.getIssueUniqId().hashCode() & 0x7fffffff));
                mrCheckpointIssueBoList.add(issue);
            }
        }
        return mrCheckpointIssueBoList;
    }

    @Override
    public String ruleKey() {
        return "mr:logback:packaging_data_true";
    }

    @Override
    public boolean support(String checkpointName) {
        return MrCheckpointEnum.LogbackCheck.name().equals(checkpointName);
    }
}
