package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Paging;
import com.kuaishou.serveree.themis.component.entity.sonar.Project;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/21 3:21 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectsResp {

    private Paging paging;

    private List<Project> components;

}
