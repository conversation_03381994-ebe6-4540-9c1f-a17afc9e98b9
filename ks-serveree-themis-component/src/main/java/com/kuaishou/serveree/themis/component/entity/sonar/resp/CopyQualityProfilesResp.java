package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/2/20 3:33 PM
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CopyQualityProfilesResp {

    private String key;
    private String name;
    private String language;
    private String languageName;
    private boolean isDefault;
    private boolean isInherited;

    public void setKey(String key) {
        this.key = key;
    }
    public String getKey() {
        return key;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
    public String getLanguage() {
        return language;
    }

    public void setLanguageName(String languageName) {
        this.languageName = languageName;
    }
    public String getLanguageName() {
        return languageName;
    }

    public void setIsDefault(boolean isDefault) {
        this.isDefault = isDefault;
    }
    public boolean getIsDefault() {
        return isDefault;
    }

    public void setIsInherited(boolean isInherited) {
        this.isInherited = isInherited;
    }
    public boolean getIsInherited() {
        return isInherited;
    }

}
