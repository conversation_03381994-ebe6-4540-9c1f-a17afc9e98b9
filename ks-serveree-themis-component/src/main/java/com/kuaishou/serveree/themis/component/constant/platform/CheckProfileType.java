package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/1/9 3:04 PM
 */
@AllArgsConstructor
public enum CheckProfileType {

    OFFLINE(1, "离线规则集", ScanModeEnum.OFFLINE),
    PIPELINE(2, "流水线规则集", ScanModeEnum.PROCESS),
    ;

    @Getter
    private final Integer type;

    @Getter
    private final String desc;

    @Getter
    private final ScanModeEnum scanModeEnum;

    private static final Map<Integer, CheckProfileType> TYPE_MAP = Maps.newHashMap();

    static {
        for (CheckProfileType checkProfileType : CheckProfileType.values()) {
            TYPE_MAP.put(checkProfileType.getType(), checkProfileType);
        }
    }

    public static CheckProfileType getByType(Integer type) {
        return TYPE_MAP.get(type);
    }

    public static CheckProfileType getByScanMode(ScanModeEnum scanMode) {
        for (CheckProfileType profileType : values()) {
            if (profileType.getScanModeEnum() == scanMode) {
                return profileType;
            }
        }
        throw new IllegalArgumentException("scanMode not support");
    }

}
