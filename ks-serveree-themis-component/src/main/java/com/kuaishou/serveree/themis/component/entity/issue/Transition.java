package com.kuaishou.serveree.themis.component.entity.issue;

import static com.google.common.base.Preconditions.checkArgument;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.entity.issue.condition.Condition;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-24
 * 流转动作与状态流的一次流转的映射关系
 */
public class Transition {
    private final String key; //触发动作：@link DefaultTransitions
    private final String from; //一次状态变更的起始状态 @link SonarIssueStatusEnum
    private final String to; //一次状态变更的结束状态 @link SonarIssueStatusEnum
    private final Condition[] conditions;

    private Transition(TransitionBuilder builder) {
        key = builder.key;
        from = builder.from;
        to = builder.to;
        conditions = builder.conditions.toArray(new Condition[0]);
    }

    public String key() {
        return key;
    }

    public String from() {
        return from;
    }

    public String to() {
        return to;
    }

    Condition[] conditions() {
        return conditions;
    }

    public boolean supportsManualTransition(Issue issue) {
        for (Condition condition : conditions) {
            if (condition.enableWhenTransition() && !condition.matches(issue)) {
                return false;
            }
        }
        return true;
    }

    public boolean supportsOutTransition(Issue issue) {
        for (Condition condition : conditions) {
            if (condition.enableWhenOutTransition() && !condition.matches(issue)) {
                return false;
            }
        }
        return true;
    }

    public static Transition create(String key, String from, String to) {
        return builder(key).from(from).to(to).build();
    }

    public static TransitionBuilder builder(String key) {
        return new TransitionBuilder(key);
    }

    public static class TransitionBuilder {
        private final String key;
        private String from;
        private String to;
        private List<Condition> conditions = Lists.newArrayList();

        private TransitionBuilder(String key) {
            this.key = key;
        }

        public TransitionBuilder from(String from) {
            this.from = from;
            return this;
        }

        public TransitionBuilder to(String to) {
            this.to = to;
            return this;
        }

        public TransitionBuilder conditions(Condition... c) {
            this.conditions.addAll(Arrays.asList(c));
            return this;
        }

        public Transition build() {
            checkArgument(!Strings.isNullOrEmpty(key), "Transition key must be set");
            checkArgument(StringUtils.isAllLowerCase(key), "Transition key must be lower-case");
            checkArgument(!Strings.isNullOrEmpty(from), "Originating status must be set");
            checkArgument(!Strings.isNullOrEmpty(to), "Destination status must be set");
            return new Transition(this);
        }
    }
}
