package com.kuaishou.serveree.themis.component.config;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-05
 */
@Component
public class EnvironmentConfig {
    @Autowired
    private Environment environment;

    public boolean isProd() {
        List<String> activeProfiles = Stream.of(environment.getActiveProfiles()).collect(Collectors.toList());
        return activeProfiles.contains("prod");
    }

    public boolean isTest() {
        List<String> activeProfiles = Stream.of(environment.getActiveProfiles()).collect(Collectors.toList());
        return activeProfiles.contains("dev") || activeProfiles.contains("beta");
    }

    public boolean isPrt() {
        List<String> activeProfiles = Stream.of(environment.getActiveProfiles()).collect(Collectors.toList());
        return activeProfiles.contains("prt");
    }
}
