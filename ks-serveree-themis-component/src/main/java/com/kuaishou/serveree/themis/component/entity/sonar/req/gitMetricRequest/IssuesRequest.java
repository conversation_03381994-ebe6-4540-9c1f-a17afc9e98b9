package com.kuaishou.serveree.themis.component.entity.sonar.req.gitMetricRequest;

import com.kuaishou.serveree.themis.component.entity.sonar.req.GitCommonRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.SonarSearchRequest;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-05-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IssuesRequest extends GitCommonRequest {
    private String statuses;
    private int page;
    private int pageSize;
    private String types;
    private String severities;
    public SonarSearchRequest getIssueRequest() {
        SonarSearchRequest request = new SonarSearchRequest();
        request.setStatuses(this.statuses);
        request.setPage(this.page);
        request.setPageSize(this.pageSize);
        return request;
    }
    public SonarSearchRequest getTotalIssueRequest() {
        SonarSearchRequest request = this.getIssueRequest();
        request.setTypes(this.types);
        request.setSeverities(this.severities);
        return request;
    }
}
