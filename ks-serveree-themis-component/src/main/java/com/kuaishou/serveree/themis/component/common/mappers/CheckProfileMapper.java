package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.Collection;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;
import com.kuaishou.serveree.themis.component.entity.platform.CheckProfileSearchCondition;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Mapper
@Repository
public interface CheckProfileMapper extends BaseMapper<CheckProfile> {

    IPage<String> pageMostUsedProfileNames(
            @Param("page") Page<String> page, @Param("condition") CheckProfileSearchCondition condition);

    @Select("<script>"
            + "select distinct parent_profile_name from check_profile "
            + "where parent_profile_name in (<foreach collection='parentProfileNames' item='item' separator=','>#{item}</foreach>)"
            + "and deleted = 0 limit 1000"
            + "</script>")
    Set<String> listParentProfileNamesExists(@Param("parentProfileNames") Collection<String> parentProfileNames);
}
