package com.kuaishou.serveree.themis.component.client.kdev;

import static com.kuaishou.serveree.themis.component.client.git.GitOperations.GIT_FILE_CONTENT_CACHE_DAY;
import static com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant.LOCAL_BUILD_DIFF_FILES_PREFIX;

import java.io.InputStream;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.annotation.Kconfig;
import com.kuaishou.qa.serveree.common.logger.service.RestService;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.entity.CommonResponse;
import com.kuaishou.serveree.themis.component.entity.kdev.GitProjectPerm;
import com.kuaishou.serveree.themis.component.entity.kdev.IdAndName;
import com.kuaishou.serveree.themis.component.entity.kdev.KdevResponse;
import com.kuaishou.serveree.themis.component.entity.kdev.LocalBuildInfo;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineBaseInfoResponse;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineBaseInfoResponse.FeatureInfo;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineBaseInfoResponse.TeamInfo;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineBuildParamVo;
import com.kuaishou.serveree.themis.component.entity.kdev.SearchRequest;
import com.kuaishou.serveree.themis.component.entity.kdev.SearchResponse;
import com.kuaishou.serveree.themis.component.entity.kdev.UserProjectIdsResponse;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.kdev.KdevPipelineCallbackRequest.KdevPipelineBuildInfo;
import com.kuaishou.serveree.themis.component.vo.response.kdev.MrStuckResultKdevReport;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/12/14 8:26 下午
 */
@Slf4j
@Component
public class KdevApi {

    @Value("${kdev.url}")
    private String kdevUrl;
    @Value("${kdev.timeout}")
    private int timeout;
    @Value("${kdev.code-search-timeout}")
    private int codeSearchTimeout;
    @Value("${kdev.token-name}")
    private String tokenKey;
    @Kconfig(key = "qa.themis.kdevToken", defaultValue = "")
    private Kconf<String> kdevToken;
    @Autowired
    private KsRedisClient ksRedisClient;
    @Autowired
    private RestService restService;

    @Value("${pipeline-detail-url.halo}")
    private String haloDetailUrl;

    @Value("${pipeline-detail-url.kdev}")
    private String kdevDetailUrl;

    public PipelineBuildParamVo getPipelineBuildParam(long kspBuildId) {
        String url = String.format("%s/api/kdev/pub/pipeline/log/getBuildParamByHaloBuildId/%s", kdevUrl, kspBuildId);
        String body = HttpRequest
                .get(url)
                .timeout(timeout)
                .execute()
                .body();
        CommonResponse<PipelineBuildParamVo> paramVoCommonResponse = JSONUtils.deserialize(body,
                new TypeReference<CommonResponse<PipelineBuildParamVo>>() {
                });
        return paramVoCommonResponse.getData();
    }

    @Deprecated
    public List<IdAndName> listGitRolesByProjectId(Integer projectId, Integer repoType, String userName) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("type", repoType);
        params.put("username", userName);
        String body = HttpRequest
                .get(CommonUtils.buildGetUrl(kdevUrl, "/kdev/internal/user/role/list", params))
                .header(tokenKey, kdevToken.get())
                .timeout(timeout)
                .execute()
                .body();
        CommonResponse<List<IdAndName>> response = JSONUtils.deserialize(body,
                new TypeReference<CommonResponse<List<IdAndName>>>() {
                });
        if (response == null) {
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "请求用户所在的项目的角色发生异常", "null");
        }
        return response.getData();
    }

    public GitProjectPerm listGitRolesByProjectId(Integer projectId, String userName) {
        Map<String, Object> params = new HashMap<>();
        params.put("projectId", projectId);
        params.put("username", userName);
        String body = HttpRequest
                .get(CommonUtils.buildGetUrl(kdevUrl, "/kdev/internal/user/project/permission", params))
                .header(tokenKey, kdevToken.get())
                .timeout(timeout)
                .execute()
                .body();
        CommonResponse<GitProjectPerm> response = JSONUtils.deserialize(body,
                new TypeReference<CommonResponse<GitProjectPerm>>() {
                });
        if (response == null || response.getData() == null) {
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "请求用户所在的项目的角色发生异常", "null");
        }
        return response.getData();
    }

    public UserProjectIdsResponse getUserGitProjectIds(String username) {
        String url = String.format("%s/kdev/internal/user/permission/project/list", kdevUrl);
        Map<String, Object> params = new HashMap<>();
        params.put("username", username);
        // 要查询的仓库类型，1：Git，2：Svn
        params.put("repoType", 1);
        String body = HttpRequest
                .get(url)
                .header(tokenKey, kdevToken.get())
                .form(params)
                .timeout(timeout)
                .execute()
                .body();
        CommonResponse<UserProjectIdsResponse> response = JSONUtils.deserialize(body,
                new TypeReference<CommonResponse<UserProjectIdsResponse>>() {
                });
        if (response == null) {
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "获取用户git projectIds失败", body);
        }
        return response.getData();
    }

    public SearchResponse codeSearch(SearchRequest searchRequest) {
        String url = String.format("%s/kdev/internal/code/search/result", kdevUrl);
        String resultResp = HttpRequest
                .get(url)
                .header(tokenKey, kdevToken.get())
                .form(BeanUtil.beanToMap(searchRequest))
                .timeout(codeSearchTimeout)//超时，毫秒
                .execute()
                .body();
        KdevResponse<SearchResponse> response = JSONUtils.deserialize(resultResp,
                new TypeReference<KdevResponse<SearchResponse>>() {
                });
        if (response == null) {
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用kdev code search失败", resultResp);
        }
        return response.getData();
    }

    /**
     * 通过ks_build_id查询流水线下关联的team任务id
     */
    public String queryTeamIdByKspBuildId(Long buildId) {
        String url =
                String.format("%s/api/kdev/pub/pipeline/log/branch/getBaseInfoWithRelationInfo/%s", kdevUrl, buildId);
        String resultResp = HttpRequest
                .get(url)
                .timeout(timeout)// 超时，毫秒
                .execute()
                .body();
        KdevResponse<PipelineBaseInfoResponse> response = JSONUtils.deserialize(resultResp,
                new TypeReference<KdevResponse<PipelineBaseInfoResponse>>() {
                });
        if (response == null) {
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR,
                    "调用 kdev getBaseInfoWithRelationInfo 失败", resultResp);
        }
        PipelineBaseInfoResponse responseData = response.getData();
        if (responseData == null) {
            return null;
        }
        List<FeatureInfo> featureList = responseData.getFeatureList();
        if (CollectionUtils.isEmpty(featureList)) {
            return null;
        }
        FeatureInfo featureInfo = featureList.get(0);
        List<TeamInfo> teamList = featureInfo.getTeamList();
        if (CollectionUtils.isEmpty(teamList)) {
            return null;
        }
        return teamList.get(0).getTaskId();
    }

    public InputStream getLocalBuildDiffInputStream(Long localBuildId) {
        String url = String.format("%s/kdev/internal/local-build/view/%s/localbuild.diff", kdevUrl, localBuildId);
        InputStream inputStream = HttpRequest
                .get(url)
                .header(tokenKey, kdevToken.get())
                .timeout(codeSearchTimeout)//超时，毫秒
                .execute()
                .bodyStream();
        return inputStream;
    }

    public List<String> getDiffFilesPathByLocalBuildId(Integer gitProjectId, Long mrId, Long localBuildId) {
        String url = "/review/api/mr/internal/cr/diffs";
        Map<String, Object> reqMap = Maps.newHashMap();
        reqMap.put("projectId", gitProjectId);
        reqMap.put("mrId", mrId);
        reqMap.put("localbuildId", localBuildId);
        String body = HttpRequest
                .get(CommonUtils.buildGetUrl(kdevUrl, url, reqMap))
                .timeout(codeSearchTimeout)//超时，毫秒
                .header(tokenKey, kdevToken.get())
                .execute()
                .body();
        KdevResponse<List<String>> diffFilePaths =
                JSONUtils.deserialize(body, new TypeReference<KdevResponse<List<String>>>() {
                });
        return diffFilePaths.getData();
    }

    public LocalBuildInfo getLocalBuildInfo(Long localBuildId) {
        String url = "%s/kdev/internal/local-build/get/%s";
        String body = HttpRequest
                .get(String.format(url, kdevUrl, localBuildId))
                .timeout(codeSearchTimeout)//超时，毫秒
                .header(tokenKey, kdevToken.get())
                .execute()
                .body();
        KdevResponse<LocalBuildInfo> localBuildInfoKdevResponse =
                JSONUtils.deserialize(body, new TypeReference<KdevResponse<LocalBuildInfo>>() {
                });
        return localBuildInfoKdevResponse.getData();
    }

    public List<String> getDiffFilesPathByLocalBuildId(Long localBuildId) {
        final String redisKey = LOCAL_BUILD_DIFF_FILES_PREFIX + localBuildId;
        String cacheContent = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(cacheContent)) {
            return JSONUtils.deserializeList(cacheContent, String.class);
        }
        LocalBuildInfo localBuildInfo = getLocalBuildInfo(localBuildId);
        if (localBuildInfo == null) {
            return Collections.emptyList();
        }
        long mrId = localBuildInfo.getMrId();
        long gitProjectId = localBuildInfo.getGitProjectId();
        List<String> diffFiles = getDiffFilesPathByLocalBuildId((int) gitProjectId, mrId, localBuildId);
        if (CollectionUtils.isNotEmpty(diffFiles)) {
            ksRedisClient.sync().setex(redisKey, TimeUnit.DAYS.toSeconds(GIT_FILE_CONTENT_CACHE_DAY),
                    JSONUtils.serialize(diffFiles));
        }
        return diffFiles;
    }

    /**
     * 上报MR卡点的结果给Kdev
     * @param report
     */
    public void reportMrStuckResult(MrStuckResultKdevReport report) {
        String requestStr = JSONUtils.serialize(report);
        String url = kdevUrl + "/review/api/mr/internal/code/scan/stuckpoint/result";
        log.info("上报mr卡点流水线结果给kdev，url is {}, request body is {}", url, requestStr);
        try {
            String response = HttpUtil.post(url, requestStr, timeout);
            KdevResponse noticeResponse = JSONUtils.deserialize(response, KdevResponse.class);
            if (Objects.isNull(noticeResponse) || HttpStatus.HTTP_OK != noticeResponse.getStatus()) {
                log.error(String.format("上报mr卡点流水线结果给kdev失败，response is %s", noticeResponse));
            }
            log.info("上报mr卡点流水线结果给kdev结果，{}", response);
        } catch (Exception e) {
            log.error("上报mr卡点流水线结果给kdev失败，", e);
        }
    }

    /**
     * 获取kdev流水线执行状态
     * @param kdevPipelineBuildId
     * @return
     */
    public KdevPipelineBuildInfo getPipelineStatus(long kdevPipelineBuildId) {
        String url = kdevUrl + "/kdev/internal/pipeline/log/get?id=" + kdevPipelineBuildId;
        log.info("获取kdev流水线执行状态，url is {}", url);
        try {
            KdevResponse<KdevPipelineBuildInfo> response =
                    JSONUtils.deserialize(
                            HttpRequest.get(url)
                                    .timeout(timeout)
                                    .header(tokenKey, kdevToken.get())
                                    .execute()
                                    .body(),
                            new TypeReference<KdevResponse<KdevPipelineBuildInfo>>() {
                            });
            if (Objects.isNull(response) || Objects.isNull(response.getData())
                    || HttpStatus.HTTP_OK != response.getStatus()) {
                log.error("获取kdev流水线执行状态失败，response is " + response);
                return null;
            }
            return response.getData();
        } catch (Exception e) {
            log.error("获取kdev流水线执行状态失败，", e);
        }
        return null;
    }

    /**
     * 拼接流水线地址
     */
    public String getPipelineUrl(Long ciJobId, Long ciTaskId) {
        if (null == ciJobId || null == ciTaskId) {
            return "";
        }
        String url;
        String getKDevURL = kdevDetailUrl + ciTaskId;
        ResponseEntity<String> responseEntity = restService.doGet(getKDevURL, null, null);
        String body = responseEntity.getBody();
        Map jsonMap = ObjectMapperUtils.fromJSON(body, Map.class);
        String kDevUrl = (String) jsonMap.get("data");
        if (StringUtils.isNotBlank(kDevUrl)) {
            url = kDevUrl;
        } else {
            log.info("kdev获取不到url，ciTaskId为{}", ciTaskId);
            url = haloDetailUrl + ciJobId + "/" + ciTaskId;
        }

        return url;
    }
}
