package com.kuaishou.serveree.themis.component.entity.sonar;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RuleDetail {
    private String key;
    private String repo;
    private String name;
    private String createdAt;
    private String htmlDesc;
    private String mdDesc;
    private String severity;
    private String status;
    private Boolean isTemplate;
    private List<Object> tags;
    private List<String> sysTags;
    private String lang;
    private String langName;
    private List<RuleDetailParam> params;
    private String defaultDebtRemFnType;
    private String defaultDebtRemFnOffset;
    private Boolean debtOverloaded;
    private String debtRemFnType;
    private String debtRemFnOffset;
    private String defaultRemFnType;
    private String defaultRemFnBaseEffort;
    private String remFnType;
    private String remFnBaseEffort;
    private Boolean remFnOverloaded;
    private String scope;
    private Boolean isExternal;
    private String type;

    @Data
    public static class RuleDetailParam {
        private String key;
        private String desc;
        private String defaultValue;
    }
}
