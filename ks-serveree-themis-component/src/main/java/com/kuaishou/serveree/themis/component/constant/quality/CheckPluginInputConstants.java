package com.kuaishou.serveree.themis.component.constant.quality;

/**
 * <AUTHOR>
 * @since 2021/1/25 4:19 下午
 */
public interface CheckPluginInputConstants {

    /**
     * mvn参数
     */
    String MVN_BUILD_ARGUMENTS = "MVN_BUILD_ARGUMENTS";

    /**
     * 是否需要编译
     */
    String NEED_BUILD = "NEED_BUILD";

    /**
     * git的commit-id
     */
    String GIT_COMMIT_ID = "GIT_COMMIT_ID";

    /**
     * git的merge-request id
     */
    String MR_ID = "MR_ID";
    /**
     * 构建模块
     */
    String BUILD_MODULES = "BUILD_MODULES";

    /**
     * 扫描插件中的构建模块参数，和编译插件区分开来
     */
    String SONAR_BUILD_MODULES = "SONAR_BUILD_MODULES";

    /**
     * 是否只考虑在diff内的issue
     */
    String ONLY_DIFF_ISSUE = "ONLY_DIFF_ISSUE";

    /**
     * 是否是mr卡点的流水线
     */
    String MR_STUCK = "MR_STUCK";

    /**
     * 灰度包
     */
    String BETA_PACKAGE_VERSIONS = "BETA_PACKAGE_VERSIONS";

    /**
     * 扫描模式，增量/全量
     */
    String SCAN_MODE = "SCAN_MODULE";

    /**
     * 是否mr触发
     */
    String MR_TRIGGER = "MR_TRIGGER";

    /**
     * 是否发送kim通知
     */
    String SEND_KIM_NOTICE = "SEND_KIM_NOTICE";


    /**
     * cicheck开关
     */
    String CI_CHECK_SWITCH = "CI_CHECK_SWITCH";

    /**
     * cicheck的检查等级
     */
    String CI_CHECK_LEVEL = "CI_CHECK_LEVEL";

    /**
     * checkstyle开关
     */
    String CHECKSTYLE_SWITCH = "CHECKSTYLE_SWITCH";

    /**
     * checkstyle检查的等级
     */
    String CHECKSTYLE_LEVEL = "CHECKSTYLE_LEVEL";

    /**
     * kcheck开关
     */
    String KCHECK_SWITCH = "KCHECK_SWITCH";

    /**
     * kcheck检查的等级
     */
    String KCHECK_LEVEL = "KCHECK_LEVEL";

    /**
     * BUG卡点开关
     */
    String STUCK_BUG_SWITCH = "STUCK_BUG_SWITCH";
    /**
     * 漏洞卡点开关
     */
    String STUCK_VUL_SWITCH = "STUCK_VUL_SWITCH";
    /**
     * BUG卡点等级
     */
    String STUCK_BUG_MIN_LEVEL = "STUCK_BUG_MIN_LEVEL";
    /**
     * BUG卡点数量
     */
    String STUCK_BUG_COUNT = "STUCK_BUG_COUNT";
    /**
     * 漏洞卡点等级
     */
    String STUCK_VUL_MIN_LEVEL = "STUCK_VUL_MIN_LEVEL";
    /**
     * 漏洞卡点数量
     */
    String STUCK_VUL_COUNT = "STUCK_VUL_COUNT";
    /**
     * 是否开启圈复杂度统计
     */
    String CALC_CYCLO_SWITCH = "CALC_CYCLO_SWITCH";
}
