package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;
import com.kuaishou.serveree.themis.component.constant.platform.LocalReportStatus;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 检查主表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
@Builder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckBase对象", description = "检查主表")
public class CheckBase implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "check_repo_id")
    private Long checkRepoId;

    @ApiModelProperty(value = "check_repo_branch表主键id")
    private Long checkRepoBranchId;

    @ApiModelProperty(value = "git repo地址")
    private String repoUrl;

    @ApiModelProperty(value = "group路径")
    private String groupPath;

    @ApiModelProperty(value = "分支名")
    private String branch;

    @ApiModelProperty(value = "git commit id")
    private String commitId;

    @ApiModelProperty(value = "mr_id")
    private String mrId;

    @ApiModelProperty(value = "ksp的build_id")
    private String kspBuildId;

    @ApiModelProperty(value = "触发类型 0.未知1.mr 2.流水线 3.度量 4.外部的接口调用")
    private Integer triggerType;

    @ApiModelProperty(value = "是否已经同步数据")
    private Boolean sync;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    private Boolean isLast;

    private Boolean canMappingOriginal;

    /**
     * 状态 {@link LocalReportStatus} 的status字段
     */
    private Integer localReportStatus;

}
