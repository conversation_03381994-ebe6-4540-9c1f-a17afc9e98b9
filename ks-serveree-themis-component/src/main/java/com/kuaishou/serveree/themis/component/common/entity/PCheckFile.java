package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "PCheckFile对象", description = "")
public class PCheckFile implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    @ApiModelProperty(value = "路径")
    private String location;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "p_check_base表主键id")
    private Long pBaseId;

    @ApiModelProperty(value = "check_repo表主键id")
    private Long checkRepoId;

    @ApiModelProperty(value = "分支")
    private String branch;

    @ApiModelProperty(value = "文件类型 0 文件 1 目录")
    private Integer fileType;

    @ApiModelProperty(value = "文件级别")
    private Integer fileLevel;

    @ApiModelProperty(value = "父文件id")
    private Long parentFileId;

    @ApiModelProperty(value = "文件后缀")
    private String fileSuffix;


}
