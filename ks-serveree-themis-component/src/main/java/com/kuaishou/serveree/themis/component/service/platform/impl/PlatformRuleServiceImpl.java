package com.kuaishou.serveree.themis.component.service.platform.impl;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.BRANCH_NOT_INIT_RULESET;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.DUPLICATE_KEY_ERROR;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.INVALID_PARAMS;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.NOT_AUTH_TO_DEL;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.NOT_AUTH_TO_UPDATE;
import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.RULE_SET_INFO_ERROR;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;
import com.kuaishou.serveree.themis.component.client.sonar.operations.ClusterNode1Operations;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfileRuleRelation;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranchProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckRule;
import com.kuaishou.serveree.themis.component.common.entity.CheckRuleLabelRelation;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.CheckProfileType;
import com.kuaishou.serveree.themis.component.constant.platform.CheckRepoBranchVersion;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformLanguageEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueType;
import com.kuaishou.serveree.themis.component.constant.quality.FacetEnum;
import com.kuaishou.serveree.themis.component.constant.quality.FacetNode;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.dto.ProfileRuleSeverityDto;
import com.kuaishou.serveree.themis.component.entity.platform.CheckRuleListCondition;
import com.kuaishou.serveree.themis.component.entity.platform.ProfileInfo;
import com.kuaishou.serveree.themis.component.entity.sonar.Profiles;
import com.kuaishou.serveree.themis.component.entity.sonar.req.QualityProfileReq;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.QualityProfileResp;
import com.kuaishou.serveree.themis.component.service.CheckProfileRuleRelationService;
import com.kuaishou.serveree.themis.component.service.CheckProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.CheckRuleLabelRelationService;
import com.kuaishou.serveree.themis.component.service.CheckRuleService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRuleService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.KconfUtil;
import com.kuaishou.serveree.themis.component.utils.NumberUtils;
import com.kuaishou.serveree.themis.component.utils.PlatformSonarInfoUtils;
import com.kuaishou.serveree.themis.component.utils.ThemisTaskTokenUtil;
import com.kuaishou.serveree.themis.component.vo.request.AddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.DeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ListRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.RuleDetailRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.SearchRuleRequest;
import com.kuaishou.serveree.themis.component.vo.request.SearchRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.UpdateRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.AddRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.DeleteRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.Facet;
import com.kuaishou.serveree.themis.component.vo.response.Facet.FacetValue;
import com.kuaishou.serveree.themis.component.vo.response.LabelListResponseVo.LabelDetailVo;
import com.kuaishou.serveree.themis.component.vo.response.ListRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.RuleDetailResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.RuleInfoResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.SearchRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.SearchRuleResponseVo.CheckRuleVo;
import com.kuaishou.serveree.themis.component.vo.response.SearchRulesResponse;
import com.kuaishou.serveree.themis.component.vo.response.UpdateRuleResponseVo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/9/6 2:29 下午
 */
@Service
@Slf4j
public class PlatformRuleServiceImpl implements PlatformRuleService {

    @Autowired
    private CheckRuleService checkRuleService;
    @Autowired
    private KsRedisClient ksRedisClient;
    @Autowired
    private CheckProfileService checkProfileService;
    @Autowired
    private CheckProfileRuleRelationService checkProfileRuleRelationService;
    @Autowired
    private CheckRuleLabelRelationService checkRuleLabelRelationService;
    @Autowired
    private PlatformPermissionService platformPermissionService;
    @Autowired
    private KconfUtil kconfUtil;
    @Autowired
    private ClusterNode1Operations node1Operations;
    @Autowired
    private PlatformSonarInfoUtils platformSonarInfoUtils;
    @Autowired
    private CheckRepoService checkRepoService;
    @Autowired
    private CheckRepoBranchProfileService checkRepoBranchProfileService;
    @Autowired
    private CheckRepoBranchService checkRepoBranchService;
    @Autowired
    private PlatformRepoService platformRepoService;

    private static final Integer SEARCH_RULE_PAGE_SIZE = 500;

    @Override
    public SearchRuleResponseVo search(SearchRuleRequestVo requestVo) {
        String tokensSource = ThemisTaskTokenUtil.get().getSource();
        final String redisKey = KsRedisPrefixConstant.QUALITY_PLATFORM_DIFF_SOURCE_RULE_LIST + tokensSource;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return new SearchRuleResponseVo(JSONUtils.deserializeList(redisVal, CheckRuleVo.class));
        }
        List<CheckRule> allRules = checkRuleService.allBySource(tokensSource);
        List<CheckRuleVo> checkRuleVos = this.convert2Vo(allRules);
        ksRedisClient.sync().setex(redisKey, TimeUnit.DAYS.toSeconds(1), JSONUtils.serialize(checkRuleVos));
        return new SearchRuleResponseVo(checkRuleVos);
    }

    @Override
    @Transactional
    public UpdateRuleResponseVo update(UpdateRuleRequestVo requestVo) {
        List<CheckRuleVo> updateRules = requestVo.getUpdateRules();
        if (CollectionUtils.isEmpty(updateRules)) {
            return new UpdateRuleResponseVo();
        }
        // 参数校验
        for (CheckRuleVo updateRule : updateRules) {
            if (StringUtils.isBlank(updateRule.getKey()) && !NumberUtils.isPositive(updateRule.getRuleId())) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "ruleId,key");
            }
            String severity = updateRule.getSeverity();
            if (StringUtils.isNotEmpty(severity) && !CheckIssueSeverity.correctType(severity)) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "severity");
            }
            String type = updateRule.getType();
            if (StringUtils.isNotEmpty(type) && !CheckIssueType.correctType(type)) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "type");
            }
        }
        String source = ThemisTaskTokenUtil.get().getSource();
        // 根据是否指定id分组
        Map<Boolean, List<CheckRuleVo>> map =
                updateRules.stream().collect(Collectors.groupingBy(a -> NumberUtils.isPositive(a.getRuleId())));
        // 指定id更新
        int updated = updateBatchById(map.getOrDefault(Boolean.TRUE, Collections.emptyList()), source);
        // 指定key更新
        updated += updateBatchByRuleKey(map.getOrDefault(Boolean.FALSE, Collections.emptyList()), source);
        // 清缓存
        final String redisKey = KsRedisPrefixConstant.QUALITY_PLATFORM_DIFF_SOURCE_RULE_LIST + source;
        ksRedisClient.sync().del(redisKey);
        // 更新标签
        updateRuleLabels(updateRules);
        return new UpdateRuleResponseVo(updated);
    }

    /**
     * 通过id查询并更新
     */
    private int updateBatchById(List<CheckRuleVo> updateRules, String source) {
        if (CollectionUtils.isEmpty(updateRules)) {
            return 0;
        }
        Map<Long, CheckRuleVo> checkRuleVoMap =
                updateRules.stream().collect(Collectors.toMap(CheckRuleVo::getRuleId, Function.identity(), (a, b) -> b));
        List<CheckRule> dbCheckRules = checkRuleService.listByIds(checkRuleVoMap.keySet());
        for (CheckRule dbCheckRule : dbCheckRules) {
            Long ruleId = dbCheckRule.getId();
            if (!dbCheckRule.getSource().equals(source)) {
                throw new ThemisException(NOT_AUTH_TO_UPDATE.getCode(),
                        NOT_AUTH_TO_UPDATE.getMessage() + " ruleId:" + ruleId);
            }
            CheckRuleVo updateRule = checkRuleVoMap.get(ruleId);
            updateRuleFields(dbCheckRule, updateRule);
        }
        if (!dbCheckRules.isEmpty()) {
            checkRuleService.updateBatchById(dbCheckRules);
        }
        return dbCheckRules.size();
    }

    /**
     * 通过key查询并更新
     */
    private int updateBatchByRuleKey(List<CheckRuleVo> updateRules, String source) {
        if (CollectionUtils.isEmpty(updateRules)) {
            return 0;
        }
        Map<String, CheckRuleVo> checkRuleVoMap =
                updateRules.stream().collect(Collectors.toMap(CheckRuleVo::getKey, Function.identity(), (a, b) -> b));
        List<CheckRule> dbCheckRules = checkRuleService.listInRuleKeys(checkRuleVoMap.keySet());
        for (CheckRule dbCheckRule : dbCheckRules) {
            String ruleKey = dbCheckRule.getRuleKey();
            if (!dbCheckRule.getSource().equals(source)) {
                throw new ThemisException(NOT_AUTH_TO_UPDATE.getCode(),
                        NOT_AUTH_TO_UPDATE.getMessage() + " ruleKey:" + ruleKey);
            }
            CheckRuleVo updateRule = checkRuleVoMap.get(ruleKey);
            updateRuleFields(dbCheckRule, updateRule);
        }
        if (!dbCheckRules.isEmpty()) {
            checkRuleService.updateBatchById(dbCheckRules);
        }
        return dbCheckRules.size();
    }

    /**
     * 更新规则字段
     */
    private void updateRuleFields(CheckRule dbCheckRule, CheckRuleVo updateRule) {
        if (StringUtils.isNotEmpty(updateRule.getSeverity())) {
            dbCheckRule.setSeverity(updateRule.getSeverity());
        }
        if (StringUtils.isNotEmpty(updateRule.getType())) {
            dbCheckRule.setRuleType(updateRule.getType());
        }
        if (StringUtils.isNotEmpty(updateRule.getDescription())) {
            dbCheckRule.setDescription(updateRule.getDescription());
        }
        if (StringUtils.isNotEmpty(updateRule.getRuleLink())) {
            dbCheckRule.setRuleLink(updateRule.getRuleLink());
        }
        if (StringUtils.isNotEmpty(updateRule.getName())) {
            dbCheckRule.setName(updateRule.getName());
        }
        if (StringUtils.isNotEmpty(updateRule.getHtmlDesc())) {
            dbCheckRule.setHtmlDesc(updateRule.getHtmlDesc());
        }
        if (StringUtils.isNotEmpty(updateRule.getNameZh())) {
            dbCheckRule.setNameZh(updateRule.getNameZh());
        }
        if (StringUtils.isNotEmpty(updateRule.getHtmlDescZh())) {
            dbCheckRule.setHtmlDescZh(updateRule.getHtmlDescZh());
        }
        dbCheckRule.setGmtModified(LocalDateTime.now());
    }

    @Override
    @Transactional
    public DeleteRuleResponseVo delete(DeleteRuleRequestVo requestVo) {
        String source = ThemisTaskTokenUtil.get().getSource();
        int deleted = 0;
        List<Long> deleteRuleIds = requestVo.getDeleteRuleIds();
        // 根据id删除
        if (CollectionUtils.isNotEmpty(deleteRuleIds)) {
            deleted += logicDeleteBatch(checkRuleService.listByIds(deleteRuleIds), source);
        }
        // 根据key删除
        List<String> deleteRuleKeys = requestVo.getDeleteRuleKeys();
        if (CollectionUtils.isNotEmpty(deleteRuleKeys)) {
            deleted += logicDeleteBatch(checkRuleService.listInRuleKeys(deleteRuleKeys), source);
        }
        final String redisKey = KsRedisPrefixConstant.QUALITY_PLATFORM_DIFF_SOURCE_RULE_LIST + source;
        ksRedisClient.sync().del(redisKey);
        return new DeleteRuleResponseVo(deleted);
    }
    private int logicDeleteBatch(List<CheckRule> dbRules, String source) {
        if (CollectionUtils.isEmpty(dbRules)) {
            return 0;
        }
        for (CheckRule dbCheckRule : dbRules) {
            String dbSource = dbCheckRule.getSource();
            if (!source.equals(dbSource)) {
                throw new ThemisException(NOT_AUTH_TO_DEL.getCode(),
                        NOT_AUTH_TO_DEL.getMessage() + String.format("%s|%s", dbCheckRule.getId(), dbCheckRule.getRuleKey()));
            }
            dbCheckRule.setDeleted(true);
            dbCheckRule.setGmtModified(LocalDateTime.now());
        }
        checkRuleService.updateBatchById(dbRules);
        // 删除关联的标签
        checkRuleLabelRelationService.removeBatchByRuleKeys(dbRules.stream().map(CheckRule::getRuleKey).collect(Collectors.toList()));
        return dbRules.size();
    }

    @Override
    @Transactional
    public AddRuleResponseVo add(AddRuleRequestVo requestVo) {
        List<CheckRuleVo> addRules = requestVo.getAddRules();
        if (CollectionUtils.isEmpty(addRules)) {
            return new AddRuleResponseVo(0);
        }
        this.checkParams(addRules);
        String source = ThemisTaskTokenUtil.get().getSource();
        List<CheckRule> checkRules = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        for (CheckRuleVo addRule : addRules) {
            CheckRule checkRule = new CheckRule();
            checkRule.setRuleType(addRule.getType());
            checkRule.setRuleKey(addRule.getKey());
            checkRule.setDescription(addRule.getDescription());
            checkRule.setSeverity(addRule.getSeverity());
            checkRule.setGmtModified(now);
            checkRule.setGmtCreate(now);
            checkRule.setSource(source);
            if (StringUtils.isNotEmpty(addRule.getRuleLink())) {
                checkRule.setRuleLink(addRule.getRuleLink());
            }
            checkRule.setLanguage(addRule.getLanguage());
            checkRule.setScanner(addRule.getScanner());
            checkRule.setHtmlDesc(addRule.getHtmlDesc());
            checkRule.setName(addRule.getName());
            checkRule.setNameZh(StringUtils.defaultString(addRule.getNameZh()));
            checkRule.setHtmlDescZh(StringUtils.defaultString(addRule.getHtmlDescZh()));
            checkRule.setCanSkip(true);
            checkRules.add(checkRule);
        }
        try {
            checkRuleService.saveBatch(checkRules);
        } catch (DuplicateKeyException e) {
            throw new ThemisException(DUPLICATE_KEY_ERROR.getCode(),
                    DUPLICATE_KEY_ERROR.getMessage() + " key " + e.getMessage());
        }
        final String redisKey = KsRedisPrefixConstant.QUALITY_PLATFORM_DIFF_SOURCE_RULE_LIST + source;
        ksRedisClient.sync().del(redisKey);
        // 保存标签
        updateRuleLabels(addRules);
        return new AddRuleResponseVo(checkRules.size());
    }

    private void updateRuleLabels(List<CheckRuleVo> voList) {
        Map<String, List<String>> map = CollectionUtils.emptyIfNull(voList).stream()
                .filter(a -> CollectionUtils.isNotEmpty(a.getLabels()))
                .collect(Collectors.toMap(CheckRuleVo::getKey, CheckRuleVo::getLabels, (a, b) -> b));
        if (map.isEmpty()) {
            return;
        }
        // 先删除
        checkRuleLabelRelationService.removeBatchByRuleKeys(map.keySet());
        // 再添加
        List<CheckRuleLabelRelation> needSaveLabels = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        for (Entry<String, List<String>> entry : map.entrySet()) {
            for (String label : entry.getValue()) {
                CheckRuleLabelRelation labelRelation = new CheckRuleLabelRelation();
                labelRelation.setLabelName(label);
                labelRelation.setRuleKey(entry.getKey());
                labelRelation.setGmtCreate(now);
                labelRelation.setGmtModified(now);
                needSaveLabels.add(labelRelation);
            }
        }
        if (!needSaveLabels.isEmpty()) {
            checkRuleLabelRelationService.saveBatch(needSaveLabels);
        }
    }

    @Override
    public ListRuleResponseVo list(ListRuleRequestVo requestVo, String userName) {
        Integer page = requestVo.getPage() == null ? 1 : requestVo.getPage();
        Integer pageSize = requestVo.getPageSize() == null ? 10 : requestVo.getPageSize();
        requestVo.setScanner(StringUtils.defaultString(requestVo.getScanner()));
        // 参数校验
        CheckProfile checkProfile = checkProfile(requestVo);
        // 没有指定规则集，对全部规则分页
        if (checkProfile == null) {
            List<String> labelRuleKeys = getLabelRuleKeys(requestVo);
            CheckRuleListCondition ruleListCondition = CheckRuleListCondition.builder()
                    .ruleKeys(labelRuleKeys)
                    .keyword(requestVo.getKeyword())
                    .language(requestVo.getLanguage())
                    .scanners(List.of(requestVo.getScanner()))
                    .page(page)
                    .pageSize(pageSize)
                    .build();
            return build(requestVo, ruleListCondition, Collections.emptyMap(), userName);
        }
        // 指定了规则集
        // 自身的关联规则列表
        List<CheckProfileRuleRelation> ruleRelationList =
                checkProfileRuleRelationService.listActiveByProfileName(requestVo.getProfileName());
        // 规则key -> 等级
        Map<String, String> selfRuleSeverityMap = ruleRelationList.stream().collect(
                Collectors.toMap(CheckProfileRuleRelation::getRuleKey, CheckProfileRuleRelation::getRuleSeverity, (existing, replacement) -> existing));
        // 加入父规则集关联的规则 并 构建规则集-规则模型
        Map<String, ProfileRuleSeverityDto> profileRuleDtoMap = addParentRuleRelationsAndConvertDtoMap(checkProfile, selfRuleSeverityMap);
        // 得到待分页的规则列表（配置了的 或 未配置的）
        List<String> profileRuleKeys =
                getCandidateRuleKeys(checkProfile, selfRuleSeverityMap.keySet(), profileRuleDtoMap.keySet(), requestVo);
        if (profileRuleKeys != null && profileRuleKeys.isEmpty()) {
            return defaultListReturn(page, pageSize);
        }
        List<String> labelRuleKeys = getLabelRuleKeys(requestVo);
        if (labelRuleKeys != null && labelRuleKeys.isEmpty()) {
            return defaultListReturn(page, pageSize);
        }
        // 标签过滤
        Collection<String> ruleKeys = intersectionRuleKeys(profileRuleKeys, labelRuleKeys);
        if (CollectionUtils.isEmpty(ruleKeys)) {
            return defaultListReturn(page, pageSize);
        }
        CheckRuleListCondition ruleListCondition = CheckRuleListCondition.builder()
                .ruleKeys(ruleKeys)
                .keyword(requestVo.getKeyword())
                .language(requestVo.getLanguage())
                .scanners(List.of(requestVo.getScanner()))
                .page(page)
                .pageSize(pageSize)
                .build();
        return build(requestVo, ruleListCondition, profileRuleDtoMap, userName);
    }

    private Map<String, ProfileRuleSeverityDto> addParentRuleRelationsAndConvertDtoMap(CheckProfile checkProfile,
            Map<String, String> selfRuleSeverityMap) {
        Map<String, ProfileRuleSeverityDto> profileRuleDtoMap = new HashMap<>();
        // 先处理所有继承过来的规则
        CheckProfile currentProfile = checkProfile;
        // 逐层向上加入父规则集关联规则
        while (true) {
            if (Objects.isNull(currentProfile) || StringUtils.isBlank(currentProfile.getParentProfileName())) {
                break;
            }
            CheckProfile parentProfile = checkProfileService.getByName(currentProfile.getParentProfileName());
            if (Objects.isNull(parentProfile)) {
                break;
            }
            // 查询父规则集规则
            List<CheckProfileRuleRelation> parentRuleRelations =
                    checkProfileRuleRelationService.listActiveByProfileName(parentProfile.getProfileName());
            if (CollectionUtils.isNotEmpty(parentRuleRelations)) {
                for (CheckProfileRuleRelation parentRuleRelation : parentRuleRelations) {
                    String ruleKey = parentRuleRelation.getRuleKey();
                    // 只关注最近一层
                    if (profileRuleDtoMap.containsKey(ruleKey)) {
                        continue;
                    }
                    // 自身覆盖了的规则
                    if (selfRuleSeverityMap.containsKey(ruleKey)) {
                        profileRuleDtoMap.put(ruleKey, ProfileRuleSeverityDto.builder()
                                .ruleKey(ruleKey).severity(selfRuleSeverityMap.get(ruleKey))
                                .inherited(true).overwritten(true).parentProfile(parentProfile)
                                .parentSeverity(parentRuleRelation.getRuleSeverity()).build());
                    } else {
                        // 自身未覆盖的规则
                        profileRuleDtoMap.put(ruleKey, ProfileRuleSeverityDto.builder()
                                .ruleKey(ruleKey).severity(parentRuleRelation.getRuleSeverity())
                                .inherited(true).overwritten(false).parentProfile(parentProfile)
                                .parentSeverity(parentRuleRelation.getRuleSeverity()).build());
                    }
                }
            }
            currentProfile = parentProfile;
        }
        // 再补充规则集自身独有的规则。
        for (Entry<String, String> entry : selfRuleSeverityMap.entrySet()) {
            String ruleKey = entry.getKey();
            if (!profileRuleDtoMap.containsKey(ruleKey)) {
                // 自身独有
                profileRuleDtoMap.put(ruleKey, ProfileRuleSeverityDto.builder()
                        .ruleKey(ruleKey).severity(entry.getValue())
                        .inherited(false).overwritten(false)
                        .parentProfile(null).parentSeverity(StringUtils.EMPTY).build());
            }
        }
        return profileRuleDtoMap;
    }

    private ListRuleResponseVo build(ListRuleRequestVo requestVo, CheckRuleListCondition ruleListCondition,
            Map<String, ProfileRuleSeverityDto> profileRuleDtoMap, String userName) {
        List<CheckRule> checkRuleList = checkRuleService.listByCondition(ruleListCondition);

        // 选择已配置的规则时，severity需要改为check_profile_rule_relation中的优先级
        setSelectedRuleSeverity(requestVo.getSelected(), checkRuleList, profileRuleDtoMap);

        // 得到 type 分类 和 severity 分类结果
        Pair<Map<String, Long>, Map<String, Long>> pair = getGroupCountMapOfSeverityAndType(requestVo, checkRuleList);
        Map<String, Long> typeCountMap = pair.getLeft();
        Map<String, Long> severityCountMap = pair.getRight();
        // 内存过滤 type 和 severity
        checkRuleList = filterByTypeAndSeverity(requestVo, checkRuleList);

        int page = ruleListCondition.getPage(), pageSize = ruleListCondition.getPageSize();
        if (CollectionUtils.isEmpty(checkRuleList)) {
            return defaultListReturn(page, pageSize);
        }
        List<String> selectRuleKeys = checkRuleList.stream()
                .map(CheckRule::getRuleKey)
                .collect(Collectors.toList());
        // 内存分页
        int start = (page - 1) * pageSize;
        int end = page * pageSize;
        List<CheckRule> pageList = CollectionUtil.sub(checkRuleList, start, end);
        if (CollectionUtils.isEmpty(pageList)) {
            return defaultListReturn(page, pageSize);
        }
        List<LabelDetailVo> labelDetailList = getLabelDetailList(selectRuleKeys);
        String profileName = requestVo.getProfileName();
        return ListRuleResponseVo.builder()
                .p(page)
                .ps(pageSize)
                .total(checkRuleList.size())
                .rules(convert2Rules(pageList, profileRuleDtoMap, profileName, userName))
                .facets(convert2Facets(typeCountMap, severityCountMap, labelDetailList))
                .build();
    }

    private Pair<Map<String, Long>, Map<String, Long>> getGroupCountMapOfSeverityAndType(
            ListRuleRequestVo requestVo, List<CheckRule> checkRuleList) {
        Map<String, Long> typeCountMap;
        Map<String, Long> severityCountMap;
        // 本次点击的是 严重等级 筛选
        if (requestVo.isClickSeverity()) {
            // 得到 severity 聚合结果
            // 不存在类型筛选
            if (StringUtils.isBlank(requestVo.getTypes())) {
                severityCountMap = groupBySeverityCount(checkRuleList);
            } else {
                // 上次是类型筛选
                severityCountMap = groupBySeverityCount(checkRuleList.stream()
                        .filter(cr -> cr.getRuleType().equals(requestVo.getTypes())).collect(Collectors.toList()));
            }
            // 得到 type 聚合结果
            // 进行严重等级筛选
            if (StringUtils.isNotBlank(requestVo.getSeverities())) {
                typeCountMap = groupByTypeCount(checkRuleList.stream().filter(
                        cr -> cr.getSeverity().equals(requestVo.getSeverities())).collect(Collectors.toList()));
            } else {
                typeCountMap = groupByTypeCount(checkRuleList);
            }
        } else {
            // 本次点击的是 类型 筛选
            // 得到 type 聚合结果
            // 不存在 severity 筛选条件
            if (StringUtils.isBlank(requestVo.getSeverities())) {
                typeCountMap = groupByTypeCount(checkRuleList);
            } else {
                // 上次是 severity 筛选
                typeCountMap = groupByTypeCount(checkRuleList.stream()
                        .filter(cr -> cr.getSeverity().equals(requestVo.getSeverities())).collect(Collectors.toList()));
            }
            // 得到 severity 聚合结果
            // 进行 type 筛选
            if (StringUtils.isNotBlank(requestVo.getTypes())) {
                severityCountMap = groupBySeverityCount(checkRuleList.stream().filter(
                        cr -> cr.getRuleType().equals(requestVo.getTypes())).collect(Collectors.toList()));
            } else {
                severityCountMap = groupBySeverityCount(checkRuleList);
            }
        }
        return Pair.of(typeCountMap, severityCountMap);
    }

    public List<CheckRule> filterByTypeAndSeverity(ListRuleRequestVo requestVo, List<CheckRule> checkRuleList) {
        if (StringUtils.isNotBlank(requestVo.getSeverities())) {
            checkRuleList = checkRuleList.stream()
                    .filter(cr -> cr.getSeverity().equals(requestVo.getSeverities())).collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(requestVo.getTypes())) {
            checkRuleList = checkRuleList.stream()
                    .filter(cr -> cr.getRuleType().equals(requestVo.getTypes())).collect(Collectors.toList());
        }
        return checkRuleList;
    }

    private List<CheckProfileRuleRelation> screenSeverities(List<CheckProfileRuleRelation> ruleRelationList,
            String severities) {
        if (StringUtils.isEmpty(severities)) {
            return ruleRelationList;
        }
        List<CheckProfileRuleRelation> noRuleSeverityRelations = ruleRelationList.stream()
                .filter(o -> StringUtils.isEmpty(o.getRuleSeverity()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noRuleSeverityRelations)) {
            List<CheckRule> checkRules = checkRuleService.listInRuleKeys(noRuleSeverityRelations.stream()
                    .map(CheckProfileRuleRelation::getRuleKey)
                    .collect(Collectors.toList()));
            Map<String, CheckRule> ruleKeyCheckRuleMap = checkRules.stream()
                    .collect(Collectors.toMap(CheckRule::getRuleKey, Function.identity(), (a, b) -> a));
            for (CheckProfileRuleRelation checkProfileRuleRelation : ruleRelationList) {
                if (StringUtils.isEmpty(checkProfileRuleRelation.getRuleSeverity())) {
                    CheckRule checkRule = ruleKeyCheckRuleMap.get(checkProfileRuleRelation.getRuleKey());
                    if (checkRule != null) {
                        checkProfileRuleRelation.setRuleSeverity(checkRule.getSeverity());
                    }
                }
            }
        }
        return ruleRelationList;
    }

    private void setSelectedRuleSeverity(Boolean selected, List<CheckRule> checkRuleList,
            Map<String, ProfileRuleSeverityDto> profileRuleDtoMap) {
        // 当profile存在规则关联关系，需要使用relation中的严重级别
        if (selected && CollUtil.isNotEmpty(profileRuleDtoMap)) {
            for (CheckRule checkRule : checkRuleList) {
                ProfileRuleSeverityDto severityDto = profileRuleDtoMap.get(checkRule.getRuleKey());
                checkRule.setSeverity(
                        Objects.nonNull(severityDto) ? severityDto.getSeverity() : checkRule.getSeverity());
            }
        }
    }

    private Map<String, Long> groupBySeverityCount(List<CheckRule> checkRuleList) {
        Map<String, Long> serverityMap =
                checkRuleList.stream().collect(Collectors.groupingBy(CheckRule::getSeverity, Collectors.counting()));
        for (String severity : CheckIssueSeverity.querySeverityList()) {
            serverityMap.putIfAbsent(severity, 0L);
        }
        return serverityMap;
    }

    private Map<String, Long> groupByTypeCount(List<CheckRule> checkRuleList) {
        Map<String, Long> typeMap =
                checkRuleList.stream().collect(Collectors.groupingBy(CheckRule::getRuleType, Collectors.counting()));
        for (String type : CheckIssueType.queryTypeList()) {
            typeMap.putIfAbsent(type, 0L);
        }
        return typeMap;
    }

    private List<LabelDetailVo> getLabelDetailList(Collection<String> ruleKeys) {
        List<CheckRuleLabelRelation> labelRelationList = checkRuleLabelRelationService.listInRuleKeys(ruleKeys);
        Map<String, Long> labelCountMap = labelRelationList.stream()
                .collect(Collectors.groupingBy(CheckRuleLabelRelation::getLabelName, Collectors.counting()));
        List<LabelDetailVo> labelList = Lists.newArrayList();
        for (Entry<String, Long> entry : labelCountMap.entrySet()) {
            LabelDetailVo labelDetailVo = new LabelDetailVo();
            labelDetailVo.setName(entry.getKey());
            labelDetailVo.setCount(entry.getValue());
            labelList.add(labelDetailVo);
        }
        return labelList;
    }

    private List<String> getLabelRuleKeys(ListRuleRequestVo requestVo) {
        List<String> labelList = requestVo.getLabelList();
        if (CollectionUtils.isEmpty(labelList)) {
            return null;
        }
        List<CheckRuleLabelRelation> labelRelationList = checkRuleLabelRelationService.listInLabelLists(labelList);
        if (CollectionUtils.isEmpty(labelRelationList)) {
            return Collections.emptyList();
        }
        // 这里需要同时满足
        Map<String, List<CheckRuleLabelRelation>> labelRelationListMap = labelRelationList.stream()
                .collect(Collectors.groupingBy(CheckRuleLabelRelation::getRuleKey));
        return Lists.newArrayList(labelRelationListMap.keySet());
    }

    private List<String> getCandidateRuleKeys(CheckProfile checkProfile, Collection<String> selfRuleKeys,
            Collection<String> allConfiguredRuleKeys, ListRuleRequestVo requestVo) {

        boolean selected = requestVo.getSelected(), onlySelf = requestVo.getOnlySelf();
        // 在已配置的规则里面分页
        if (selected) {
            // 是否只看自身配置的规则key
            return onlySelf ? Lists.newArrayList(selfRuleKeys) : Lists.newArrayList(allConfiguredRuleKeys);
        }
        // 在未配置的规则里面分页
        // 所有规则 - 已配置的规则
        CheckRuleListCondition listCondition = CheckRuleListCondition.builder()
                .scanners(List.of(requestVo.getScanner())) // 由请求参数决定，而不是由规则集绑定的扫描器决定
                .language(checkProfile.getLanguage())
                .build();
        List<CheckRule> checkRules = checkRuleService.listByCondition(listCondition);
        List<String> scannerRuleKeys = checkRules.stream()
                .map(CheckRule::getRuleKey)
                .collect(Collectors.toList());
        return CollectionUtil.subtractToList(scannerRuleKeys, allConfiguredRuleKeys);
    }

    /**
     * 基本信息校验，并获取checkProfile
     */
    private CheckProfile checkProfile(ListRuleRequestVo requestVo) {
        String profileName = requestVo.getProfileName();
        if (StringUtils.isEmpty(profileName)) {
            return null;
        }
        Boolean selected = requestVo.getSelected();
        if (selected == null) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "selected");
        }
        if (requestVo.getOnlySelf() == null) {
            requestVo.setOnlySelf(false);
        }
        CheckProfile checkProfile = checkProfileService.getByName(profileName);
        if (checkProfile == null) {
            throw new ThemisException(ResultCodeConstant.PROFILE_NOT_EXIST);
        }
        return checkProfile;
    }

    private Collection<String> intersectionRuleKeys(List<String> profileRuleKeys, List<String> labelRuleKeys) {
        if (profileRuleKeys == null && labelRuleKeys == null) {
            return Collections.emptyList();
        }
        if (profileRuleKeys == null) {
            return labelRuleKeys;
        }
        if (labelRuleKeys == null) {
            return profileRuleKeys;
        }
        return CollectionUtil.intersection(profileRuleKeys, labelRuleKeys);
    }

    private List<Facet> convert2Facets(Map<String, Long> typeKeyMap, Map<String, Long> severityKeyMap,
            List<LabelDetailVo> labelDetailList) {
        List<Facet> facets = Lists.newArrayList();
        Facet typeFacet = convert2Facet("types", typeKeyMap);
        facets.add(typeFacet);
        Facet severityFacet = convert2Facet("severities", severityKeyMap);
        facets.add(severityFacet);
        Facet facet = new Facet();
        facet.setProperty("labels");
        List<FacetValue> facetValues = labelDetailList.stream().map(o -> FacetValue.builder()
                        .val(o.getName())
                        .count(o.getCount().intValue())
                        .build())
                .collect(Collectors.toList());
        facet.setValues(facetValues);
        facets.add(facet);
        return facets;
    }

    private Facet convert2Facet(String property, Map<String, Long> typeKeyMap) {
        Facet facet = new Facet();
        facet.setProperty(property);
        FacetEnum facetEnum = FacetEnum.getByCode(property);
        facet.setPropertyDesc(facetEnum.getDesc());
        FacetNode facetNode = facetEnum.getFacetNodeList()[0];
        List<FacetValue> facetValues = Lists.newArrayList();
        for (Entry<String, Long> entry : typeKeyMap.entrySet()) {
            FacetValue facetValue = new FacetValue();
            String key = entry.getKey();
            facetValue.setVal(key);
            facetValue.setDesc(facetNode.valueOfName(key).getDesc());
            facetValue.setCount(entry.getValue().intValue());
            facetValues.add(facetValue);
        }
        facet.setValues(facetValues);
        return facet;
    }

    private ListRuleResponseVo defaultListReturn(Integer page, Integer pageSize) {
        return ListRuleResponseVo.builder()
                .p(page)
                .ps(pageSize)
                .rules(Collections.emptyList())
                .total(0)
                .facets(Collections.emptyList())
                .build();
    }

    private List<RuleInfoResponseVo> convert2Rules(List<CheckRule> checkRules,
            Map<String, ProfileRuleSeverityDto> profileRuleDtoMap, String profileName, String userName) {
        List<String> ruleKeys = checkRules.stream()
                .map(CheckRule::getRuleKey)
                .collect(Collectors.toList());
        List<CheckRuleLabelRelation> labelRelationList = checkRuleLabelRelationService.listInRuleKeys(ruleKeys);

        Map<String, List<CheckRuleLabelRelation>> ruleKeysRelationListMap = labelRelationList.stream()
                .collect(Collectors.groupingBy(CheckRuleLabelRelation::getRuleKey));

        CheckProfile checkProfile = checkProfileService.getByName(profileName);

        boolean canOperation = canOperation(checkProfile, userName);

        List<RuleInfoResponseVo> ruleInfoResponseVos = Lists.newArrayList();
        for (CheckRule checkRule : checkRules) {
            String ruleKey = checkRule.getRuleKey();
            List<String> labelNames = Lists.newArrayList();
            List<CheckRuleLabelRelation> checkRuleLabelRelations = ruleKeysRelationListMap.get(ruleKey);
            if (CollectionUtils.isNotEmpty(checkRuleLabelRelations)) {
                labelNames = checkRuleLabelRelations.stream()
                        .map(CheckRuleLabelRelation::getLabelName)
                        .collect(Collectors.toList());
            }
            RuleInfoResponseVo infoResponseVo = new RuleInfoResponseVo();
            infoResponseVo.setName(checkRule.getName());
            infoResponseVo.setHtmlDesc(checkRule.getHtmlDesc());
            infoResponseVo.setKey(ruleKey);
            infoResponseVo.setSeverity(checkRule.getSeverity());
            infoResponseVo.setType(checkRule.getRuleType());
            infoResponseVo.setLang(checkRule.getLanguage());
            infoResponseVo.setLangNames(checkRule.getLanguage());
            infoResponseVo.setSysTags(labelNames);
            infoResponseVo.setScanner(checkRule.getScanner());
            infoResponseVo.setDescription(checkRule.getDescription());
            // 只有在规则集筛选时才有值
            if (MapUtils.isNotEmpty(profileRuleDtoMap)) {
                ProfileRuleSeverityDto severityDto = profileRuleDtoMap.get(ruleKey);
                infoResponseVo.setSelected(severityDto != null);
                infoResponseVo.setFromParent(severityDto != null && severityDto.isInherited());
                if (infoResponseVo.isFromParent()) {
                    infoResponseVo.setParentProfileName(severityDto.getParentProfile().getProfileName());
                    infoResponseVo.setParentProfileDisplayName(severityDto.getParentProfile().getDisplayName());
                    infoResponseVo.setOverwritten(severityDto.isOverwritten());
                }
            }
            infoResponseVo.setAllowSeverityList(queryServerList(checkProfile, profileRuleDtoMap.get(ruleKey)));
            infoResponseVo.setCanOperation(canOperation);
            // 这里特殊处理一下
            if ("IRA_INEFFICIENT_REPLACEALL".equals(infoResponseVo.getKey())) {
                infoResponseVo.setLabels(Lists.newArrayList("在jdk17中不生效"));
            }
            ruleInfoResponseVos.add(infoResponseVo);
        }
        return ruleInfoResponseVos;
    }

    private List<String> queryServerList(CheckProfile checkProfile, ProfileRuleSeverityDto profileRuleSeverityDto) {
        if (checkProfile == null) {
            return null;
        }
        // 判断scanner是否支持编辑规则严重级别
        if (kconfUtil.canEditRuleSeverityScannerList(checkProfile.getScanner())) {
            List<String> severityList = CheckIssueSeverity.querySeverityList();
            // 非继承
            if (profileRuleSeverityDto == null || !profileRuleSeverityDto.isInherited()) {
                return severityList;
            }
            // 继承过来的，只能提高等级
            List<String> aboveSeverities = CheckIssueSeverity.getByAboveLevel(profileRuleSeverityDto.getParentSeverity());
            // 如果可更改的等级列表里面不包括当前等级，那就加进去，否则前端下拉框回显当前值会无法展示。
            if (!aboveSeverities.contains(profileRuleSeverityDto.getSeverity())) {
                aboveSeverities.add(profileRuleSeverityDto.getSeverity());
            }
            return aboveSeverities;
        }

        return null;
    }

    private boolean canOperation(CheckProfile checkProfile, String userName) {
        if (checkProfile == null) {
            return false;
        }
        return platformPermissionService.hasProfileEditPermission(userName, checkProfile);
    }

    @Override
    public RuleDetailResponseVo detail(RuleDetailRequestVo requestVo) {
        String ruleKey = requestVo.getRuleKey();
        if (StringUtils.isEmpty(ruleKey)) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "ruleKeys不能为空");
        }
        CheckRule checkRule = checkRuleService.getByRuleKey(ruleKey);
        if (checkRule == null) {
            throw new ThemisException(-1, "规则不存在");
        }
        List<CheckProfileRuleRelation> ruleRelationList = checkProfileRuleRelationService.listByRuleKey(ruleKey);
        List<String> profileNames = ruleRelationList.stream()
                .map(CheckProfileRuleRelation::getProfileName)
                .collect(Collectors.toList());
        List<CheckProfile> checkProfiles = checkProfileService.listByProfileNames(profileNames);
        Map<String, CheckProfile> profileNameMap =
                checkProfiles.stream().collect(Collectors.toMap(CheckProfile::getProfileName, o -> o, (a, b) -> a));
        List<ProfileInfo> profileInfos = ruleRelationList.stream()
                .map(o -> ProfileInfo.builder()
                        .profileName(o.getProfileName())
                        .displayName(profileNameMap.get(o.getProfileName()).getDisplayName())
                        .severity(StringUtils.isEmpty(o.getRuleSeverity()) ? checkRule.getSeverity()
                                                                           : o.getRuleSeverity())
                        .build())
                .collect(Collectors.toList());
        List<String> ruleKeys = Lists.newArrayList(ruleKey);
        List<CheckRuleLabelRelation> labelRelationList = checkRuleLabelRelationService.listInRuleKeys(ruleKeys);
        List<String> labelNames = labelRelationList.stream()
                .map(CheckRuleLabelRelation::getLabelName)
                .collect(Collectors.toList());
        return RuleDetailResponseVo.builder()
                .htmlDesc(checkRule.getHtmlDesc())
                .labelList(labelNames)
                .severity(checkRule.getSeverity())
                .type(checkRule.getRuleType())
                .profileInfoList(profileInfos)
                .title(checkRule.getName())
                .build();
    }

    private void checkParams(List<CheckRuleVo> addRules) {
        for (CheckRuleVo addRule : addRules) {
            String key = addRule.getKey();
            if (StringUtils.isEmpty(key)) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "key");
            }
            String description = addRule.getDescription();
            if (StringUtils.isEmpty(description)) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "description");
            }
            String severity = addRule.getSeverity();
            if (!CheckIssueSeverity.correctType(severity)) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "severity");
            }
            String type = addRule.getType();
            if (!CheckIssueType.correctType(type)) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "type");
            }
            String language = addRule.getLanguage();
            PlatformLanguageEnum languageEnum = PlatformLanguageEnum.getEnumByName(language);
            if (languageEnum == null) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "language");
            }
            String scanner = addRule.getScanner();
            PlatformScannerEnum enumByScanner = PlatformScannerEnum.getEnumByScanner(scanner);
            if (enumByScanner == null) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "scanner");
            }
            if (StringUtils.isBlank(addRule.getName())) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "name");
            }
            if (StringUtils.isBlank(addRule.getHtmlDesc())) {
                throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "htmlDesc");
            }
        }
    }

    private List<CheckRuleVo> convert2Vo(List<CheckRule> allRules) {
        List<CheckRuleVo> checkRuleVos = Lists.newArrayList();
        for (CheckRule checkRule : allRules) {
            CheckRuleVo checkRuleVo = new CheckRuleVo();
            checkRuleVo.setRuleId(checkRule.getId());
            checkRuleVo.setKey(checkRule.getRuleKey());
            checkRuleVo.setDescription(checkRule.getDescription());
            checkRuleVo.setSeverity(checkRule.getSeverity());
            checkRuleVo.setType(checkRule.getRuleType());
            checkRuleVos.add(checkRuleVo);
        }
        return checkRuleVos;
    }

    /**
     * 同步底层sonar规则集中规则的severity到relation表中
     */
    @Override
    public void sysSonarSeverity() {
        // 1. 查relation表的所有profile_name
        List<String> relationAllProfiles = checkProfileRuleRelationService.listAllDistinctProfile();

        // 2. 根据profile_name查实体中对应的language
        List<CheckProfile> checkProfiles = checkProfileService.listByProfileNames(relationAllProfiles);

        for (CheckProfile checkProfile : checkProfiles) {
            SonarCommonApi sonarCommonApi = node1Operations.sonarApi();
            // 3. 根据profile_name和language查sonar中的profile_key
            String profileName = getQualityProfile(checkProfile, sonarCommonApi);
            Map<String, List<SearchRulesResponse.ActiveRuleInfo>> activeRules = Maps.newHashMap();

            if (StringUtils.isNotEmpty(profileName)) {
                // 4. 根据profile_key查sonar中的rule list
                SearchRuleRequest searchRuleRequest = new SearchRuleRequest();
                searchRuleRequest.setQprofile(profileName);
                searchRuleRequest.setP(1);
                searchRuleRequest.setPs(SEARCH_RULE_PAGE_SIZE);
                searchRuleRequest.setLanguages(checkProfile.getLanguage());
                SearchRulesResponse searchRulesResponse = sonarCommonApi.searchRules(searchRuleRequest);
                activeRules = searchRulesResponse.getActives();
            }

            // 5. 将sonar的严重程度赋值给relation，如果sonar未查到则使用check_rule默认的严重程度
            List<CheckProfileRuleRelation> relations =
                    checkProfileRuleRelationService.listActiveByProfileName(checkProfile.getProfileName());
            List<CheckProfileRuleRelation> updateRelations = convert2RuleRelations(profileName, activeRules, relations);
            if (CollUtil.isEmpty(updateRelations)) {
                log.info("profile name = [{}] not found update relation!", checkProfile.getProfileName());
                continue;
            }

            // 6. 根据rule list批量更新relation中的severity
            checkProfileRuleRelationService.updateBatchById(updateRelations);
        }
    }

    @Override
    public List<CheckRuleVo> projectPipelineRules(Integer gitProjectId, String language, String scanner) {
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        if (Objects.isNull(checkRepo)) {
            // 创建默认项目
            checkRepo = platformRepoService.createProjectDefault(gitProjectId, language);
        }
        List<CheckRepoBranchProfile> checkRepoBranchProfiles =
                checkRepoBranchProfileService.listByCheckRepoId(checkRepo.getId());
        List<CheckRepoBranchProfile> branchProfiles = checkRepoBranchProfiles.stream()
                .filter(o -> CheckProfileType.PIPELINE.getType().equals(o.getProfileType()))
                .collect(Collectors.toList());
        List<Long> branchIds = branchProfiles.stream()
                .map(CheckRepoBranchProfile::getCheckRepoBranchId)
                .collect(Collectors.toList());
        List<CheckRepoBranch> checkRepoBranches = checkRepoBranchService.listByIds(branchIds);
        Long hitCheckBranchId = checkRepoBranches.stream()
                .filter(o -> CheckRepoBranchVersion.NEW_VERSION.getCode() == o.getVersion())
                .findFirst()
                .orElseThrow(() -> new ThemisException(BRANCH_NOT_INIT_RULESET))
                .getId();
        String profileName = branchProfiles.stream()
                .filter(o -> hitCheckBranchId.equals(o.getCheckRepoBranchId()))
                .findFirst()
                .orElseThrow(() -> new ThemisException(RULE_SET_INFO_ERROR))
                .getProfileName();
        CheckProfile checkProfile = checkProfileService.getNonnullByName(profileName);
        // CHECKSTYLE:OFF
        ListRuleRequestVo listRuleRequestVo = ListRuleRequestVo.builder()
                .onlySelf(false)
                .page(1)
                .pageSize(1_000)
                .selected(true)
                .profileName(profileName)
                .language(StringUtils.defaultString(language, checkProfile.getLanguage()))
                .scanner(StringUtils.defaultString(scanner, checkProfile.getScanner()))
                .build();
        ListRuleResponseVo responseVo = this.list(listRuleRequestVo, "lixiaoxin");
        return responseVo.getRules()
                .stream()
                .map(o -> CheckRuleVo.builder()
                        .key(o.getKey())
                        .severity(o.getSeverity())
                        .type(o.getType())
                        .htmlDesc(o.getHtmlDesc())
                        .name(o.getName())
                        .build())
                .collect(Collectors.toList());
        // CHECKSTYLE:ON
    }

    @Override
    public List<CheckRuleVo> projectRules(Integer gitProjectId, Integer profileType) {
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        if (checkRepo == null) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "项目不存在");
        }
        // 查询配置的长期分支
        CheckRepoBranch branch = checkRepoBranchService.getOfflineScanBranchByRepoId(checkRepo.getId());
        return projectRules(branch, profileType);
    }

    @Override
    public List<CheckRuleVo> projectRules(Integer gitProjectId, String branch, Integer profileType) {
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        if (checkRepo == null) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "项目不存在");
        }
        CheckRepoBranch repoBranch = checkRepoBranchService.getByRepoIdBranch(checkRepo.getId(), branch);
        if (repoBranch == null) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "分支不存在");
        }
        return projectRules(repoBranch, profileType);
    }

    private List<CheckRuleVo> projectRules(CheckRepoBranch repoBranch, Integer profileType) {
        CheckRepoBranchProfile branchProfile =
                checkRepoBranchProfileService.getByBranchIdAndProfileType(repoBranch.getId(), profileType);
        if (branchProfile == null) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "分支未关联规则集");
        }
        // 查询规则集中规则
        ListRuleRequestVo listRuleRequestVo = ListRuleRequestVo.builder()
                .onlySelf(false)
                .page(1)
                .pageSize(1 << 20)
                .selected(true)
                .profileName(branchProfile.getProfileName())
                .build();
        ListRuleResponseVo responseVo = this.list(listRuleRequestVo, "system");
        return responseVo.getRules()
                .stream()
                .map(o -> CheckRuleVo.builder()
                        .key(o.getKey())
                        .severity(o.getSeverity())
                        .type(o.getType())
                        .build())
                .collect(Collectors.toList());
    }

    private String getQualityProfile(CheckProfile checkProfile, SonarCommonApi sonarCommonApi) {
        String finalProfileName = platformSonarInfoUtils.getFinalProfileName(checkProfile.getProfileName());
        QualityProfileReq profileReq = QualityProfileReq.builder()
                .qualityProfile(finalProfileName)
                .language(checkProfile.getLanguage())
                .build();
        QualityProfileResp qualityProfile = sonarCommonApi.getQualityProfile(profileReq);
        List<Profiles> profilesList = qualityProfile.getProfiles();
        if (CollUtil.isEmpty(profilesList)) {
            return null;
        }

        return profilesList.get(0).getKey();
    }

    private List<CheckProfileRuleRelation> convert2RuleRelations(String profileName,
            Map<String, List<SearchRulesResponse.ActiveRuleInfo>> activeRules,
            List<CheckProfileRuleRelation> relations) {
        // sonar active不为空，则设置active的severity
        if (CollUtil.isNotEmpty(activeRules)) {
            Map<String, CheckProfileRuleRelation> ruleRelationMap = relations.stream()
                    .collect(Collectors.toMap(CheckProfileRuleRelation::getRuleKey, Function.identity(),
                            (v1, v2) -> v1));
            for (Entry<String, CheckProfileRuleRelation> entry : ruleRelationMap.entrySet()) {
                String ruleKey = entry.getKey();
                if (activeRules.containsKey(ruleKey) && CollUtil.isNotEmpty(activeRules.get(ruleKey))) {
                    String sonarSeverity = activeRules.get(ruleKey).get(0).getSeverity();
                    String severity = CheckIssueSeverity.getKeyBySonarStatus(sonarSeverity);
                    entry.getValue().setRuleSeverity(severity);
                }
            }
        }

        // 获取severity属性为空和不为空的集合
        List<CheckProfileRuleRelation> emptySeverityRelation =
                relations.stream().filter(relation -> StringUtils.isEmpty(relation.getRuleSeverity()))
                        .collect(Collectors.toList());
        List<String> emptySeverityRuleKey =
                emptySeverityRelation.stream().map(CheckProfileRuleRelation::getRuleKey).collect(Collectors.toList());
        Collection<CheckProfileRuleRelation> hasSeverityRelation = CollUtil.subtract(relations, emptySeverityRelation);

        // 把severity属性为空的集合设置check_rule的默认severity属性
        List<CheckRule> checkRules = checkRuleService.listInRuleKeys(emptySeverityRuleKey);
        Map<String, CheckRule> ruleKeyMap =
                checkRules.stream().collect(Collectors.toMap(CheckRule::getRuleKey, Function.identity(), (existing, replacement) -> existing));
        for (CheckProfileRuleRelation relation : emptySeverityRelation) {
            if (ruleKeyMap.containsKey(relation.getRuleKey())) {
                relation.setRuleSeverity(ruleKeyMap.get(relation.getRuleKey()).getSeverity());
            }
        }

        return new ArrayList<>(CollUtil.union(emptySeverityRelation, hasSeverityRelation));
    }
}
