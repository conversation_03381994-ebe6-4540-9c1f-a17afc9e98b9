package com.kuaishou.serveree.themis.component.service.platform.scan.scanners;

import static com.kuaishou.serveree.themis.component.constant.platform.KconfConstants.SCAN_PLATFORM_PARALLEL_COUNT_MAP_KCONF;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.gitlab.api.models.GitlabCommit;

import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckTriggerRecord;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckPriorityEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckSourceConstants;
import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.constant.quality.TaskType;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.entity.platform.ExecuteScanContext;
import com.kuaishou.serveree.themis.component.entity.scanner.ScannerSendKimContext;
import com.kuaishou.serveree.themis.component.utils.PlatformSonarInfoUtils;
import com.kuaishou.serveree.themis.component.vo.request.ChangeParentProfileRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProfileAddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCopyRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCreateRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSettingsRequest;

/**
 * <AUTHOR>
 * @since 2022/4/19 11:37 上午
 */
public interface PlatformScanner {

    Integer DEFAULT_OFFLINE_SCAN_LIMIT = 15;

    void scan(ExecuteScanContext context);

    PlatformScannerEnum platformScanner();

    void afterProjectCreate(RepoCreateRequest searchRequest);

    void afterProfileCreate(ProfileCreateRequestVo requestVo);

    void afterProfileAddRule(ProfileAddRuleRequestVo requestVo);

    void afterProfileDeleteRule(ProfileDeleteRuleRequestVo requestVo);

    void afterProjectUpdateProfile(RepoProfileUpdateRequest updateRequest);

    boolean judgeNeedAllScan(RepoSettingsRequest request);

    void afterProfileDelete(ProfileDeleteRequestVo requestVo);

    default void afterProjectDeleted(RepoCreateRequest searchRequest) {

    }

    default String getLatestCommitId(GitOperations gitOperations, Integer gitProjectId, String branch) {
        List<GitlabCommit> lastCommits = gitOperations.getLastCommitsByGitProjectId(gitProjectId, branch);
        if (CollectionUtils.isEmpty(lastCommits)) {
            return "";
        }
        return lastCommits.get(0).getId();
    }

    default CheckExecution fillUpCheckExecution(CheckBase checkBase, LocalDateTime now, long triggerRecordId,
            Task task, int referType) {
        return CheckExecution.builder()
                .baseId(checkBase.getId())
                .triggerRecordId(triggerRecordId)
                .taskId(task.getId())
                .referType(referType)
                .gmtCreate(now)
                .gmtModified(now)
                .build();
    }

    default CheckBase fillUpCheckBase(CheckRepo checkRepo, CheckRepoBranch repoBranch, LocalDateTime now,
            GitOperations gitOperations) {
        return CheckBase.builder()
                .checkRepoBranchId(repoBranch.getId())
                .checkRepoId(checkRepo.getId())
                .repoUrl(checkRepo.getRepoUrl())
                .branch(repoBranch.getBranchName())
                .gmtCreate(now)
                .gmtModified(now)
                .commitId(getLatestCommitId(gitOperations, checkRepo.getGitProjectId(), repoBranch.getBranchName()))
                .build();
    }

    default Task fillUpTask(CheckRepo checkRepo, CheckRepoBranch checkRepoBranch, LocalDateTime now) {
        return Task.builder()
                .repoUrl(checkRepo.getRepoUrl())
                .branch(checkRepoBranch.getBranchName())
                .priority(CheckPriorityEnum.LOW.name())
                .source(CheckSourceConstants.PLATFORM_OFFLINE_CHECK)
                .taskStatus(TaskStatusEnum.WAITING.name())
                .checker("system")
                .projectId(checkRepo.getGitProjectId())
                .remark(TaskStatusEnum.WAITING.getDescription())
                .createdTime(now)
                .updatedTime(now)
                .syncProductLabel(true)
                .taskType(TaskType.PLATFORM_OFFLINE.getType())
                .build();
    }

    default String getPipelineProjectKey(Integer gitProjectId, PlatformSonarInfoUtils sonarInfoUtils) {
        return sonarInfoUtils.getPipelineFinalProjectKey(gitProjectId);
    }

    default String getPipelineProjectName(String repoName, PlatformSonarInfoUtils sonarInfoUtils) {
        return sonarInfoUtils.getPipelineProjectName(repoName);
    }

    default String getFinalProjectKey(Integer gitProjectId, PlatformSonarInfoUtils sonarInfoUtils) {
        return sonarInfoUtils.getFinalProjectKey(gitProjectId);
    }

    default String getFinalProjectName(String repoName, PlatformSonarInfoUtils sonarInfoUtils) {
        return sonarInfoUtils.getFinalProjectName(repoName);
    }

    default String getFinalProfileName(String profileName, PlatformSonarInfoUtils sonarInfoUtils) {
        return sonarInfoUtils.getFinalProfileName(profileName);
    }

    void afterIssueTransition(IssueSummary issueSummary, String transition);

    void sendPipelineKimNotice(ScannerSendKimContext scannerSendKimContext);

    default void afterProfileCopy(ProfileCopyRequestVo requestVo) {

    }

    /**
     * 离线扫描，扫描器维度的并发控制，获取当前值的 redis key
     */
    default String getOfflineScanTicketRedisKey(CheckTriggerRecord checkTriggerRecord) {
        return KsRedisPrefixConstant.PLATFORM_SCAN_EXECUTING_COUNT + ":" + checkTriggerRecord.getScanner();
    }

    /**
     * 离线扫描，扫描器维度的并发控制，上限值
     */
    default Integer getOfflineScanLimitCount(CheckTriggerRecord checkTriggerRecord) {
        Integer scanCount = SCAN_PLATFORM_PARALLEL_COUNT_MAP_KCONF.get().get(checkTriggerRecord.getScanner());
        if (scanCount == null) {
            return DEFAULT_OFFLINE_SCAN_LIMIT;
        }
        return scanCount;
    }

    default void afterChangeParentProfile(ChangeParentProfileRequest requestVo) {

    }
}
