package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.kuaishou.serveree.themis.component.common.entity.ThemisAnalyzeIssue;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-17
 */
@Mapper
@Repository
public interface ThemisAnalyzeIssueMapper extends BaseMapper<ThemisAnalyzeIssue> {

    @Select("SELECT COUNT(0) FROM (SELECT illegal_file FROM themis_analyze_issue WHERE (analyze_id = #{analyzeId} AND"
            + " rule_type = #{ruleType}) GROUP BY illegal_file limit 10000) TOTAL")
    Integer issueGroupByCount(@Param("analyzeId") Long analyzeId, @Param("ruleType") Integer ruleType);

    @Select("SELECT illegal_file FROM themis_analyze_issue WHERE (analyze_id = #{analyzeId} AND rule_type = "
            + "#{ruleType}) GROUP BY illegal_file LIMIT #{start},#{pageSize}")
    List<String> issueGroupByFilePathList(@Param("analyzeId") Long analyzeId, @Param("ruleType") Integer ruleType,
            @Param("start") Long start, @Param("pageSize") Long pageSize);

}
