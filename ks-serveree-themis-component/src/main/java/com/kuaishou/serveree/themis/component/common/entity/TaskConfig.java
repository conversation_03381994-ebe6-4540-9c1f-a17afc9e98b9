package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "TaskConfig对象", description = "")
@Builder
public class TaskConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "执行类型")
    private String executionType;

    @ApiModelProperty(value = "执行参数")
    private String executionParams;

    @ApiModelProperty(value = "执行结果")
    private String executionResult;

    @ApiModelProperty(value = "原ksp_pipeline_id")
    private Long originalKspPipelineId;

    @ApiModelProperty(value = "原ksp_build_id")
    private Long originalKspBuildId;

    @ApiModelProperty(value = "原ksp_pipeline的step_id")
    private Long originalKspStepId;

    @ApiModelProperty(value = "原ksp_pipeline的名称")
    private String originalKspName;

    @ApiModelProperty(value = "发起的ksp_pipeline_id")
    private Long sponsorKspPipelineId;

    @ApiModelProperty(value = "发起的ksp_pipeline_id")
    private Long sponsorKspBuildId;

    @ApiModelProperty(value = "发起的ksp_step_id")
    private Long sponsorKspStepId;

    private String scriptType;

    private Integer javaVersion;

    private Long pExecutionId;

    @ApiModelProperty(value = "job执行类型")
    private String jobType;

    @ApiModelProperty(value = "检查运行分类  0.运行报错 1.分类上报")
    private Integer pluginCheckRunType;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime updatedTime;

    private String language;

    private String languageVersion;


}
