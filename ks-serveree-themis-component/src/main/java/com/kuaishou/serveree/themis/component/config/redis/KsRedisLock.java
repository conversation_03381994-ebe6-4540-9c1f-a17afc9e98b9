package com.kuaishou.serveree.themis.component.config.redis;

import static io.lettuce.core.ScriptOutputType.INTEGER;

import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import io.lettuce.core.SetArgs.Builder;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/6/3 1:02 下午
 */
@Slf4j
@Component
public class KsRedisLock {

    private final KsRedisClient ksRedisClient;

    @Autowired
    public KsRedisLock(KsRedisClient ksRedisClient) {
        this.ksRedisClient = ksRedisClient;
    }

    private static final Long WAIT_TIME = 10L;

    private static final String OK = "OK";

    private static final Long LOCK_TIME = 30 * 1000L;

    /**
     * 自旋挂起的lock
     *
     * @param key : 锁key
     * @param millisecond : 超时自旋时间
     */
    public boolean spinLock(String key, long millisecond) {
        int waitTime = 0;
        while (true) {
            if (waitTime > millisecond) {
                return false;
            }
            if (OK.equals(ksRedisClient.sync()
                    .set(key, String.valueOf(Thread.currentThread().hashCode()), Builder.nx().px(LOCK_TIME)))) {
                return true;
            } else {
                try {
                    TimeUnit.MILLISECONDS.sleep(WAIT_TIME);
                } catch (InterruptedException e) {
                    log.error("spinLock thread sleep error!", e);
                }
                waitTime += WAIT_TIME;
            }
        }
    }

    /**
     * 自旋挂起的lock
     *
     * @param key : 锁key
     */
    public boolean spinLock(String key) {
        return spinLock(key, LOCK_TIME);
    }

    /**
     * 分布式加锁，不等待，返回加锁是否成功
     */
    public boolean lock(String key) {
        return OK.equals(ksRedisClient.sync()
                .set(key, String.valueOf(Thread.currentThread().hashCode()), Builder.nx().px(LOCK_TIME)));
    }

    /**
     * 分布式加锁，不等待，返回加锁是否成功
     */
    public boolean lock(String key, long timeout) {
        return OK.equals(ksRedisClient.sync()
                .set(key, String.valueOf(Thread.currentThread().hashCode()), Builder.nx().px(timeout)));
    }

    /**
     * 解锁
     */
    public boolean unlock(String key) {
        String script =
                "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        return 1L == (Long) ksRedisClient.sync().eval(script, INTEGER, new String[] {key},
                String.valueOf(Thread.currentThread().hashCode()));
    }

    /**
     * 强制解锁 不检查线程是否匹配
     */
    public void forceUnlock(String key) {
        ksRedisClient.sync().del(key);
    }

}