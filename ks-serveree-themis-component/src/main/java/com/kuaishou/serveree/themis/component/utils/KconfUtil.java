package com.kuaishou.serveree.themis.component.utils;

import java.util.List;
import java.util.function.Supplier;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.annotation.Kconfig;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023/1/4 下午2:41
 **/
@Slf4j
@Component
public class KconfUtil {
    @Kconfig("qa.themis.canEditRuleSeverityScannerList")
    private Kconf<List<String>> canEditRuleSeverityScannerList;

    @Kconfig("qa.themis.buildTeamByIssueProjectWhiteList")
    private Kconf<List<Integer>> buildTeamByIssueProjectWhiteList;


    public boolean canEditRuleSeverityScannerList(String scanner) {
        List<String> whiteList = query(() -> canEditRuleSeverityScannerList.get(), Lists.newArrayList(), "canEditRuleSeverityScannerList");

        return CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(scanner);
    }

    public boolean buildTeamByIssueProjectWhiteList(Integer gitProjectId) {
        List<Integer> whiteList = query(() -> buildTeamByIssueProjectWhiteList.get(), Lists.newArrayList(), "buildTeamByIssueProjectWhiteList");

        return CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(gitProjectId);
    }

    public <T> T query(Supplier<T> supplier, T defaultValue, String msg) {
        try {
            T value = supplier.get();
            return supplier.get();
        } catch (Exception e) {
            log.error("{} failed! msg == {}", msg, e.getMessage(), e);
            return defaultValue;
        }
    }
}
