package com.kuaishou.serveree.themis.component.service.sonar.impl;

import static com.kuaishou.serveree.themis.component.service.impl.KdevInteractiveServiceImpl.MAVEN_SCANNER_NEW_PLUGIN_ID;
import static com.kuaishou.serveree.themis.component.utils.BuildParamsUtils.fillUpCustomBuildParamVo;
import static java.util.stream.Collectors.toList;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.gitlab.api.models.GitlabUser;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.phantomthief.util.MoreFunctions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.component.client.ares.AresApi;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.kdev.KdevApi;
import com.kuaishou.serveree.themis.component.client.kim.KimApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.CorpSonarApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.KFormatSonarApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.MetricSonarApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarApiFactory;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;
import com.kuaishou.serveree.themis.component.client.sonar.operations.KFormatSonarOperations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterSimpleFactory;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarOperationFactory;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarOperations;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranchProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoLanguage;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckConfig;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.ScanDirectory;
import com.kuaishou.serveree.themis.component.common.entity.SonarConfig;
import com.kuaishou.serveree.themis.component.common.entity.SonarMetric;
import com.kuaishou.serveree.themis.component.common.entity.SonarPipelineMeasure;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.kdev.KdevBuildParamsConstant;
import com.kuaishou.serveree.themis.component.constant.kdev.KdevBuildParamsConstant.ModuleInfoEnum;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.process.ProcessScannerType;
import com.kuaishou.serveree.themis.component.constant.process.ProcessSponsorType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueType;
import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.constant.sonar.SonarValuesEnum;
import com.kuaishou.serveree.themis.component.entity.ares.AresMessageEntity;
import com.kuaishou.serveree.themis.component.entity.ares.RobotParam;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineBuildParamVo;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineJobBuildParamVo;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineJobParam;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineJobParamValue;
import com.kuaishou.serveree.themis.component.entity.plugin.SonarNewPluginSettings;
import com.kuaishou.serveree.themis.component.entity.plugin.SonarNewPluginSettings.SonarNewPluginMeta;
import com.kuaishou.serveree.themis.component.entity.sonar.Facet;
import com.kuaishou.serveree.themis.component.entity.sonar.History;
import com.kuaishou.serveree.themis.component.entity.sonar.HookPayload;
import com.kuaishou.serveree.themis.component.entity.sonar.HookProperties;
import com.kuaishou.serveree.themis.component.entity.sonar.Issue;
import com.kuaishou.serveree.themis.component.entity.sonar.Profiles;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarConfigContent;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarPipelineMeasureElement;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarValues;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MetricComponentRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MetricComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MetricIssueRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.QualityProfileReq;
import com.kuaishou.serveree.themis.component.entity.sonar.req.SonarSearchRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.HistoryMeasureResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.HistoryMeasureResp.HistoryMeasures;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.IssuesResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentTreeResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.QualityProfileResp;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoLanguageService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PCheckConfigService;
import com.kuaishou.serveree.themis.component.service.PCheckExecutionService;
import com.kuaishou.serveree.themis.component.service.PCheckIssueService;
import com.kuaishou.serveree.themis.component.service.ScanDirectoryService;
import com.kuaishou.serveree.themis.component.service.SonarConfigService;
import com.kuaishou.serveree.themis.component.service.SonarMetricService;
import com.kuaishou.serveree.themis.component.service.SonarPipelineMeasureService;
import com.kuaishou.serveree.themis.component.service.pipeline.impl.PipelineIssueReportService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoService;
import com.kuaishou.serveree.themis.component.service.sonar.SonarKimApiHelper;
import com.kuaishou.serveree.themis.component.service.sonar.SonarService;
import com.kuaishou.serveree.themis.component.service.sonar.parser.ParserContext;
import com.kuaishou.serveree.themis.component.service.sonar.parser.ParserHandleChain;
import com.kuaishou.serveree.themis.component.service.sonar.process.ProcessHookHelper;
import com.kuaishou.serveree.themis.component.service.sonar.router.SonarNodeRouter;
import com.kuaishou.serveree.themis.component.utils.IssueUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.PlatformSonarInfoUtils;
import com.kuaishou.serveree.themis.component.utils.SonarLogic;
import com.kuaishou.serveree.themis.component.vo.request.PluginScanStartRequest;
import com.kuaishou.serveree.themis.component.vo.request.SettingsValidateRequest;
import com.kuaishou.serveree.themis.component.vo.request.SonarStuckPointRequest;
import com.kuaishou.serveree.themis.component.vo.request.sonar.PluginPipelineReportRequest;
import com.kuaishou.serveree.themis.component.vo.response.CustomBuildParamVo;
import com.kuaishou.serveree.themis.component.vo.response.PipelineReportResponse;
import com.kuaishou.serveree.themis.component.vo.response.PluginScanStartResponse;
import com.kuaishou.serveree.themis.component.vo.response.SonarStuckPointResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.NeedCompileResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.ParallelExecuteResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SettingsValidateResponse;
import com.kuaishou.util.DateUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/7/14 5:11 下午
 */
@Slf4j
@Service
public class SonarServiceImpl implements SonarService, PipelineIssueReportService {

    public static final Kconf<List<String>> WEIXIN_BLACKLIST =
            Kconfs.ofStringList("qa.themis.sonarHookWeixinBlacklist", Lists.newArrayList()).build();
    public static final Kconf<Integer> DEFAULT_PARALLEL_COUNT =
            Kconfs.ofInteger("qa.themis.defaultParallelCount", 4).build();
    private static final Logger logger = LoggerFactory.getLogger(SonarServiceImpl.class);
    @Autowired
    private SonarOperationFactory sonarOperationFactory;
    @Autowired
    private GitOperations gitOperations;
    @Autowired
    private KsRedisClient ksRedisClient;
    @Autowired
    private KsRedisLock ksRedisLock;
    @Autowired
    private List<SonarCommonApi> sonarCommonApis;
    @Autowired
    private SonarKimApiHelper sonarKimApiHelper;
    @Autowired
    private ParserHandleChain parserHandleChain;
    @Autowired
    private SonarApiFactory sonarApiFactory;
    @Autowired
    private SonarConfigService sonarConfigService;
    @Autowired
    private SonarMetricService sonarMetricService;
    @Autowired
    private KimApi kimApi;
    @Autowired
    private AresApi aresApi;
    @Autowired
    private KFormatSonarOperations kFormatSonarOperations;
    @Autowired
    private CorpSonarApi corpSonarApi;
    @Autowired
    private MetricSonarApi metricSonarApi;
    @Autowired
    private KFormatSonarApi kFormatSonarApi;
    @Autowired
    private CheckRepoService checkRepoService;
    @Autowired
    private SonarPipelineMeasureService sonarPipelineMeasureService;
    @Autowired
    private SonarClusterSimpleFactory sonarClusterSimpleFactory;
    @Autowired
    private ProcessHookHelper processHookHelper;
    @Autowired
    private PCheckBaseService pCheckBaseService;
    @Autowired
    private PlatformSonarInfoUtils platformSonarInfoUtils;
    @Autowired
    private PCheckExecutionService pCheckExecutionService;
    @Autowired
    private SonarNodeRouter sonarNodeRouter;
    @Autowired
    private CheckRepoLanguageService checkRepoLanguageService;
    @Autowired
    private KdevApi kdevApi;
    @Autowired
    private PCheckConfigService pCheckConfigService;
    @Autowired
    private PCheckIssueService pCheckIssueService;
    @Autowired
    private SonarLogic sonarLogic;
    @Resource
    private ScanDirectoryService scanDirectoryService;
    @Resource
    private PlatformRepoService platformRepoService;
    @Resource
    private CheckRepoBranchProfileService checkRepoBranchProfileService;

    private static final String LIMIT_BRANCH = "kem_measure";
    private static final List<Long> BUILD_PLUGIN_IDS = Lists.newArrayList(10028L);

    @Override
    public void hook(HookPayload payload, List<String> ccReceivers, boolean globalHook) {
        String projectKey = payload.getProject().getKey();
        String analysedAt = payload.getAnalysedAt();
        String serverUrl = payload.getServerUrl();
        String branchName = payload.getBranch().getName();
        SonarOperations realOperations = sonarOperationFactory.getRealOperations(serverUrl);

        if (globalHook) { // 检查项目设置中是否已有通知hook，有则跳过。
            try {
                List<String> hooks = realOperations.getProjectHookUrls(projectKey);
                if (CollectionUtils.isNotEmpty(hooks)) {
                    logger.info("project: {}, hooks: {}", projectKey, hooks);
                }
                if (hooks.stream().anyMatch(url -> url.startsWith("http://server-devops-api.internal/api/sonar/hook")
                        || url.startsWith("https://serveree-themis.corp.kuaishou.com/api/quality/sonar/hook"))) {
                    return;
                }
            } catch (Exception e) {
                logger.error("project: {}, get hooks error", projectKey, e);
            }
        }

        List<String> blacklist = WEIXIN_BLACKLIST.get(); // 黑名单
        List<String> receivers = Optional.ofNullable(ccReceivers).orElse(Collections.emptyList())
                .stream().filter(s -> !"@all".equals(s))
                .filter(s -> !blacklist.contains(s)) // 过滤黑名单
                .collect(toList());

        // reco:reco_leaf_git项目特殊检测，不包括坏味道
        Map<String, List<Issue>> openIssues;
        Map<String, List<Issue>> closedIssues;
        if (projectKey.startsWith("reco:reco_leaf_git")) {
            openIssues = realOperations.getOpenIssuesWithoutCodeSmell(projectKey, branchName, analysedAt);
            closedIssues = realOperations.getClosedIssuesWithoutCodeSmell(projectKey, branchName, analysedAt);
        } else {
            openIssues = realOperations.getOpenIssues(projectKey, branchName, analysedAt);
            closedIssues = realOperations.getClosedIssues(projectKey, branchName, analysedAt);
        }

        int openCount = openIssues.values().stream().mapToInt(List::size).sum();
        int closedCount = closedIssues.values().stream().mapToInt(List::size).sum();
        logger.info("project: {}, analysedAt: {}, issues open: {}, closed: {}", projectKey,
                analysedAt, openCount, closedCount);

        sonarKimApiHelper.send(openIssues, "[SONARQUBE] 新增%s个问题", "open", payload, receivers);
        sonarKimApiHelper.send(closedIssues, "[SONARQUBE] 关闭%s个问题", "closed", payload, receivers);
    }

    @Override
    public void createUsers() {
        // gitlab 获取所有用户
        List<GitlabUser> gitlabUsers = gitOperations.getAllUsers();
        logger.info("finish getting all gitlab users, size={}", gitlabUsers.size());

        // redis 获取sonar用户名列表
        List<String> sonarUsernames = Lists.newArrayList();
        String sonarUsernamesStr = ksRedisClient.sync().get(KsRedisPrefixConstant.SONAR_CASHED_USER_LOGINS);
        if (StringUtils.isNotEmpty(sonarUsernamesStr)) {
            sonarUsernames = JSONUtils.deserializeList(sonarUsernamesStr, String.class);
        }
        Set<String> sonarUsernameSet = Sets.newHashSet(sonarUsernames.iterator());
        logger.info("finish getting all sonar users, size={}", sonarUsernameSet.size());

        // 获取未创建sonar的gitlab用户
        List<GitlabUser> newGitlabUsers = gitlabUsers.stream()
                .filter(gitlabUser -> !sonarUsernameSet.contains(gitlabUser.getUsername()))
                .collect(toList());

        // 创建sonar用户
        logger.info("start creating sonar users, {} will be added ", newGitlabUsers.size());
        sonarCommonApis.forEach(sonarApi -> newGitlabUsers.forEach(gitlabUser -> {
            String login = gitlabUser.getUsername();
            String name = gitlabUser.getName();
            String email = gitlabUser.getEmail();
            sonarApi.createUser(login, name, email);
            sonarUsernameSet.add(login);
        }));
        // redis 存sonar用户名列表
        sonarUsernames = Lists.newArrayList(sonarUsernameSet.iterator());
        ksRedisClient.sync().set(KsRedisPrefixConstant.SONAR_CASHED_USER_LOGINS, JSONUtils.serialize(sonarUsernames));
        logger.info("finish creating sonar users, adds {}", newGitlabUsers.size());
    }

    @Override
    @Transactional
    public void checkParser(HookPayload payload) {
        // 检查一下参数 c++指定分支才能继续
        if (!this.checkParam(payload)) {
            return;
        }
        String key = payload.getProject().getKey();
        String branch = payload.getBranch().getName();
        // 加一个自旋锁 防止数据错乱
        final String redisKey = KsRedisPrefixConstant.CHECK_PARSER_SPIN_LOCK + key + ":" + branch;
        ksRedisLock.spinLock(redisKey);
        try {
            ParserContext parserContext = ParserContext.builder()
                    .branch(payload.getBranch())
                    .project(payload.getProject())
                    .analysedAt(payload.getAnalysedAt())
                    .hookProperties(payload.getProperties())
                    .nowTime(LocalDateTime.now())
                    .realOperations(sonarOperationFactory.getRealOperations(payload.getServerUrl()))
                    .build();
            parserHandleChain.execute(parserContext);
        } catch (Exception e) {
            log.error("check parser error! hookPayload is {}", JSONUtils.serialize(payload), e);
            throw new RuntimeException(e);
        }
    }

    private boolean checkParam(HookPayload payload) {
        // base数据
        HookProperties properties = payload.getProperties();
        if (properties == null) {
            logger.error("非完整hook数据，跳过，payload is {}", JSONUtils.serialize(payload));
            return false;
        }
        // 这里直接根据来源判断 等多样化以后再抽出策略
        // c++的单独校验 约定的分支来解析
        if (kFormatSonarOperations.sonarApi().sonarUrl().equals(payload.getServerUrl())
                && !LIMIT_BRANCH.equals(payload.getBranch().getName())) {
            log.info("非指定分支的kformat sonar hook,跳过");
            return false;
        }
        return true;
    }

    @Override
    public MeasureComponentResp measureComponent(MeasureComponentRequest request) {
        SonarCommonApi realSonarApi = sonarApiFactory.getRealSonarApi(request.getServerUrl());
        return realSonarApi.measuresComponent(request.getComponent(), request.getBranch(),
                request.getAdditionalFields(), request.getMetricKeys());
    }

    @Override
    public void customHook(HookPayload payload) {
        HookProperties properties = payload.getProperties();
        String gitProjectId = properties.getGitProjectId();
        if (StringUtils.isEmpty(gitProjectId)) {
            return;
        }
        SonarConfig sonarConfig = sonarConfigService.getByProjectId(Integer.valueOf(gitProjectId));
        // 获取group配置
        if (sonarConfig == null) {
            sonarConfig = this.getGroupConfig(Integer.valueOf(gitProjectId));
            // 没有任何配置 直接返回
            if (sonarConfig == null) {
                log.warn("not found any sonar config, return, projectId is {}", gitProjectId);
                return;
            }
        }
        String configContent = sonarConfig.getConfigContent();
        if (StringUtils.isEmpty(configContent)) {
            log.warn("configContent is empty, return, projectId is {}", gitProjectId);
            return;
        }
        SonarConfigContent sonarConfigContent = JSONUtils.deserialize(configContent, SonarConfigContent.class);
        if (sonarConfigContent == null) {
            log.error("sonarConfigContent is null, return, projectId is {}", gitProjectId);
            return;
        }
        Map<String, Map<String, String>> settingsMap = sonarConfigContent.getSettingsMap();
        if (MapUtils.isEmpty(settingsMap)) {
            log.error("illegal settingsMap is {}", settingsMap);
            return;
        }
        String markDownContent = this.getNoticeMarkDownContent(payload, settingsMap);
        if (StringUtils.isEmpty(markDownContent)) {
            log.warn("markDownContent  is {}, return", markDownContent);
            return;
        }
        String pipelineBuildUserName = properties.getPipelineBuildUserName();
        try {
            String result = kimApi.sendText(pipelineBuildUserName, markDownContent);
            log.info("kimApi custom hook sendText result is {}", result);
        } catch (Exception e) {
            log.error("kimApi custom hook sendText error", e);
        }
        try {
            List<String> robotIds = sonarConfigContent.getRobotIds();
            if (CollectionUtils.isNotEmpty(robotIds)) {
                this.sendRobotNotice(markDownContent, pipelineBuildUserName, robotIds);
            }
        } catch (Exception e) {
            log.error("kimApi custom hook ares send kim notice error", e);
        }
    }

    @Override
    @Transactional
    public void processHook(HookPayload payload) {
        processHookHelper.execute(payload);
    }

    @Override
    public ThemisResponse<SonarStuckPointResponse> pluginStuckPoint(SonarStuckPointRequest request) {
        // 流水线标识
        Long kspBuildId = request.getKspBuildId();
        // 获取执行记录
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(request.getKspBuildId());
        if (pCheckBase == null || TaskStatusEnum.FAIL.name().equals(pCheckBase.getStatus())) {
            return ThemisResponse.fail(ResultCodeConstant.PIPELINE_SCAN_REPORT_FAILED);
        }
        // 获取本次扫描卡点配置
        SonarNewPluginSettings pluginSetting = getPluginSetting(request, pCheckBase);
        // 获取(本次or缓存)扫描结果的统计指标
        SonarPipelineMeasure sonarPipelineMeasure;
        if (Objects.isNull(request.getCacheBuildId())) {
            // 未命中缓存，直接取数据
            sonarPipelineMeasure = sonarPipelineMeasureService.getByKspBuildId(kspBuildId);
        } else {
            // 命中缓存，重新计算
            sonarPipelineMeasure = buildCacheMeasure(request.getCacheBuildId(), pluginSetting);
        }
        if (sonarPipelineMeasure == null) {
            // 还没有收到hook数据
            return ThemisResponse.fail(ResultCodeConstant.SYNCING_SONAR_RESULT);
        }
        SonarStuckPointResponse checkResult = this.getPipelineStuckResult(sonarPipelineMeasure, pluginSetting);
        // 对比是否卡点x
        return ThemisResponse.success(checkResult);
    }

    /**
     * 用 缓存数据（历史扫描结果issue） + 当前卡点配置，重新计算、过滤卡点生效的issue的统计指标
     */
    private SonarPipelineMeasure buildCacheMeasure(Long cacheKspBuildId, SonarNewPluginSettings currentSetting) {
        // 流水线标识
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(cacheKspBuildId);
        List<PCheckIssue> pCheckIssues = pCheckIssueService.listByPBaseIdAndType(pCheckBase.getId(),
                ProcessExecutionReferType.MAVEN_SONAR.getType());
        // 重新计算是否卡点，因为命中缓存时其他参数都一致（commit、模块、mrId、是否增量、增量模式），所以inDiff不用判断，这里只需要判断等级
        // 这里改值后重新算结果就行，不能更新数据库 pCheckIssues
        pCheckIssues.forEach(p -> p.setValidStuck(p.getInDiff() && IssueUtils.validStuck(currentSetting, p.getType(), p.getSeverity())));
        return sonarLogic.getPipelineMeasure(pCheckIssues, pCheckBase);
    }

    private SonarNewPluginSettings getPluginSetting(SonarStuckPointRequest request, PCheckBase pCheckBase) {
        String stuckPointSettings = pCheckBase.getStuckPointSettings();
        // 获取卡点配置
        SonarNewPluginSettings pluginSettings = JSONUtils.deserialize(stuckPointSettings, SonarNewPluginSettings.class);
        // 数据库中没有，就以请求参数构造 // never
        if (Objects.isNull(pluginSettings)) {
            log.warn("从PCheckBase获取卡点配置失败，用请求参数构造。pBase: {}, req: {}", pCheckBase, request);
            pluginSettings = parsePluginSetting(request);
            pCheckBase.setStuckPointSettings(JSONUtils.serialize(pluginSettings));
            pCheckBase.setGmtModified(LocalDateTime.now());
            pCheckBaseService.updateById(pCheckBase);
        }
        return pluginSettings;
    }

    private SonarStuckPointResponse getPipelineStuckResult(SonarPipelineMeasure measure, SonarNewPluginSettings pluginSettings) {
        SonarNewPluginMeta bugSettings = pluginSettings.getBugMeta();
        SonarNewPluginMeta vulnerabilitySettings = pluginSettings.getVulnerabilityMeta();
        // measureElement
        SonarPipelineMeasureElement bugMeasureElement =
                JSONUtils.deserialize(measure.getBugMeasure(), SonarPipelineMeasureElement.class);
        SonarPipelineMeasureElement vulnerabilityMeasureElement =
                JSONUtils.deserialize(measure.getVulnerabilityMeasure(), SonarPipelineMeasureElement.class);
        StringBuilder detailMessageSb = new StringBuilder();
        StringBuilder reasonSb = new StringBuilder();
        // 处理BUG
        boolean bugValidate = this.checkAndPaddingResult("BUG", detailMessageSb, reasonSb,
                bugMeasureElement, bugSettings);
        // 处理漏洞
        boolean vulValidate = this.checkAndPaddingResult("漏洞", detailMessageSb, reasonSb,
                vulnerabilityMeasureElement, vulnerabilitySettings);
        int result = bugValidate && vulValidate ? 1 : 0;
        String detailStr = detailMessageSb.toString();
        String reasonStr = reasonSb.toString();
        if (StringUtils.isNotEmpty(reasonStr)) {
            reasonStr = reasonStr.substring(0, reasonStr.length() - 1);
        }
        return new SonarStuckPointResponse(result, detailStr, reasonStr);
    }

    private SonarNewPluginSettings parsePluginSetting(SonarStuckPointRequest request) {
        String bugSettings = request.getBugSettings();
        String vulnerabilitySettings = request.getVulnerabilitySettings();
        SonarNewPluginMeta bugMeta = JSONUtils.deserialize(bugSettings, SonarNewPluginMeta.class);
        SonarNewPluginMeta vulMeta = JSONUtils.deserialize(vulnerabilitySettings, SonarNewPluginMeta.class);
        if (Objects.isNull(bugMeta)) {
            bugMeta = SonarNewPluginMeta.builder().stuckSwitch(false).severity("major").count(0).build();
        }
        if (Objects.isNull(vulMeta)) {
            vulMeta = SonarNewPluginMeta.builder().stuckSwitch(false).severity("major").count(0).build();
        }
        return SonarNewPluginSettings.builder().bugMeta(bugMeta).vulnerabilityMeta(vulMeta).build();
    }

    private boolean checkAndPaddingResult(String token, StringBuilder sb, StringBuilder reasonSb,
            SonarPipelineMeasureElement measureElement,
            SonarNewPluginMeta stuckPointSettings) {
        // SonarPipelineMeasure是基于卡点issue统计出来的，已经过滤过等级，这里直接判断总数就行
        // 设置的数量
        int settingsCount = stuckPointSettings.getCount();
        // 卡点issue的数据
        int sumCount = measureElement.getBlockerCount() + measureElement.getCriticalCount() + measureElement.getMajorCount();
        sb.append(token).append(":");
        sb.append("用户是否打开了检查配置:").append(stuckPointSettings.isStuckSwitch());
        sb.append("用户设置的issue最低级别为:").append(stuckPointSettings.getSeverity());
        sb.append("设置的issue的数量为:").append(settingsCount);
        sb.append("检查出命中的issue数量为：").append(sumCount);
        sb.append(" \n ");
        boolean lessThanSettingsCount = sumCount <= settingsCount;
        if (!lessThanSettingsCount) {
            reasonSb.append(token).append("数量超过设置的阈值").append(";");
        }
        return lessThanSettingsCount;
    }

    @Override
    public void stuckPointHook(HookPayload payload) {
        HookProperties properties = payload.getProperties();
        if (properties == null) {
            log.info("not stuck point hook, properties is null, return");
            return;
        }
        if (!properties.isStuckPoint()) {
            log.info("not stuck point hook, stuck point is false, return");
            return;
        }
        // sonar project ley
        String projectKey = payload.getProject().getKey();
        // 分支
        String branch = payload.getBranch().getName();
        SonarCommonApi sonarCommonApi = corpSonarApi;
        // 如果projectKey是纯数字
        if (projectKey.matches("[0-9]+")) {
            SonarOperations sonarOperations = sonarClusterSimpleFactory.getClusterOperations(Long.valueOf(projectKey));
            sonarCommonApi = sonarOperations.sonarApi();
        }
        // BUG
        IssuesResp bugIssuesRes =
                getIssueResp(sonarCommonApi, projectKey, branch, CheckIssueType.BUG.getType());
        // CODE_SMELL
        IssuesResp codeSmellIssuesRes =
                getIssueResp(sonarCommonApi, projectKey, branch, CheckIssueType.CODE_SMELL.getType());
        // VULNERABILITY
        IssuesResp vulnerabilityIssuesRes =
                getIssueResp(sonarCommonApi, projectKey, branch, CheckIssueType.VULNERABILITY.getType());
        SonarPipelineMeasure sonarPipelineMeasure =
                this.getSonarPipelineMeasure(bugIssuesRes, codeSmellIssuesRes, vulnerabilityIssuesRes, payload);
        sonarPipelineMeasureService.save(sonarPipelineMeasure);
    }

    @Override
    public MeasureComponentResp sonarMeasures(MeasureComponentRequest request) {
        String target = request.getTarget();
        SonarCommonApi sonarCommonApi;
        if (corpSonarApi.getClass().getSimpleName().equals(target)) {
            sonarCommonApi = corpSonarApi;
        } else if (metricSonarApi.getClass().getSimpleName().equals(target)) {
            sonarCommonApi = metricSonarApi;
        } else if (kFormatSonarApi.getClass().getSimpleName().equals(target)) {
            sonarCommonApi = kFormatSonarApi;
        } else {
            sonarCommonApi = corpSonarApi;
        }
        return sonarCommonApi.measuresComponent(request.getComponent(),
                request.getBranch(),
                request.getAdditionalFields(),
                request.getMetricKeys());
    }

    @Override
    public MeasureComponentTreeResp sonarMeasureTree(MeasureComponentTreeRequest request) {
        String target = request.getTarget();
        if (corpSonarApi.getClass().getSimpleName().equals(target)) {
            return corpSonarApi.measuresComponentTree(request);
        }
        if (metricSonarApi.getClass().getSimpleName().equals(target)) {
            return metricSonarApi.measuresComponentTree(request);
        }
        if (kFormatSonarApi.getClass().getSimpleName().equals(target)) {
            return kFormatSonarApi.measuresComponentTree(request);
        }
        throw new RuntimeException("未找到有效的sonar实例");
    }

    @Override
    public IssuesResp sonarSearchIssues(MetricIssueRequest issueRequest) {
        String sonarProjectKey = getSonarKey(issueRequest.getGitProjectId());
        issueRequest.getSearchRequest().setComponentKeys(sonarProjectKey);
        return metricSonarApi.searchIssues(issueRequest.getSearchRequest());
    }

    @Override
    public MeasureComponentResp metricMeasures(MetricComponentRequest componentRequest) {
        String sonarProjectKey = getSonarKey(componentRequest.getGitProjectId());
        componentRequest.getMeasureComponent().setComponent(sonarProjectKey);
        MeasureComponentRequest request = componentRequest.getMeasureComponent();
        return metricSonarApi.measuresComponent(request.getComponent(), request.getBranch(),
                request.getAdditionalFields(), request.getMetricKeys());
    }

    @Override
    public MeasureComponentTreeResp metricMeasuresTree(MetricComponentTreeRequest componentTreeRequest) {
        String sonarProjectKey = getSonarKey(componentTreeRequest.getGitProjectId());
        componentTreeRequest.getMeasureComponentTree().setComponent(sonarProjectKey);
        componentTreeRequest.getMeasureComponentTree().setTarget(metricSonarApi.getClass().getSimpleName());
        return metricSonarApi.measuresComponentTree(componentTreeRequest.getMeasureComponentTree());
    }

    @Transactional
    @Override
    public PluginScanStartResponse pluginScanStart(PluginScanStartRequest request) {
        // java项目
        if (ProcessExecutionReferType.MAVEN_SONAR.getType() == request.getExecutionReferType()) {
            sonarNodeRouter.activeOrRedirectNodeRoute(request.getGitProjectId(),
                    Integer.parseInt(request.getLanguageVersion()));
        }
        // 流水线扫描初始化
        if (ScanModeEnum.PROCESS.getCode() == request.getScanMode()) {
            // 在发起流水线时创建check base
            initCheckBase(request);
        }
        return new PluginScanStartResponse(1);
    }

    private void initCheckBase(PluginScanStartRequest request) {
        // 新建记录
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(request.getKspBuildId());
        if (pCheckBase == null) {
            pCheckBase = fillUpPCheckBase(request, true);
            pCheckBase = pCheckBaseService.initBase(pCheckBase);
        } else {
            PCheckBase initPCheckBase = fillUpPCheckBase(request, false);
            initPCheckBase.setId(pCheckBase.getId());
            pCheckBaseService.updateById(initPCheckBase);
            pCheckBase = initPCheckBase;
        }
        PCheckConfig pCheckConfig = initPCheckConfig(request, pCheckBase);
        pCheckConfigService.initConfig(pCheckConfig);
        PCheckExecution pCheckExecution = initPCheckExecution(request, pCheckBase);
        pCheckExecutionService.initExecution(pCheckExecution);
    }

    @Override
    public String getSonarProfileNameByGitProjectId(Integer gitProjectId, String language) {
        SonarOperations clusterOperations = sonarClusterSimpleFactory.getClusterOperations(Long.valueOf(gitProjectId));
        QualityProfileReq qualityProfileReq = QualityProfileReq.builder()
                .project(platformSonarInfoUtils.getPipelineFinalProjectKey(gitProjectId))
                .language(language)
                .build();
        QualityProfileResp qualityProfile = clusterOperations.sonarApi().getQualityProfile(qualityProfileReq);
        List<Profiles> profiles = qualityProfile.getProfiles();
        return profiles.get(0).getName();
    }

    @Override
    public ThemisResponse<SettingsValidateResponse> settingsValidate(SettingsValidateRequest request) {
        String language = request.getLanguage();
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(request.getGitProjectId());
        // 项目不存在则新建
        if (checkRepo == null) {
            if (StringUtils.isBlank(request.getLanguage())) {
                // 从git获取项目主语言
                language = gitOperations.getMainLanguage(request.getGitProjectId());
            }
            checkRepo = platformRepoService.createProjectDefault(request.getGitProjectId(), language);
        }
        List<CheckRepoLanguage> checkRepoLanguages = checkRepoLanguageService.listByCheckRepoId(checkRepo.getId());
        CheckRepoLanguage checkRepoLanguage = checkRepoLanguages.get(0);
        // 查询扫描目录配置
        ScanDirectory scanDirectory = scanDirectoryService.getByCheckRepoId(checkRepo.getId());
        // 查询要使用的扫描器，当前只会有1个
        List<String> scanners = checkRepoBranchProfileService.listByCheckRepoId(checkRepo.getId()).stream()
                .map(CheckRepoBranchProfile::getScanner).distinct().collect(toList());
        SettingsValidateResponse validateResponse = SettingsValidateResponse.builder()
                .language(checkRepoLanguage.getLanguage())
                .languageVersion(checkRepoLanguage.getVersion())
                .includeDirs(scanDirectory != null ? scanDirectory.getInclude() : "")
                .excludeDirs(scanDirectory != null ? scanDirectory.getExclude() : "")
                .scanners(scanners)
                .build();
        return ThemisResponse.success(validateResponse);
    }

    @Override
    public ThemisResponse<Integer> getLocalParallelScanCount(Integer gitProjectId) {
        SonarConfig sonarConfig = sonarConfigService.getByProjectId(gitProjectId);
        if (sonarConfig == null || sonarConfig.getParallelCount() == 0) {
            return ThemisResponse.success(DEFAULT_PARALLEL_COUNT.get());
        }
        return ThemisResponse.success(sonarConfig.getParallelCount());
    }

    @Override
    public ThemisResponse<NeedCompileResponse> needCompile(Long kspBuildId) {
        PipelineBuildParamVo pipelineBuildParam = kdevApi.getPipelineBuildParam(kspBuildId);
        if (pipelineBuildParam == null || pipelineBuildParam.getJobBuildParams() == null
                || pipelineBuildParam.getCustomEnvs() == null || pipelineBuildParam.getPipelineLogId() == null) {
            NeedCompileResponse needCompileResponse = NeedCompileResponse.newBuilder().needCompile(true).build();
            return ThemisResponse.success(needCompileResponse);
        }
        Long buildJobId = null;
        Long mavenScannerNewJobId = null;
        Map<Long, List<Long>> jobIdDependIdsMap = Maps.newHashMap();
        Map<Long, PipelineJobBuildParamVo> jobIdParamVoMap = Maps.newHashMap();
        // 只有前置编译（必须是引用前置，是不编译的必要条件） 并且参数都一致或者maven-sonar-new无参数才不用编译
        for (PipelineJobBuildParamVo jobBuildParam : pipelineBuildParam.getJobBuildParams()) {
            if (MAVEN_SCANNER_NEW_PLUGIN_ID.equals(jobBuildParam.getTemplateId())) {
                mavenScannerNewJobId = jobBuildParam.getJobId();
            }
            if (BUILD_PLUGIN_IDS.contains(jobBuildParam.getTemplateId())) {
                buildJobId = jobBuildParam.getJobId();
            }
            jobIdDependIdsMap.put(jobBuildParam.getJobId(), jobBuildParam.getDependIds());
            jobIdParamVoMap.put(jobBuildParam.getJobId(), jobBuildParam);
        }
        // 不存在编译 那么mavenscannernew需要强制编译
        if (buildJobId == null) {
            NeedCompileResponse needCompileResponse = NeedCompileResponse.newBuilder().needCompile(true).build();
            return ThemisResponse.success(needCompileResponse);
        }
        // 如果并行的话 那么就需要编译
        if (CollectionUtils.isEqualCollection(jobIdDependIdsMap.get(mavenScannerNewJobId),
                jobIdDependIdsMap.get(buildJobId))) {
            NeedCompileResponse needCompileResponse = NeedCompileResponse.newBuilder().needCompile(true).build();
            return ThemisResponse.success(needCompileResponse);
        }
        // 如果构建的模块不包含runner（编译时不会copy dependency），也需要编译
        if (!hasCopyDependencyBeenExecuted(jobIdParamVoMap.get(buildJobId))) {
            NeedCompileResponse needCompileResponse = NeedCompileResponse.newBuilder().needCompile(true).build();
            return ThemisResponse.success(needCompileResponse);
        }
        boolean needCompile = true;
        List<Long> dependIds = jobIdDependIdsMap.get(mavenScannerNewJobId);
        while (true) {
            if (dependIds.contains(buildJobId)) {
                if (equalArgs(mavenScannerNewJobId, buildJobId, jobIdParamVoMap)) {
                    needCompile = false;
                }
                break;
            } else {
                dependIds = batchGetJobIdsDependIds(dependIds, jobIdDependIdsMap);
                if (CollectionUtils.isEmpty(dependIds)) {
                    break;
                }
            }
        }
        NeedCompileResponse needCompileResponse = NeedCompileResponse.newBuilder().needCompile(needCompile).build();
        return ThemisResponse.success(needCompileResponse);
    }

    /**
     * 判断构建任务（编译插件）是否执行过 copy-dependency
     * 不会执行的情况：
     *  1、设置了 skip copy-dependency 为 true
     *  2、没有构建runner
     */
    private boolean hasCopyDependencyBeenExecuted(PipelineJobBuildParamVo pipelineJobBuildParamVo) {
        String buildModules = "";
        List<List<PipelineJobParamValue>> moduleInfoList = Collections.emptyList();
        for (PipelineJobParam param : pipelineJobBuildParamVo.getParamList()) {
            // 是否跳过copy-dependency
            if (KdevBuildParamsConstant.SKIP_COPY_DEPENDENCY.getParamKey().equals(param.getName())) {
                String value = param.getValue();
                if (Boolean.TRUE.toString().equals(value)) {
                    return false;
                }
                continue;
            }
            // 构建模块
            if (KdevBuildParamsConstant.BUILD_MODULES.getParamKey().equals(param.getName())) {
                buildModules = param.getValue();
                if (StringUtils.isBlank(buildModules) || "null".equals(buildModules)) {
                    return false;
                }
                continue;
            }
            // 模块类型
            if (KdevBuildParamsConstant.MODULES_TYPE_MAP.getParamKey().equals(param.getName())) {
                moduleInfoList = param.getChildrenValue();
                if (CollectionUtils.isEmpty(moduleInfoList)) {
                    return false;
                }
            }
        }
        // 构建的模块名
        String[] modules = buildModules.split(",");
        for (String module : modules) {
            for (List<PipelineJobParamValue> moduleParam : moduleInfoList) {
                // 当前模块名称和类型
                String moduleName = "", moduleType = "";
                for (PipelineJobParamValue paramValue : moduleParam) {
                    if (ModuleInfoEnum.MODULE_NAME.getKey().equals(paramValue.getName())) {
                        moduleName = paramValue.getValue();
                        continue;
                    }
                    if (ModuleInfoEnum.MODULE_TYPE.getKey().equals(paramValue.getName())) {
                        moduleType = paramValue.getValue();
                    }
                }
                // runner 类型
                if (module.equals(moduleName) && "runner".equals(moduleType)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public ParallelExecuteResponse canProcessParallelExecute(Integer gitProjectId) {
        SonarConfig sonarConfig = sonarConfigService.getByProjectId(gitProjectId);
        return ParallelExecuteResponse.newBuilder()
                .canProcessParallelExecute(sonarConfig.getParallelCount() != 0)
                .build();
    }

    @Override
    @Transactional
    public PipelineReportResponse pipelineReport(PluginPipelineReportRequest request) {
        request.setExecutionReferType(ProcessExecutionReferType.MAVEN_SONAR.getType());
        return report(request);
    }

    private PCheckExecution initPCheckExecution(PluginScanStartRequest request, PCheckBase pCheckBase) {
        LocalDateTime now = LocalDateTime.now();
        return PCheckExecution.builder()
                .pBaseId(pCheckBase.getId())
                .incrementMode(request.getIncrementMode())
                .incrementType(request.getIncrementType())
                .moreProcess(true)
                .realCompile(request.getRealCompile())
                .referType(request.getExecutionReferType())
                .gmtCreate(now)
                .gmtModified(now)
                .build();
    }

    private PCheckConfig initPCheckConfig(PluginScanStartRequest request, PCheckBase pCheckBase) {
        LocalDateTime now = LocalDateTime.now();
        return PCheckConfig.builder()
                .pBaseId(pCheckBase.getId())
                .sponsorType(Boolean.TRUE.equals(request.getMrStuck())
                             ? ProcessSponsorType.MR_STUCK.getType() : ProcessSponsorType.PIPELINE.getType())
                .gmtModified(now)
                .gmtCreate(now)
                .build();
    }

    private PCheckBase fillUpPCheckBase(PluginScanStartRequest request, boolean init) {
        // 卡点配置
        SonarNewPluginSettings stuckPointSetting = request.getStuckPointSetting();
        SonarNewPluginMeta bugMeta = stuckPointSetting.getBugMeta();
        SonarNewPluginMeta vulnerabilityMeta = stuckPointSetting.getVulnerabilityMeta();
        // 手动处理中文严重等级
        bugMeta.setSeverity(CheckIssueSeverity.descZh2SonarSeverity(bugMeta.getSeverity()));
        vulnerabilityMeta.setSeverity(CheckIssueSeverity.descZh2SonarSeverity(vulnerabilityMeta.getSeverity()));
        LocalDateTime now = LocalDateTime.now();
        PCheckBase base = PCheckBase.builder()
                .localBuildId(request.getLocalBuildId())
                .kspBuildId(request.getKspBuildId())
                .projectId(request.getGitProjectId())
                .commitId(request.getCommitId())
                .mrId(request.getMrId().intValue())
                .buildModules(request.getBuildModules())
                .branch(request.getGitBranch())
                .sponsor(request.getSponsor())
                .gmtModified(now)
                .repoUrl(gitOperations.getCacheProject(request.getGitProjectId()).getSshUrl())
                .stuckPointSettings(JSONUtils.serialize(stuckPointSetting))
                .build();
        if (init) {
            base.setScannerType(ProcessScannerType.JAVA_MAVEN_SCANNER.getType());
            base.setStatus(TaskStatusEnum.EXECUTING.name());
            base.setGmtCreate(now);
        }
        return base;
    }

    private boolean equalArgs(Long mavenScannerNewJobId, Long buildJobId,
            Map<Long, PipelineJobBuildParamVo> jobIdParamVoMap) {
        PipelineJobBuildParamVo mavenScannerNewParamVo = jobIdParamVoMap.get(mavenScannerNewJobId);
        PipelineJobBuildParamVo buildParamVo = jobIdParamVoMap.get(buildJobId);

        CustomBuildParamVo buildCustomBuildParamVo = new CustomBuildParamVo();
        List<String> buildKeys = KdevBuildParamsConstant.paramKeysMavenScannerCares();
        fillUpCustomBuildParamVo(buildKeys, buildParamVo, buildCustomBuildParamVo);

        CustomBuildParamVo mavenScannerNewBuildParamVo = new CustomBuildParamVo();
        fillUpCustomBuildParamVo(buildKeys, mavenScannerNewParamVo, mavenScannerNewBuildParamVo);

        if (StringUtils.isEmpty(mavenScannerNewBuildParamVo.getBuildModules())
                && StringUtils.isEmpty(mavenScannerNewBuildParamVo.getUserMavenArgs())
                && StringUtils.isEmpty(mavenScannerNewBuildParamVo.getBetaPackageVersion())) {
            return true;
        }
        if (!StringUtils.equals(mavenScannerNewBuildParamVo.getBuildModules(),
                buildCustomBuildParamVo.getBuildModules())) {
            return false;
        }
        if (!StringUtils.equals(mavenScannerNewBuildParamVo.getUserMavenArgs(),
                buildCustomBuildParamVo.getUserMavenArgs())) {
            return false;
        }
        if (!StringUtils.equals(mavenScannerNewBuildParamVo.getBetaPackageVersion(),
                buildCustomBuildParamVo.getBetaPackageVersion())) {
            return false;
        }
        return true;
    }

    private List<Long> batchGetJobIdsDependIds(List<Long> dependIds, Map<Long, List<Long>> jobIdDependIdsMap) {
        List<Long> jobIdsDependIds = Lists.newArrayList();
        for (Long dependId : dependIds) {
            jobIdsDependIds.addAll(jobIdDependIdsMap.get(dependId));
        }
        return jobIdsDependIds;
    }

    public String getSonarKey(int gitProjectId) {
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        if (checkRepo == null) {
            throw new ThemisException(-1, "gitProjectId not found");
        }
        if (checkRepo.getSonarKey().length() == 0) {
            throw new ThemisException(-1, "sonarProjectKey cannot be found by gitProjectId");
        }
        return checkRepo.getSonarKey();
    }

    private SonarPipelineMeasure getSonarPipelineMeasure(IssuesResp bugIssuesRes, IssuesResp codeSmellIssuesRes,
            IssuesResp vulnerabilityIssuesRes, HookPayload payload) {
        // 获取度量数据
        String bugMeasureJson = this.getMeasureJson(bugIssuesRes);
        String vulnerabilityMeasureJson = this.getMeasureJson(vulnerabilityIssuesRes);
        String codeSmellMeasureJson = this.getMeasureJson(codeSmellIssuesRes);
        // 入库数据封装
        SonarPipelineMeasure sonarPipelineMeasure = new SonarPipelineMeasure();
        sonarPipelineMeasure.setBugMeasure(bugMeasureJson);
        sonarPipelineMeasure.setCodeSmellMeasure(codeSmellMeasureJson);
        sonarPipelineMeasure.setVulnerabilityMeasure(vulnerabilityMeasureJson);
        sonarPipelineMeasure.setKspBuildId(Long.valueOf(payload.getProperties().getKspBuildId()));
        sonarPipelineMeasure.setKspPipelineId(Long.valueOf(payload.getProperties().getKspPipelineId()));
        sonarPipelineMeasure.setGmtModified(LocalDateTime.now());
        sonarPipelineMeasure.setGmtCreate(LocalDateTime.now());
        sonarPipelineMeasure.setGitProjectId(Integer.valueOf(payload.getProperties().getGitProjectId()));
        sonarPipelineMeasure.setBranch(payload.getBranch().getName());
        sonarPipelineMeasure.setProjectKey(payload.getProject().getKey());
        return sonarPipelineMeasure;
    }

    private String getMeasureJson(IssuesResp bugIssuesRes) {
        List<Facet> facets = bugIssuesRes.getFacets();
        // sonar返回的数据结构只有第一层 所以直接get就行
        Facet facet = facets.get(0);
        List<SonarValues> values = facet.getValues();
        SonarPipelineMeasureElement measureElement = new SonarPipelineMeasureElement();
        measureElement.setBlockerCount(getCountFromVal(values, SonarValuesEnum.BLOCKER.name()));
        measureElement.setCriticalCount(getCountFromVal(values, SonarValuesEnum.CRITICAL.name()));
        measureElement.setMajorCount(getCountFromVal(values, SonarValuesEnum.MAJOR.name()));
        return JSONUtils.serialize(measureElement);
    }

    private int getCountFromVal(List<SonarValues> values, String name) {
        return values.stream()
                .filter(o -> name.equalsIgnoreCase(o.getVal()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("获取sonar数据异常"))
                .getCount();
    }

    private IssuesResp getIssueResp(SonarCommonApi sonarCommonApi, String projectKey, String branch, String type) {
        SonarSearchRequest sonarSearchRequest = SonarSearchRequest.builder()
                .componentKeys(projectKey)
                .branch(branch)
                .resolved(false)
                .types(type)
                .facets("severities")
                .pageSize(1)
                .build();
        return sonarCommonApi.searchIssues(sonarSearchRequest);
    }

    private void sendRobotNotice(String markDownContent, String pipelineBuildUserName, List<String> robotIds) {
        AresMessageEntity aresMessageEntity = AresMessageEntity.builder()
                .msgTypes(Lists.newArrayList(6))
                .templateId(0)
                .userNames(Lists.newArrayList(""))
                .text(markDownContent)
                .robotParam(RobotParam.builder()
                        .robotIds(Lists.newArrayList(robotIds))
                        .mentionedUserIdList(Lists.newArrayList(pipelineBuildUserName))
                        .msgType("markdown")
                        .build())
                .build();
        MoreFunctions.runCatching(() -> {
            String result = aresApi.sendKimNotice(aresMessageEntity);
            log.info("kimApi custom hook ares send kim notice result is {}", result);
        });
    }

    private String getNoticeMarkDownContent(HookPayload payload, Map<String, Map<String, String>> metricSettingsMap) {
        String key = payload.getProject().getKey();
        Map<String, String> thresholdMap = metricSettingsMap.get("threshold");
        String metricsKey = String.join(",", thresholdMap.keySet());
        String analysedAt = payload.getAnalysedAt();
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ");
        Date date;
        try {
            date = sd.parse(analysedAt);
        } catch (ParseException e) {
            log.error("date time parse error", e);
            return null;
        }
        SonarCommonApi realSonarApi = sonarApiFactory.getRealSonarApi(payload.getServerUrl());
        HistoryMeasureResp historyMeasure = realSonarApi
                .getHistoryMeasure(key, metricsKey, sd.format(DateUtils.getDateOf0Clock(date, 30)), analysedAt);
        if (historyMeasure == null) {
            return null;
        }
        List<HistoryMeasures> measures = historyMeasure.getMeasures();
        Map<String, HistoryMeasures> metricKeyHistoryMap =
                measures.stream().collect(Collectors.toMap(HistoryMeasures::getMetric, Function.identity(), (existing, replacement) -> existing));
        List<String> contentList = Lists.newArrayList();
        firsLoop:
        for (Entry<String, String> entrySet : thresholdMap.entrySet()) {
            String metricKey = entrySet.getKey();
            BigDecimal preValue = new BigDecimal(0);
            BigDecimal afterValue = new BigDecimal(0);
            for (String metric : metricKey.split(",")) {
                HistoryMeasures historyMeasures = metricKeyHistoryMap.get(metric);
                if (historyMeasures == null) {
                    continue;
                }
                List<History> historyList = historyMeasures.getHistory();
                if (CollectionUtils.isEmpty(historyList)) {
                    continue;
                }
                historyList = historyList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(
                        () -> new TreeSet<>(Comparator.comparing(History::getDate))), ArrayList::new));
                historyList.sort((x, y) -> y.getDate().compareTo(x.getDate()));
                if (historyList.size() < 2) {
                    // 说明是第一次的 不进行比较
                    continue firsLoop;
                }
                try {
                    if (StringUtils.isEmpty(historyList.get(0).getValue())
                            || StringUtils.isEmpty(historyList.get(1).getValue())) {
                        continue;
                    }
                } catch (Exception e) {
                    log.error("StringUtils check value is error! history is {}", JSONUtils.serialize(historyList));
                    throw new RuntimeException(e);
                }
                BigDecimal bigDecimal0 = BigDecimal.valueOf(Double.parseDouble(historyList.get(0).getValue()));
                BigDecimal bigDecimal1 = BigDecimal.valueOf(Double.parseDouble(historyList.get(1).getValue()));
                preValue = preValue.add(bigDecimal1);
                afterValue = afterValue.add(bigDecimal0);
            }
            SonarMetric sonarMetric = sonarMetricService.getByMetric(metricKey);
            if (sonarMetric == null) {
                continue;
            }
            StringBuilder sb = new StringBuilder(sonarMetric.getMetricDesc());
            this.contrastResult(entrySet.getValue(), sb, preValue, afterValue, contentList);
        }
        if (CollectionUtils.isEmpty(contentList)) {
            return null;
        }
        StringBuilder sb = new StringBuilder("## [SONARQUBE]").append(String.join(",", contentList)).append("\n");
        sb.append("时间: ").append(DateTime.parse(payload.getAnalysedAt()).toString("yyyy-MM-dd HH:mm")).append("\n");
        sb.append("项目: ").append(payload.getProject().getName()).append("\n");
        sb.append("触发人: ").append(payload.getProperties().getPipelineBuildUserName()).append("\n");
        sb.append("[跳转链接](").append(payload.getBranch().getUrl()).append(")");
        return sb.toString();
    }

    private void contrastResult(String threshold, StringBuilder sb, BigDecimal preValue, BigDecimal afterValue,
            List<String> contentList) {
        boolean result = false;
        switch (threshold) {
            // 小于
            case "lt":
                if (afterValue.compareTo(preValue) < 0) {
                    sb.append("下降了");
                    result = true;
                }
                break;
            // 大于
            case "gt":
                if (afterValue.compareTo(preValue) > 0) {
                    sb.append("增加了");
                    result = true;
                }
                break;
            // 等于
            case "eq":
                if (afterValue.compareTo(preValue) == 0) {
                    sb.append("对比上次相等");
                    result = true;
                }
                break;
            // 不等于
            case "neq":
                if (afterValue.compareTo(preValue) != 0) {
                    sb.append("对比上次不相等");
                    result = true;
                }
                break;
            // 小于等于
            case "lte":
                if (afterValue.compareTo(preValue) <= 0) {
                    sb.append("下降了");
                    result = true;
                }
                break;
            // 大于等于
            case "gte":
                if (afterValue.compareTo(preValue) >= 0) {
                    sb.append("新增了");
                    result = true;
                }
                break;
            default:
                break;
        }
        if (result) {
            sb.append(afterValue.subtract(preValue).toString());
            contentList.add(sb.toString());
        }
    }

    private SonarConfig getGroupConfig(Integer gitProjectId) {
        List<Integer> orderGroupIds = gitOperations.getGroupsByProjectId(gitProjectId);
        List<SonarConfig> sonarConfigList = sonarConfigService.listInGroupIds(orderGroupIds);
        // 最近的group配置，依次来进行匹配
        if (CollectionUtils.isEmpty(sonarConfigList)) {
            return null;
        }
        Map<Integer, SonarConfig> groupIdSonarConfigMap = sonarConfigList.stream()
                .collect(Collectors.toMap(SonarConfig::getGitGroupId, Function.identity(), (existing, replacement) -> existing));
        // 获取最新的生效的配置
        for (Integer groupId : orderGroupIds) {
            SonarConfig sonarConfig = groupIdSonarConfigMap.get(groupId);
            if (sonarConfig != null) {
                return sonarConfig;
            }
        }
        return null;
    }

}
