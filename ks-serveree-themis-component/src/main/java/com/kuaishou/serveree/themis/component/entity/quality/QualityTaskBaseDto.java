package com.kuaishou.serveree.themis.component.entity.quality;

import javax.validation.constraints.NotNull;

import com.kuaishou.serveree.themis.component.constant.quality.JobTypeEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/29 8:08 下午
 * 质量任务分发的实体
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QualityTaskBaseDto {

    @NotNull
    private String repoUrl;

    private Integer repoId;

    @NotNull
    private String branch;

    private String kdevCommitId;

    /**
     * 只要是kdev过来的 都有这个东西
     */
    private String kdevBranchType;

    @NotNull
    private String commitId;

    private String params;

    @NotNull
    private String checkType;

    @NotNull
    private String jobType = JobTypeEnum.KSP_PIPELINE.name();

    private Long originKspPipelineId;

    private Long originKspBuildId;

    private Long originKspStepId;

    private String originKspName;

    private String mrId;

    private String localBuildId;

    private String kdevProjectId;

    /**
     * 用户指定的模块名称
     */
    private String module;

    private String kdevJavaPreCompileCommand;

    private String kdevJavaPreCompileInput;

    private Integer javaVersion;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserParams {

        /**
         * 用户maven参数
         */
        private String mvnOpts;

    }

}
