package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "Az2DataAllIpList对象", description = "")
public class Az2DataAllIpList implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "内网IP列表数组")
    private String ipLans;

    @ApiModelProperty(value = "主机名，格式：bjlt-h8179.sy，文档：https://docs.corp.kuaishou"
            + ".com/k/home/<USER>/fcAAr3Y4kC4giom5modD1Z1ID")
    private String hostname;

    @ApiModelProperty(value = "分区字段")
    private String pDate;


}
