package com.kuaishou.serveree.themis.component.entity.sonar.req;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/7/28 5:35 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MeasureComponentRequest extends SonarBaseRequest {

    private String additionalFields;
    private String component;
    private String metricKeys;

    private String target;

}
