package com.kuaishou.serveree.themis.component.entity.sonar.req.gitMetricRequest;

import com.kuaishou.serveree.themis.component.entity.sonar.req.GitCommonRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentRequest;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-05-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MeasureRequest extends GitCommonRequest {
    private String metricKeys;
    public MeasureComponentRequest getComponentRequest() {
        MeasureComponentRequest componentRequest = new MeasureComponentRequest();
        componentRequest.setMetricKeys(this.metricKeys);
        return componentRequest;
    }
}
