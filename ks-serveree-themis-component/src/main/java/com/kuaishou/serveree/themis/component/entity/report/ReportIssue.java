package com.kuaishou.serveree.themis.component.entity.report;

/**
 * <AUTHOR>
 * @since 2023/4/7 11:34 AM
 */
public class ReportIssue {


    private String location;
    private String type;
    private String status;
    private String message;
    private String ruleKey;
    private String severity;
    private String author;

    private Integer startLine;
    private Integer endLine;
    private Integer startOffset;
    private Integer endOffset;

    public ReportIssue() {
    }

    private ReportIssue(Builder builder) {
        setLocation(builder.location);
        setType(builder.type);
        setStatus(builder.status);
        setMessage(builder.message);
        setRuleKey(builder.ruleKey);
        setSeverity(builder.severity);
        setAuthor(builder.author);
        setStartLine(builder.startLine);
        setEndLine(builder.endLine);
        setStartOffset(builder.startOffset);
        setEndOffset(builder.endOffset);
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getRuleKey() {
        return ruleKey;
    }

    public void setRuleKey(String ruleKey) {
        this.ruleKey = ruleKey;
    }

    public String getSeverity() {
        return severity;
    }

    public void setSeverity(String severity) {
        this.severity = severity;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public Integer getStartLine() {
        return startLine;
    }

    public void setStartLine(Integer startLine) {
        this.startLine = startLine;
    }

    public Integer getEndLine() {
        return endLine;
    }

    public void setEndLine(Integer endLine) {
        this.endLine = endLine;
    }

    public Integer getStartOffset() {
        return startOffset;
    }

    public void setStartOffset(Integer startOffset) {
        this.startOffset = startOffset;
    }

    public Integer getEndOffset() {
        return endOffset;
    }

    public void setEndOffset(Integer endOffset) {
        this.endOffset = endOffset;
    }

    public static final class Builder {
        private String location;
        private String type;
        private String status;
        private String message;
        private String ruleKey;
        private String severity;
        private String author;
        private Integer startLine;
        private Integer endLine;
        private Integer startOffset;
        private Integer endOffset;

        private Builder() {
        }

        public Builder location(String val) {
            location = val;
            return this;
        }

        public Builder type(String val) {
            type = val;
            return this;
        }

        public Builder status(String val) {
            status = val;
            return this;
        }

        public Builder message(String val) {
            message = val;
            return this;
        }

        public Builder ruleKey(String val) {
            ruleKey = val;
            return this;
        }

        public Builder severity(String val) {
            severity = val;
            return this;
        }

        public Builder author(String val) {
            author = val;
            return this;
        }

        public Builder startLine(Integer val) {
            startLine = val;
            return this;
        }

        public Builder endLine(Integer val) {
            endLine = val;
            return this;
        }

        public Builder startOffset(Integer val) {
            startOffset = val;
            return this;
        }

        public Builder endOffset(Integer val) {
            endOffset = val;
            return this;
        }

        public ReportIssue build() {
            return new ReportIssue(this);
        }
    }
}
