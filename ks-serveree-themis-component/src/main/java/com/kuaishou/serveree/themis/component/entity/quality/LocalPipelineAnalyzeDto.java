package com.kuaishou.serveree.themis.component.entity.quality;

import java.util.List;

import com.kuaishou.serveree.themis.component.common.entity.Task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/3/30 3:02 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocalPipelineAnalyzeDto {

    /**
     * runenr调度执行的信息
     */
    private ExecutionArgs executionArgs;

    private String sshUrl;

    private String branch;

    private int gitProjectId;

    private List<Task> sponsorTasks;

}
