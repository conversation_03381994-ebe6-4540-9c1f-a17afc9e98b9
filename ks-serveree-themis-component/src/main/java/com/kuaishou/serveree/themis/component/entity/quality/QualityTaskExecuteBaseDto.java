package com.kuaishou.serveree.themis.component.entity.quality;

import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.constant.quality.CheckPriorityEnum;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/11/2 11:24 上午
 * 检查base对象
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QualityTaskExecuteBaseDto extends QualityTaskBaseDto {

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 1低优 2中优 3高优
     */
    private Integer priority;

    public static QualityTaskExecuteBaseDto convert(Task task, TaskConfig taskConfig) {
        QualityTaskExecuteBaseDto qualityTaskExecuteBaseDto = new QualityTaskExecuteBaseDto();
        qualityTaskExecuteBaseDto.setRepoUrl(task.getRepoUrl());
        qualityTaskExecuteBaseDto.setBranch(task.getBranch());
        qualityTaskExecuteBaseDto.setCommitId(task.getCommitId());
        qualityTaskExecuteBaseDto.setTaskId(task.getId());
        qualityTaskExecuteBaseDto.setParams(taskConfig.getExecutionParams());
        qualityTaskExecuteBaseDto.setPriority(CheckPriorityEnum.getPriorityNoByPriorityName(task.getPriority()));
        qualityTaskExecuteBaseDto.setCheckType(taskConfig.getExecutionType());
        return qualityTaskExecuteBaseDto;
    }
}
