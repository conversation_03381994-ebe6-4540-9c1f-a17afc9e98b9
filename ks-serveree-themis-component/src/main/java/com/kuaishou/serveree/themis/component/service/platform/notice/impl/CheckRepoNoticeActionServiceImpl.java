package com.kuaishou.serveree.themis.component.service.platform.notice.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.github.phantomthief.util.MoreFunctions;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoNotice;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoNoticeRecord;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.PlatformUserRoleRelation;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.CheckNoticeTypeEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformSendStatus;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueType;
import com.kuaishou.serveree.themis.component.entity.kim.IssueTransitionNoticeContext;
import com.kuaishou.serveree.themis.component.entity.scanner.ScannerSendKimContext;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoNoticeRecordService;
import com.kuaishou.serveree.themis.component.service.CheckRepoNoticeService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PlatformUserRoleRelationService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformProfileService;
import com.kuaishou.serveree.themis.component.service.platform.notice.CheckRepoNoticeActionService;
import com.kuaishou.serveree.themis.component.service.platform.notice.PlatformKimMsgSendService;
import com.kuaishou.serveree.themis.component.utils.DateUtils;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.NumberUtils;
import com.kuaishou.serveree.themis.component.utils.TransactionUtils;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.BooleanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/12/25 10:23
 */
@Service
@Slf4j
public class CheckRepoNoticeActionServiceImpl implements CheckRepoNoticeActionService {
    // 离线通知，最早发送时间，早上10点
    private static final Integer OFFLINE_NOTICE_SEND_START_HOUR = 10;
    // 离线通知，最晚发送时间，晚上10点
    private static final Integer OFFLINE_NOTICE_SEND_END_HOUR = 22;
    @Autowired
    private CheckRepoNoticeRecordService checkRepoNoticeRecordService;
    @Autowired
    private IssueSummaryService issueSummaryService;
    @Autowired
    private CheckRepoService checkRepoService;
    @Autowired
    private CheckRepoNoticeService checkRepoNoticeService;
    @Autowired
    private PlatformKimMsgSendService kimMsgSendService;
    @Autowired
    private PCheckBaseService pCheckBaseService;
    @Autowired
    private CheckRepoBranchService checkRepoBranchService;
    // 扫描结束消息通知模版
    private static final String USER_ISSUE_CNT_TEMPLATE = "%s存在 <font color='red'>%d</font> 个未处理问题，请前往[代码扫描平台](%s)进行治理。";
    private static final String SEND_TO_AUTHOR_PREFIX_TEMPLATE = "### 代码扫描通知——%s扫描通知%n项目 %s 于 %s 完成【%s】扫描。%n";
    private static final String SEND_TO_AUTHOR_TEMPLATE = SEND_TO_AUTHOR_PREFIX_TEMPLATE + USER_ISSUE_CNT_TEMPLATE;
    // 流水线扫描issue列表地址
    private static final String PIPELINE_ISSUE_LIST_URL =
            "%s/web/codescan/project/issueList?executionReferType=%s&kspBuildId=%s&source=2&statuses=OPEN,RE_OPEN";
    private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    @Value("${scan-platform.url}")
    private String platformUrl;

    @Autowired
    private PlatformProfileService platformProfileService;
    @Autowired
    private PlatformUserRoleRelationService platformUserRoleRelationService;

    @Resource
    private Executor asyncTaskExecutor;

    /**
     * 发送离线扫描通知
     */
    @Override
    public void sendOfflineScanNotice(CheckBase checkBase) {
        Long checkRepoId = checkBase.getCheckRepoId();
        CheckRepoNotice checkNotice = checkRepoNoticeService.getByCheckRepoIdAndNoticeType(checkRepoId,
                CheckNoticeTypeEnum.OFFLINE_NOTICE);
        // 未配置离线扫描通知
        if (Objects.isNull(checkNotice)) {
            return;
        }
        TransactionUtils.asyncAfterCommit(asyncTaskExecutor, () -> {
            log.info("[代码扫描消息通知]离线扫描通知，checkBaseId:{}", checkBase.getId());
            MoreFunctions.runCatching(() -> doSendOfflineScanNotice(checkBase, checkNotice));
        });
    }

    private void doSendOfflineScanNotice(CheckBase checkBase, CheckRepoNotice checkNotice) {
        CheckRepo checkRepo = checkRepoService.getById(checkNotice.getCheckRepoId());
        // 生成待发送的消息记录
        List<CheckRepoNoticeRecord> checkNoticeRecords =
                generateOfflineScanNoticeRecords(checkNotice, checkRepo, checkBase.getBranch());
        if (CollectionUtils.isEmpty(checkNoticeRecords)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        // 没到可以发送的时间
        if (now.getHour() < OFFLINE_NOTICE_SEND_START_HOUR) {
            // 保存，等待发送
            checkRepoNoticeRecordService.saveBatch(checkNoticeRecords);
            return;
        }
        // 已经过了最晚时间
        if (now.getHour() > OFFLINE_NOTICE_SEND_END_HOUR) {
            for (CheckRepoNoticeRecord checkNoticeRecord : checkNoticeRecords) {
                // 更新发送时间到第二天
                checkNoticeRecord.setSendTime(now.plusHours(12));
            }
            // 保存，等待发送
            checkRepoNoticeRecordService.saveBatch(checkNoticeRecords);
            return;
        }
        // 可以发送，发送并保存
        sendNoticeBatchAndSaveRecord(checkNoticeRecords);
    }

    /**
     * 发送流水线扫描通知
     */
    @Override
    public void sendPipelineScanNotice(ScannerSendKimContext context) {
        PCheckBase pCheckBase = context.getPCheckBase();
        Integer gitProject = pCheckBase.getProjectId();
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProject);
        if (Objects.isNull(checkRepo)) {
            log.warn("[流水线扫描通知发送失败]，无效的gitProjectId[{}]，p_base_id[{}]", gitProject, pCheckBase.getId());
            return;
        }
        CheckRepoNotice checkNotice = checkRepoNoticeService.getByCheckRepoIdAndNoticeType(checkRepo.getId(),
                CheckNoticeTypeEnum.PIPELINE_NOTICE);
        // 未配置流水线扫描通知
        if (Objects.isNull(checkNotice)) {
            return;
        }
        TransactionUtils.asyncAfterCommit(asyncTaskExecutor, () -> {
            log.info("[代码扫描消息通知]流水线扫描通知，pCheckBaseId:{}", pCheckBase.getId());
            MoreFunctions.runCatching(() -> doSendPipelineScanNotice(checkNotice, checkRepo, context));
        });
    }

    private void doSendPipelineScanNotice(CheckRepoNotice checkRepoNotice, CheckRepo checkRepo,
            ScannerSendKimContext context) {
        // 生成所有待发送的消息记录
        List<CheckRepoNoticeRecord> checkNoticeRecords =
                generatePipelineScanNoticeRecords(checkRepoNotice, checkRepo, context);
        if (CollectionUtils.isEmpty(checkNoticeRecords)) {
            return;
        }
        // 直接发送完了保存
        sendNoticeBatchAndSaveRecord(checkNoticeRecords);
    }

    /**
     * 发送issue打标通知
     */
    @Override
    public void sendIssueTransitionNotice(IssueTransitionNoticeContext context) {
        TransactionUtils.asyncAfterCommit(asyncTaskExecutor, () -> MoreFunctions.runCatching(() -> doSendIssueTransitionNotice(context)));
    }

    private void doSendIssueTransitionNotice(IssueTransitionNoticeContext context) {
        Integer gitProjectId = context.getGitProjectId();
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        CheckRepoNotice checkNotice = checkRepoNoticeService.getByCheckRepoIdAndNoticeType(checkRepo.getId(),
                CheckNoticeTypeEnum.ISSUE_TRANSITION_NOTICE);
        // 未配置issue打标通知
        if (Objects.isNull(checkNotice)) {
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        CheckRepoNoticeRecord checkNoticeRecord = new CheckRepoNoticeRecord();
        checkNoticeRecord.setCheckRepoId(checkNotice.getCheckRepoId());
        checkNoticeRecord.setBranch(getIssueTransitionNoticeBranch(context, checkRepo));
        checkNoticeRecord.setSendTime(now);
        checkNoticeRecord.setMsgContent(generateIssueTransitionNoticeContent(context, checkRepo));
        checkNoticeRecord.setReceivers(checkNotice.getReceivers());
        checkNoticeRecord.setGmtCreate(now);
        checkNoticeRecord.setKimRobot(checkNotice.getKimRobot());
        checkNoticeRecord.setSendStatus(PlatformSendStatus.WAITING.getStatus());
        sendNoticeBatchAndSaveRecord(List.of(checkNoticeRecord));
    }

    /**
     * 得到 issue打标，通知地址里面的分支
     */
    private String getIssueTransitionNoticeBranch(IssueTransitionNoticeContext context, CheckRepo checkRepo) {
        // 流水线扫描
        if (ScanModeEnum.PROCESS.equals(context.getScanMode())) {
            long kspBuildId = Objects.nonNull(context.getKspBuildId()) ? context.getKspBuildId() : 0L;
            PCheckBase pCheckBase = pCheckBaseService.getByBuildId(kspBuildId);
            if (Objects.isNull(pCheckBase)) {
                log.error("issue打标，获取分支失败。context：" + context);
                throw new ThemisException(ResultCodeConstant.INVALID_PARAMS.getCode(),
                        "无效的kspBuildId:" + context.getIssueId());
            }
            return pCheckBase.getBranch();
        }
        // 离线扫描
        CheckRepoBranch repoBranch = checkRepoBranchService.getOfflineScanBranchByRepoId(checkRepo.getId());
        return repoBranch.getBranchName();
    }

    /**
     * 构建issue打标通知消息内容
     */
    private String generateIssueTransitionNoticeContent(IssueTransitionNoticeContext context, CheckRepo checkRepo) {
        String operator = context.getOperator();
        String operateTime = DTF.format(LocalDateTime.now());
        String beforeStatus = CheckIssueStatus.valueOf(context.getBeforeStatus()).getDesc();
        String afterStatus = CheckIssueStatus.valueOf(context.getAfterStatus()).getDesc();
        CheckRepoBranch branch =
                checkRepoBranchService.getOfflineScanBranchByRepoId(checkRepo.getId());
        return String.format("### 代码扫描——Issue状态变更通知\n"
                        + "Issue状态改变通知\n"
                        + "项目：%s\n"
                        + "操作人：%s\n"
                        + "操作时间：%s\n"
                        + "状态流转：%s ——> %s\n"
                        + "issue详情：[点击跳转](%s)", GitUtils.getRepoName(checkRepo.getRepoUrl()), operator,
                operateTime, beforeStatus, afterStatus, getIssueDetailUrl(context, branch));
    }

    /**
     * 构建issue详情地址
     */
    private String getIssueDetailUrl(IssueTransitionNoticeContext context, CheckRepoBranch branch) {
        // 流水线模式
        if (ScanModeEnum.PROCESS.equals(context.getScanMode())) {
            return String.format(
                    "%s/web/codescan/project/issueDetail?issueId=%s&executionReferType=%s&kspBuildId=%s&source=2",
                    platformUrl, context.getIssueId(), context.getExecutionReferType(), context.getKspBuildId());
        }
        // 离线模式
        return String.format(
                "%s/web/codescan/project/issueDetail?issueId=%s&gitProjectId=%s&branch=%s&statuses=RE_OPEN,CLOSED,"
                        + "TO_REVIEW",
                platformUrl, context.getIssueId(), context.getGitProjectId(), branch.getBranchName());
    }

    /**
     * 补偿任务，发送 待发送的消息
     */
    @Override
    public void compensatePushNotice() {
        LocalDateTime now = LocalDateTime.now();
        // 没到可以发送的时间 or 已经过了最晚时间
        if (now.getHour() < OFFLINE_NOTICE_SEND_START_HOUR || now.getHour() > OFFLINE_NOTICE_SEND_END_HOUR) {
            return;
        }
        // 查询60条 今天的待发送通知
        List<CheckRepoNoticeRecord> needSendRecords = checkRepoNoticeRecordService.listTodayRecordsNeedToSend();
        if (CollectionUtils.isEmpty(needSendRecords)) {
            return;
        }
        log.info("[代码扫描消息通知补偿任务]，通知记录：{}", needSendRecords);
        // 发送
        sendNoticeBatchAndSaveRecord(needSendRecords);
    }

    /**
     * 生成本地离线扫描，所有通知记录
     */
    private List<CheckRepoNoticeRecord> generateOfflineScanNoticeRecords(CheckRepoNotice checkNotice,
            CheckRepo checkRepo, String branch) {
        LocalDateTime now = LocalDateTime.now();
        // 根据是否设置增量区间 确定查询的起始时间
        Integer interval = checkNotice.getIncrementInterval();
        long startTime = NumberUtils.isPositive(interval)
                         ? DateUtils.getMillTimestampFromLocalDateTime(LocalDateTimeUtil.beginOfDay(now.minusDays(interval)))
                         : 0;
        List<IssueSummary> openIssueSummaries = issueSummaryService.listOpenByProjectBranch(checkRepo.getGitProjectId(),
                branch, ScanModeEnum.OFFLINE.getCode(), startTime);
        if (CollectionUtils.isEmpty(openIssueSummaries)) {
            return Collections.emptyList();
        }
        List<CheckRepoNoticeRecord> checkNoticeRecords = Lists.newArrayList();
        // 需要通知具体作者
        if (checkNotice.getNoticeAuthor()) {
            // 生成个每个用户的通知内容
            Map<String, String> userContentMap =
                    generateOfflineNoticeAuthorContentMap(checkRepo, branch, openIssueSummaries);
            // 消息前缀
            String msgPrefix =
                    String.format(SEND_TO_AUTHOR_PREFIX_TEMPLATE, "离线", GitUtils.getRepoName(checkRepo.getRepoUrl()),
                            DTF.format(now), "离线");
            if (NumberUtils.isPositive(interval)) {
                msgPrefix = msgPrefix + "<font color='red'>" + interval + "</font>日内新扫出的问题中，";
            }
            // 构造发送给用户的内容
            String finalMsgPrefix = msgPrefix;
            userContentMap.forEach((key, val) ->
                    checkNoticeRecords.add(
                        generateNoticeRecordToIssueAuthor(checkNotice, branch, key, finalMsgPrefix + val
                    )
            ));
            // 将各作者的issue情况 拼接整合 发送到群里
            if (NumberUtils.isPositive(interval) && StringUtils.isNotBlank(checkNotice.getKimRobot())) {
                StringBuilder msg = new StringBuilder(msgPrefix);
                msg.append("各个用户需要解决的问题情况如下：\n");
                for (Entry<String, String> entry : userContentMap.entrySet()) {
                    msg.append("> ").append(entry.getValue()).append("\n");
                }
                // 需要通知的所有人 = 所有 issue 作者 + 配置的接收人 + 规则集维护者
                Set<String> users = Sets.newHashSet();
                users.addAll(userContentMap.keySet());
                if (StringUtils.isNotBlank(checkNotice.getReceivers())) {
                    users.addAll(Arrays.asList(checkNotice.getReceivers().split(",")));
                }
                // 获取规则集维护者
                Pair<String, String> profilePair =
                        platformProfileService.getProfilePairByGitProjectId(checkRepo.getGitProjectId());
                String offlineProfile = profilePair.getRight();
                List<String> profileEditors =
                        platformUserRoleRelationService.listProfileEditorsByProfileName(offlineProfile).stream()
                                .map(PlatformUserRoleRelation::getUserName).collect(Collectors.toList());
                users.addAll(profileEditors);
                // 生成消息
                checkNoticeRecords.add(
                        buildNoticeRecordToBeSent(
                                checkRepo.getId(), branch, "", checkNotice.getKimRobot(),
                                Joiner.on(',').join(users), msg.toString()
                        )
                );
            }
        }
        // 生成整体通知
        // 没有有效接收者，返回
        if (StringUtils.isBlank(checkNotice.getReceivers()) && StringUtils.isBlank(checkNotice.getKimRobot())) {
            return checkNoticeRecords;
        }
        // 生成离线扫描通知消息
        String offlineScanContent = generateOfflineScanNoticeContent(checkRepo, branch, openIssueSummaries, interval);
        checkNoticeRecords.add(generateFinishScanNoticeRecord(checkNotice, branch, offlineScanContent));
        return checkNoticeRecords;
    }

    /**
     * 得到issue聚合结果，<类型，<严重等级，数量>>
     */
    private String generateIssueStatisticString(List<IssueSummary> summaryList) {
        Map<String, Map<String, Integer>> typeSeverityCountMap = summaryList.stream()
                .collect(Collectors.groupingBy(IssueSummary::getType,
                        Collectors.groupingBy(IssueSummary::getSeverity, Collectors.summingInt(e -> 1))));
        StringBuilder sb = new StringBuilder();
        // bug类型
        String bugMessage =
                generateStringContentByIssueType(CheckIssueType.BUG.getType(), typeSeverityCountMap);
        if (StringUtils.isNotEmpty(bugMessage)) {
            sb.append(bugMessage).append("；");
        }
        // 漏洞类型
        String vulnerabilityMessage =
                generateStringContentByIssueType(CheckIssueType.VULNERABILITY.getType(), typeSeverityCountMap);
        if (StringUtils.isNotEmpty(vulnerabilityMessage)) {
            sb.append(vulnerabilityMessage);
        }
        // 先忽略坏味道
        String msg = sb.toString();
        return msg.isEmpty() ? "0" : msg;
    }

    /**
     * 每种问题数量，拼接字符串
     */
    private String generateStringContentByIssueType(String type,
            Map<String, Map<String, Integer>> typeSeverityCountMap) {
        Map<String, Integer> severityCountMap = typeSeverityCountMap.get(type);
        if (MapUtils.isEmpty(severityCountMap)) {
            return StringUtils.EMPTY;
        }
        String typeDesc = CheckIssueType.getEnumByType(type).getDesc();
        int blockerCount = severityCountMap.getOrDefault(CheckIssueSeverity.SERIOUS.getKey(), 0);
        int criticalCount = severityCountMap.getOrDefault(CheckIssueSeverity.MAJOR.getKey(), 0);
        int majorCount = severityCountMap.getOrDefault(CheckIssueSeverity.COMMON.getKey(), 0);
        StringBuilder sb = new StringBuilder();
        if (blockerCount > 0) {
            sb.append("阻断级别 ").append("<font color='red'>").append(blockerCount).append("</font> 个，");
        }
        if (criticalCount > 0) {
            sb.append("严重级别 ").append("<font color='red'>").append(criticalCount).append("</font> 个，");
        }
        if (majorCount > 0) {
            sb.append("主要级别 ").append("<font color='red'>").append(majorCount).append("</font> 个，");
        }
        if (sb.length() == 0) {
            return StringUtils.EMPTY;
        }
        // 去掉最后一个逗号
        sb.setLength(sb.length() - 1);
        return typeDesc + "：" + sb;
    }

    /**
     * 生成本地离线扫描通知的消息内容
     */
    private String generateOfflineScanNoticeContent(CheckRepo checkRepo, String branch, List<IssueSummary> openIssueSummaries,
            Integer incrementInterval) {
        // issue聚合结果
        String issueStatisticString = generateIssueStatisticString(openIssueSummaries);
        String repoName = GitUtils.getRepoName(checkRepo.getRepoUrl());
        String time = DTF.format(LocalDateTime.now());
        String platformRepoUrl =
                String.format("%s/web/codescan/project/detail?gitProjectId=%s&branch=%s",
                        platformUrl, checkRepo.getGitProjectId(), branch);
        String template = "### 代码扫描通知——离线扫描通知\n"
                + "离线扫描结束，请前往[代码扫描平台](%s)查看最新扫描结果。\n"
                + "项目名称：%s\n"
                + "分支名称：%s\n"
                + "完成时间：%s\n"
                + "存留问题：%s";
        String msg = String.format(template, platformRepoUrl, repoName, branch, time, issueStatisticString);
        // 补充增量内容
        if (NumberUtils.isPositive(incrementInterval)) {
            return String.format("%s%n<b>注：当前项目开启了增量统计，统计范围是<font color='red'>%s</font>天内扫出的问题</b>", msg, incrementInterval);
        }
        return msg;
    }

    /**
     * 生成本次离线扫描，给每个作者的消息 Map
     */
    private Map<String, String> generateOfflineNoticeAuthorContentMap(CheckRepo checkRepo, String branch,
            List<IssueSummary> openIssueSummaries) {
        Map<String, String> userContentMap = Maps.newHashMap();
        Map<String, List<IssueSummary>> authorIssueSummariesMap =
                openIssueSummaries.stream().collect(Collectors.groupingBy(IssueSummary::getAuthor));
        for (Entry<String, List<IssueSummary>> entry : authorIssueSummariesMap.entrySet()) {
            String author = entry.getKey();
            String sb = String.format(USER_ISSUE_CNT_TEMPLATE,
                    author,
                    entry.getValue().size(),
                    String.format("%s/web/codescan/project/issueList?gitProjectId=%s&branch=%s&authors=%s",
                            platformUrl, checkRepo.getGitProjectId(), branch, author));
            userContentMap.put(author, sb);
        }
        return userContentMap;
    }

    /**
     * 生成本次流水线扫描所有要发送的消息记录
     */
    private List<CheckRepoNoticeRecord> generatePipelineScanNoticeRecords(CheckRepoNotice checkNotice,
            CheckRepo checkRepo,
            ScannerSendKimContext context) {
        // 查询本次流水线扫描所有未解决问题
        PCheckBase pCheckBase = context.getPCheckBase();
        List<PCheckIssue> pCheckIssueList = context.getPCheckIssueList();
        List<IssueSummary> summaryList =
                issueSummaryService.listOpenByProjectBranchAndIssueUniqIds(pCheckBase.getProjectId(),
                        pCheckBase.getBranch(), ScanModeEnum.PROCESS.getCode(),
                        pCheckIssueList.stream().map(PCheckIssue::getIssueUniqId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(summaryList)) {
            return Collections.emptyList();
        }
        List<CheckRepoNoticeRecord> checkNoticeRecords = Lists.newArrayList();
        // 需要通知issue作者
        if (checkNotice.getNoticeAuthor()) {
            Map<String, String> userContentMap =
                    generatePipelineNoticeAuthorContentMap(summaryList, checkRepo, context);
            userContentMap.forEach((key, val) -> {
                checkNoticeRecords.add(
                        generateNoticeRecordToIssueAuthor(checkNotice, pCheckBase.getBranch(), key, val)
                );
            });
        }
        // 没有有效接收者，返回
        if (StringUtils.isBlank(checkNotice.getKimRobot()) && StringUtils.isBlank(checkNotice.getReceivers())) {
            return checkNoticeRecords;
        }
        // issue聚合结果
        String issueStatisticString = generateIssueStatisticString(summaryList);
        // 生成流水线扫描通知消息
        String pipelineScanContent = generatePipelineScanNoticeContent(context, issueStatisticString);
        CheckRepoNoticeRecord checkNoticeRecord =
                generateFinishScanNoticeRecord(checkNotice, pCheckBase.getBranch(), pipelineScanContent);
        checkNoticeRecords.add(checkNoticeRecord);
        return checkNoticeRecords;
    }

    /**
     * 生成本次流水线扫描通知的消息内容
     */
    private String generatePipelineScanNoticeContent(ScannerSendKimContext context, String issueStatisticString) {
        PCheckBase pCheckBase = context.getPCheckBase();
        PCheckExecution pCheckExecution = context.getPCheckExecution();
        String kimNoticeUrl = getPipelineIssueListUrl(context);
        Boolean incrementMode = pCheckExecution.getIncrementMode();
        StringBuilder sb = new StringBuilder("### 代码扫描——流水线扫描通知").append("\n");
        sb.append("项目: ").append(GitUtils.getRepoName(pCheckBase.getRepoUrl())).append("\n");
        sb.append("分支: ").append(pCheckBase.getBranch()).append("\n");
        sb.append("扫描模式: ").append(BooleanUtil.isTrue(incrementMode) ? "增量" : "全量").append("\n");
        sb.append("触发人: ").append(pCheckBase.getSponsor()).append("\n");
        sb.append("问题：").append(issueStatisticString).append("\n");
        sb.append("扫描结果：[跳转链接](").append(kimNoticeUrl).append(")");
        return sb.toString();
    }

    /**
     * 生成本次流水线扫描给issue作者的消息内容 Map
     */
    private Map<String, String> generatePipelineNoticeAuthorContentMap(List<IssueSummary> openIssueSummaries,
            CheckRepo checkRepo,
            ScannerSendKimContext context) {
        Map<String, String> userContentMap = Maps.newHashMap();
        Map<String, List<IssueSummary>> authorIssueSummariesMap = openIssueSummaries.stream()
                .collect(Collectors.groupingBy(IssueSummary::getAuthor));
        LocalDateTime now = LocalDateTime.now();
        String authorIssuesUrl = getPipelineIssueListUrl(context) + "&authors=%s";
        for (Entry<String, List<IssueSummary>> entry : authorIssueSummariesMap.entrySet()) {
            String author = entry.getKey();
            String sb = String.format(SEND_TO_AUTHOR_TEMPLATE,
                    "流水线",
                    GitUtils.getRepoName(checkRepo.getRepoUrl()),
                    DTF.format(now),
                    "流水线",
                    author,
                    entry.getValue().size(),
                    String.format(authorIssuesUrl, author));
            userContentMap.put(author, sb);
        }
        return userContentMap;
    }

    /**
     * 得到流水线模式，issue列表页面地址
     */
    private String getPipelineIssueListUrl(ScannerSendKimContext context) {
        PCheckBase pCheckBase = context.getPCheckBase();
        PCheckExecution pCheckExecution = context.getPCheckExecution();
        Integer referType = pCheckExecution.getReferType();
        return String.format(PIPELINE_ISSUE_LIST_URL, platformUrl, referType, pCheckBase.getKspBuildId());
    }

    /**
     * 生成待发送的扫描通知消息记录
     */
    private CheckRepoNoticeRecord generateFinishScanNoticeRecord(CheckRepoNotice checkRepoNotice, String branch,
            String msgContent) {
        return buildNoticeRecordToBeSent(
                checkRepoNotice.getCheckRepoId(),
                branch,
                checkRepoNotice.getReceivers(),
                checkRepoNotice.getKimRobot(),
                msgContent
        );
    }

    /**
     * 生成待发送的通知到具体作者的消息记录
     */
    private CheckRepoNoticeRecord generateNoticeRecordToIssueAuthor(CheckRepoNotice checkNotice, String branch,
            String author, String msgContent) {
        return buildNoticeRecordToBeSent(
                checkNotice.getCheckRepoId(),
                branch,
                author,
                StringUtils.EMPTY,
                msgContent
        );
    }

    /**
     * 构造消息记录
     */
    private CheckRepoNoticeRecord buildNoticeRecordToBeSent(long checkRepoId, String branch,
            String receivers, String kimRobot, String msgContent) {
        return buildNoticeRecordToBeSent(checkRepoId, branch, receivers, kimRobot, "", msgContent);
    }

    private CheckRepoNoticeRecord buildNoticeRecordToBeSent(long checkRepoId, String branch,
            String receivers, String kimRobot, String mentionedUsers, String msgContent) {
        LocalDateTime now = LocalDateTime.now();
        CheckRepoNoticeRecord checkNoticeRecord = new CheckRepoNoticeRecord();
        checkNoticeRecord.setCheckRepoId(checkRepoId);
        checkNoticeRecord.setBranch(branch);
        checkNoticeRecord.setSendTime(now);
        checkNoticeRecord.setGmtCreate(now);
        checkNoticeRecord.setMsgContent(msgContent);
        checkNoticeRecord.setReceivers(receivers);
        checkNoticeRecord.setKimRobot(kimRobot);
        checkNoticeRecord.setMentionedUsers(mentionedUsers);
        checkNoticeRecord.setSendStatus(PlatformSendStatus.WAITING.getStatus());
        return checkNoticeRecord;
    }

    /**
     * 批量发送并保存消息记录
     */
    private void sendNoticeBatchAndSaveRecord(List<CheckRepoNoticeRecord> checkNoticeRecords) {
        if (CollectionUtils.isEmpty(checkNoticeRecords)) {
            return;
        }
        // 发送
        for (CheckRepoNoticeRecord checkNoticeRecord : checkNoticeRecords) {
            kimMsgSendService.sendMarkdownMsgToKimRobotAndUsers(
                    checkNoticeRecord.getKimRobot(),
                    checkNoticeRecord.getMsgContent(),
                    StringUtils.isBlank(checkNoticeRecord.getReceivers())
                    ? Collections.emptySet() : Sets.newHashSet(checkNoticeRecord.getReceivers().split(",")),
                    StringUtils.isBlank(checkNoticeRecord.getMentionedUsers())
                    ? Collections.emptySet() : Sets.newHashSet(checkNoticeRecord.getMentionedUsers().split(",")));
        }
        // 发送记录保存下来
        for (CheckRepoNoticeRecord checkNoticeRecord : checkNoticeRecords) {
            // 更新发送状态
            checkNoticeRecord.setSendStatus(PlatformSendStatus.SENT.getStatus());
        }
        // 保存发送记录
        checkRepoNoticeRecordService.saveOrUpdateBatch(checkNoticeRecords);
    }
}