package com.kuaishou.serveree.themis.component.client.nexus;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.entity.nexus.NexusMetaData;
import com.kuaishou.serveree.themis.component.entity.report.DependencyDTO;
import com.kuaishou.serveree.themis.component.utils.NexusUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/5/21 11:35 上午
 * <p>
 * http://nexus.corp.kuaishou.com:88/nexus/content/groups/public/acfun/acfun-admin-webservice-parent/2.0
 * .10/acfun-admin-webservice-parent-2.0.10.pom
 */
@Component
@Slf4j
public class NexusApi {

    @Value("${nexus.url}")
    private String url;

    @Value("${nexus.timeout}")
    private int timeout;

    @Autowired
    private KsRedisClient ksRedisClient;

    public long getArtifactLength(DependencyDTO dependencyDTO) {
        Preconditions.checkArgument(Objects.nonNull(dependencyDTO), "dependencyDTO must not be null");
        String groupId = dependencyDTO.getGroupId();
        String artifactId = dependencyDTO.getArtifactId();
        String version = dependencyDTO.getVersion();
        String type = dependencyDTO.getType();
        final String redisKey = KsRedisPrefixConstant.DEPENDENCY_LENGTH_PREFIX + groupId + ":" + artifactId
                + ":" + version + ":" + type;
        String redisVal = ksRedisClient.sync().get(redisKey);
        if (StringUtils.isNotEmpty(redisVal)) {
            return NumberUtils.toInt(redisVal);
        }
        Preconditions.checkArgument(StringUtils.isNotEmpty(groupId), "groupId need value");
        Preconditions.checkArgument(StringUtils.isNotEmpty(artifactId), "artifactId need value");
        Preconditions.checkArgument(StringUtils.isNotEmpty(version), "version need value");
        Preconditions.checkArgument(StringUtils.isNotEmpty(type), "type need value");
        groupId = groupId.replace(".", "/");
        String realVersion = version;
        if (version.toUpperCase().contains("-SNAPSHOT")) {
            // 先找出最新的版本的数据的maven-metadata数据
            String requestUrl = String.format("%s/nexus/content/groups/public/%s/%s/%s/maven-metadata.xml",
                    url, groupId, artifactId, version);
            HttpResponse httpResponse = HttpRequest.get(requestUrl).timeout(timeout).execute();
            if (!httpResponse.isOk()) {
                return 0;
            }
            Map<String, Object> result = Maps.newHashMap();
            XmlUtil.xmlToMap(httpResponse.body(), result);
            realVersion = NexusUtils.getSnapshotRealVersion(BeanUtil.toBean(result, NexusMetaData.class));
        }
        String requestUrl = String.format("%s/nexus/content/groups/public/%s/%s/%s/%s-%s.%s", url,
                groupId, artifactId, version, artifactId, realVersion, type);
        HttpResponse httpResponse = HttpRequest.head(requestUrl).execute();
        if (!httpResponse.isOk()) {
            return 0;
        }
        int length = NumberUtils.toInt(httpResponse.header("Content-Length"), 0);
        if (length != 0) {
            ksRedisClient.sync().setex(redisKey, TimeUnit.HOURS.toSeconds(6), String.valueOf(length));
        }
        return length;
    }

}
