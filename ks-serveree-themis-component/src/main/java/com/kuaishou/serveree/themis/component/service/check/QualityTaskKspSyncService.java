package com.kuaishou.serveree.themis.component.service.check;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.client.kdev.KdevApi;
import com.kuaishou.serveree.themis.component.common.entity.MrStuckRecord;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.constant.kdev.KdevPipelineJobStatusEnum;
import com.kuaishou.serveree.themis.component.constant.quality.JobTypeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.service.MrStuckRecordService;
import com.kuaishou.serveree.themis.component.service.QualityCheckService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.service.kdev.MrStuckPointService;
import com.kuaishou.serveree.themis.component.utils.DateUtils;
import com.kuaishou.serveree.themis.component.vo.request.kdev.KdevPipelineCallbackRequest.KdevPipelineBuildInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/11/14 5:08 下午
 */
@Component
@Slf4j
public class QualityTaskKspSyncService {

    @Resource
    private ThreadPoolExecutor kspSponsorExecutor;

    @Resource
    private ThreadPoolExecutor kspCheckResultExecutor;

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskConfigService taskConfigService;

    @Autowired
    private QualityCheckService qualityCheckService;

    @Resource
    private MrStuckRecordService mrStuckRecordService;

    @Resource
    private MrStuckPointService mrStuckPointService;

    @Resource
    private KdevApi kdevApi;

    public void kdevPipelineSync() {
        kdevPipelineSync(KdevPipelineJobStatusEnum.RUNNING);
    }

    private void kdevPipelineSync(KdevPipelineJobStatusEnum statusEnum) {
        // 状态5min未更新的记录
        long timestamp = DateUtils.getMillTimestampFromLocalDateTime(LocalDateTime.now().minusMinutes(5));
        List<MrStuckRecord> mrStuckRecords =
                mrStuckRecordService.listRecordsByStatusAndUpdatedBefore(statusEnum.getCode(), timestamp);
        if (CollectionUtils.isEmpty(mrStuckRecords)) {
            log.info(String.format("Empty kdev pipeline records[status='%s'], finish scan", statusEnum.getDesc()));
            return;
        }
        // 如果是同步执行中的任务状态
        if (KdevPipelineJobStatusEnum.RUNNING.equals(statusEnum)) {
            for (MrStuckRecord stuckRecord : mrStuckRecords) {
                // 调用kdev接口，获取执行状态
                KdevPipelineBuildInfo pipelineBuildInfo = kdevApi.getPipelineStatus(stuckRecord.getKdevBuildId());
                if (Objects.isNull(pipelineBuildInfo) || Objects.isNull(pipelineBuildInfo.getKspBuildId())) {
                    continue;
                }
                // 执行成功或失败
                if (pipelineBuildInfo.getStatus() == KdevPipelineJobStatusEnum.SUCCESS.getCode()
                        || pipelineBuildInfo.getStatus() == KdevPipelineJobStatusEnum.FAIL.getCode()) {
                    mrStuckPointService.finishKdevPipelineExecute(pipelineBuildInfo);
                }
            }
        }
        // 如果是同步其他状态
    }


    /**
     * 同步ksp pipeline运行数据
     */
    public void kspPipelineSync() {

        // 同步waiting数据
        this.syncKspPipelineData(TaskStatusEnum.WAITING.name());

        // 同步executing数据
        this.syncKspPipelineData(TaskStatusEnum.EXECUTING.name());

        // 同步created数据
        this.syncKspPipelineData(TaskStatusEnum.CREATED.name());

    }

    private void syncKspPipelineData(String taskStatusName) {
        List<Task> taskList = taskService.listTasksByCondition(
                Task.builder().taskStatus(taskStatusName).compensateTab(true).build()
        );
        if (CollectionUtils.isEmpty(taskList)) {
            log.info("{} status taskList is empty,skip this scan", taskStatusName);
            return;
        }
        List<Long> allTaskIds = taskList.stream().map(Task::getId).collect(Collectors.toList());
        List<TaskConfig> taskConfigList = taskConfigService.listTaskConfigsByCondition(
                TaskConfig.builder().jobType(JobTypeEnum.KSP_PIPELINE.name()).build(),
                allTaskIds
        );
        List<Long> needTaskIds = taskConfigList.stream().map(TaskConfig::getTaskId).collect(Collectors.toList());
        // 生成map
        Map<Long, Task> taskIdTaskMap = taskList.stream().collect(Collectors.toMap(Task::getId, Function.identity(), (existing, replacement) -> existing));
        Map<Long, TaskConfig> taskIdConfigMap = taskConfigList.stream().collect(
                Collectors.toMap(TaskConfig::getTaskId, Function.identity(), (existing, replacement) -> existing)
        );
        CountDownLatch countDownLatch = new CountDownLatch(needTaskIds.size());
        needTaskIds.forEach(taskId -> {
            Task task = taskIdTaskMap.get(taskId);
            TaskConfig taskConfig = taskIdConfigMap.get(taskId);
            if (TaskStatusEnum.WAITING.name().equals(taskStatusName)) {
                kspSponsorExecutor.execute(() -> {
                    try {
                        qualityCheckService.sponsorKspPipeline(task, taskConfig);
                    } catch (Exception e) {
                        log.error("qualityCheckService sponsorKspPipeline error,task is {}", task, e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            } else if (TaskStatusEnum.EXECUTING.name().equals(taskStatusName) || TaskStatusEnum.CREATED.name()
                    .equals(taskStatusName)) {
                kspCheckResultExecutor.execute(() -> {
                    try {
                        qualityCheckService.syncKspPipelineResult(task, taskConfig);
                    } catch (Exception e) {
                        log.error("qualityCheckService syncKspPipelineResult error,task is {}", task, e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
        });
        try {
            countDownLatch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("QualityTaskKspSyncService syncKspPipelineData countDownLatch await exception", e);
            Thread.currentThread().interrupt();
        }
    }
}
