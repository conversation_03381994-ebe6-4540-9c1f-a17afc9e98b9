package com.kuaishou.serveree.themis.component.service.statics.impl;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.TASK_NOT_EXIST;
import static com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant.INTERFACE_SCAN_LOCK;
import static org.apache.commons.lang3.StringUtils.isEmpty;
import static org.apache.commons.lang3.StringUtils.isNotEmpty;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.gitlab.api.GitlabAPI;
import org.gitlab.api.models.GitlabProject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.git.SelfGitApi;
import com.kuaishou.serveree.themis.component.client.kdev.KdevApi;
import com.kuaishou.serveree.themis.component.common.entity.ScanCase;
import com.kuaishou.serveree.themis.component.common.entity.ScanFilter;
import com.kuaishou.serveree.themis.component.common.entity.ScanIssue;
import com.kuaishou.serveree.themis.component.common.entity.ScanPlan;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.kdev.ScanSponsorChannel;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.constant.statics.ScanCaseType;
import com.kuaishou.serveree.themis.component.constant.statics.ScanFilterType;
import com.kuaishou.serveree.themis.component.entity.kdev.CodeElement;
import com.kuaishou.serveree.themis.component.entity.kdev.SearchRequest;
import com.kuaishou.serveree.themis.component.entity.kdev.SearchResponse;
import com.kuaishou.serveree.themis.component.entity.statics.InterfaceScanIssueExcelMeta;
import com.kuaishou.serveree.themis.component.entity.statics.KconfCaseExtend;
import com.kuaishou.serveree.themis.component.entity.statics.ScanContext;
import com.kuaishou.serveree.themis.component.entity.statics.ScanFilterConfig;
import com.kuaishou.serveree.themis.component.entity.statics.ScanGroupProject;
import com.kuaishou.serveree.themis.component.entity.statics.ScanIssueExcelMeta;
import com.kuaishou.serveree.themis.component.entity.statics.ScanProject;
import com.kuaishou.serveree.themis.component.entity.statics.ScanProjectInfo;
import com.kuaishou.serveree.themis.component.entity.statics.ScanProjectInfo.ScanGroupInfo;
import com.kuaishou.serveree.themis.component.entity.statics.UserScanProjectInfo;
import com.kuaishou.serveree.themis.component.service.ScanCaseService;
import com.kuaishou.serveree.themis.component.service.ScanFilterService;
import com.kuaishou.serveree.themis.component.service.ScanIssueService;
import com.kuaishou.serveree.themis.component.service.ScanPlanService;
import com.kuaishou.serveree.themis.component.service.statics.LocalStringMatchScanService;
import com.kuaishou.serveree.themis.component.service.statics.StaticsScanner;
import com.kuaishou.serveree.themis.component.service.statics.handler.CustomCellWriteHandler;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.SponsorCodeScanRequest;
import com.kuaishou.serveree.themis.component.vo.response.CodeScanPlanInfo;
import com.kuaishou.serveree.themis.component.vo.response.kdev.SponsorCodeScanResponse;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/10/29 4:32 下午
 */
@Service
@Slf4j
public class LocalStringMatchScanServiceImpl implements LocalStringMatchScanService {

    private static final String KUAISHOU_URL_PREFIX = "https://git.corp.kuaishou.com/";

    @Autowired
    private SelfGitApi selfGitApi;

    @Autowired
    private ScanPlanService scanPlanService;

    @Autowired
    private ScanFilterService scanFilterService;

    @Autowired
    private GitlabAPI gitlabApi;

    @Autowired
    private StaticsScanner staticsScanner;

    @Autowired
    private ScanIssueService scanIssueService;

    @Autowired
    private ScanCaseService scanCaseService;

    @Resource
    private ThreadPoolExecutor stringMatchExecutor;

    @Resource
    private ThreadPoolExecutor codeMatchExecutor;
    @Autowired
    private KsRedisLock ksRedisLock;

    @Autowired
    private KdevApi kdevApi;

    @Autowired
    private GitOperations gitOperations;

    private static final Integer PAGE_SIZE = 500;

    private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    @Value("${kdev.url}")
    private String kdevUrl;

    private static final String EXCEL_DOWNLOAD_URL =
            "%s/api/quality/scan/download/interface/scanIssue/excel?scanPlanId=%s";

    /**
     * 检查
     */
    @Override
    public void startUp() {
        List<ScanPlan> scanPlans = scanPlanService.listActivePlans();
        if (CollectionUtils.isEmpty(scanPlans)) {
            return;
        }
        // 数据比较少比较少 直接load出来全部数据
        List<ScanCase> allCaseList = scanCaseService.list();
        for (ScanPlan scanPlan : scanPlans) {
            Long scanPlanId = scanPlan.getId();
            List<ScanFilter> scanFilters = scanFilterService.listByScanPlanId(scanPlanId);
            ScanFilterConfig scanFilterConfig = this.getScanFilterConfig(scanFilters);
            String projectInfoStr = scanPlan.getProjectInfo();
            if (isEmpty(projectInfoStr)) {
                continue;
            }
            ScanProjectInfo scanProjectInfo = JSONUtils.deserialize(projectInfoStr, ScanProjectInfo.class);
            if (scanProjectInfo == null) {
                continue;
            }
            List<Integer> scanTypes = JSONUtils.deserializeList(scanPlan.getScanCaseTypes(), Integer.class);
            List<ScanCase> scanCases = allCaseList.stream()
                    .filter(scanCase -> scanTypes.contains(scanCase.getCaseType()))
                    .collect(Collectors.toList());
            List<ScanGroupProject> scanGroupProjects = this.filterScanGroupProjects(scanProjectInfo, scanPlan);
            List<ScanProject> scanProjectList = getNeedScanProjects(scanGroupProjects);
            CountDownLatch countDownLatch = new CountDownLatch(scanProjectList.size());
            scanProjectList.forEach(scanProject -> stringMatchExecutor.execute(() -> {
                try {
                    ScanContext scanContext = ScanContext.builder()
                            .scanFilterConfig(scanFilterConfig)
                            .scanProject(scanProject)
                            .scanCaseList(scanCases)
                            .build();
                    staticsScanner.scan(scanContext);
                } catch (Exception e) {
                    log.error("stringMatchExecutor async execute error", e);
                } finally {
                    countDownLatch.countDown();
                }
            }));
            try {
                countDownLatch.await(30, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("stringMatchExecutor foreach countDownLatch await exception", e);
                Thread.currentThread().interrupt();
            }
        }

    }

    private List<ScanProject> getNeedScanProjects(List<ScanGroupProject> scanGroupProjects) {

        Map<Integer, ScanProject> projectMap = Maps.newHashMap();
        for (ScanGroupProject scanGroupProject : scanGroupProjects) {
            List<ScanProject> scanProjects = scanGroupProject.getScanProjects();
            if (CollectionUtils.isEmpty(scanProjects)) {
                continue;
            }
            for (ScanProject scanProject : scanProjects) {
                projectMap.put(scanProject.getProjectId(), scanProject);
            }
        }
        return Lists.newArrayList(projectMap.values());
    }

    /**
     * 下载
     */
    @Override
    public void downloadScanIssueExcel(HttpServletResponse response) throws IOException {
        setResponseInfo(response);
        List<ScanPlan> scanPlans = scanPlanService.listActivePlans();
        // 注册一个handler
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellWriteHandler())
                .build();
        HashMap<Integer, Integer> projectMap = Maps.newHashMap();
        // 一个plan一个sheet
        for (int i = 0; i < scanPlans.size(); i++) {
            ScanPlan scanPlan = scanPlans.get(i);
            // plan的定制化的 project等信息
            String projectInfo = scanPlan.getProjectInfo();
            // 转化
            ScanProjectInfo scanProjectInfo = JSONUtils.deserialize(projectInfo, ScanProjectInfo.class);
            WriteSheet writeSheet = EasyExcel.writerSheet(i + 1, scanProjectInfo.getLogicGroupName())
                    .head(ScanIssueExcelMeta.class)
                    .build();
            // 根据plan id获取问题
            List<ScanIssue> scanIssues = scanIssueService.listByPlanId(scanPlan.getId());
            // 解决团队内多个组的project重复问题：一个project一个团队内只能跟一个group
            scanIssues.removeIf(scanIssue -> {
                Integer groupId = scanIssue.getGroupId();
                Integer projectId = scanIssue.getProjectId();
                if (projectMap.containsKey(projectId)) {
                    return !projectMap.get(projectId).equals(groupId);
                } else {
                    projectMap.put(projectId, groupId);
                    return false;
                }
            });
            List<ScanIssueExcelMeta> excelMetaList = convertIssue2Meta(scanIssues, scanProjectInfo);
            excelMetaList.sort(Comparator.comparing(ScanIssueExcelMeta::getBusinessName)
                    .thenComparing(ScanIssueExcelMeta::getGroupId).thenComparing(ScanIssueExcelMeta::getRepoUrl)
                    .thenComparing(ScanIssueExcelMeta::getCaseType));
            excelWriter.write(excelMetaList, writeSheet);
        }
        excelWriter.finish();
    }

    @Override
    public SponsorCodeScanResponse sponsorCodeScan(SponsorCodeScanRequest request) {
        Assert.notEmpty(request.getUserName(), () -> new ThemisException(-1, "userName不能为空"));
        Assert.notEmpty(request.getScanCases(), () -> new ThemisException(-1, "scanCases不能为空"));
        if (!request.isAllProjects()) {
            Assert.isTrue(isNotEmpty(request.getGitProjectIds()) || isNotEmpty(request.getGitGroupIds()),
                    () -> new ThemisException(-1, "gitProjectIds与gitGroupIds不能同时为空")
            );
        }
        final String redisKey = INTERFACE_SCAN_LOCK + request;
        boolean lock = ksRedisLock.lock(redisKey, TimeUnit.MINUTES.toMillis(5));
        if (!lock) {
            throw new ThemisException(-1, "相同任务不要重复提交，间隔五分钟后重试");
        }
        ScanPlan scanPlan = generateScanPlan(request);
        scanPlanService.save(scanPlan);
        return SponsorCodeScanResponse.newBuilder()
                .scanPlanId(scanPlan.getId())
                .excelDownloadUrl(String.format(EXCEL_DOWNLOAD_URL, kdevUrl, scanPlan.getId()))
                .build();
    }

    private ScanPlan generateScanPlan(SponsorCodeScanRequest request) {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setApplicant(request.getUserName());
        scanPlan.setScanCases(request.getScanCases());
        scanPlan.setSponsorChannel(ScanSponsorChannel.INTERFACE.getChannel());
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        scanPlan.setProjectInfo("");
        scanPlan.setUserProjectInfo(JSONUtils.serialize(this.getUserProjectInfo(request)));
        return scanPlan;
    }

    @Override
    public void scanInterfacePlans() {
        List<ScanPlan> scanPlans = scanPlanService.listInterfaceActivePlans();
        for (ScanPlan scanPlan : scanPlans) {
            // 每个任务锁10分钟
            String redisKey = KsRedisPrefixConstant.INTERFACE_SCAN_PLAN + ":" + scanPlan.getId();
            boolean lock = ksRedisLock.lock(redisKey, TimeUnit.MINUTES.toMillis(10));
            if (!lock) {
                continue;
            }
            CompletableFuture.runAsync(() -> {
                try {
                    deleteScanPlanIssues(scanPlan);
                    UserScanProjectInfo userScanProjectInfo = JSONUtils.deserialize(scanPlan.getUserProjectInfo(),
                            UserScanProjectInfo.class);
                    if (userScanProjectInfo == null) {
                        log.error("scanPlan is is {},deserialize userScanProjectInfo  error,", scanPlan.getId());
                    }
                    for (String scanCase : scanPlan.getScanCases().split(",")) {
                        codeSearchAndSave2Db(userScanProjectInfo.getGitProjectIds(),
                                userScanProjectInfo.getGitGroupIds(),
                                userScanProjectInfo.isAllProjects(),
                                scanCase,
                                scanPlan.getId()
                        );
                    }
                    changeStatus(scanPlan);
                } catch (Exception e) {
                    log.error("scanPlan is is {},scan error,", scanPlan.getId(), e);
                } finally {
                    ksRedisLock.unlock(redisKey);
                }
            }, codeMatchExecutor);
        }
    }

    @Override
    public void downloadInterfaceScanIssueExcel(long scanPlanId, HttpServletResponse response) throws IOException {
        ScanPlan scanPlan = scanPlanService.getById(scanPlanId);
        if (scanPlan == null) {
            throw new ThemisException(TASK_NOT_EXIST);
        }
        Boolean deleted = scanPlan.getDeleted();
        if (!deleted) {
            throw new ThemisException(-1, "任务还未扫描完成");
        }
        List<ScanIssue> scanIssues = scanIssueService.listByPlanId(scanPlanId);
        if (CollectionUtils.isEmpty(scanIssues)) {
            throw new ThemisException(0, "未扫描出问题");
        }
        // 注册一个handler
        this.setResponseInfo(response, "ipv4扫描");
        ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellWriteHandler())
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet(1, "扫描结果")
                .head(InterfaceScanIssueExcelMeta.class)
                .build();
        List<InterfaceScanIssueExcelMeta> issueExcelMetas = Lists.newArrayList();
        appendIssueExcelMetas(issueExcelMetas, scanPlanId);
        excelWriter.write(issueExcelMetas, writeSheet);
        excelWriter.finish();
    }

    private void appendIssueExcelMetas(List<InterfaceScanIssueExcelMeta> issueExcelMetas, long scanPlanId) {
        List<ScanIssue> scanIssues = scanIssueService.listByPlanId(scanPlanId);
        for (ScanIssue scanIssue : scanIssues) {
            GitlabProject localCacheProject = gitOperations.getLocalCacheProject(scanIssue.getProjectId());
            InterfaceScanIssueExcelMeta interfaceScanIssueExcelMeta = new InterfaceScanIssueExcelMeta();
            interfaceScanIssueExcelMeta.setLocation(scanIssue.getLocation());
            interfaceScanIssueExcelMeta.setCaseStr(scanIssue.getIllegalStr());
            interfaceScanIssueExcelMeta.setLineNo(scanIssue.getStartLine());
            interfaceScanIssueExcelMeta.setProjectId(scanIssue.getProjectId());
            interfaceScanIssueExcelMeta.setGroupId(scanIssue.getGroupId());
            String repoUrl = localCacheProject == null ? "" : localCacheProject.getWebUrl();
            if (StringUtils.isNotEmpty(repoUrl)) {
                interfaceScanIssueExcelMeta.setRepoUrl(repoUrl);
                String gitLink = GitUtils.getGitLink(repoUrl, "master",
                        scanIssue.getLocation(), scanIssue.getStartLine(), scanIssue.getStartLine());
                interfaceScanIssueExcelMeta.setHyperlink(gitLink);
            }
            interfaceScanIssueExcelMeta.setCodeStr(scanIssue.getCodeStr());
            issueExcelMetas.add(interfaceScanIssueExcelMeta);
        }
    }

    @Override
    public List<CodeScanPlanInfo> userCodeScanPlanList(String userName) {
        List<ScanPlan> scanPlans = scanPlanService.listByInterfaceApplicant(userName);
        return convertCodeScanPlanInfos(scanPlans);
    }

    private List<CodeScanPlanInfo> convertCodeScanPlanInfos(List<ScanPlan> scanPlans) {
        if (CollectionUtils.isEmpty(scanPlans)) {
            return Lists.newArrayList();
        }
        List<CodeScanPlanInfo> codeScanPlanInfos = Lists.newArrayList();
        for (ScanPlan scanPlan : scanPlans) {
            CodeScanPlanInfo codeScanPlanInfo = new CodeScanPlanInfo();
            codeScanPlanInfo.setSponsorTime(DTF.format(scanPlan.getGmtCreate()));
            codeScanPlanInfo.setScanPlanId(scanPlan.getId());
            codeScanPlanInfo.setExcelDownloadUrl(String.format(EXCEL_DOWNLOAD_URL, kdevUrl, scanPlan.getId()));
            codeScanPlanInfo.setStatus(scanPlan.getDeleted() ? "已完成" : "扫描中");
            codeScanPlanInfos.add(codeScanPlanInfo);
        }
        return codeScanPlanInfos;
    }

    private void deleteScanPlanIssues(ScanPlan scanPlan) {
        scanIssueService.deleteByScanPlanId(scanPlan.getId());
    }

    private void changeStatus(ScanPlan scanPlan) {
        scanPlan.setDeleted(true);
        scanPlanService.updateById(scanPlan);
    }

    private void codeSearchAndSave2Db(List<Integer> gitProjectIds, List<Integer> gitGroupIds, Boolean allProjects,
            String scanCase, Long scanPlanId) {
        int pageNo = 1;

        if (allProjects) {
            while (true) {
                SearchRequest searchRequest = SearchRequest.newBuilder()
                        .search(scanCase)
                        .pageNo(pageNo)
                        .pageSize(PAGE_SIZE)
                        .disableHighlight(true)
                        .build();
                boolean success = searchAndSave(searchRequest, scanPlanId, scanCase);
                if (!success) {
                    break;
                }
                pageNo++;
            }
            return;
        }

        if (CollectionUtils.isNotEmpty(gitProjectIds)) {
            for (Integer gitProjectId : gitProjectIds) {
                while (true) {
                    SearchRequest searchRequest = SearchRequest.newBuilder()
                            .search(scanCase)
                            .projectId(gitProjectId)
                            .disableHighlight(true)
                            .pageNo(pageNo)
                            .pageSize(PAGE_SIZE)
                            .build();
                    boolean success = searchAndSave(searchRequest, scanPlanId, scanCase);
                    if (!success) {
                        break;
                    }
                    pageNo++;
                }
            }
            return;
        }

        if (CollectionUtils.isNotEmpty(gitGroupIds)) {
            for (Integer gitGroupId : gitGroupIds) {
                while (true) {
                    SearchRequest searchRequest = SearchRequest.newBuilder()
                            .search(scanCase)
                            .gitGroupId(gitGroupId)
                            .pageNo(pageNo)
                            .pageSize(PAGE_SIZE)
                            .build();
                    boolean success = searchAndSave(searchRequest, scanPlanId, scanCase);
                    if (!success) {
                        break;
                    }
                    pageNo++;
                }
            }
        }
    }

    private boolean searchAndSave(SearchRequest searchRequest, Long scanPlanId, String scanCase) {
        SearchResponse searchResponse = kdevApi.codeSearch(searchRequest);
        if (Objects.isNull(searchResponse)) {
            return false;
        }
        List<CodeElement> list = searchResponse.getList();
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        List<ScanIssue> scanIssues = convert2ScanIssue(list, scanPlanId, scanCase);
        scanIssueService.insertBatch(scanIssues);
        return true;
    }

    private List<ScanIssue> convert2ScanIssue(List<CodeElement> list, Long scanPlanId, String scanCase) {
        LocalDateTime now = LocalDateTime.now();
        List<ScanIssue> scanIssues = Lists.newArrayList();
        for (CodeElement codeElement : list) {
            ScanIssue scanIssue = new ScanIssue();
            scanIssue.setScanPlanId(scanPlanId);
            scanIssue.setLocation(codeElement.getFilePath());
            scanIssue.setIllegalStr(scanCase);
            scanIssue.setProjectId(codeElement.getProjectId());
            scanIssue.setCodeStr(getCodeStr(codeElement.getData(), scanCase));
            scanIssue.setStartLine(codeElement.getHighlightLine());
            scanIssue.setGmtCreate(now);
            scanIssue.setGroupId(0);
            scanIssue.setGmtModified(now);
            scanIssue.setExtendStr("");
            scanIssue.setCaseType(0);
            scanIssue.setHttpRepoUrl("");
            scanIssue.setCommitter("");
            scanIssues.add(scanIssue);
        }
        return scanIssues;
    }

    private String getCodeStr(String data, String scanCase) {
        for (String str : data.split("\n")) {
            String trim = str.trim();
            // CHECKSTYLE:OFF
            if (trim.contains(scanCase)) {
                if (trim.length() > 300) {
                    return trim.substring(0, 300);
                }
            }
            // CHECKSTYLE:ON
        }
        return "";
    }

    private UserScanProjectInfo getUserProjectInfo(SponsorCodeScanRequest request) {
        return UserScanProjectInfo.newBuilder()
                .gitProjectIds(defaultEmptyList(request.getGitProjectIds()))
                .gitGroupIds(defaultEmptyList(request.getGitGroupIds()))
                .containsImport(request.getContainsImport())
                .allProjects(request.isAllProjects())
                .build();
    }

    private List<Integer> defaultEmptyList(String str) {
        return isEmpty(str) ? Lists.newArrayList() : Arrays.stream(str.split(","))
                .map(Integer::parseInt).collect(Collectors.toList());
    }

    private List<String> defaultEmptyStringList(String str) {
        return isEmpty(str) ? Lists.newArrayList() : Lists.newArrayList(str.split(","));
    }

    private List<ScanIssueExcelMeta> convertIssue2Meta(List<ScanIssue> scanIssueList, ScanProjectInfo scanProjectInfo) {
        Map<Integer, List<ScanGroupInfo>> groupIdInfoListMap =
                scanProjectInfo.getScanGroupInfos().stream().collect(Collectors.groupingBy(ScanGroupInfo::getGroupId));

        List<ScanIssueExcelMeta> excelMetaList = Lists.newArrayList();

        for (ScanIssue scanIssue : scanIssueList) {
            ScanIssueExcelMeta scanIssueExcelMeta = new ScanIssueExcelMeta();
            scanIssueExcelMeta.setGroupId(scanIssue.getGroupId());
            scanIssueExcelMeta.setBusinessName(
                    getLogicBusinessName(groupIdInfoListMap, scanIssue.getGroupId(), scanIssue.getProjectId()));
            scanIssueExcelMeta.setLineNo(scanIssue.getStartLine());
            scanIssueExcelMeta.setLocation(scanIssue.getLocation());
            // 拆分 https://git.corp.kuaishou.com/ 与 kwai/kwai-webservice.git
            scanIssueExcelMeta.setRepoUrl(scanIssue.getHttpRepoUrl().substring(KUAISHOU_URL_PREFIX.length()));
            scanIssueExcelMeta.setCaseStr(scanIssue.getIllegalStr());
            scanIssueExcelMeta.setCaseType(ScanCaseType.getDescByType(scanIssue.getCaseType()));
            String extendStr = scanIssue.getExtendStr();
            // 添加 project id
            scanIssueExcelMeta.setProjectId(scanIssue.getProjectId());
            // 具体代码codeStr
            scanIssueExcelMeta.setCodeStr(scanIssue.getCodeStr());
            scanIssueExcelMeta.setCommitter(scanIssue.getCommitter());
            // 超链接hyperLink
            String httpRepoUrl = scanIssue.getHttpRepoUrl();
            httpRepoUrl = httpRepoUrl.substring(0, httpRepoUrl.length() - 4);
            String gitLink = GitUtils.getGitLink(httpRepoUrl, "master",
                    scanIssue.getLocation(), scanIssue.getStartLine(), scanIssue.getStartLine());
            scanIssueExcelMeta.setHyperlink(gitLink.replace(" ", ""));
            if (StringUtils.isNotEmpty(extendStr)) {
                KconfCaseExtend kconfCaseExtend = JSONUtils.deserialize(extendStr, KconfCaseExtend.class);
                scanIssueExcelMeta.setCaseCreator(kconfCaseExtend.getCreator());
                scanIssueExcelMeta.setCaseUpdater(kconfCaseExtend.getLastUpdater());
                scanIssueExcelMeta.setCaseLinkUrl(kconfCaseExtend.getHardCodeDomainStr());
            }
            // 记一下日志，抛到sentry
            if (scanIssueExcelMeta.getBusinessName() == null
                    || scanIssueExcelMeta.getGroupId() == null
                    || scanIssueExcelMeta.getRepoUrl() == null) {
                log.error("Bad ScanIssueExcelMeta: {}, {}, {}", scanIssueExcelMeta.getBusinessName(),
                        scanIssueExcelMeta.getGroupId(),
                        scanIssueExcelMeta.getRepoUrl());
                continue;
            }
            excelMetaList.add(scanIssueExcelMeta);
        }

        return excelMetaList;
    }

    private String getLogicBusinessName(Map<Integer, List<ScanGroupInfo>> groupIdInfoListMap, Integer groupId,
            Integer projectId) {
        List<ScanGroupInfo> scanGroupInfos = groupIdInfoListMap.get(groupId);
        if (CollectionUtils.isEmpty(scanGroupInfos)) {
            return "";
        }
        if (scanGroupInfos.size() == 1) {
            return scanGroupInfos.get(0).getLogicGroupName();
        }
        if (scanGroupInfos.size() > 1) {
            for (ScanGroupInfo scanGroupInfo : scanGroupInfos) {
                List<Integer> projectIds = scanGroupInfo.getProjectIds();
                if (projectIds.contains(projectId)) {
                    return scanGroupInfo.getLogicGroupName();
                }
            }
        }
        return "";
    }

    private void setResponseInfo(HttpServletResponse response) {
        setResponseInfo(response, "域名硬编码扫描结果");
    }

    private void setResponseInfo(HttpServletResponse response, String excelName) {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = null;
        try {
            fileName = URLEncoder.encode(excelName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log.error("URLEncoder.encode error", e);
        }
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
    }


    private ScanFilterConfig getScanFilterConfig(List<ScanFilter> scanFilters) {
        ScanFilterConfig scanFilterConfig = new ScanFilterConfig();
        for (ScanFilter scanFilter : scanFilters) {
            ScanFilterType scanFilterType = ScanFilterType.getByType(scanFilter.getFilterType());
            String filterValue = scanFilter.getFilterValue();
            List<String> filterList = JSONUtils.deserializeList(filterValue, String.class);
            switch (scanFilterType) {
                case DIRECT:
                    scanFilterConfig.setExcludeDirectList(filterList);
                    break;
                case FILE_EXTENSION:
                    scanFilterConfig.setExcludeFileExtensionList(filterList);
                    break;
                case FILE_NAME:
                    scanFilterConfig.setExcludeFileNameList(filterList);
                    break;
                case FILE_EXTEND_INCLUDE:
                    scanFilterConfig.setIncludeFileExtensionList(filterList);
                    break;
                case CUSTOM_EXCLUDE_STR:
                    scanFilterConfig.setCustomExcludeList(filterList);
                    break;
                case IMPORT_ITEM_SCAN:
                    scanFilterConfig.setIncludeImport(true);
                    break;
                default:
                    break;
            }
        }
        return scanFilterConfig;
    }

    private List<ScanGroupProject> filterScanGroupProjects(ScanProjectInfo scanProjectInfo, ScanPlan scanPlan) {
        List<ScanGroupProject> scanGroupProjects = Lists.newArrayList();

        List<Integer> scanUserProjects = scanProjectInfo.getScanUserProjects();
        if (scanUserProjects == null) {
            scanUserProjects = Lists.newArrayList();
        }

        for (ScanGroupInfo scanGroupInfo : scanProjectInfo.getScanGroupInfos()) {
            List<GitlabProject> matchGitlabProjects = Lists.newArrayList();

            ScanGroupProject scanGroupProject = new ScanGroupProject();
            Integer groupId = scanGroupInfo.getGroupId();
            scanGroupProject.setGroupId(groupId);

            List<Integer> projectIds = scanGroupInfo.getProjectIds();
            List<Integer> excludeSubGroupIds = scanGroupInfo.getExcludeSubGroupIds();
            // 如果只有一个group下的部分仓库 则直接筛选即可
            if (CollectionUtils.isNotEmpty(projectIds)) {
                for (Integer projectId : projectIds) {
                    try {
                        GitlabProject project = gitlabApi.getProject(projectId);
                        matchGitlabProjects.add(project);
                    } catch (IOException e) {
                        log.error("filterGitProjects gitlabApi getProject error , project id is {}", projectId);
                    }
                }
            }
            // 判断group
            if (groupId != null && groupId != 0) {
                // 初步筛选出符合条件的项目
                List<GitlabProject> gitlabProjects;
                try {
                    gitlabProjects = selfGitApi.listAllProjectsByGroupIdWithSubGroups(groupId, excludeSubGroupIds);
                } catch (Exception e) {
                    log.error("scanGroupInfo is {}", JSONUtils.serialize(scanGroupInfo));
                    throw new RuntimeException(e);
                }
                matchGitlabProjects.addAll(gitlabProjects);
            }

            List<Integer> excludeProjectIds = scanGroupInfo.getExcludeProjectIds();
            List<ScanProject> scanProjects = Lists.newArrayList();
            for (GitlabProject gitlabProject : matchGitlabProjects) {

                boolean containsUserProjects = scanProjectInfo.isContainsUserProjects();
                if (!containsUserProjects) {
                    // 增加逻辑，指定的userProject不判断是否是：个人项目
                    if (!scanUserProjects.contains(gitlabProject.getId()) && "user".equals(
                            gitlabProject.getNamespace().getKind())) {
                        continue;
                    }
                }

                if (CollectionUtils.isNotEmpty(excludeProjectIds)
                        && excludeProjectIds.contains(gitlabProject.getId())) {
                    continue;
                }
                ScanProject scanProject = new ScanProject();
                scanProject.setScanPlanId(scanPlan.getId());
                scanProject.setGroupId(groupId);
                scanProject.setSshUrl(gitlabProject.getSshUrl());
                scanProject.setBranch("master");
                scanProject.setProjectId(gitlabProject.getId());
                scanProject.setNeedCommitter(scanProjectInfo.isNeedCommitter());
                scanProjects.add(scanProject);
            }
            scanGroupProject.setScanProjects(scanProjects);
            scanGroupProjects.add(scanGroupProject);
        }

        return scanGroupProjects;
    }

}
