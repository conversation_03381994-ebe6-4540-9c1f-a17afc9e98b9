package com.kuaishou.serveree.themis.component.constant.kdev;

import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-12
 */
@Getter
@AllArgsConstructor
public enum GitRoleEnum {
    OWNER("Owner"),
    MAINTAINER("Maintainer"),
    DEVELOPER("Developer"),
    REPORTER("Reporter"),
    PIPELINE_ADMIN("PipelineAdmin")
    ;

    private String role;

    private static final Map<String, GitRoleEnum> CACHE = new HashMap<>();
    static {
        CACHE.put(OWNER.role, OWNER);
        CACHE.put(MAINTAINER.role, MAINTAINER);
        CACHE.put(DEVELOPER.role, DEVELOPER);
        CACHE.put(REPORTER.role, REPORTER);
        CACHE.put(PIPELINE_ADMIN.role, PIPELINE_ADMIN);
    }

    public static boolean isGitRole(String role) {
        return CACHE.contains<PERSON><PERSON>(role);
    }
}
