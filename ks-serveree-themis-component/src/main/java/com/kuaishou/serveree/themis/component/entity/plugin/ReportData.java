package com.kuaishou.serveree.themis.component.entity.plugin;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.report.IllegalMeta;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-23
 */
public class ReportData {

    private String repoUrl;

    private String branch;

    private String commitId;

    private Long pipelineId;

    private Long buildId;

    private String gavArgs;

    private Integer projectId;

    private String buildUserName;

    private List<IllegalMeta> illegalMetaList;

    private ReportData(Builder builder) {
        setRepoUrl(builder.repoUrl);
        setBranch(builder.branch);
        setCommitId(builder.commitId);
        setPipelineId(builder.pipelineId);
        setBuildId(builder.buildId);
        setGavArgs(builder.gavArgs);
        setProjectId(builder.projectId);
        setBuildUserName(builder.buildUserName);
        setIllegalMetaList(builder.illegalMetaList);
    }

    public ReportData() {
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public String getRepoUrl() {
        return repoUrl;
    }

    public void setRepoUrl(String repoUrl) {
        this.repoUrl = repoUrl;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getCommitId() {
        return commitId;
    }

    public void setCommitId(String commitId) {
        this.commitId = commitId;
    }

    public Long getPipelineId() {
        return pipelineId;
    }

    public void setPipelineId(Long pipelineId) {
        this.pipelineId = pipelineId;
    }

    public Long getBuildId() {
        return buildId;
    }

    public void setBuildId(Long buildId) {
        this.buildId = buildId;
    }

    public String getGavArgs() {
        return gavArgs;
    }

    public void setGavArgs(String gavArgs) {
        this.gavArgs = gavArgs;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public String getBuildUserName() {
        return buildUserName;
    }

    public void setBuildUserName(String buildUserName) {
        this.buildUserName = buildUserName;
    }

    public List<IllegalMeta> getIllegalMetaList() {
        return illegalMetaList;
    }

    public void setIllegalMetaList(
            List<IllegalMeta> illegalMetaList) {
        this.illegalMetaList = illegalMetaList;
    }

    public static final class Builder {
        private String repoUrl;
        private String branch;
        private String commitId;
        private Long pipelineId;
        private Long buildId;
        private String gavArgs;
        private Integer projectId;
        private String buildUserName;
        private List<IllegalMeta> illegalMetaList;

        private Builder() {
        }

        public Builder repoUrl(String val) {
            repoUrl = val;
            return this;
        }

        public Builder branch(String val) {
            branch = val;
            return this;
        }

        public Builder commitId(String val) {
            commitId = val;
            return this;
        }

        public Builder pipelineId(Long val) {
            pipelineId = val;
            return this;
        }

        public Builder buildId(Long val) {
            buildId = val;
            return this;
        }

        public Builder gavArgs(String val) {
            gavArgs = val;
            return this;
        }

        public Builder projectId(Integer val) {
            projectId = val;
            return this;
        }

        public Builder buildUserName(String val) {
            buildUserName = val;
            return this;
        }

        public Builder illegalMetaList(List<IllegalMeta> val) {
            illegalMetaList = val;
            return this;
        }

        public ReportData build() {
            return new ReportData(this);
        }
    }
}