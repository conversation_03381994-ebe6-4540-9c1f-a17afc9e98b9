package com.kuaishou.serveree.themis.component.constant.platform;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/7/12 11:15 AM
 */
@AllArgsConstructor
public enum PlatformProfileOperationEnum {

    DELETE("delete", "删除"),
    UPDATE("update", "修改"),
    COPY("copy", "复制"),
    GRANT("grant", "授权"),
    ;

    @Getter
    private final String key;
    @Getter
    private final String desc;

}
