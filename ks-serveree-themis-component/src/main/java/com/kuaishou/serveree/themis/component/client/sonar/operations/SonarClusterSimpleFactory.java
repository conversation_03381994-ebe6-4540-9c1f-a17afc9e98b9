package com.kuaishou.serveree.themis.component.client.sonar.operations;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.service.sonar.router.SonarNodeRouter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-16
 */
@Component
public class SonarClusterSimpleFactory {

    @Autowired
    private SonarNodeRouter sonarNodeRouter;
    @Autowired
    private UnderJdk17SonarCluster underJdk17SonarCluster;
    @Autowired
    private ClusterNode1Operations node1Operations;


    public SonarClusterOperations getClusterOperations(Long projectId) {
        if (projectId == null) {
            return node1Operations;
        }
        // 先去路由表里查询 jdk17版本的必须走这个
        SonarClusterOperations sonarOperations = sonarNodeRouter.getSonarOperationsByGitProjectId(projectId.intValue());
        if (sonarOperations != null) {
            return sonarOperations;
        }
        SonarClusterOperations sonarClusterOperations =
                underJdk17SonarCluster.underJdk17NodeRoute(projectId.intValue());
        sonarNodeRouter.safeRefreshNodeRouter(projectId.intValue(), sonarClusterOperations.nodeNumber());
        return sonarClusterOperations;
    }

}
