package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/8/11 4:38 下午
 */
@AllArgsConstructor
public enum EffectEnum {

    STOCK(1, "存量"),
    INCREMENT(2, "增量");

    @Getter
    private Integer type;

    @Getter
    private String desc;

    private static final Map<Integer, EffectEnum> ENUM_MAP = new HashMap<>();

    static {
        Arrays.stream(EffectEnum.values())
                .forEach(checkTypeEnum -> ENUM_MAP.put(checkTypeEnum.getType(), checkTypeEnum));
    }

    public static boolean validType(Integer type) {
        if (type != null) {
            return false;
        }
        return ENUM_MAP.containsKey(type);
    }

}
