package com.kuaishou.serveree.themis.component.dto;

import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ProfileRuleSeverityDto {

    // 规则key
    private String ruleKey;
    // 严重等级
    private String severity;
    // 是否是继承下来的规则。
    private boolean inherited;
    // 是否覆盖了严重等级。
    private boolean overwritten;
    // 来自哪个父规则集
    private CheckProfile parentProfile;
    // 在父规则集中的严重等级
    private String parentSeverity;
}
