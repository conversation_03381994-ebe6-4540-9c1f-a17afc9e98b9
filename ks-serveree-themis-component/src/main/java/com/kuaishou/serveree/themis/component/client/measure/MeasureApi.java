package com.kuaishou.serveree.themis.component.client.measure;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/10/18 4:32 下午
 */
@Component
@Slf4j
public class MeasureApi {

    @Value("${measure.url}")
    private String hostUrl;

    @Value("${measure.timeout}")
    private int timeout;

    public List<Integer> oneMonthCommitProjects() {
        String url = String.format("%s/v1/gitlab/projects", hostUrl);
        HttpRequest httpRequest = HttpRequest.get(url)
                .setFollowRedirects(true)
                .timeout(timeout);
        HttpResponse httpResponse = httpRequest.execute();
        if (!httpResponse.isOk()) {
            log.error("get oneMonthCommitProjects failed,request is {},response is {}", httpRequest.toString(),
                    httpResponse.toString());
            throw new RuntimeException("get oneMonthCommitProjects fail,response code is " + httpResponse.getStatus());
        }
        String body = httpResponse.body();
        if (StringUtils.isEmpty(body)) {
            log.error("get oneMonthCommitProjects failed,body is empty");
            throw new RuntimeException("get oneMonthCommitProjects body is empty");
        }
        JSONObject jsonObject = JSONUtils.deserialize(body, JSONObject.class);
        if (jsonObject == null) {
            throw new RuntimeException("get oneMonthCommitProjects body jsonObject is null");
        }
        return jsonObject.getJSONArray("data").toList(Integer.class);
    }

}
