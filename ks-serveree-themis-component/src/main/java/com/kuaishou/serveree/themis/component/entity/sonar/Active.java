package com.kuaishou.serveree.themis.component.entity.sonar;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Active {
    private String qProfile;
    private String inherit;
    private String severity;
    private List<ActiveParam> params;

    @Data
    public static class ActiveParam {
        private String key;
        private String value;
    }
}
