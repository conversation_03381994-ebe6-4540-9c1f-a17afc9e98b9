package com.kuaishou.serveree.themis.component.config;

import static com.kuaishou.serveree.themis.component.constant.platform.DynamicThreadPoolSizeKconfKey.MYBATIS_SQL_INTERCEPTOR;

import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import com.google.common.collect.Maps;
import com.kuaishou.framework.concurrent.DynamicThreadExecutor;
import com.kuaishou.framework.concurrent.ExecutorsEx;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/10/29 6:42 下午
 */
// CHECKSTYLE:OFF
@Slf4j
@Configuration
public class ExecutorConfig {

    private static final String THEAD_POOL_PREFIX = "thread-pool-";

    public static final Kconf<Map<String, Integer>> KCONF_DYNAMIC_THREAD_COUNT_MAP =
            Kconfs.ofIntegerMap("qa.themis.dynamicThreadCountMap", Maps.newHashMap()).build();

    @Bean
    ThreadPoolExecutor taskCheckExecutor() {
        return new ThreadPoolExecutor(
                6,
                6,
                10,
                TimeUnit.MINUTES,
                new PriorityBlockingQueue<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "taskCheck-")
        );
    }

    @Bean
    ThreadPoolExecutor kspSponsorExecutor() {
        return new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors(),
                Runtime.getRuntime().availableProcessors(),
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "kspSponsor-")
        );
    }

    @Bean
    ThreadPoolExecutor kspCheckResultExecutor() {
        return new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors(),
                Runtime.getRuntime().availableProcessors(),
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "kspCheckResult-")
        );
    }

    @Bean
    ThreadPoolExecutor kspAddLabelExecutor() {
        return new ThreadPoolExecutor(
                2,
                2,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "kspAddLabel-")
        );
    }

    @Bean
    ThreadPoolExecutor queryPageExecutor() {
        return new ThreadPoolExecutor(
                128,
                128,
                24,
                TimeUnit.HOURS,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "queryPage-")
        );
    }

    @Bean
    ThreadPoolExecutor processQueryExecutor() {
        return new ThreadPoolExecutor(
                128,
                128,
                24,
                TimeUnit.HOURS,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "processQuery-")
        );
    }

    @Bean
    ThreadPoolExecutor mrCheckSponsorExecutor() {
        return new ThreadPoolExecutor(
                128,
                128,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "mrCheckSponsor-")
        );
    }

    @Bean
    ThreadPoolExecutor createSonarMappingExecutor() {
        return new ThreadPoolExecutor(
                4,
                4,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "createSonarMapping-")
        );
    }

    @Bean
    ThreadPoolExecutor stringMatchExecutor() {
        return new ThreadPoolExecutor(
                8,
                8,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "stringMatch-")
        );
    }

    @Bean
    ThreadPoolExecutor codeMatchExecutor() {
        return new ThreadPoolExecutor(
                16,
                16,
                16,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "codeMatch-")
        );
    }

    @Bean
    ThreadPoolExecutor platformRepoSearchExecutor() {
        return new ThreadPoolExecutor(
                256,
                256,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingQueue<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "repoSearch-")
        );
    }

    @Bean
    ThreadPoolExecutor platformDetailExecutor() {
        return new ThreadPoolExecutor(
                256,
                256,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "repoDetail-")
        );
    }

    @Bean
    ThreadPoolExecutor platformSkyeyeDetailExecutor() {
        return new ThreadPoolExecutor(
                256,
                256,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "skyeyeDetail-")
        );
    }

    @Bean
    ThreadPoolExecutor platformIssueSearchExecutor() {
        return new ThreadPoolExecutor(
                256,
                256,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "issueSearch-")
        );
    }

    @Bean
    ThreadPoolExecutor az2StringMatchExecutor() {
        return new ThreadPoolExecutor(
                22,
                22,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "az2StringMatch-")
        );
    }

    @Bean
    ExecutorService logExecutor() {
        // 使用动态线程池
        // 通过kconf动态控制 核心线程、最大线程数
        return DynamicThreadExecutor
                .dynamic(() -> KCONF_DYNAMIC_THREAD_COUNT_MAP.get().getOrDefault(MYBATIS_SQL_INTERCEPTOR, 64),
                        n -> ExecutorsEx.newBlockingThreadPool(n, 50_000, "dynamic-themis-sql-%d"));
    }

    @Bean
    ThreadPoolExecutor sonarDataRemoveExecutor() {
        return new ThreadPoolExecutor(
                5,
                5,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "sonarDataRemove-")
        );
    }

    @Bean
    ThreadPoolExecutor fillUpCommonUniqIdExecutor() {
        return new ThreadPoolExecutor(
                40,
                40,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "fillUpCommonUniqId-")
        );
    }

    @Bean
    ThreadPoolExecutor issueSummaryBaseSyncExecutor() {
        return new ThreadPoolExecutor(
                10,
                10,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "issueSummaryBaseSync-")
        );
    }

    @Bean
    ThreadPoolExecutor issueSummarySyncExecutor() {
        return new ThreadPoolExecutor(
                10,
                10,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "issueSummarySync-")
        );
    }

    @Bean
    ThreadPoolExecutor pCheckIssueSyncExecutor() {
        return new ThreadPoolExecutor(
                10,
                10,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "pCheckIssueSync-")
        );
    }

    @Bean
    ThreadPoolExecutor checkIssueSyncExecutor() {
        return new ThreadPoolExecutor(
                10,
                10,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "checkIssueSync-")
        );
    }

    @Bean
    ThreadPoolExecutor issueChangesSyncExecutor() {
        return new ThreadPoolExecutor(
                10,
                10,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "issueChangeSync-")
        );
    }

    @Bean
    ThreadPoolExecutor saveScanResultOfFileDataExecutor() {
        return new ThreadPoolExecutor(
                1,
                1,
                10,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(),
                new CustomizableThreadFactory(THEAD_POOL_PREFIX + "saveScanFileData-")
        );
    }
}
// CHECKSTYLE:ON
