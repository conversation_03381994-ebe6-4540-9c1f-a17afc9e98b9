package com.kuaishou.serveree.themis.component.entity.statics;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/24 7:49 PM
 */
public class HardCodeScanContext {
    private List<Integer> projectIds;
    private Integer shardNo;
    private Integer totalShard;
    private Long azScanPlanId;

    public HardCodeScanContext() {
    }

    private HardCodeScanContext(Builder builder) {
        setProjectIds(builder.projectIds);
        setShardNo(builder.shardNo);
        setTotalShard(builder.totalShard);
        setAzScanPlanId(builder.azScanPlanId);
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public List<Integer> getProjectIds() {
        return projectIds;
    }

    public void setProjectIds(List<Integer> projectIds) {
        this.projectIds = projectIds;
    }

    public Integer getShardNo() {
        return shardNo;
    }

    public void setShardNo(Integer shardNo) {
        this.shardNo = shardNo;
    }

    public Integer getTotalShard() {
        return totalShard;
    }

    public void setTotalShard(Integer totalShard) {
        this.totalShard = totalShard;
    }

    public Long getAzScanPlanId() {
        return azScanPlanId;
    }

    public void setAzScanPlanId(Long azScanPlanId) {
        this.azScanPlanId = azScanPlanId;
    }

    public static final class Builder {
        private List<Integer> projectIds;
        private Integer shardNo;
        private Integer totalShard;
        private Long azScanPlanId;

        private Builder() {
        }

        public Builder projectIds(List<Integer> val) {
            projectIds = val;
            return this;
        }

        public Builder shardNo(Integer val) {
            shardNo = val;
            return this;
        }

        public Builder totalShard(Integer val) {
            totalShard = val;
            return this;
        }

        public Builder azScanPlanId(Long val) {
            azScanPlanId = val;
            return this;
        }

        public HardCodeScanContext build() {
            return new HardCodeScanContext(this);
        }
    }
}
