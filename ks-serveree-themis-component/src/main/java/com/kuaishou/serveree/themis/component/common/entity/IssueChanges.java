package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "IssueChanges对象", description = "")
public class IssueChanges implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "git的project id")
    private Integer gitProjectId;

    @ApiModelProperty(value = "git分支名")
    private String gitBranch;

    @ApiModelProperty(value = "issue唯一key")
    private String issueUniqId;

    @ApiModelProperty(value = "修改人")
    private String changeAuthor;

    @ApiModelProperty(value = "修改类型")
    private String changeType;

    @ApiModelProperty(value = "修改数据")
    private String changeData;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    private Integer scanMode;

    private String commonIssueUniqId;
    private String commonIssueUniqIdCopy;
    private String issueUniqIdCopy;

}
