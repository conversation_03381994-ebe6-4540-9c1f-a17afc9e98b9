package com.kuaishou.serveree.themis.component.constant.redis;

/**
 * <AUTHOR>
 * @since 2020/11/4 11:12 上午
 */
public class KsRedisPrefixConstant {

    public static final String CHECK_DATA_TOKEN_PREFIX = "quality:check:token:";

    public static final String KSP_PIPELINE_JOB_SYNC = "quality:ksp:pipeline:sync:lock";

    public static final String KSP_PIPELINE_LABEL_SYNC = "quality:ksp:pipeline:add:label:sync:lock";

    public static final String CHECK_RULE_PREFIX = "quality:check:rule:";

    public static final String CHECK_RULE_ALL_MAP = "quality:check:rule:all:map";

    public static final String RULE_TYPE_RULE_MAP_PREFIX = "quality:check:rule:map:type:";

    public static final String RULE_TYPE_RULE_LIST_PREFIX = "quality:check:rule:list:type:";

    public static final String CHECK_RULE_EFFECTIVE_TYPE_VERSION = "quality:check:rule:version:";

    public static final String KSP_PIPELINE_CHECK_LOCK_SYNC = "quality:ksp:pipeline:check:sync:lock:";

    public static final String PLUGIN_CHECK_MORE_TIME = "plugin:check:attach:more:time";

    public static final String PLUGIN_CHECK_MORE_TIME_TOTAL_KEY = "plugin:check:attach:time:total:";

    public static final String KS_PLUGIN_CHECK = "ks:plugin:check:";

    public static final String PLUGIN_ANALYZE_DETAIL_PREFIX = "quality:analyze:detail:buildId:";

    public static final String PLUGIN_ANALYZE_MR_PREFIX = "quality:analyze:detail:mrId:";

    public static final String LOCAL_ANALYZE_DISTRIBUTE_LIMIT_PREFIX = "local:analyze:distribute:limit:";

    public static final String LOCAL_ANALYZE_SPIN_LOCK_PREFIX = "local:analyze:spin:local:";

    public static final String LOCAL_ANALYZE_CAL_LOCK = "local:analyze:calculation:lock:";

    public static final String GITLAB_PROJECT_CACHE_PREFIX = "gitlab:project:cache:";

    public static final String GITLAB_PROJECT_ID_CACHE_PREFIX = "gitlab:project:id:cache:";

    public static final String GIT_GROUP_PROJECTS_SSH_URL_CACHE_PREFIX = "gitlab:group:projects:ssh:url:cache:";

    public static final String DEPENDENCY_COMMON_CONFIG = "dependency:common:config";

    public static final String DEPENDENCY_SELF_CONFIG_PREFIX = "dependency:self:config:";

    public static final String DEPENDENCY_COLLECT_LENGTH_LOCK = "dependency:collect:length:lock";

    public static final String DEPENDENCY_LENGTH_PREFIX = "dependency:collect:length:cache:";

    public static final String DEPENDENCY_LENGTH_GET_MAX_LIMIT_PREFIX = "dependency:collect:length:get:max:limit:";

    public static final String QUALITY_SYNC_KSP_RESULT_TASK_ID_LOCK_PREFIX = "quality:sync:ksp:result:prefix:";

    public static final String SONAR_CASHED_USER_LOGINS = "sonar:user:create:cashed:user:login";

    public static final String CHECK_PARSER_SPIN_LOCK = "quality:sonar:check:parser:spin:lock:";

    public static final String SONAR_CHECK_SPONSOR_EXECUTE_PREFIX = "quality:sonar:check:sponsor:";

    public static final String SONAR_PROJECT_EXIST_PREFIX = "quality:sonar:projectKey:";

    public static final String QUALITY_CUSTOM_CHECK_ANALYSIS_PREFIX = "quality:custom:check:analysis:";

    public static final String QUALITY_PLATFORM_CHECK_ALL_METRICS = "quality:platform:check:all:metrics";

    public static final String QUALITY_PLATFORM_CHECK_ACTION_PREFIX = "quality:platform:check:action:prefix";

    public static final String QUALITY_PLATFORM_CHECK_REPO_INIT_LOCK = "quality:platform:check:repo:init:lock:";

    public static final String CHECK_REPO_PROJECT_ID_INIT_LOCK = "quality:platform:check:project:id:init:lock:";

    public static final String COMMON_REPO_PROJECT_ID_INIT_LOCK = "quality:platform:common:check:project:id:init:lock:";

    public static final String QUALITY_PLATFORM_REPO_BRANCH_INIT_LOCK = "quality:platform:check:repo:branch:init:lock:";

    public static final String QUALITY_PLATFORM_DIFF_SOURCE_RULE_LIST = "quality:platform:check:rule:list:source:";

    public static final String SONAR_MAPPING_PERMISSION = "sonar:permission:group:key";

    public static final String SONAR_METRIC_PREFIX = "ks:sonar:metric:";

    public static final String PROCESS_DETAIL_RESULT_BUILD_PREFIX = "process:detail:result:build:info:";

    public static final String PROCESS_DETAIL_RESULT_MR_PREFIX = "process:detail:result:mr:info:";

    public static final String ANALYZE_REPORT_LOCK_PREFIX = "analyze:report:lock:";

    public static final String SONAR_PROCESS_HOOK_LOCK_PREFIX = "sonar:process:hook:lock:";

    public static final String CODE_SOURCES_FILE_PREFIX = "code:sources:file:prefix";

    public static final String PLATFORM_SCAN_EXECUTE_LOCK = "platform:scan:execute:lock";

    public static final String PLATFORM_SCAN_EXECUTING_COUNT = "platform:scan:executing:count";

    public static final String PLATFORM_SCAN_ACQUIRE_LOCK = "platform:scan:acquire:lock";

    public static final String CHECK_REPO_STAR_PREFIX = "platform:repo:star:";

    public static final String GIT_USER_PROJECT_IDS_PREFIX = "git:project:ids:user:";

    public static final String GIT_USER_PROJECT_ROLES_PREFIX = "git:project:roles:user:";
    public static final String GIT_USER_PROJECT_PERMS_PREFIX = "git:project:perms:user:";

    public static final String PLATFORM_PROFILE_BASE_INFO_PREFIX = "platform:profile:base:info:";

    public static final String PLATFORM_PROFILE_INFO_PREFIX = "platform:profile:info:";

    public static final String PLATFORM_REPO_CREATE_LOCK = "platform:repo:create:lock:";

    public static final String PLATFORM_REPO_UPDATE_LOCK = "platform:repo:update:lock:";

    public static final String PLATFORM_REPO_DELETE_LOCK = "platform:repo:delete:lock:";

    public static final String PLATFORM_ISSUE_TRANS_LOCK = "platform:issue:trans:lock:";

    public static final String PLATFORM_CODE_QUALITY_SCORE_INFO = "platform:code:quality:score:info";

    public static final String PLATFORM_SKYEYE_REPORT_DETAIL_INFO = "platform:skyeye:report:detail:info";

    public static final String PLATFORM_SKYEYE_RULE_ISSUE_NUM_INFO = "platform:skyeye:rule:issue:num:info";

    public static final String PLATFORM_SKYEYE_FOLDER_LEVEL_INFO = "platform:skyeye:folder:level:info:";

    public static final String PLATFORM_SKYEYE_ISSUE_FILE_RANK = "platform:skyeye:issue:file:rank:";

    public static final String PLATFORM_SKYEYE_RULE_ISSUE_RANK = "platform:skyeye:rule:issue:rank:";

    public static final String PLATFORM_SKYEYE_COMMON_METRIC_RANK = "platform:skyeye:common:metric:rank:";

    public static final String PLATFORM_SKYEYE_FOLDER_ISSUE_DISTRIBUTION_RANK =
            "platform:skyeye:folder:issue:distribution:rank:";

    public static final String PLATFORM_SKYEYE_FOLDER_MAINTAINABILITY_RANK =
            "platform:skyeye:folder:maintainability:rank:";

    public static final String REPO_FILE_DETAIL_CONTENT = "file:detail:content:";
    public static final String REPO_FILE_DETAIL_RAW_CONTENT = "file:detail:raw:content:";
    public static final String REPO_FILE_LIST = "file:list:";

    public static final String PLATFORM_SKYEYE_REPORT = "platform:skyeye:report:";

    public static final String PLATFORM_DATA_APPEND = "platform:data:append:";

    public static final String PLUGIN_KIM_NOTICE_PREFIX = "scanner:plugin:kim:notice:";

    public static final String QUALITY_PLATFORM_PIPELINE_CHECK_REPORT_PREFIX =
            "quality:platform:pipeline:check:report:action:prefix";

    public static final String AZ2_PLAN_STATUS_LOCK = "quality:az2:plan:status:lock:";

    public static final String INTERFACE_SCAN_PLAN = "scan:plan:from:interface:plan:";

    public static final String INTERFACE_SCAN_LOCK = "scan:plan:from:interface:lock:";

    public static final String SONAR_PLUGIN_KSP_BUILD_ID_IS_INCREMENT = "maven:sonar:new:is:increment:kspbuildid:";

    public static final String LOCAL_BUILD_ID_FILE_CONTENT = "local:build:id:file:content:";

    public static final String LOCAL_BUILD_DIFF_FILES_PREFIX = "local:build:id:diff:files:";
    public static final String PLATFORM_PIPELINE_PMD_REPORT_PREFIX = "platform:pipeline:report:pmd:";

    public static final String PLATFORM_REPO_MR_STUCK_PIPELINE_PREFIX = "platform:repo:mr:stuck:pipeline:";

    public static final String MR_DIFF_PREFIX = "mr:diff:";
    public static final String INCREMENT_SCAN_DIFF_PREFIX = "increment:diff:ksp:build:id:";

    public static final String MR_STUCK_RESULT_KEY_TEMPLATE = "mr:stuck:scan:result:%s:%s:%s";

    public static final String COMPLEXITY_EXEC_RECORD_PREFIX = "project:statistic:complexity:exec:kdevtask:";
    public static final String COMPLEXITY_EXEC_RECORD_PROJECT_PREFIX = "project:statistic:complexity:%s:%s";

    public static final String RULE_KEYS_CAN_NOT_BE_SKIPPED_KEY = "platform:rules:can:not:be:skipped";


}
