package com.kuaishou.serveree.themis.component.constant.quality;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.constant.analyze.LocalAnalyzeTaskEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2020/11/2 2:12 下午
 */
@AllArgsConstructor
public enum CheckTypeEnum {

    JAVA_KS_PLUGIN_CHECK("java快手插件检查", "/data/storage/themis/ks_plugin_check",
            Lists.newArrayList(LocalAnalyzeTaskEnum.LOCAL_ANALYZE.getName(),
                    LocalAnalyzeTaskEnum.LOCAL_OFFLINE_CHECK.getName())),
    FRONT_PIPELINE_CHECK("前端流水线", "", Lists.newArrayList(LocalAnalyzeTaskEnum.LOCAL_FRONT_METRIC_CHECK.getName())),
    MAVEN_SONAR_NEW("maven-sonar-new扫描", "", Lists.newArrayList()),

    SONAR_SCANNER_NEW("sonar-scanner-new扫描", "", Lists.newArrayList()),

    COVERITY_CHECK("coverity扫描", "", Lists.newArrayList());

    @Getter
    private final String display;

    @Getter
    private final String logStorageUrl;

    @Getter
    private final List<String> taskNames;

    private static final Map<String, CheckTypeEnum> ENUM_MAP = new HashMap<>();
    private static final Map<String, String> TASK_NAME_TYPE_MAP = new HashMap<>();

    static {
        Arrays.stream(CheckTypeEnum.values()).forEach(checkTypeEnum -> {
                    ENUM_MAP.put(checkTypeEnum.name(), checkTypeEnum);
                    checkTypeEnum.getTaskNames().forEach(taskName -> TASK_NAME_TYPE_MAP.put(taskName,
                            checkTypeEnum.name()));
                }
        );
    }

    public static CheckTypeEnum getEnumByEnumName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        return ENUM_MAP.get(name);
    }

    public static String getTypeByTaskName(String taskName) {
        return TASK_NAME_TYPE_MAP.get(taskName);
    }

}
