package com.kuaishou.serveree.themis.component.constant.process;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/10/22 6:09 下午
 */
@AllArgsConstructor
public enum ProcessBaseCheckType {

    QUICK(1, "快速检查", Lists.newArrayList(ProcessExecutionReferType.CHECKSTYLE)),
    OVER_ALL(2, "全面检查",
            Lists.newArrayList(ProcessExecutionReferType.CHECKSTYLE, ProcessExecutionReferType.MAVEN_SONAR)),
    ;

    @Getter
    private Integer type;
    @Getter
    private String desc;
    @Getter
    private List<ProcessExecutionReferType> executionReferTypes;

    private static final Map<Integer, ProcessBaseCheckType> CODE_MAP = Maps.newHashMap();

    static {
        Arrays.stream(ProcessBaseCheckType.values()).forEach(baseCheckType -> {
            CODE_MAP.put(baseCheckType.getType(), baseCheckType);
        });
    }

    public static ProcessBaseCheckType getByType(Integer type) {
        return CODE_MAP.get(type);
    }

}
