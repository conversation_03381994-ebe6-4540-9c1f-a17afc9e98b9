package com.kuaishou.serveree.themis.component.entity.sonar.req.gitMetricRequest;

import com.kuaishou.serveree.themis.component.entity.sonar.req.GitCommonRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentTreeRequest;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-05-27
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MeasureTreeRequest extends GitCommonRequest {
    private String metricKeys;
    private int page;
    private int pageSize;
    public MeasureComponentTreeRequest getTreeRequest() {
        MeasureComponentTreeRequest request = new MeasureComponentTreeRequest();
        request.setMetricKeys(this.metricKeys);
        request.setPage(this.page);
        request.setPageSize(this.pageSize);
        return request;
    }
}
