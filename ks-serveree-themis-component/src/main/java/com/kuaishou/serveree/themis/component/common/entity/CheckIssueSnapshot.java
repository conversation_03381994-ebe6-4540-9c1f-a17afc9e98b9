package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 最新生效的issue表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CheckIssueSnapshot对象", description = "最新生效的issue表")
public class CheckIssueSnapshot extends CheckIssue implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "快照id")
    private Long snapshotId;

}
