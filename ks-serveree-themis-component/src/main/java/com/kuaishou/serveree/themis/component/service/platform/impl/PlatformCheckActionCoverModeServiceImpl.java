package com.kuaishou.serveree.themis.component.service.platform.impl;

import static java.util.stream.Collectors.toList;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckComplexity;
import com.kuaishou.serveree.themis.component.common.entity.CheckDuplication;
import com.kuaishou.serveree.themis.component.common.entity.CheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.CheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.CheckMeasures;
import com.kuaishou.serveree.themis.component.common.entity.CheckMeasuresSnapshot;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckSnapshot;
import com.kuaishou.serveree.themis.component.common.entity.IssueChanges;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckRepoType;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckComplexityService;
import com.kuaishou.serveree.themis.component.service.CheckDuplicationService;
import com.kuaishou.serveree.themis.component.service.CheckExecutionService;
import com.kuaishou.serveree.themis.component.service.CheckIssueService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresSnapshotService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.IssueChangesService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryBaseService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCheckActionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCommonService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformIssueSummaryService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.ThemisTaskTokenUtil;
import com.kuaishou.serveree.themis.component.vo.request.CheckActionRequest;
import com.kuaishou.serveree.themis.component.vo.request.CheckMeasureVo;
import com.kuaishou.serveree.themis.component.vo.request.DataAppendRequest;
import com.kuaishou.serveree.themis.component.vo.request.PipelineReportRequest;
import com.kuaishou.serveree.themis.component.vo.response.CheckActionResponse;
import com.kuaishou.serveree.themis.component.vo.response.DataAppendResponse;
import com.kuaishou.serveree.themis.component.vo.response.PipelineReportResponse;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-29
 */
@Slf4j
@Service("PlatformCheckActionCoverModeServiceImpl")
public class PlatformCheckActionCoverModeServiceImpl implements PlatformCheckActionService {
    @Autowired
    private CheckIssueService checkIssueService;

    @Autowired
    private CheckRepoService checkRepoService;

    @Autowired
    private CheckRepoBranchService checkRepoBranchService;

    @Autowired
    private CheckDuplicationService checkDuplicationService;

    @Autowired
    private CheckBaseService checkBaseService;

    @Autowired
    private CheckExecutionService checkExecutionService;

    @Autowired
    private CheckComplexityService checkComplexityService;

    @Autowired
    private CheckMeasuresService checkMeasuresService;

    @Autowired
    private CheckMeasuresSnapshotService checkMeasuresSnapshotService;

    @Autowired
    private PlatformCommonService platformCommonService;

    @Autowired
    private PlatformCheckActionServiceImpl checkActionService;

    @Autowired
    private IssueSummaryService issueSummaryService;

    @Autowired
    private IssueChangesService issueChangesService;

    @Autowired
    private IssueSummaryBaseService issueSummaryBaseService;
    @Autowired
    private PlatformIssueSummaryService platformIssueSummaryService;

    @Override
    public CheckActionResponse action(CheckActionRequest request) {
        log.info("PlatformCheckActionCoverModeServiceImpl action is {}", JSONUtils.serialize(request));
        checkActionService.dataValidate(request);
        String source = ThemisTaskTokenUtil.get().getSource();
        // 保存repo
        CheckRepo checkRepo = checkRepoService.init(request.getRepoUrl(), CheckRepoType.getCodeBySource(source), false);
        // 鉴权
        if (this.validatePermission(checkRepo, source)) {
            checkRepoService.updateById(checkRepo);
        }
        //保存check base
        String branch = CommonUtils.defaultBranchIfEmpty(request.getBranch());
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.init(checkRepo.getId(), branch);
        CheckBase checkBase = this.fillCheckBase(checkRepo, checkRepoBranch, request);
        checkBaseService.save(checkBase);
        //保存 execution
        CheckExecution checkExecution = this.fillCheckExecution(checkBase,
                ProcessExecutionReferType.getBySource(source).getType());
        checkExecutionService.save(checkExecution);

        // 保存用户的所有请求参数，来进行问题回溯 已经很稳定了 可以去掉了
        List<CheckIssue> checkIssues = Lists.newArrayList();
        // 需要保存的宽数据的映射map
        Map<Integer, List<CheckDuplication>> duplicationMap = Maps.newHashMap();
        Map<Integer, CheckComplexity> complexityMap = Maps.newHashMap();
        // 处理新增issue
        this.dealIssues(request.getIssues(), checkIssues, duplicationMap, complexityMap, checkRepoBranch,
                checkExecution, checkRepo);
        List<CheckMeasures> needSaveMeasures = Lists.newArrayList();
        List<Long> needDelMeasureIds = Lists.newArrayList();

        Pair<List<IssueSummary>, List<IssueSummary>> summaryPair =
                platformIssueSummaryService.compareAndGenSummaryIssues(checkIssues, checkBase, checkExecution);
        List<IssueSummary> needSaveList = summaryPair.getLeft();
        List<IssueSummary> needUpdateList = summaryPair.getRight();
        if (CollectionUtils.isNotEmpty(needSaveList)) {
            for (IssueSummary issueSummary : needSaveList) {
                issueSummary.setExecutionReferType(checkExecution.getReferType());
            }
            issueSummaryService.saveBatch(needSaveList);
        }
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            for (IssueSummary issueSummary : needUpdateList) {
                issueSummary.setExecutionReferType(checkExecution.getReferType());
            }
            issueSummaryService.updateBatchByProjectBranchUniqId(needUpdateList);
        }
        // 构造changes数据
        List<IssueChanges> changesIssueList = issueChangesService.packageChangesIssues(
                Lists.newArrayList(CollectionUtil.union(needSaveList, needUpdateList)));
        if (CollectionUtils.isNotEmpty(changesIssueList)) {
            issueChangesService.saveBatch(changesIssueList);
        }
        // 更新 base 表数据
        issueSummaryBaseService.batchCreateOrUpdate(CollectionUtils.union(needSaveList, needUpdateList));
        // 处理度量数据
        dealCheckMeasures(request.getMeasures(), checkExecution, checkRepoBranch, needDelMeasureIds, needSaveMeasures);
        // 变更snapshot
        Pair<List<Long>, CheckSnapshot> changePair =
                platformCommonService.changeSnapshot(checkBase, checkExecution.getId());
        CheckSnapshot checkSnapshot = changePair.getRight();
        // 操作整体数据
        // 度量数据相关
        if (CollectionUtils.isNotEmpty(needDelMeasureIds)) {
            checkMeasuresService.deleteBatchByIds(needDelMeasureIds);
        }
        if (CollectionUtils.isNotEmpty(needSaveMeasures)) {
            checkMeasuresService.saveBatch(needSaveMeasures);
        }
        //保存度量的快照数据
        List<CheckMeasures> checkMeasures = checkMeasuresService.listByCheckRepoBranchId(checkRepoBranch.getId());
        if (CollectionUtils.isNotEmpty(checkMeasures)) {
            List<CheckMeasuresSnapshot> measuresSnapshots = this.convert2Snapshots(checkMeasures, checkSnapshot);
            checkMeasuresSnapshotService.saveBatch(measuresSnapshots);
        }
        // base issue相关 先清除再保存
        boolean removeIssue =
                checkIssueService.deleteCheckIssuesByRepoIdAndBranchId(checkRepo.getId(), checkRepoBranch.getId());
        if (!removeIssue) {
            log.warn("用户上报扫描数据，清理issue失败，repoId：{}，branchId：{}", checkRepo.getId(), checkRepoBranch.getId());
        }
        if (CollectionUtils.isNotEmpty(checkIssues)) {
            checkIssueService.saveBatch(checkIssues);
        }

        // 重复度分组相关 先清除再保存
        boolean removeDuplications =
                checkDuplicationService.deleteCheckDuplicationsByRepoIdAndBranchId(checkRepo.getId(),
                        checkRepoBranch.getId());
        if (!removeDuplications) {
            log.warn("用户上报扫描数据，清理重复度失败，repoId：{}，branchId：{}", checkRepo.getId(), checkRepoBranch.getId());
        }
        if (MapUtils.isNotEmpty(duplicationMap)) {
            List<CheckDuplication> duplications =
                    this.fillIssueId2Duplications(duplicationMap, checkIssues, Lists.newArrayList());
            checkDuplicationService.saveBatch(duplications);
        }

        // 复杂度分组相关
        boolean removeComplexity = checkComplexityService.deleteCheckComplexitiesByRepoIdAndBranchId(checkRepo.getId(),
                checkRepoBranch.getId());
        if (!removeComplexity) {
            log.warn("用户上报扫描数据，清理复杂度失败，repoId：{}，branchId：{}", checkRepo.getId(), checkRepoBranch.getId());
        }
        if (MapUtils.isNotEmpty(complexityMap)) {
            List<CheckComplexity> complexities = this.fillIssueId2Complexity(complexityMap, checkIssues);
            checkComplexityService.saveBatch(complexities);
        }
        List<CheckBase> dbCheckBase = checkBaseService.listByCheckRepoBranchId(checkRepoBranch.getId());
        Long baseId = checkBase.getId();
        List<CheckBase> needChangeBaseList = dbCheckBase.stream()
                .filter(o -> !baseId.equals(o.getId()))
                .map(o -> o.setIsLast(false))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needChangeBaseList)) {
            checkBaseService.updateBatchById(needChangeBaseList);
        }
        checkBase.setIsLast(true);
        checkBase.setGmtModified(LocalDateTime.now());
        checkBaseService.updateById(checkBase);
        return new CheckActionResponse().setBaseId(checkBase.getId());
    }

    @Override
    public DataAppendResponse dataAppend(DataAppendRequest reportRequest) {
        return null;
    }

    @Override
    public PipelineReportResponse pipelineReport(PipelineReportRequest reportRequest) {
        return null;
    }

    public void dealCheckMeasures(List<CheckMeasureVo> measures, CheckExecution checkExecution,
            CheckRepoBranch checkRepoBranch, List<Long> needDelMeasureIds, List<CheckMeasures> needSaveMeasures) {
        if (CollectionUtils.isEmpty(measures)) {
            return;
        }
        //处理新增的度量数据
        measures.forEach(requestMeasure -> this.add2SaveList(needSaveMeasures, checkExecution, checkRepoBranch,
                requestMeasure.getKey(), requestMeasure.getValue()));
        //数据库中现存的度量数据删除
        List<CheckMeasures> dbCheckMeasures = checkMeasuresService.listByCheckRepoBranchId(checkRepoBranch.getId());
        if (CollectionUtils.isNotEmpty(dbCheckMeasures)) {
            needDelMeasureIds.addAll(dbCheckMeasures.stream().map(CheckMeasures::getId).collect(toList()));
        }
    }
}
