package com.kuaishou.serveree.themis.component.service.impl;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.PROFILE_NOT_EXIST;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.common.mappers.CheckProfileMapper;
import com.kuaishou.serveree.themis.component.entity.platform.CheckProfileSearchCondition;
import com.kuaishou.serveree.themis.component.service.CheckProfileService;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Slf4j
@Service
public class CheckProfileServiceImpl extends ServiceImpl<CheckProfileMapper, CheckProfile>
        implements CheckProfileService {

    @Override
    public CheckProfile getByName(String profileName) {
        Wrapper<CheckProfile> queryWrapper = new QueryWrapper<CheckProfile>().lambda()
                .eq(CheckProfile::getProfileName, profileName)
                .eq(CheckProfile::getDeleted, false);
        return this.getOne(queryWrapper);
    }

    @Override
    public CheckProfile getNonnullByName(String profileName) {
        CheckProfile checkProfile = getByName(profileName);
        if (Objects.isNull(checkProfile)) {
            throw new ThemisException(PROFILE_NOT_EXIST);
        }
        return checkProfile;
    }

    @Override
    public List<CheckProfile> listByProfileNames(Collection<String> profileNames) {
        if (CollectionUtils.isEmpty(profileNames)) {
            return Collections.emptyList();
        }
        Wrapper<CheckProfile> queryWrapper = new QueryWrapper<CheckProfile>().lambda()
                .in(CheckProfile::getProfileName, profileNames)
                .eq(CheckProfile::getDeleted, false);
        return this.list(queryWrapper);
    }

    @Override
    public IPage<CheckProfile> pageByCondition(CheckProfileSearchCondition searchCondition) {
        String language = searchCondition.getLanguage();
        String scanner = searchCondition.getScanner();
        String search = searchCondition.getSearch();
        long page = searchCondition.getPage();
        long pageSize = searchCondition.getPageSize();
        Collection<String> profileNames = searchCondition.getProfileNames();
        Wrapper<CheckProfile> queryWrapper = new QueryWrapper<CheckProfile>().lambda()
                .eq(StringUtils.isNotEmpty(language), CheckProfile::getLanguage, language)
                .eq(StringUtils.isNotEmpty(scanner), CheckProfile::getScanner, scanner)
                .like(StringUtils.isNotEmpty(search), CheckProfile::getDisplayName, search)
                .le(searchCondition.getMaxLayer() > 0, CheckProfile::getLayer, searchCondition.getMaxLayer())
                .in(CollectionUtils.isNotEmpty(profileNames), CheckProfile::getProfileName, profileNames)
                .eq(CheckProfile::getDeleted, false);
        return this.page(new Page<>(page, pageSize), queryWrapper);
    }

    @Override
    public IPage<CheckProfile> pageMostUsedByCondition(CheckProfileSearchCondition condition) {
        // 树形结构情况下搜索
        if (condition.isHierarchical() && StringUtils.isNotBlank(condition.getSearch())) {
            // 先模糊搜索规则集列表
            List<CheckProfile> profileList = list(Wrappers.<CheckProfile> lambdaQuery()
                    .eq(StringUtils.isNotEmpty(condition.getLanguage()), CheckProfile::getLanguage, condition.getLanguage())
                    .eq(StringUtils.isNotEmpty(condition.getScanner()), CheckProfile::getScanner, condition.getScanner())
                    .like(StringUtils.isNotEmpty(condition.getSearch()), CheckProfile::getDisplayName, condition.getSearch())
                    .eq(CheckProfile::getDeleted, false)
            );
            // 查找所有模糊搜索到的规则集的祖先规则集，并拿到第一级规则集列表
            List<CheckProfile> allAncestorProfiles = findAllAncestorProfiles(profileList);
            Set<String> firstLayerProfiles = allAncestorProfiles.stream()
                    .filter(p -> StringUtils.isBlank(p.getParentProfileName()))
                    .map(CheckProfile::getProfileName)
                    .collect(Collectors.toSet());
            if (firstLayerProfiles.isEmpty()) {
                return new Page<>(condition.getPage(), condition.getPageSize());
            }
            // 对这些规则集进行分页查找，并按照使用项目数倒序排序，清空其他条件，因为一级规则集的名字可能不符合这些条件
            condition.setProfileNames(firstLayerProfiles);
            condition.setSearch(null);
            condition.setMaxLayer(1);
        }
        // 查询 使用项目数最多的 规则集名称
        IPage<String> page =
                this.baseMapper.pageMostUsedProfileNames(new Page<>(condition.getPage(), condition.getPageSize()),
                        condition);
        // 构造待返回的分页对象
        IPage<CheckProfile> iPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        if (CollectionUtils.isNotEmpty(page.getRecords())) {
            // 填充记录
            iPage.setRecords(listByProfileNames(page.getRecords()));
        }
        return iPage;
    }

    @Override
    public void updateProfileToDelete(String profileName, String userName) {
        CheckProfile checkProfile = getByName(profileName);
        if (checkProfile == null) {
            throw new ThemisException(PROFILE_NOT_EXIST);
        }
        checkProfile.setDeleted(true);
        checkProfile.setUpdater(userName);
        checkProfile.setGmtModified(LocalDateTime.now());
        this.updateById(checkProfile);
    }

    @Override
    public List<CheckProfile> listByParentProfileName(String parentProfileName) {
        if (StringUtils.isBlank(parentProfileName)) {
            return Collections.emptyList();
        }
        Wrapper<CheckProfile> queryWrapper = new QueryWrapper<CheckProfile>().lambda()
                .eq(CheckProfile::getParentProfileName, parentProfileName)
                .eq(CheckProfile::getDeleted, false);
        return this.list(queryWrapper);
    }

    @Override
    public List<CheckProfile> listByParentProfileNames(List<String> parentProfileNames) {
        if (CollectionUtils.isEmpty(parentProfileNames)) {
            return Collections.emptyList();
        }
        Wrapper<CheckProfile> queryWrapper = new QueryWrapper<CheckProfile>().lambda()
                .in(CheckProfile::getParentProfileName, parentProfileNames)
                .eq(CheckProfile::getDeleted, false);
        return this.list(queryWrapper);
    }

    @Override
    public Set<String> selectExistingParentProfileNames(List<String> profileNames) {
        if (CollectionUtils.isEmpty(profileNames)) {
            return Collections.emptySet();
        }
        return baseMapper.listParentProfileNamesExists(profileNames);
    }

    @Override
    public List<CheckProfile> findAllAncestorProfiles(String profileName) {
        if (StringUtils.isBlank(profileName)) {
            return Collections.emptyList();
        }
        CheckProfile checkProfile = getNonnullByName(profileName);
        return findAllAncestorProfiles(List.of(checkProfile));
    }

    private List<CheckProfile> findAllAncestorProfiles(Collection<CheckProfile> checkProfiles) {
        if (CollectionUtils.isEmpty(checkProfiles)) {
            return Collections.emptyList();
        }
        // 先加入自己
        List<CheckProfile> ancestorProfiles = Lists.newArrayList(checkProfiles);
        Set<String> profileNameSet = checkProfiles.stream().map(CheckProfile::getProfileName).collect(Collectors.toSet());
        // 逐层向上加入祖先
        Collection<CheckProfile> current = checkProfiles;
        int cnt = 0;
        while (!current.isEmpty()) {
            List<CheckProfile> parentProfiles = listByProfileNames(current.stream()
                    .map(CheckProfile::getParentProfileName).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            for (CheckProfile parentProfile : parentProfiles) {
                if (profileNameSet.add(parentProfile.getProfileName())) {
                    ancestorProfiles.add(parentProfile);
                }
            }
            current = parentProfiles;
            if (++cnt > 10) {
                log.error("[findAllAncestorProfiles]存在无限循环，请排查数据问题! checkProfiles: {}", checkProfiles);
                break;
            }
        }
        return ancestorProfiles;
    }

}
