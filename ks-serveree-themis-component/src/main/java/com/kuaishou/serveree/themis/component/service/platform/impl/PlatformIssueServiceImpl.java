package com.kuaishou.serveree.themis.component.service.platform.impl;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.INVALID_PARAMS;
import static com.kuaishou.serveree.themis.component.utils.IssueUtils.convertMap;
import static java.util.stream.Collectors.groupingBy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckComplexity;
import com.kuaishou.serveree.themis.component.common.entity.CheckDuplication;
import com.kuaishou.serveree.themis.component.common.entity.CheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.CheckMeasures;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranchProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckRule;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.analyze.CheckComplexityType;
import com.kuaishou.serveree.themis.component.constant.platform.IssueGroupKeyEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformDuplicationEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueType;
import com.kuaishou.serveree.themis.component.entity.issue.IssueWorkflow;
import com.kuaishou.serveree.themis.component.entity.issue.Transition;
import com.kuaishou.serveree.themis.component.entity.platform.IssueSearchCondition;
import com.kuaishou.serveree.themis.component.entity.platform.IssueSummaryListCondition;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckComplexityService;
import com.kuaishou.serveree.themis.component.service.CheckDuplicationService;
import com.kuaishou.serveree.themis.component.service.CheckIssueService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.CheckRuleService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryBaseService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.openapi.BpmOpenApi;
import com.kuaishou.serveree.themis.component.service.openapi.BpmOpenApi.BpmRspDto;
import com.kuaishou.serveree.themis.component.service.openapi.BpmOpenApi.BpmSubmitDto;
import com.kuaishou.serveree.themis.component.service.openapi.BpmOpenApi.BpmSubmitDto.FormDataListDTO;
import com.kuaishou.serveree.themis.component.service.openapi.BpmStandardIssueSkipFormKeys;
import com.kuaishou.serveree.themis.component.service.platform.PlatformIssueService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.IssueUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.PlatformSonarInfoUtils;
import com.kuaishou.serveree.themis.component.utils.ThemisTaskTokenUtil;
import com.kuaishou.serveree.themis.component.vo.request.CheckIssueVo;
import com.kuaishou.serveree.themis.component.vo.request.CheckIssueVo.CheckDuplicationVo;
import com.kuaishou.serveree.themis.component.vo.request.CheckIssueVo.Complexity;
import com.kuaishou.serveree.themis.component.vo.request.IssueCycleComplexityRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueDuplicationRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueListRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueSearchRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionBpmRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.response.Facet;
import com.kuaishou.serveree.themis.component.vo.response.Facet.FacetValue;
import com.kuaishou.serveree.themis.component.vo.response.IssueCycleComplexityResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueCycleComplexityResponse.CycleComplexityMeta;
import com.kuaishou.serveree.themis.component.vo.response.IssueDuplicationResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueDuplicationResponse.DuplicationMeta;
import com.kuaishou.serveree.themis.component.vo.response.IssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueSearchResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SonarIssueVo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/17 3:43 下午
 */
@Service
@Slf4j
public class PlatformIssueServiceImpl implements PlatformIssueService {

    @Autowired
    private CheckIssueService checkIssueService;
    @Autowired
    private CheckRepoService checkRepoService;
    @Autowired
    private CheckRepoBranchService checkRepoBranchService;
    @Autowired
    private CheckDuplicationService checkDuplicationService;
    @Autowired
    private CheckComplexityService checkComplexityService;
    @Autowired
    private CheckRuleService checkRuleService;

    @Autowired
    private CheckRepoBranchProfileService checkRepoBranchProfileService;

    @Autowired
    @Qualifier("platformIssueSearchExecutor")
    private ThreadPoolExecutor searchExecutor;
    @Autowired
    private IssueWorkflow workflow;
    @Autowired
    private IssueSummaryService issueSummaryService;
    @Autowired
    private PlatformPermissionService platformPermissionService;

    @Autowired
    private CheckMeasuresService checkMeasuresService;

    @Autowired
    private PlatformSonarInfoUtils platformSonarInfoUtils;

    @Autowired
    private CheckBaseService checkBaseService;

    @Autowired
    private IssueSummaryBaseService issueSummaryBaseService;

    @Autowired
    private OfflineScanIssueTransitionService offlineScanIssueTransitionService;

    @Autowired
    private BpmOpenApi bpmOpenApi;
    @Autowired
    private BpmStandardIssueSkipFormKeys bpmStandardIssueSkipFormKeys;

    private static final Long THREAD_REQUEST_TIME = 2L;

    @Override
    public IssueSearchResponse search(IssueSearchRequest searchRequest) {
        this.searchParamValidate(searchRequest);
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(searchRequest.getGitProjectId());
        if (checkRepo == null) {
            throw new ThemisException(ResultCodeConstant.NOT_FOUND_CHECK_REPO);
        }
        // 鉴权
        if (StringUtils.isNotEmpty(checkRepo.getSource())
                && !checkRepo.getSource().equals(ThemisTaskTokenUtil.get().getSource())) {
            throw new ThemisException(ResultCodeConstant.NOT_SEARCH_AUTH);
        }
        Long checkRepoId = checkRepo.getId();
        String branch = CommonUtils.defaultBranchIfEmpty(searchRequest.getBranch());
        CheckRepoBranch repoBranch = checkRepoBranchService.getByRepoIdBranch(checkRepoId, branch);
        if (repoBranch == null) {
            throw new ThemisException(ResultCodeConstant.NOT_BRANCH_CHECK);
        }
        IssueSearchCondition searchCondition = IssueSearchCondition.builder()
                .checkRepoBranchId(repoBranch.getId())
                .checkRepoId(checkRepoId)
                .commitId(searchRequest.getCommitId())
                .locations(searchRequest.getLocations())
                .types(searchRequest.getTypes())
                .build();
        List<CheckIssue> dbCheckIssues = checkIssueService.searchIssuesByCondition(searchCondition);
        if (CollectionUtils.isEmpty(dbCheckIssues)) {
            return IssueSearchResponse.builder().locationGroupMap(Maps.newHashMap()).build();
        }
        return this.distributeIssuesByDiffType(dbCheckIssues);
    }

    private void searchParamValidate(IssueSearchRequest searchRequest) {
        if (searchRequest.getGitProjectId() == null) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "gitProjectId");
        }
        if (CollectionUtils.isEmpty(searchRequest.getLocations())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "locations");
        }
    }

    private IssueSearchResponse distributeIssuesByDiffType(List<CheckIssue> issuePage) {
        List<CheckIssue> duplicationIssues = Lists.newArrayList();
        List<CheckIssue> complexityIssues = Lists.newArrayList();
        List<CheckIssue> defaultIssues = Lists.newArrayList();
        issuePage.forEach(issue -> {
            switch (CheckIssueType.getEnumByType(issue.getType())) {
                case DUPLICATION:
                    duplicationIssues.add(issue);
                    break;
                case COMPLEXITY:
                    complexityIssues.add(issue);
                    break;
                default:
                    defaultIssues.add(issue);
                    break;
            }
        });
        List<CheckIssueVo> issueVos = Lists.newArrayList();
        for (CheckIssue defaultIssue : defaultIssues) {
            CheckIssueVo checkIssueVo = new CheckIssueVo();
            BeanUtil.copyProperties(defaultIssue, checkIssueVo);
            issueVos.add(checkIssueVo);
        }
        if (CollectionUtils.isNotEmpty(duplicationIssues)) {
            this.followDuplicationIssues(duplicationIssues, issueVos);
        }
        if (CollectionUtils.isNotEmpty(complexityIssues)) {
            this.followComplexityIssues(complexityIssues, issueVos);
        }
        Map<String, Map<String, List<CheckIssueVo>>> locationGroupMap =
                issueVos.stream().collect(groupingBy(CheckIssueVo::getLocation, groupingBy(CheckIssueVo::getType)));
        return IssueSearchResponse.builder().locationGroupMap(locationGroupMap).build();
    }

    private void followComplexityIssues(List<CheckIssue> complexityIssues, List<CheckIssueVo> issueVos) {
        List<Long> issueIds = complexityIssues.stream().map(CheckIssue::getId).collect(Collectors.toList());
        List<CheckComplexity> checkComplexities = checkComplexityService.listByIssueIds(issueIds);
        Map<Long, List<CheckComplexity>> issueIdCheckComplexityMap =
                checkComplexities.stream().collect(groupingBy(CheckComplexity::getCheckIssueId));
        for (CheckIssue complexityIssue : complexityIssues) {
            CheckIssueVo checkIssueVo = new CheckIssueVo();
            BeanUtil.copyProperties(complexityIssue, checkIssueVo);
            List<CheckComplexity> followCheckComplexities = issueIdCheckComplexityMap.get(complexityIssue.getId());
            Complexity complexity = new Complexity();
            for (CheckComplexity followCheckComplexity : followCheckComplexities) {
                if (CheckComplexityType.CYCLE_COMPLEXITY.getType().equals(followCheckComplexity.getType())) {
                    complexity.setCycleComplexity(followCheckComplexity.getValue());
                }
            }
            checkIssueVo.setComplexity(complexity);
            issueVos.add(checkIssueVo);
        }
    }

    private void followDuplicationIssues(List<CheckIssue> duplicationIssues, List<CheckIssueVo> issueVos) {
        List<Long> issueIds = duplicationIssues.stream().map(CheckIssue::getId).collect(Collectors.toList());
        List<CheckDuplication> checkDuplications = checkDuplicationService.listByIssueIds(issueIds);
        Map<Long, List<CheckDuplication>> issueIdDuplicationsMap =
                checkDuplications.stream().collect(groupingBy(CheckDuplication::getCheckIssueId));
        for (CheckIssue duplicationIssue : duplicationIssues) {
            CheckIssueVo checkIssueVo = new CheckIssueVo();
            BeanUtil.copyProperties(duplicationIssue, checkIssueVo);
            // 补充duplications数据
            List<CheckDuplication> followCheckDuplications = issueIdDuplicationsMap.get(duplicationIssue.getId());
            List<CheckDuplicationVo> checkDuplicationVos = Lists.newArrayList();
            for (CheckDuplication followCheckDuplication : followCheckDuplications) {
                CheckDuplicationVo checkDuplicationVo = new CheckDuplicationVo();
                checkDuplicationVo.setLocation(followCheckDuplication.getLocation());
                checkDuplicationVo.setStartLine(followCheckDuplication.getStartLine());
                checkDuplicationVo.setEndLine(followCheckDuplication.getEndLine());
                checkDuplicationVos.add(checkDuplicationVo);
            }
            checkIssueVo.setDuplications(checkDuplicationVos);
            issueVos.add(checkIssueVo);
        }
    }


    @Override
    public IssueDuplicationResponse duplication(IssueDuplicationRequest request, String userName) {
        checkUserLogin(userName);
        checkArguments(request);
        int gitProjectId = request.getGitProjectId();
        String branch = request.getBranch();
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        platformPermissionService.checkKdevGitPermission(checkRepo, userName);
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.getByRepoIdBranch(checkRepo.getId(), branch);
        CheckRepoBranchProfile checkRepoBranchProfile =
                checkRepoBranchProfileService.getByBranchIdOffline(checkRepoBranch.getId());
        int page = request.getPage() == null ? 1 : request.getPage();
        int pageSize = request.getPageSize() == null ? 10 : request.getPageSize();
        int type = request.getType();

        IPage<CheckDuplication> checkDuplicationIPage = checkDuplicationService.pageByCheckRepoBranchId(
                checkRepoBranch.getId(),
                type,
                page,
                pageSize);
        String metricKey = null;
        if (PlatformDuplicationEnum.LINE.getType() == type) {
            metricKey =
                    PlatformScannerEnum.getEnumByScanner(checkRepoBranchProfile.getScanner()).getDuplicationLineKy();
        } else if (PlatformDuplicationEnum.BLOCK.getType() == type) {
            metricKey =
                    PlatformScannerEnum.getEnumByScanner(checkRepoBranchProfile.getScanner()).getDuplicationBlockKey();
        }
        CheckBase checkBase = checkBaseService.getIsLastCheckBaseByBranchId(checkRepoBranch.getId());
        CheckMeasures checkMeasures = checkMeasuresService.getByMetricKey(checkRepoBranch.getId(), metricKey);
        return IssueDuplicationResponse.builder()
                .scanner(checkRepoBranchProfile.getScanner())
                .total(checkDuplicationIPage.getTotal())
                .scanMode(ScanModeEnum.OFFLINE.getCode())
                .displayValue(checkMeasures == null ? "" : checkMeasures.getMetricValue())
                .duplications(convert2Duplications(checkDuplicationIPage.getRecords(), type, checkBase))
                .build();
    }

    private void checkUserLogin(String userName) {
        if (StringUtils.isEmpty(userName)) {
            throw new ThemisException(ResultCodeConstant.NO_PERMISSION);
        }
    }

    private void checkArguments(IssueDuplicationRequest request) {
        if (StringUtils.isEmpty(request.getBranch())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "branch不能为空");
        }
        if (request.getType() == null) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "type不能为空");
        }
        if (request.getGitProjectId() == null) {
            throw new ThemisException(INVALID_PARAMS.getCode(), INVALID_PARAMS.getMessage() + "gitProjectId不能为空");
        }
    }

    private List<DuplicationMeta> convert2Duplications(List<CheckDuplication> checkDuplications, int type,
            CheckBase checkBase) {
        List<DuplicationMeta> duplicationMetas = Lists.newArrayList();
        for (CheckDuplication checkDuplication : checkDuplications) {
            DuplicationMeta duplicationMeta = new DuplicationMeta();
            duplicationMeta.setPath(checkDuplication.getLocation());
            duplicationMeta.setFileId(checkDuplication.getFileId());
            duplicationMeta.setGitProjectId(checkDuplication.getGitProjectId());
            duplicationMeta.setBranch(checkDuplication.getGitBranch());
            duplicationMeta.setBaseId(checkBase.getId());
            if (PlatformDuplicationEnum.LINE.getType() != type) {
                if (PlatformDuplicationEnum.BLOCK.getType() == type) {
                    duplicationMeta.setBlockNum(checkDuplication.getBlockCount());
                }
            } else {
                duplicationMeta.setLine(checkDuplication.getLineCount());
                String percent = BigDecimal.valueOf(checkDuplication.getLinePercent())
                        .divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP).toString();
                duplicationMeta.setPercentage(percent + "%");
            }
            duplicationMetas.add(duplicationMeta);
        }
        return duplicationMetas;
    }

    @Override
    public IssueCycleComplexityResponse cycleComplexity(IssueCycleComplexityRequest request, String userName) {
        checkUserLogin(userName);
        checkArguments(request);
        int gitProjectId = request.getGitProjectId();
        String branch = request.getBranch();
        int page = request.getPage() == null ? 1 : request.getPage();
        int pageSize = request.getPageSize() == null ? 10 : request.getPageSize();
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        platformPermissionService.checkKdevGitPermission(checkRepo, userName);
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.getByRepoIdBranch(checkRepo.getId(), branch);
        CheckRepoBranchProfile checkRepoBranchProfile =
                checkRepoBranchProfileService.getByBranchIdOffline(checkRepoBranch.getId());
        Long branchId = checkRepoBranch.getId();
        IPage<CheckComplexity> checkComplexityIPage = checkComplexityService.pageByCheckRepoBranchIdAndComplexityType(
                branchId, CheckComplexityType.CYCLE_COMPLEXITY, page, pageSize
        );
        String metricKey =
                PlatformScannerEnum.getEnumByScanner(checkRepoBranchProfile.getScanner()).getCycleComplexityKey();
        CheckMeasures checkMeasures = checkMeasuresService.getByMetricKey(branchId, metricKey);
        CheckBase checkBase = checkBaseService.getIsLastCheckBaseByBranchId(checkRepoBranch.getId());
        return IssueCycleComplexityResponse.builder()
                .displayValue(checkMeasures == null ? "" : checkMeasures.getMetricValue())
                .total(checkComplexityIPage.getTotal())
                .scanMode(ScanModeEnum.OFFLINE.getCode())
                .metricValues(convert2CheckComplexities(checkComplexityIPage.getRecords(), checkBase))
                .build();
    }

    private void checkArguments(IssueCycleComplexityRequest request) {
        Assert.notNull(request.getGitProjectId(), () -> new ThemisException(
                ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "gitProjectId参数不能为空"));
        Assert.notNull(request.getBranch(), () -> new ThemisException(
                ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "branch参数不能为空"));
    }

    private List<CycleComplexityMeta> convert2CheckComplexities(List<CheckComplexity> complexities,
            CheckBase checkBase) {
        List<CycleComplexityMeta> complexityMetas = Lists.newArrayList();
        for (CheckComplexity complexity : complexities) {
            CycleComplexityMeta cycleComplexityMeta = new CycleComplexityMeta();
            cycleComplexityMeta.setPath(complexity.getLocation());
            cycleComplexityMeta.setScore(complexity.getValue());
            cycleComplexityMeta.setFileId(complexity.getFileId());
            cycleComplexityMeta.setBaseId(checkBase.getId());
            cycleComplexityMeta.setGitProjectId(complexity.getGitProjectId());
            cycleComplexityMeta.setBranch(complexity.getGitBranch());
            complexityMetas.add(cycleComplexityMeta);
        }
        return complexityMetas;
    }

    @Override
    public IssueListResponse list(IssueListRequest request) {
        checkParams(request);
        Assert.notNull(request.getGitProjectId(), () -> new ThemisException(
                ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "gitProjectId参数不能为空"));
        Assert.notNull(request.getBranch(), () -> new ThemisException(
                ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "branch参数不能为空"));
        int gitProjectId = request.getGitProjectId();
        String branch = request.getBranch();
        CompletableFuture<IPage<IssueSummary>> issueSummaryIPageFuture = CompletableFuture
                .supplyAsync(() -> listByCondition(gitProjectId, branch, request), searchExecutor)
                .exceptionally(e -> {
                    log.error("platform issue search CompletableFuture statusGroupListFuture error", e);
                    throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_TASK_ERROR);
                });
        CompletableFuture<List<Map<String, Object>>> statusGroupListFuture = CompletableFuture
                .supplyAsync(() -> listGroupCount(request, "status"), searchExecutor)
                .exceptionally(e -> {
                    log.error("platform issue search CompletableFuture statusGroupListFuture error", e);
                    throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_TASK_ERROR);
                });
        CompletableFuture<List<Map<String, Object>>> severityGroupListFuture = CompletableFuture
                .supplyAsync(() -> listGroupCount(request, "severity"), searchExecutor)
                .exceptionally(e -> {
                    log.error("platform issue search CompletableFuture severityGroupListFuture error", e);
                    throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_TASK_ERROR);
                });
        CompletableFuture<List<Map<String, Object>>> typeGroupListFuture = CompletableFuture
                .supplyAsync(() -> listGroupCount(request, "type"), searchExecutor)
                .exceptionally(e -> {
                    log.error("platform issue search CompletableFuture typeGroupListFuture error", e);
                    throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_TASK_ERROR);
                });
        // 根据规则｜责任人分组计数
        CompletableFuture<List<Map<String, Object>>> ruleGroupListFuture = CompletableFuture
                .supplyAsync(() -> listGroupCount(request, "rule"), searchExecutor)
                .exceptionally(e -> {
                    log.error("platform issue search CompletableFuture ruleGroupListFuture error", e);
                    throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_TASK_ERROR);
                });
        CompletableFuture<List<Map<String, Object>>> authorGroupListFuture = CompletableFuture
                .supplyAsync(() -> listGroupCount(request, "author"), searchExecutor)
                .exceptionally(e -> {
                    log.error("platform issue search CompletableFuture authorGroupListFuture error", e);
                    throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_TASK_ERROR);
                });
        List<Map<String, Object>> statusGroupList;
        try {
            statusGroupList = statusGroupListFuture.get(THREAD_REQUEST_TIME, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("platform issue search get statusGroupList error", e);
            throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR);
        }
        List<Map<String, Object>> severityGroupList;
        try {
            severityGroupList = severityGroupListFuture.get(THREAD_REQUEST_TIME, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("platform issue search get severityGroupList error", e);
            throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR);
        }
        List<Map<String, Object>> typeGroupList;
        try {
            typeGroupList = typeGroupListFuture.get(THREAD_REQUEST_TIME, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("platform issue search get typeGroupList error", e);
            throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR);
        }
        // 新加筛选条件
        List<Map<String, Object>> ruleGroupList;
        try {
            ruleGroupList = ruleGroupListFuture.get(THREAD_REQUEST_TIME, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("platform issue search get ruleGroupList error", e);
            throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR);
        }
        List<Map<String, Object>> authorGroupList;
        try {
            authorGroupList = authorGroupListFuture.get(THREAD_REQUEST_TIME, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("platform issue search get authorGroupList error", e);
            throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR);
        }
        IPage<IssueSummary> issueIPage;
        try {
            issueIPage = issueSummaryIPageFuture.get(THREAD_REQUEST_TIME, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("platform issue search get statusGroupList error", e);
            throw new ThemisException(ResultCodeConstant.ISSUE_LIST_SPONSOR_ASYNC_GET_TASK_VALUE_ERROR);
        }
        List<Facet> facetList = IssueUtils.buildIssueFacets(convertMap(typeGroupList), convertMap(severityGroupList),
                convertMap(statusGroupList));
        // 负责人分组
        Facet authorIssueFacet = IssueUtils.issueMapSort(convertMap(authorGroupList), IssueGroupKeyEnum.AUTHOR);
        // 规则分组
        Facet ruleIssueFacet = IssueUtils.issueMapSort(convertMap(ruleGroupList), IssueGroupKeyEnum.RULE);
        List<CheckRule> checkRules = checkRuleService
                .listInRuleKeys(
                        ruleIssueFacet.getValues().stream().map(FacetValue::getVal).collect(Collectors.toList()));
        Map<String, String> ruleKeyToDes = checkRules.stream()
                .collect(Collectors.toMap(CheckRule::getRuleKey, CheckRule::getName, (a, b) -> a));
        ruleIssueFacet.getValues().forEach(o -> o.setDesc(ruleKeyToDes.get(o.getVal())));
        // 组装facet
        facetList.addAll(Lists.newArrayList(authorIssueFacet, ruleIssueFacet));
        return IssueListResponse.builder()
                .total(issueIPage.getTotal())
                .issues(convertIssueVo(issueIPage.getRecords()))
                .facets(facetList)
                .build();
    }

    /**
     * 解决由于前端传值 "null" 造成的 bug
     */
    private void checkParams(IssueListRequest request) {
        if ("null".equals(request.getTypes())) {
            request.setTypes(null);
        }
        if ("null".equals(request.getSeverities())) {
            request.setSeverities(null);
        }
        if ("null".equals(request.getStatuses())) {
            request.setStatuses(null);
        }
    }


    private List<SonarIssueVo> convertIssueVo(List<IssueSummary> records) {
        List<SonarIssueVo> sonarIssueVos = Lists.newArrayList();
        Map<String, String> issueModifierMap = issueSummaryBaseService.getModifierByIssueUniqKeys(
                records.stream().map(IssueUtils::getIssueUniqkey).collect(Collectors.toList()));
        for (IssueSummary issueSummary : records) {
            List<Transition> transitions = workflow.outTransitions(issueSummary.newIssue());
            SonarIssueVo vo = SonarIssueVo.convertFromIssueSummary(issueSummary,
                    transitions.stream()
                            .map(Transition::key)
                            .collect(Collectors.toList()), issueModifierMap.get(IssueUtils.getIssueUniqkey(issueSummary)));
            vo.setComponentKey(platformSonarInfoUtils.getFinalProjectKey(issueSummary.getGitProjectId()) + ":"
                    + issueSummary.getLocation());
            sonarIssueVos.add(vo);
        }
        return sonarIssueVos.stream().sorted(Comparator.comparing(SonarIssueVo::getCreationDate).reversed()).collect(Collectors.toList());
    }

    private IPage<IssueSummary> listByCondition(Integer gitProjectId, String branch, IssueListRequest request) {
        long l = System.currentTimeMillis();
        IssueSummaryListCondition condition = IssueSummaryListCondition.builder()
                .gitProjectId(gitProjectId)
                .branch(branch)
                .page(request.getP() == null ? 1 : request.getP())
                .pageSize(request.getPs() == null ? 10 : request.getPs())
                .severities(getSplitStr(request.getSeverities()))
                .statuses(fillStatues(request.getStatuses(), request.getIssueKeys()))
                .types(getSplitStr(request.getTypes()))
                .rules(getSplitStr(request.getRules()))
                .authors(getSplitStr(request.getAuthors()))
                .issueKeys(getSplitStr(request.getIssueKeys()))
                .scanMode(ScanModeEnum.OFFLINE.getCode())
                .build();
        // 如果指定了文件路径
        Long issueId = request.getIssueId();
        if (issueId != null && issueId > 0) {
            condition.setLocation(issueSummaryService.getLocationById(issueId));
        }
        IPage<IssueSummary> issueSummaryIPage = issueSummaryService.pageByCondition(condition);
        long l1 = System.currentTimeMillis();
        log.info("listByCondition cost time is {} ms", (l1 - l));
        return issueSummaryIPage;
    }

    private List<String> fillStatues(String statuses, String issueKeys) {
        if (StringUtils.isEmpty(statuses)) {
            if (StringUtils.isNotEmpty(issueKeys)) {
                return CheckIssueStatus.allIssueStatus();
            }
            return Lists.newArrayList(CheckIssueStatus.OPEN.getStatus(), CheckIssueStatus.RE_OPEN.getStatus());
        }
        return getSplitStr(statuses);
    }

    private List<String> getSplitStr(String str) {
        return str == null ? null : Lists.newArrayList(str.split(","));
    }

    private List<Map<String, Object>> listGroupCount(IssueListRequest request, String columnName) {
        List<String> uniqIds = null;
        if (request.getIssueKeys() != null && request.getIssueKeys().length() != 0) {
            uniqIds = Lists.newArrayList(request.getIssueKeys().split(","));
        }
        return issueSummaryService.groupCountByCondition(request, columnName, ScanModeEnum.OFFLINE.getCode(), uniqIds);
    }

    @Override
    public SonarIssueVo transition(IssueTransitionRequest request, String userName) {
        return offlineScanIssueTransitionService.transition(request, userName);
    }

    @Override
    public void issueTransitionByBpm(IssueTransitionBpmRequest request, String userName) {
        List<FormDataListDTO> formItems = new ArrayList<>();
        // 构造表单数据
        formItems.add(new FormDataListDTO(bpmStandardIssueSkipFormKeys.getIssueUrl(), request.getIssueUrl()));
        formItems.add(new FormDataListDTO(bpmStandardIssueSkipFormKeys.getOperation(), request.getOperation()));
        formItems.add(new FormDataListDTO(bpmStandardIssueSkipFormKeys.getReason(), request.getReason()));
        formItems.add(new FormDataListDTO(bpmStandardIssueSkipFormKeys.getOperator(), userName));
        formItems.add(new FormDataListDTO(bpmStandardIssueSkipFormKeys.getTransitionRequestJson(),
                JSONUtils.serialize(request.getTransitionPayload())));
        BpmSubmitDto bpmSubmitDto = new BpmSubmitDto(bpmStandardIssueSkipFormKeys.getFormCode(), formItems, userName);
        try {
            // 发起快流程
            BpmRspDto rspDto = bpmOpenApi.processSubmit(bpmSubmitDto).execute().body();
            if (Objects.isNull(rspDto) || !"000000".equals(rspDto.getCode())) {
                throw new RuntimeException("流程中心提交失败：" + rspDto);
            }
        } catch (Exception e) {
            log.error("流程中心提交失败，request={}", JSONUtils.serialize(bpmSubmitDto));
            throw new ThemisException(org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
        }
    }

}
