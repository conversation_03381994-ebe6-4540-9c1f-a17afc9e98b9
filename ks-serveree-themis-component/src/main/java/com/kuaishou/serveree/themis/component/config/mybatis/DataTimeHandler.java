package com.kuaishou.serveree.themis.component.config.mybatis;

import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-24
 */
@Component
public class DataTimeHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        long time = System.currentTimeMillis();
        this.setFieldValByName("gmtCreate", time, metaObject);
        this.setFieldValByName("gmtModified", time, metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        long time = System.currentTimeMillis();
        this.setFieldValByName("gmtModified", time, metaObject);
    }
}
