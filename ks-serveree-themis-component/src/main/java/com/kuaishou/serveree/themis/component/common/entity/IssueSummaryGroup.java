package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "IssueSummaryGroup对象", description = "")
public class IssueSummaryGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "git项目id")
    private Integer gitProjectId;

    @ApiModelProperty(value = "git项目分支")
    private String gitBranch;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "严重程度")
    private String severity;

    @ApiModelProperty(value = "聚合数量")
    private Integer groupCount;

    @ApiModelProperty(value = "聚合时间（yyyyMMdd）")
    private Integer groupTime;

    @ApiModelProperty(value = "聚合类型（1 每周聚合）")
    private Integer groupType;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;


}
