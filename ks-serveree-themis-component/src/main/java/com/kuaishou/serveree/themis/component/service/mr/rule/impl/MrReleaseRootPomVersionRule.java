package com.kuaishou.serveree.themis.component.service.mr.rule.impl;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import com.kuaishou.serveree.themis.component.client.compile.CompileApi;
import com.kuaishou.serveree.themis.component.constant.kdev.MrCheckpointEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueType;
import com.kuaishou.serveree.themis.component.entity.compile.ReleaseRootPomVersionCheckRsp;
import com.kuaishou.serveree.themis.component.entity.issue.MrCheckpointIssueBo;
import com.kuaishou.serveree.themis.component.service.kdev.MrCheckpointBo;
import com.kuaishou.serveree.themis.component.service.mr.rule.MrCheckpointRule;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-11-15
 */
@Component
public class MrReleaseRootPomVersionRule implements MrCheckpointRule {

    @Resource
    private CompileApi compileApi;

    @Override
    public List<MrCheckpointIssueBo> check(MrCheckpointBo checkpointBo, List<String> filePaths) {
        if (!support(checkpointBo.getCheckpointName())) {
            return Collections.emptyList();
        }
        List<MrCheckpointIssueBo> issueList = Lists.newArrayList();

        // 过滤出所有根pom
        filePaths = CommonUtils.findAllParentPoms(filePaths);
        // 遍历，检测
        for (String pomPath : filePaths) {
            // 检查rootpom版本
            ReleaseRootPomVersionCheckRsp checkRsp =
                    compileApi.checkReleaseRootPomVersion(checkpointBo.getGitProjectId(), checkpointBo.getSourceBranch(),
                            checkpointBo.getCommitId(), pomPath);
            // 检查通过
            if (checkRsp.isValid()) {
                continue;
            }
            // 检查不通过
            String message = checkRsp.getMessage();
            // 行号
            int tagLine = checkRsp.getLineNumber();
            // 构造issue
            MrCheckpointIssueBo issue = new MrCheckpointIssueBo();
            issue.setCheckpointName(checkpointBo.getCheckpointName());
            issue.setRule(ruleKey());
            issue.setSeverity(CheckIssueSeverity.SERIOUS.getKey());
            issue.setType(CheckIssueType.BUG.getType());
            issue.setStuck(true);
            issue.setStatus(CheckIssueStatus.OPEN.getStatus());
            issue.setMessage(message);
            issue.setLocation(pomPath);
            issue.setStartLine(tagLine);
            issue.setEndLine(tagLine);
            issue.setStartOffset(0);
            issue.setEndOffset(0);
            issue.setAuthor("");
            issue.setOperator("");
            issue.setGmtCreate(LocalDateTime.now());
            issue.setGmtModified(LocalDateTime.now());
            issue.setIssueUniqId(issue.generateUniqId(checkpointBo.getGitProjectId(), checkpointBo.getCheckpointName()));
            issue.setIssueId((long) (issue.getIssueUniqId().hashCode() & 0x7fffffff));

            issueList.add(issue);
        }
        return issueList;
    }

    @Override
    public String ruleKey() {
        return "mr:rootpom:release_version";
    }

    @Override
    public boolean support(String checkpointName) {
        return MrCheckpointEnum.RootPomCheck.name().equals(checkpointName);
    }
}
