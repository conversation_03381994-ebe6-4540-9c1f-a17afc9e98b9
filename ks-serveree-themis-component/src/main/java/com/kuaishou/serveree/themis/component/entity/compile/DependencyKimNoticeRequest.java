package com.kuaishou.serveree.themis.component.entity.compile;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/11/24 9:30 AM
 */
public class DependencyKimNoticeRequest {

    /**
     * 接收kim消息的用户
     */
    private List<String> userList;

    /**
     * 流水线id
     */
    private Long ciJobId;

    /**
     * 流水线构建id
     */
    private Long ciTaskId;

    /**
     * 发送内容 一个case就是一个条目
     */
    private List<DependencyCheckResult> resultItems;

    public DependencyKimNoticeRequest() {
    }

    private DependencyKimNoticeRequest(Builder builder) {
        setUserList(builder.userList);
        setCiJobId(builder.ciJobId);
        setCiTaskId(builder.ciTaskId);
        setResultItems(builder.resultItems);
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public List<String> getUserList() {
        return userList;
    }

    public void setUserList(List<String> userList) {
        this.userList = userList;
    }

    public Long getCiJobId() {
        return ciJobId;
    }

    public void setCiJobId(Long ciJobId) {
        this.ciJobId = ciJobId;
    }

    public Long getCiTaskId() {
        return ciTaskId;
    }

    public void setCiTaskId(Long ciTaskId) {
        this.ciTaskId = ciTaskId;
    }

    public List<DependencyCheckResult> getResultItems() {
        return resultItems;
    }

    public void setResultItems(
            List<DependencyCheckResult> resultItems) {
        this.resultItems = resultItems;
    }

    public static final class Builder {
        private List<String> userList;
        private Long ciJobId;
        private Long ciTaskId;
        private List<DependencyCheckResult> resultItems;

        private Builder() {
        }

        public Builder userList(List<String> val) {
            userList = val;
            return this;
        }

        public Builder ciJobId(Long val) {
            ciJobId = val;
            return this;
        }

        public Builder ciTaskId(Long val) {
            ciTaskId = val;
            return this;
        }

        public Builder resultItems(List<DependencyCheckResult> val) {
            resultItems = val;
            return this;
        }

        public DependencyKimNoticeRequest build() {
            return new DependencyKimNoticeRequest(this);
        }
    }
}
