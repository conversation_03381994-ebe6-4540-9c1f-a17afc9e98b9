package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.config.mybatis.RootMapper;
import com.kuaishou.serveree.themis.component.entity.issue.IssueSummaryVo;

/**
 * <p>
 * 最新生效的issue表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Mapper
@Repository
public interface IssueSummaryMapper extends RootMapper<IssueSummary> {

    void updateBatchByProjectBranchUniqId(@Param("needUpdateList") Collection<IssueSummaryVo> needUpdateList);

    @Select({"<script>",
            "select count(case when type='BUG' then id end) as BUG,",
            "count(case when type='VULNERABILITY' then id end) as VULNERABILITY,",
            "count(case when severity='MAJOR' then id end) as MAJOR,",
            "count(case when severity='SERIOUS' then id end) as SERIOUS,",
            "count(case when severity='COMMON' then id end) as COMMON,",
            "count(case when severity='NOT_INDUCED' then id end) as NOT_INDUCED,",
            "count(case when status='OPEN' then id end) as OPEN,",
            "count(case when status='RE_OPEN' then id end) as RE_OPEN ",
            "from `issue_summary` where `git_project_id` = #{projectId} and `git_branch` = #{gitBranch} ",
            "<if test='uniqIdList != null and uniqIdList.size() > 0'> ",
            "and `issue_uniq_id` in <foreach collection='uniqIdList' item='element' open='(' separator=',' close=')'> #{element} </foreach> ",
            "</if> ",
            "<if test='severityList != null and severityList.size() > 0'> ",
            "and `severity` in <foreach collection='severityList' item='element' open='(' separator=',' close=')'> #{element} </foreach> ",
            "</if> ",
            "<if test='typeList != null and typeList.size() > 0'> ",
            "and `type` in <foreach collection='typeList' item='element' open='(' separator=',' close=')'> #{element} </foreach> ",
            "</if> ",
            "<if test='statusList != null and statusList.size() > 0'> ",
            "and `status` in <foreach collection='statusList' item='element' open='(' separator=',' close=')'> #{element} </foreach> ",
            "</if> ",
            "order by `location`, `start_line` ",
            "</script>"})
    Map<String, Integer> countFacetNum(@Param("projectId") Long projectId, @Param("gitBranch") String gitBranch,
            @Param("uniqIdList") List<String> uniqIdList, @Param("severityList") List<String> severityList,
            @Param("typeList") List<String> typeList, @Param("statusList") List<String> statusList);

    @Select("select location from issue_summary where id = #{id} limit 1")
    String getLocationById(Long id);
}
