package com.kuaishou.serveree.themis.component.service.platform.notice;

import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.entity.mr.MrStuckResultData;

/**
 * <AUTHOR>
 * @since 2022/11/18 5:22 PM
 */
public interface CheckNoticeActionService {

    void sendNotice(CheckBase checkBase);

    void sendMrStuckResultNotice(MrStuckResultData resultData);

    void compensatePushNotice();


}