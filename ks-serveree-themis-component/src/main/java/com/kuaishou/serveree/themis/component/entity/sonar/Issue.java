package com.kuaishou.serveree.themis.component.entity.sonar;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/21 3:24 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Issue {
    private String key;
    private String rule;
    private String severity;
    private String component;
    private String project;
    private String assignee;
    private int line;
    private String hash;
    private TextRange textRange;
    private String status;
    private String message;
    private String effort;
    private String debt;
    private String author;
    private List<String> tags;
    private List<String> transitions;
    private List<String> actions;
    private List<String> comments;
    private String creationDate;
    private String updateDate;
    private String type;
    private String organization;
    private boolean fromHotspot;
    private String closeDate;
    private List<SonarFlow> flows;
    private String resolution;

}
