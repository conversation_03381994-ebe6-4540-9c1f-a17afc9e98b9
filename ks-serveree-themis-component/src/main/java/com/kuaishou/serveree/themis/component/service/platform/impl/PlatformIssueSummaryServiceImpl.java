package com.kuaishou.serveree.themis.component.service.platform.impl;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.CheckIssue;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranchProfile;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummaryBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.constant.platform.CheckProfileType;
import com.kuaishou.serveree.themis.component.constant.platform.IssueRepairType;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.entity.platform.BaseIssue;
import com.kuaishou.serveree.themis.component.entity.platform.IssueSummaryListCondition;
import com.kuaishou.serveree.themis.component.service.CheckProfileRuleRelationService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryBaseService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.PCheckIssueService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformIssueSummaryService;

import cn.hutool.core.bean.BeanUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-03-15
 */
@Slf4j
@Service
public class PlatformIssueSummaryServiceImpl implements PlatformIssueSummaryService {

    private final IssueSummaryBaseService issueSummaryBaseService;
    private final IssueSummaryService issueSummaryService;
    private final CheckRepoService checkRepoService;
    private final CheckRepoBranchService checkRepoBranchService;
    private final PCheckIssueService pCheckIssueService;
    private final CheckRepoBranchProfileService checkRepoBranchProfileService;
    private final CheckProfileRuleRelationService profileRuleRelationService;

    // CHECKSTYLE:OFF
    public PlatformIssueSummaryServiceImpl(IssueSummaryBaseService issueSummaryBaseService,
            IssueSummaryService issueSummaryService, CheckRepoService checkRepoService,
            CheckRepoBranchService checkRepoBranchService,
            PCheckIssueService pCheckIssueService, CheckRepoBranchProfileService checkRepoBranchProfileService,
            CheckProfileRuleRelationService profileRuleRelationService) {
        this.issueSummaryBaseService = issueSummaryBaseService;
        this.issueSummaryService = issueSummaryService;
        this.checkRepoService = checkRepoService;
        this.checkRepoBranchService = checkRepoBranchService;
        this.pCheckIssueService = pCheckIssueService;
        this.checkRepoBranchProfileService = checkRepoBranchProfileService;
        this.profileRuleRelationService = profileRuleRelationService;
    }
    // CHECKSTYLE:ON

    /**
     * 对比本次和 三个月内还未解决的Issue，汇总出 要新增、修改 的 IssueSummary （离线）
     * 使用 新的issueUniqId 策略
     */
    @Override
    public Pair<List<IssueSummary>, List<IssueSummary>> compareAndGenSummaryIssues(List<CheckIssue> checkIssues,
            CheckBase checkBase, CheckExecution checkExecution) {
        // 查出已经存在的相同项目分支scanner issue
        CheckRepo checkRepo = checkRepoService.getById(checkBase.getCheckRepoId());
        CheckRepoBranch checkRepoBranch = checkRepoBranchService.getById(checkBase.getCheckRepoBranchId());
        // 最近3个月内所有还未解决的问题
        List<IssueSummary> dbCheckIssues = issueSummaryService.listByCondition(
                IssueSummaryListCondition.builder()
                        .gitProjectId(checkRepo.getGitProjectId())
                        .branch(checkRepoBranch.getBranchName())
                        .scanMode(ScanModeEnum.OFFLINE.getCode())
                        .statuses(CheckIssueStatus.unresolvedStatuses())
                        .minGmtModified(checkBase.getGmtCreate().minusMonths(3))
                .build()
        );
        int referType = checkExecution.getReferType();
        Pair<List<IssueSummary>, List<IssueSummary>> pair = compareAndGenSummaryIssues(
                checkRepo.getGitProjectId(), checkRepoBranch.getBranchName(),
                checkIssues.stream().map(c -> BaseIssue.convertFromCheckIssue(c, referType))
                        .collect(Collectors.toList()),
                dbCheckIssues.stream().map(c -> BaseIssue.convertFromCheckIssue(c, referType))
                        .collect(Collectors.toList()),
                ScanModeEnum.OFFLINE
        );
        log.debug("offline compareAndGenSummaryIssues, baseId:{}, needSaveList:{}, needUpdateList:{}",
                    checkBase.getId(), pair.getLeft(), pair.getRight());
        return pair;
    }

    /**
     * 对比本次和上一次的issue，汇总出 要新增、修改 的 IssueSummary （流水线）
     * 使用 新的issueUniqId 策略
     */
    @Override
    public Pair<List<IssueSummary>, List<IssueSummary>> compareAndGenSummaryIssues(List<PCheckIssue> pCheckIssueList,
            PCheckBase pCheckBase, PCheckExecution pCheckExecution, PCheckBase latestPCheckBase) {
        Integer referType = pCheckExecution.getReferType();
        // 上次扫描的数据
        List<PCheckIssue> latestPCheckIssueList =
                Objects.isNull(latestPCheckBase) ? Collections.emptyList()
                                                 : pCheckIssueService.listByPBaseIdAndType(latestPCheckBase.getId(),
                                                         referType);
        Pair<List<IssueSummary>, List<IssueSummary>> pair = compareAndGenSummaryIssues(
                pCheckBase.getProjectId(), pCheckBase.getBranch(),
                pCheckIssueList.stream().map(c -> BaseIssue.convertFromPCheckIssue(c, referType))
                        .collect(Collectors.toList()),
                latestPCheckIssueList.stream().map(c -> BaseIssue.convertFromPCheckIssue(c, referType))
                        .collect(Collectors.toList()),
                ScanModeEnum.PROCESS
        );
        log.info("pipeline compareAndGenSummaryIssues, baseId:{}, prevBaseId:{}", pCheckBase.getId(),
                Objects.nonNull(latestPCheckBase) ? latestPCheckBase.getId() : null);
        log.debug("pipeline compareAndGenSummaryIssues, baseId: {}, needSaveList:{}, needUpdateList:{}",
                    pCheckBase.getId(), pair.getLeft(), pair.getRight());
        return pair;
    }

    /**
     * 对比本次和上一次的issue，汇总出 要新增、修改 的 IssueSummary （离线/流水线）
     * 使用 新的issueUniqId 策略
     */
    private Pair<List<IssueSummary>, List<IssueSummary>> compareAndGenSummaryIssues(Integer gitProjectId, String branch,
            List<BaseIssue> checkIssues, List<BaseIssue> dbCheckIssues, ScanModeEnum scanMode) {

        List<IssueSummary> needSaveList = Lists.newArrayList();
        List<IssueSummary> needUpdateList = Lists.newArrayList();
        // 上次是空的
        if (CollectionUtils.isEmpty(dbCheckIssues)) {
            // 本次是空的
            if (CollectionUtils.isEmpty(checkIssues)) {
                // 不需要操作
                return Pair.of(needSaveList, needUpdateList);
            }
            // 本次不空
            handleThisTimeIssues(gitProjectId, branch, checkIssues, needSaveList, needUpdateList, scanMode);
            return Pair.of(needSaveList, needUpdateList);
        }
        // 上次不空
        // 寻找在本次结果中不存在的issue，更改状态为解决
        Set<String> checkIssueUniqIdSet =
                checkIssues.stream().map(BaseIssue::getIssueUniqId)
                        .filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Set<String> checkIssueUniqIdV2Set =
                checkIssues.stream().map(BaseIssue::getIssueUniqIdV2).collect(Collectors.toSet());
        solveLastTimeIssues(
                dbCheckIssues.stream().filter(issue -> {
                    if (StringUtils.isNotBlank(issue.getIssueUniqIdV2())) {
                        return !checkIssueUniqIdV2Set.contains(issue.getIssueUniqIdV2());
                    }
                    return !checkIssueUniqIdSet.contains(issue.getIssueUniqId());
                }).collect(Collectors.toList()),
                gitProjectId, branch, scanMode, needUpdateList
        );
        // 处理本次扫描出的issue
        handleThisTimeIssues(gitProjectId, branch, checkIssues, needSaveList, needUpdateList, scanMode);
        return Pair.of(needSaveList, needUpdateList);
    }

    private Set<String> getActiveRuleKeysByGitProjectIdAndScanMode(Integer gitProjectId, ScanModeEnum scanMode) {
        // 查仓库
        CheckRepo checkRepo = checkRepoService.getCheckRepoByProjectId(gitProjectId);
        // 查分支
        CheckRepoBranch repoBranch = checkRepoBranchService.getOfflineScanBranchByRepoId(checkRepo.getId());
        if (repoBranch == null) {
            return Collections.emptySet();
        }
        CheckProfileType profileType = CheckProfileType.getByScanMode(scanMode);
        // 查规则集
        CheckRepoBranchProfile branchProfile =
                checkRepoBranchProfileService.getByBranchIdAndProfileType(repoBranch.getId(), profileType.getType());
        // 非java、go、skyeye扫描器，返回空集合，对于 skyeye 扫描器，暂时不存在 流水线规则集，也返回空集合
        if (branchProfile == null) {
            return Collections.emptySet();
        }
        // 查规则
        return profileRuleRelationService.listActiveRuleKeysWithAncestorsByProfileName(branchProfile.getProfileName());
    }


    /**
     * 保存本次的issue
     */
    private void handleThisTimeIssues(Integer gitProjectId, String branch, List<BaseIssue> checkIssues,
            List<IssueSummary> needSaveList, List<IssueSummary> needUpdateList, ScanModeEnum scanMode) {
        if (CollectionUtils.isEmpty(checkIssues)) {
            return;
        }
        // 使用新的idV2
        List<String> issueUniqIdV2List = checkIssues.stream().map(BaseIssue::getIssueUniqIdV2)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        // 查询出所有的summaryBase数据
        Map<String, IssueSummaryBase> summaryBaseUniqIdV2Map =
                issueSummaryBaseService.aggragateIssueUniqIdV2SummaryBaseMap(issueUniqIdV2List);
        List<BaseIssue> preparedSaveList = Lists.newArrayList();
        // 查询所有issue在同分支上的issueSummary记录
        Map<String, IssueSummary> sameBranchSummaryV2Map =
                issueSummaryService.sameBranchSummaryUseIssueUniqIdV2(issueUniqIdV2List, gitProjectId, branch, scanMode.getCode());
        LocalDateTime now = LocalDateTime.now();
        // 遍历本次的issue
        for (BaseIssue checkIssue : checkIssues) {
            IssueSummaryBase summaryBase = summaryBaseUniqIdV2Map.get(checkIssue.getIssueUniqIdV2());
            if (Objects.isNull(summaryBase)) {
                // 新key判断不存在，预备新增
                preparedSaveList.add(checkIssue);
                continue;
            }
            // 新key判断存在base，则更新或新增同分支记录
            IssueSummary sameBranchSummary = sameBranchSummaryV2Map.get(checkIssue.getIssueUniqIdV2());
            String effectStatus = issueSummaryBaseService.getEffectStatus(summaryBase);
            saveOrUpdateThisTimeIssue(checkIssue, sameBranchSummary, gitProjectId, branch,
                    effectStatus, needSaveList, needUpdateList, scanMode, now);
        }
        // 处理预备新增的issue
        processPreparedSaveIssues(preparedSaveList, gitProjectId, branch, scanMode, needSaveList, needUpdateList);
    }

    /**
     * 解决上一次的issue
     */
    private void solveLastTimeIssues(List<BaseIssue> dbCheckIssues, Integer gitProjectId, String branch, ScanModeEnum scanMode,
            List<IssueSummary> needUpdateList) {
        if (CollectionUtils.isEmpty(dbCheckIssues)) {
            return;
        }
        // 获取当前项目、当前扫描模式、当前绑定的规则集中的规则列表
        Set<String> ruleKeys = getActiveRuleKeysByGitProjectIdAndScanMode(gitProjectId, scanMode);
        // 有新key则用新key，否则用旧key
        Map<Boolean, List<BaseIssue>> map = dbCheckIssues.stream()
                .collect(Collectors.groupingBy(a -> StringUtils.isNotBlank(a.getIssueUniqIdV2())));
        List<BaseIssue> dbIssuesUseUniqIdV2 = map.get(Boolean.TRUE);
        List<BaseIssue> dbIssuesUseUniqId = map.get(Boolean.FALSE);
        // 查询所有issue在同分支上的issueSummary记录
        Map<String, IssueSummary> sameBranchSummaryMap;
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isNotEmpty(dbIssuesUseUniqIdV2)) {
            // 使用新key
            List<String> issueUniqIdV2List =
                    dbCheckIssues.stream().map(BaseIssue::getIssueUniqIdV2).collect(Collectors.toList());
            sameBranchSummaryMap = issueSummaryService.sameBranchSummaryUseIssueUniqIdV2(
                    issueUniqIdV2List, gitProjectId, branch, scanMode.getCode()
            );
            solveLastTimeIssues(dbIssuesUseUniqIdV2, needUpdateList, now, sameBranchSummaryMap, true, ruleKeys);
        }
        if (CollectionUtils.isNotEmpty(dbIssuesUseUniqId)) {
            // 使用旧key
            List<String> issueUniqIdList =
                    dbCheckIssues.stream().map(BaseIssue::getIssueUniqId).collect(Collectors.toList());
            sameBranchSummaryMap = issueSummaryService.sameBranchSummary(issueUniqIdList, branch, scanMode.getCode());
            solveLastTimeIssues(dbIssuesUseUniqId, needUpdateList, now, sameBranchSummaryMap, false, ruleKeys);
        }
    }

    private void solveLastTimeIssues(List<BaseIssue> lastIssues, List<IssueSummary> needUpdateList, LocalDateTime now,
            Map<String, IssueSummary> sameBranchSummaryMap,
            boolean useUniqIdV2, Set<String> ruleKeys) {
        // 遍历上次的issue
        for (BaseIssue dbCheckIssue : lastIssues) {
            // 获取同分支summary，区分新旧key字段
            IssueSummary sameBranchSummary = sameBranchSummaryMap.get(
                    useUniqIdV2 ? dbCheckIssue.getIssueUniqIdV2() : dbCheckIssue.getIssueUniqId()
            );
            if (Objects.isNull(sameBranchSummary)) {
                // 先打error日志，手动查库验证下，没问题后可以删除
                log.error("未被保存的历史issue：{}", dbCheckIssue);
                continue;
            }
            sameBranchSummary.setStatus(CheckIssueStatus.RESOLVED.getStatus());
            sameBranchSummary.setRepairType(getIssueRepairType(sameBranchSummary, ruleKeys));
            sameBranchSummary.setRepairTime(now);
            sameBranchSummary.setGmtModified(now);
            needUpdateList.add(sameBranchSummary);
        }
    }

    private int getIssueRepairType(IssueSummary issueSummary, Set<String> activeRuleKeys) {
        return activeRuleKeys.contains(issueSummary.getRule())
               ? IssueRepairType.CODE_UPDATED.getType() : IssueRepairType.PROFILE_UPDATED.getType();
    }

    /**
     * 处理预备插入数据库的issue
     */
    private void processPreparedSaveIssues(List<BaseIssue> preparedSaveList, Integer gitProjectId, String branch,
            ScanModeEnum scanMode, List<IssueSummary> needSaveList, List<IssueSummary> needUpdateList) {
        if (CollectionUtils.isEmpty(preparedSaveList)) {
            return;
        }
        // 使用旧key做二次判断
        List<String> issueUniqIdList =
                preparedSaveList.stream().map(BaseIssue::getIssueUniqId).collect(Collectors.toList());
        // 查询出所有的summaryBase数据
        Map<String, IssueSummaryBase> summaryBaseUniqIdMap =
                issueSummaryBaseService.aggragateIssueUniqIdSummaryBaseMap(issueUniqIdList);
        // 查询所有issue在同分支上的issueSummary记录
        Map<String, IssueSummary> sameBranchSummaryMap =
                issueSummaryService.sameBranchSummary(issueUniqIdList, branch, scanMode.getCode());
        LocalDateTime now = LocalDateTime.now();
        // 遍历本次的issue
        for (BaseIssue checkIssue : preparedSaveList) {
            // 查询同分支记录
            IssueSummary sameBranchSummary = sameBranchSummaryMap.get(checkIssue.getIssueUniqId());
            String effectStatus =
                    issueSummaryBaseService.getEffectStatus(summaryBaseUniqIdMap.get(checkIssue.getIssueUniqId()));
            saveOrUpdateThisTimeIssue(checkIssue, sameBranchSummary, gitProjectId,
                    branch, effectStatus, needSaveList, needUpdateList, scanMode, now);
        }
    }

    // CHECKSTYLE:OFF
    /**
     * 插入新的issue或更新已有的同分支的issue
     */
    private void saveOrUpdateThisTimeIssue(BaseIssue checkIssue, IssueSummary sameBranchSummary,
            Integer gitProjectId, String branch, String effectStatus, List<IssueSummary> needSaveList,
            List<IssueSummary> needUpdateList, ScanModeEnum scanMode, LocalDateTime now) {
        // 不存在同分支记录，需要新插入一条summary记录
        if (Objects.isNull(sameBranchSummary)) {
            IssueSummary issueSummary = new IssueSummary();
            BeanUtil.copyProperties(checkIssue, issueSummary);
            issueSummary.setId(null);
            issueSummary
                    .setStatus(StringUtils.defaultString(effectStatus, CheckIssueStatus.OPEN.getStatus()));
            issueSummary.setGitProjectId(gitProjectId);
            issueSummary.setGitBranch(branch);
            issueSummary.setScanMode(scanMode.getCode());
            needSaveList.add(issueSummary);
            return;
        }
        // 存在同分支的summary记录，直接更新
        String finalStatus = CheckIssueStatus.OPEN.getStatus();
        String status = sameBranchSummary.getStatus();
        if (StringUtils.isEmpty(effectStatus)) {
            if (CheckIssueStatus.RESOLVED.getStatus().equals(status)) {
                finalStatus = CheckIssueStatus.RE_OPEN.getStatus();
            }
        } else {
            finalStatus = effectStatus;
        }
        sameBranchSummary.setStatus(finalStatus);
        sameBranchSummary.setGmtModified(now);
        sameBranchSummary.setSeverity(checkIssue.getSeverity());
        sameBranchSummary.setScanMode(scanMode.getCode());
        sameBranchSummary.setIssueUniqIdV2(checkIssue.getIssueUniqIdV2());
        sameBranchSummary.setSonarIssueKey(checkIssue.getSonarIssueKey());
        sameBranchSummary.setIssueLink(checkIssue.getIssueLink());
        sameBranchSummary.setGitLink(checkIssue.getGitLink());
        sameBranchSummary.setStartLine(checkIssue.getStartLine());
        sameBranchSummary.setEndLine(checkIssue.getEndLine());
        sameBranchSummary.setStartOffset(checkIssue.getStartOffset());
        sameBranchSummary.setEndOffset(checkIssue.getEndOffset());
        sameBranchSummary.setLocation(checkIssue.getLocation());
        sameBranchSummary.setMessage(checkIssue.getMessage());
        needUpdateList.add(sameBranchSummary);
    }
    // CHECKSTYLE:ON
}
