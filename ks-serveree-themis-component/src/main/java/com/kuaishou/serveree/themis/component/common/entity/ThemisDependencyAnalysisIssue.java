package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ThemisDependencyAnalysisIssue对象", description = "")
public class ThemisDependencyAnalysisIssue implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "依赖分析id")
    private Long dependencyAnalysisId;

    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    @ApiModelProperty(value = "使用的未声明的依赖")
    private String usedUndeclaredDependency;

    @ApiModelProperty(value = "未使用的声明的依赖")
    private String unusedDeclaredDependency;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "依赖大小")
    private Long dependencyLength;

    @ApiModelProperty(value = "无用依赖是否打点标记")
    private Boolean unusedDependencyPerfFlag;

}
