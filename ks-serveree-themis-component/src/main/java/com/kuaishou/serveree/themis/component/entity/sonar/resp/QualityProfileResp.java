package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Actions;
import com.kuaishou.serveree.themis.component.entity.sonar.Profiles;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/7/26 5:11 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QualityProfileResp {

    private List<Profiles> profiles;
    private Actions actions;

}
