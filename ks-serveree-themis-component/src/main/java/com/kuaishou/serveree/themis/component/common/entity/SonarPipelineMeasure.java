package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarPipelineMeasureElement;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "SonarPipelineMeasure对象", description = "")
public class SonarPipelineMeasure implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "ksp的buildId")
    private Long kspBuildId;

    private Long kspPipelineId;

    private Integer gitProjectId;

    private String branch;

    private String projectKey;

    /**
     * {@link SonarPipelineMeasureElement}的 json结构
     */
    @ApiModelProperty(value = "bug度量数据")
    private String bugMeasure;

    /**
     * {@link SonarPipelineMeasureElement}的 json结构
     */
    @ApiModelProperty(value = "漏洞度量数据")
    private String vulnerabilityMeasure;

    /**
     * {@link SonarPipelineMeasureElement}的 json结构
     */
    @ApiModelProperty(value = "坏味道度量数据")
    private String codeSmellMeasure;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;


}
