package com.kuaishou.serveree.themis.component.entity.statics;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/11/4 2:28 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ContentRowHeight(20)
public class InterfaceScanIssueExcelMeta {

    @ColumnWidth(13)
    @ExcelProperty({"git group id"})
    private Integer groupId;

    // project id
    @ColumnWidth(13)
    @ExcelProperty({"project id"})
    private Integer projectId;

    @ColumnWidth(30)
    @ExcelProperty({"项目地址"})
    private String repoUrl;

    @ColumnWidth(75)
    @ExcelProperty({"文件定位"})
    private String location;

    // 错误代码
    @ColumnWidth(75)
    @ExcelProperty({"具体代码"})
    private String codeStr;

    @ColumnWidth(10)
    @ExcelProperty({"代码行号"})
    private int lineNo;

    @ColumnWidth(30)
    @ExcelProperty({"扫描case"})
    private String caseStr;

    // 超链接
    @ColumnWidth(75)
    @ExcelProperty({"链接"})
    private String hyperlink;

}
