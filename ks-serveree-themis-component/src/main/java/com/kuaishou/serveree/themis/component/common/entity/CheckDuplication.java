package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 最新生效的issue表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "CheckDuplication对象", description = "最新生效的issue表")
public class CheckDuplication implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "检查主表id")
    private Long baseId;

    @ApiModelProperty(value = "执行表id")
    private Long executionId;

    @ApiModelProperty(value = "标记")
    private String kee;

    @ApiModelProperty(value = "check_issue主键id")
    private Long checkIssueId;

    @ApiModelProperty(value = "代码路径")
    private String location;

    @ApiModelProperty(value = "问题作者")
    private String author;

    @ApiModelProperty(value = "创建时的commitId")
    private String createCommitId;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "状态时间")
    private String status;

    @ApiModelProperty(value = "修复者")
    private String repairer;

    @ApiModelProperty(value = "修复时commitId")
    private String repairCommitId;

    @ApiModelProperty(value = "修复时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime repairTime;

    @ApiModelProperty(value = "1.存量 2.增量")
    private Integer effect;

    @ApiModelProperty(value = "check_repo表的id")
    private Long checkRepoId;

    @ApiModelProperty(value = "repo_branch表主键id")
    private Long checkRepoBranchId;

    @ApiModelProperty(value = "第几行开始")
    private Integer startLine;

    @ApiModelProperty(value = "第几行结束")
    private Integer endLine;

    private String gitLink;

    @ApiModelProperty(value = "起始偏移量")
    private Integer startOffset;

    @ApiModelProperty(value = "结束偏移量")
    private Integer endOffset;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    private Integer gitProjectId;
    private String gitBranch;
    private String issueUniqId;
    private Integer type;
    private Integer lineCount;
    private Integer linePercent;
    private Integer blockCount;
    private Long fileId;

}
