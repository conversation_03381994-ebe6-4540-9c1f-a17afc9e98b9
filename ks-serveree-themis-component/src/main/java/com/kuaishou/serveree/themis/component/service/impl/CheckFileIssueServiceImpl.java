package com.kuaishou.serveree.themis.component.service.impl;

import static com.google.common.util.concurrent.Uninterruptibles.sleepUninterruptibly;
import static java.util.stream.Collectors.toList;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileIssue;
import com.kuaishou.serveree.themis.component.common.mappers.CheckFileIssueMapper;
import com.kuaishou.serveree.themis.component.entity.platform.FileIssueNumDto;
import com.kuaishou.serveree.themis.component.service.CheckFileIssueService;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Service
public class CheckFileIssueServiceImpl extends ServiceImpl<CheckFileIssueMapper, CheckFileIssue>
        implements CheckFileIssueService {

    private static final Integer PAGE_SIZE = 1000;

    @Autowired
    private CheckFileIssueMapper checkFileIssueMapper;

    @Override
    public Map<Long, List<CheckFileIssue>> listFilesIssuesByRule(CheckBase checkBase, String ruleKey) {
        Wrapper<CheckFileIssue> query = new QueryWrapper<CheckFileIssue>().lambda()
                .eq(CheckFileIssue::getCheckRepoId, checkBase.getCheckRepoId())
                .eq(CheckFileIssue::getCheckRepoBranchId, checkBase.getCheckRepoBranchId())
                .eq(CheckFileIssue::getBaseId, checkBase.getId())
                .eq(CheckFileIssue::getRuleKey, ruleKey);
        return this.list(query).stream()
                .collect(Collectors.groupingBy(CheckFileIssue::getFileId));
    }

    @Override
    public Map<Long, List<CheckFileIssue>> listFileIssuesByRuleAndId(CheckBase checkBase, String ruleKey, Long fileId) {
        Wrapper<CheckFileIssue> query = new QueryWrapper<CheckFileIssue>().lambda()
                .eq(CheckFileIssue::getCheckRepoId, checkBase.getCheckRepoId())
                .eq(CheckFileIssue::getCheckRepoBranchId, checkBase.getCheckRepoBranchId())
                .eq(CheckFileIssue::getBaseId, checkBase.getId())
                .eq(CheckFileIssue::getRuleKey, ruleKey)
                .eq(CheckFileIssue::getFileId, fileId);
        return this.list(query).stream()
                .collect(Collectors.groupingBy(CheckFileIssue::getFileId));
    }

    @Override
    public List<CheckFileIssue> listFileIssuesByRule(CheckBase checkBase, Long fileId) {
        Wrapper<CheckFileIssue> query = new QueryWrapper<CheckFileIssue>().lambda()
                .eq(CheckFileIssue::getCheckRepoId, checkBase.getCheckRepoId())
                .eq(CheckFileIssue::getCheckRepoBranchId, checkBase.getCheckRepoBranchId())
                .eq(CheckFileIssue::getBaseId, checkBase.getId())
                .eq(CheckFileIssue::getFileId, fileId);
        return this.list(query);
    }


    @Override
    public Map<Long, Integer> mapIssuesNumOfFile(long repoId, long branchId, long baseId) {
        return checkFileIssueMapper.fileIssueNumSort(repoId, branchId, baseId, null).stream()
                .collect(Collectors.toMap(FileIssueNumDto::getFileId, FileIssueNumDto::getIssueNum, (existing, replacement) -> existing));
    }

    @Override
    public List<FileIssueNumDto> fileIssueNumSort(long repoId, long branchId, long baseId, int limit) {
        return checkFileIssueMapper.fileIssueNumSort(repoId, branchId, baseId, limit);
    }

    @Override
    public Map<String, Integer> mapIssueNumOfRule(long repoId, long branchId, long baseId) {
        return checkFileIssueMapper.ruleIssueNumSort(repoId, branchId, baseId, null).stream()
                .collect(Collectors.toMap(FileIssueNumDto::getRuleKey, FileIssueNumDto::getIssueNum, (existing, replacement) -> existing));
    }

    @Override
    public List<FileIssueNumDto> ruleIssueNumSort(long repoId, long branchId, long baseId, int limit) {
        return checkFileIssueMapper.ruleIssueNumSort(repoId, branchId, baseId, limit);
    }

    @Override
    public List<CheckFileIssue> listByBaseId(long repoId, long branchId, long baseId) {
        Wrapper<CheckFileIssue> query = new QueryWrapper<CheckFileIssue>().lambda()
                .eq(CheckFileIssue::getCheckRepoId, repoId)
                .eq(CheckFileIssue::getCheckRepoBranchId, branchId)
                .eq(CheckFileIssue::getBaseId, baseId);
        return this.list(query);
    }

    @Override
    public IPage<CheckFileIssue> pageByCheckRepoAndBaseId(Long checkRepoId, Long checkRepoBranchId, Long baseId,
            Integer page, Integer pageSize) {
        Wrapper<CheckFileIssue> queryWrapper = new QueryWrapper<CheckFileIssue>().lambda()
                .eq(CheckFileIssue::getCheckRepoId, checkRepoId)
                .eq(CheckFileIssue::getCheckRepoBranchId, checkRepoBranchId)
                .eq(CheckFileIssue::getBaseId, baseId);
        return this.page(new Page<>(page, pageSize), queryWrapper);
    }

    @Override
    public void pageDelByCheckRepoAndBaseId(Long checkRepoId, Long checkRepoBranchId, Long baseId) {
        while (true) {
            IPage<CheckFileIssue> checkFileIPage =
                    pageByCheckRepoAndBaseId(checkRepoId, checkRepoBranchId, baseId, 1, PAGE_SIZE);
            List<CheckFileIssue> records = checkFileIPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                return;
            }
            Wrapper<CheckFileIssue> queryWrapper = new QueryWrapper<CheckFileIssue>().lambda()
                    .eq(CheckFileIssue::getCheckRepoId, checkRepoId)
                    .eq(CheckFileIssue::getCheckRepoBranchId, checkRepoBranchId)
                    .eq(CheckFileIssue::getBaseId, baseId)
                    .in(CheckFileIssue::getId, records.stream().map(CheckFileIssue::getId).collect(toList()));
            this.remove(queryWrapper);
            sleepUninterruptibly(1, TimeUnit.SECONDS);
        }
    }

    @Override
    public Integer insertBatch(Collection<CheckFileIssue> checkFileIssues) {
        return checkFileIssueMapper.insertBatchSomeColumn(checkFileIssues);
    }
}
