package com.kuaishou.serveree.themis.component.common.mappers;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import com.kuaishou.serveree.themis.component.common.entity.ComplexityFile;
import com.kuaishou.serveree.themis.component.config.mybatis.RootMapper;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Mapper
@Repository
public interface ComplexityFileMapper extends RootMapper<ComplexityFile> {

    @Select("select id from complexity_file where repository_id = #{repositoryId}")
    List<Long> selectIdsByRepositoryId(Long repositoryId);
}
