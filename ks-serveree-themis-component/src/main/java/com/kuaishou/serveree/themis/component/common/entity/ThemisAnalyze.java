package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ThemisAnalyze对象", description = "")
public class ThemisAnalyze implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "执行检查的任务id")
    private Long taskId;

    @ApiModelProperty(value = "repo地址")
    private String repoUrl;

    @ApiModelProperty(value = "分支名")
    private String branch;

    private Integer repoId;

    @ApiModelProperty(value = "commitId")
    private String commitId;

    @ApiModelProperty(value = "检查人")
    private String checker;

    @ApiModelProperty(value = "流水线id")
    private Long originalKspPipelineId;

    @ApiModelProperty(value = "构建id")
    private Long originalKspBuildId;

    @ApiModelProperty(value = "git的merge-request id")
    private String mrId;

    @ApiModelProperty(value = "cicheck检查级别")
    private String cicheckLevel;

    @ApiModelProperty(value = "checkstyle检查级别")
    private String checkstyleLevel;

    @ApiModelProperty(value = "kcheck检查级别")
    private String kcheckLevel;

    @ApiModelProperty(value = "来源")
    private String source;

    private Integer type;

    private Boolean sync;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createdTime;


}
