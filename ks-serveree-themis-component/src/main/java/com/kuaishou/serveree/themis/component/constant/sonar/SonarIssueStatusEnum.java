package com.kuaishou.serveree.themis.component.constant.sonar;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-24
 */
@Getter
@AllArgsConstructor
public enum SonarIssueStatusEnum {
    OPEN("已打开"),
    CONFIRMED("已确认"),
    REOPENED("重新打开"),
    RESOLVED_FP("已解决误判"),
    RESOLVED_WF("已解决不会修复"),
    RESOLVED_FIXED("已解决已修复"),
    TO_REVIEW("待审查"),
    IN_REVIEW("审查中"),
    REVIEWED("已审查"),
    ;

    private String desc;
}
