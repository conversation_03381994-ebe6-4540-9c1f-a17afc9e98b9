package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "IssueTeamRelation对象", description = "")
public class IssueTeamRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "ksp的build_id")
    private String kspBuildId;

    @ApiModelProperty(value = "team任务的id")
    private String teamId;

    @ApiModelProperty(value = "全局唯一issueId")
    private String issueUniqId;

    @ApiModelProperty(value = "问题状态 'OPEN','CLOSED','RESOLVED','TO_REVIEW'")
    private String status;

    @ApiModelProperty(value = "问题作者")
    private String author;

    @ApiModelProperty(value = "级别")
    private String severity;

    @ApiModelProperty(value = "git project id")
    private Integer gitProjectId;

    @ApiModelProperty(value = "git的分支")
    private String gitBranch;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "1.流程 2.离线扫描")
    private Integer scanMode;

    @ApiModelProperty(value = "0.增量 1.全量")
    private Integer checkMode;

    @ApiModelProperty(value = "0.未删除 1.删除")
    private Integer deleted;

    private String commonIssueUniqId;
    private String commonIssueUniqIdCopy;
    private String issueUniqIdCopy;
}
