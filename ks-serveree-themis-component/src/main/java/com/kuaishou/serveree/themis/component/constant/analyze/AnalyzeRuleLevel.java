package com.kuaishou.serveree.themis.component.constant.analyze;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2021/1/8 6:32 下午
 */
@AllArgsConstructor
public enum AnalyzeRuleLevel {

    NO_CHECK("0", 0, "无检查"),
    SERIOUS("I", 1, "阻断"),
    MAJOR("II", 2, "严重"),
    ORDINARY("III", 3, "主要"),
    ;

    @Getter
    private String level;

    @Getter
    private int order;

    @Getter
    private String description;

    private static final Map<String, AnalyzeRuleLevel> CODE_MAP = Maps.newHashMap();

    static {
        Arrays.stream(AnalyzeRuleLevel.values()).forEach(analyzeRuleLevel -> {
            CODE_MAP.put(analyzeRuleLevel.getLevel(), analyzeRuleLevel);
        });
    }

    /**
     * 根据一个初始的level获取包含与未包含的level pair
     * @param level 初始的level
     * @return left为cover的 right为未cover的
     */
    public static Pair<List<String>, List<String>> getLevelPairByLevel(String level) {
        if (StringUtils.isEmpty(level)) {
            return new ImmutablePair<>(Collections.emptyList(), Collections.emptyList());
        }
        AnalyzeRuleLevel analyzeRuleLevel = CODE_MAP.get(level);
        int order = analyzeRuleLevel.getOrder();
        List<String> coverList = Lists.newArrayList();
        List<String> unCoverList = Lists.newArrayList();
        CODE_MAP.values().forEach(ruleLevel -> {
            if (ruleLevel.getOrder() > order) {
                unCoverList.add(ruleLevel.getLevel());
            } else {
                if (ruleLevel.getOrder() != 0) {
                    coverList.add(ruleLevel.getLevel());
                }
            }
        });
        return new ImmutablePair<>(coverList, unCoverList);
    }

}
