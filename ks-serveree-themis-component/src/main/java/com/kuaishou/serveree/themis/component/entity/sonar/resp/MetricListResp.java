package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-01
 */
@Data
public class MetricListResp {
    private List<MetricsInfo> metrics;
    private Integer p;
    private Integer ps;
    private Integer total;

    @Data
    public static class MetricsInfo {
        private String id;
        private String key;
        private String name;
        private String description;
        private String domain;
        private String type;
        private Boolean qualitative;
        private String hidden;
        private String custom;
    }
}
