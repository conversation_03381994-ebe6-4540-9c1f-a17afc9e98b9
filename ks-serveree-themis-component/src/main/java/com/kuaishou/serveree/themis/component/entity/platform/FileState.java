package com.kuaishou.serveree.themis.component.entity.platform;

import java.math.BigDecimal;
import java.math.RoundingMode;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-07-12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FileState {

    private IssueState issueState;
    private ComplexityState complexityState;
    private DuplicationState duplicationState;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class IssueState {
        private int healthFile;
        private int unHealthFile;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ComplexityState {
        private int excellentFile;
        private int goodFile;
        private int unqualifiedFile;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class DuplicationState {
        private int totalFile;
        private int duplicationFile;

        public String getPercent() {
            if (this.totalFile == 0) {
                return "0.00";
            }
            BigDecimal duplication = new BigDecimal(this.duplicationFile);
            BigDecimal total = new BigDecimal(this.totalFile);
            return duplication.divide(total, 2, RoundingMode.HALF_UP).toString();
        }
    }
}
