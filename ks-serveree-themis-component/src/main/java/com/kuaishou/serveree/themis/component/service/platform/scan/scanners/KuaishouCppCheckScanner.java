package com.kuaishou.serveree.themis.component.service.platform.scan.scanners;

import org.springframework.stereotype.Service;

import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.entity.platform.ExecuteScanContext;
import com.kuaishou.serveree.themis.component.entity.scanner.ScannerSendKimContext;
import com.kuaishou.serveree.themis.component.vo.request.ProfileAddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCopyRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCreateRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSettingsRequest;

/**
 * <AUTHOR>
 * @since 2022/11/10 8:06 PM
 */
@Service
public class KuaishouCppCheckScanner implements PlatformScanner {

    @Override
    public void scan(ExecuteScanContext context) {

    }

    @Override
    public PlatformScannerEnum platformScanner() {
        return PlatformScannerEnum.KUAISHOU_CPPCHECK_SCANNER;
    }

    @Override
    public void afterProjectCreate(RepoCreateRequest searchRequest) {

    }

    @Override
    public void afterProfileCreate(ProfileCreateRequestVo requestVo) {

    }

    @Override
    public void afterProfileAddRule(ProfileAddRuleRequestVo requestVo) {

    }

    @Override
    public void afterProfileDeleteRule(ProfileDeleteRuleRequestVo requestVo) {

    }

    @Override
    public void afterProjectUpdateProfile(RepoProfileUpdateRequest updateRequest) {

    }

    @Override
    public boolean judgeNeedAllScan(RepoSettingsRequest request) {
        return false;
    }

    @Override
    public void afterProfileDelete(ProfileDeleteRequestVo requestVo) {

    }

    @Override
    public void afterIssueTransition(IssueSummary issueSummary, String transition) {

    }

    @Override
    public void sendPipelineKimNotice(ScannerSendKimContext scannerSendKimContext) {

    }

    @Override
    public void afterProfileCopy(ProfileCopyRequestVo requestVo) {

    }

}
