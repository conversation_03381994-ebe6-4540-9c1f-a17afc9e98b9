package com.kuaishou.serveree.themis.component.constant.platform;

import java.util.Map;

import com.google.common.collect.Maps;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2022/4/18 7:50 下午
 */
@AllArgsConstructor
public enum PlatformScannerEnum {

    MAVEN_SCANNER_NEW("maven-scanner-new", "新maven-sonar扫描", "duplicated_lines", "duplicated_blocks", "complexity",
            ""),

    SKY_EYE("skyeye", "天眼扫描", "duplication_line_percent", "duplication_block_count", "complexity",
            "business-front"),
    SONAR_SCANNER_NEW("sonar-scanner-new", "新sonar-sonar扫描", "duplicated_lines", "duplicated_blocks", "complexity",
            ""),

    K_FORMAT("k_format", "k_format扫描", "duplicated_lines", "duplicated_blocks", "complexity",
            "sonar-cplus"),

    KUAISHOU_CPPCHECK_SCANNER("kuaishou-cppcheck-scanner", "快手cppcheck扫描", "duplicated_lines", "duplicated_blocks", "complexity",
            "ks-cppcheck"),
    COVERITY_SCANNER("coverity-scanner", "Coverity扫描器", "", "", "", ""),
    AI_SCANNER("ai-scanner", "AI扫描器", "", "", "", ""),
    ;

    @Getter
    private final String scanner;
    @Getter
    private final String desc;
    @Getter
    private final String duplicationLineKy;
    @Getter
    private final String duplicationBlockKey;
    @Getter
    private final String cycleComplexityKey;
    @Getter
    private final String source;
    private static final Map<String, PlatformScannerEnum> SCANNER_MAP = Maps.newHashMap();
    private static final Map<String, PlatformScannerEnum> SOURCE_MAP = Maps.newHashMap();

    static {
        for (PlatformScannerEnum platformScannerEnum : PlatformScannerEnum.values()) {
            SCANNER_MAP.put(platformScannerEnum.getScanner(), platformScannerEnum);
            SOURCE_MAP.put(platformScannerEnum.getSource(), platformScannerEnum);
        }
    }

    public static PlatformScannerEnum getEnumByScanner(String scanner) {
        return SCANNER_MAP.get(scanner);
    }

    public static PlatformScannerEnum getEnumBySource(String scanner) {
        return SOURCE_MAP.get(scanner);
    }

}
