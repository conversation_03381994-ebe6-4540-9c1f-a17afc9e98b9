package com.kuaishou.serveree.themis.component.client.ksp;

import static io.jsonwebtoken.SignatureAlgorithm.HS256;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.kuaishou.framework.util.ObjectMapperUtils;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.EnvironmentConfig;
import com.kuaishou.serveree.themis.component.entity.kdev.NameAndValue;
import com.kuaishou.serveree.themis.component.entity.ksp.KsPipelineJobLogRequest;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineDataRun;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineGetProductVersionsRequest;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineGetProductVersionsResponse;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineGetProductVersionsResponse.ProductVersion;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineGetResultRequest;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineGetResultResponse;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineJobLogResponse;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineOperateLabelRequest;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelinePartLogResponse;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineSponsorRequest;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.response.ReturnMessageVo;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/11/14 2:17 下午
 */
@Component
@Slf4j
public class KspApi {

    @Value("${ksp.url}")
    private String kspUrl;
    @Autowired
    private EnvironmentConfig environmentConfig;

    private static final Kconf<Map<String, String>> TOKEN_MAP =
            Kconfs.ofStringMap("qa.themis.kspToken", new HashMap<>()).build();

    private static final long TIMEOUT = 120000L;


    /**
     * 发起pipeline
     */
    public HttpResponse sponsorPipelineV3(KspPipelineSponsorRequest sponsorRequest) {
        String requestUrl =
                kspUrl + String.format("/api/ci/v3/pub/%s/pipeline_build/", sponsorRequest.getKspPipelineId());
        Map<String, Object> param = Collections.singletonMap("cover", sponsorRequest.getParams());
        return HttpRequest
                .post(requestUrl)
                .body(JSONUtils.serialize(param))
                .headerMap(tokenMap(null), true)
                .timeout((int) TIMEOUT)
                .execute();
    }

    /**
     * 发起pipelineV4
     */
    public HttpResponse sponsorPipelineV4(KspPipelineSponsorRequest sponsorRequest) {
        String requestUrl =
                kspUrl + String.format("/api/ci/v4/pub/pipeline/build/%s", sponsorRequest.getKspPipelineId());
        Map<String, Object> param = Map.of(
                "cover", sponsorRequest.getParams(),
                "env", buildTriggerEnvs(sponsorRequest)
        );
        return HttpRequest
                .post(requestUrl)
                .body(JSONUtils.serialize(param))
                .headerMap(tokenMap(null), true)
                .timeout((int) TIMEOUT)
                .execute();
    }

    /**
     * 添加执行ksp流水线的环境变量
     * ENV_DOMAIN 用于区分测试、线上、prt域名
     */
    private List<NameAndValue> buildTriggerEnvs(KspPipelineSponsorRequest sponsorRequest) {
        List<NameAndValue> envs = sponsorRequest.getEnvs();
        if (Objects.isNull(envs)) {
            envs = new ArrayList<>();
        }
        if (environmentConfig.isPrt()) {
            envs.add(new NameAndValue("ENV_DOMAIN", "PRT_ENV"));
        } else if (environmentConfig.isTest()) {
            envs.add(new NameAndValue("ENV_DOMAIN", "TEST_ENV"));
        }
        return envs;
    }

    /**
     * 获取pipeline结果
     */
    public HttpResponse getPipelineResultV4(KspPipelineGetResultRequest getResultRequest) {
        String url = String.format("%s/api/ci/v4/executions/builds_by_id/?job_id=%s&ci_pipeline=true&build_id=%s",
                kspUrl, getResultRequest.getKspPipelineId(), getResultRequest.getKspBuildId());
        HttpRequest httpRequest = HttpRequest.get(url)
                .setFollowRedirects(true)
                .headerMap(tokenMap(null), true)
                .timeout((int) TIMEOUT);
        HttpResponse httpResponse = httpRequest.execute();
        if (!httpResponse.isOk()) {
            log.warn("sync ksp api/ci/v4/executions/builds_by_id interface failed , request is {} , response is {}",
                    httpRequest.toString(), httpResponse.toString());
        }
        return httpResponse;
    }

    /**
     * 获取执行参数
     */
    public KspPipelineDataRun getPipelineParamCovers(Long kspPipelineId)  {
        String url = String.format("%s/api/ci/v3/pipeline/%s/covers/", kspUrl, kspPipelineId);
        HttpResponse resp = HttpRequest
                .get(url)
                .headerMap(tokenMap(null), true)
                .timeout((int) TIMEOUT)
                .execute();
        if (!resp.isOk()) {
            log.error("调用ksp接口查询构建参数！url: {}, resp: {}", url, resp);
            throw new ThemisException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "调用ksp接口查询构建参数失败！");
        }
        KspPipelineDataRun kspPipelineDataRun = ObjectMapperUtils.fromJSON(resp.body(), KspPipelineDataRun.class);
        if (kspPipelineDataRun == null) {
            log.error("调用ksp接口查询构建参数！url: {}, resp: {}", url, resp.body());
            throw new ThemisException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "调用ksp接口查询构建参数失败！");
        }
        return kspPipelineDataRun;
    }

    private Map<String, String> tokenMap(String userName) {
        Map<String, String> head = new LinkedHashMap<>();
        String token = Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setClaims(Jwts.claims().setIssuer(TOKEN_MAP.get().get("issuer"))
                        .setExpiration(new Date(System.currentTimeMillis() + TIMEOUT)))
                .claim("user", StringUtils.isEmpty(userName) ? "kdev" : userName)
                .signWith(Keys.hmacShaKeyFor(TOKEN_MAP.get().get("secret").getBytes()), HS256)
                .compact();
        head.put("X-Halo-Token", token);
        return head;
    }

    public KspPipelineJobLogResponse getStepLog(KsPipelineJobLogRequest jobLogRequest) {
        String url = String.format("%s/api/ci/v3/pipeline/%s/step_log/", kspUrl, jobLogRequest.getKspPipelineId());
        Map<String, Object> param = new LinkedHashMap<>();
        param.put("pipeline_id", jobLogRequest.getKspPipelineId());
        param.put("build_id", jobLogRequest.getBuildId());
        param.put("step_id", jobLogRequest.getStepId());
        param.put("full", jobLogRequest.getFull());
        if (jobLogRequest.getStart() != null && jobLogRequest.getStart() > 0) {
            param.put("start", jobLogRequest.getStart());
        }
        String body = HttpRequest
                .get(url)
                .form(param)
                .headerMap(tokenMap(null), true)
                .timeout((int) TIMEOUT)
                .execute()
                .body();
        return JSONUtils.deserialize(body, KspPipelineJobLogResponse.class);
    }

    public KspPipelineJobLogResponse getV4StepLog(KsPipelineJobLogRequest jobLogRequest) {
        String url = String.format("%s/api/ci/v4/executions/build_step/log", kspUrl);
        Map<String, Object> param = new LinkedHashMap<>();
        param.put("job_id", jobLogRequest.getKspPipelineId());
        param.put("build_id", jobLogRequest.getBuildId());
        param.put("step_build_id", jobLogRequest.getStepId());
        param.put("full", jobLogRequest.getFull());
        String body = HttpRequest
                .get(url)
                .form(param)
                .headerMap(tokenMap(null), true)
                .timeout((int) TIMEOUT)
                .execute()
                .body();
        return JSONUtils.deserialize(body, KspPipelineJobLogResponse.class);
    }

    // CHECKSTYLE:OFF
    public String getPartLog(KsPipelineJobLogRequest jobLogRequest) {
        String url = String.format("%s/api/ci/v4/executions/build_step/part_log", kspUrl);
        Map<String, Object> param = new LinkedHashMap<>();
        param.put("step_build_id", jobLogRequest.getStepId());
        param.put("limit", 10000);
        String body = HttpRequest
                .get(url)
                .form(param)
                .headerMap(tokenMap(null), true)
                .timeout((int) TIMEOUT)
                .execute()
                .body();
        KspPipelinePartLogResponse kspPipelinePartLogResponse =
                JSONUtils.deserialize(body, KspPipelinePartLogResponse.class);
        return kspPipelinePartLogResponse.getData();
    }
    // CHECKSTYLE:ON

    public KspPipelineGetProductVersionsResponse getProductVersions(KspPipelineGetProductVersionsRequest request) {
        String url = String.format("%s/api/halo_product/private/v1/product_version/", kspUrl);
        Map<String, Object> param = new HashMap<>();
        if (StringUtils.isNotEmpty(request.getName())) {
            param.put("name", request.getName());
        }
        if (request.getLimit() != null) {
            param.put("limit", request.getLimit());
        }
        if (request.getOffset() != null) {
            param.put("offset", request.getOffset());
        }
        if (request.getBuildId() != null) {
            param.put("build_id", request.getBuildId());
        }
        if (request.getProductId() != null) {
            param.put("product_id", request.getProductId());
        }
        if (request.getJobId() != null) {
            param.put("job_id", request.getJobId());
        }
        String body = HttpRequest
                .get(url)
                .form(param)
                .headerMap(tokenMap("kdev"), true)
                .timeout((int) TIMEOUT)
                .execute()
                .body();
        return JSONUtils.deserialize(body, KspPipelineGetProductVersionsResponse.class);
    }


    public ProductVersion addLabels(KspPipelineOperateLabelRequest request) {
        String url = String.format("%s/api/halo_product/private/v1/product_version/%s/label", kspUrl,
                request.getVersionId());
        String body = HttpRequest
                .post(url)
                .body(JSONUtils.serialize(request.getLabels()))
                .headerMap(tokenMap("kdev"), true)
                .timeout((int) TIMEOUT)
                .execute()
                .body();
        return JSONUtils.deserialize(body, ProductVersion.class);
    }

    public String getPipelineStatus(TaskConfig taskConfig) {
        HttpResponse originPipelineResultV4 = this.getPipelineResultV4(
                KspPipelineGetResultRequest.builder()
                        .kspBuildId(taskConfig.getOriginalKspBuildId())
                        .kspPipelineId(taskConfig.getOriginalKspPipelineId())
                        .build()
        );
        if (!originPipelineResultV4.isOk()) {
            log.warn("sync original ksp pipeline fail, pipelineResult is {}",
                    JSONUtils.serialize(originPipelineResultV4));
            return null;
        }
        String body = originPipelineResultV4.body();
        if (StringUtils.isEmpty(body)) {
            log.warn("sync original ksp pipeline fail,next check result");
            return null;
        }
        ReturnMessageVo<KspPipelineGetResultResponse> deserialize = JSONUtils.deserialize(body,
                new TypeReference<ReturnMessageVo<KspPipelineGetResultResponse>>() {
                });
        if (deserialize == null) {
            log.warn("sync original ksp pipeline fail ReturnMessageVo is null,next check result");
            return null;
        }
        KspPipelineGetResultResponse getResultResponse = deserialize.getData();
        if (getResultResponse == null) {
            log.warn("sync original ksp pipeline fail,next check result");
            return null;
        }
        return getResultResponse.getStatus();
    }

}
