package com.kuaishou.serveree.themis.component.common.entity;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "Task对象", description = "")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Task implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "任务状态")
    private String taskStatus;

    private Integer projectId;

    @ApiModelProperty(value = "repo地址")
    private String repoUrl;

    @ApiModelProperty(value = "分支名")
    private String branch;

    @ApiModelProperty(value = "commitId")
    private String commitId;

    @ApiModelProperty(value = "git的merge-request id")
    private String mrId;

    private String localBuildId;

    @ApiModelProperty(value = "用户指定的项目模块名")
    private String module;

    @ApiModelProperty(value = "来源")
    private String source;

    @ApiModelProperty(value = "来源id")
    private String sourceId;

    @ApiModelProperty(value = "优先级（HIGH、MEDIUM、LOW）")
    private String priority;

    @ApiModelProperty(value = "标记")
    private String remark;

    @ApiModelProperty(value = "日志存储地址")
    private String logStorageUrl;

    @ApiModelProperty(value = "检查者")
    private String checker;

    @ApiModelProperty(value = "补偿标记")
    private Boolean compensateTab;

    @ApiModelProperty(value = "是否同步ksp产品标签")
    private Boolean syncProductLabel;

    @ApiModelProperty(value = "kdev的分支的类型")
    private String kdevBranchType;

    @ApiModelProperty(value = "kdev预编译命令行")
    private String kdevJavaPreCompileCommand;

    @ApiModelProperty(value = "kdev预编译入参数")
    private String kdevJavaPreCompileInput;

    @ApiModelProperty(value = "执行开始时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime executeStart;

    @ApiModelProperty(value = "执行结束时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime executeEnd;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime updatedTime;

    private String taskType;

    /**
     * 扫描时间，插件上报，不等于 execute_end - execute_start
     */
    private Long scanDuration;


}
