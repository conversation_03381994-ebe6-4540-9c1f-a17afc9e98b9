package com.kuaishou.serveree.themis.component.entity.sonar;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/7/28 5:30 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SonarComponent {

    private String id;
    private String key;
    private String name;
    private String qualifier;
    private String path;
    private String language;
    private List<SonarMeasures> measures;

}
