package com.kuaishou.serveree.themis.component.entity.statics;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/11/4 2:28 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ContentRowHeight(20)
public class ScanIssueExcelMeta {

    @ColumnWidth(13)
    @ExcelProperty({"业务部门"})
    private String businessName;

    @ColumnWidth(13)
    @ExcelProperty({"git group id"})
    private Integer groupId;

    // project id
    @ColumnWidth(13)
    @ExcelProperty({"project id"})
    private Integer projectId;

    @ColumnWidth(30)
    @ExcelProperty({"项目地址"})
    private String repoUrl;

    @ColumnWidth(75)
    @ExcelProperty({"文件定位"})
    private String location;

    // 错误代码
    @ColumnWidth(75)
    @ExcelProperty({"具体代码"})
    private String codeStr;

    @ColumnWidth(10)
    @ExcelProperty({"代码行号"})
    private int lineNo;

    @ColumnWidth(25)
    @ExcelProperty({"代码提交人"})
    private String committer;

    @ColumnWidth(30)
    @ExcelProperty({"扫描case"})
    private String caseStr;

    @ColumnWidth(25)
    @ExcelProperty({"扫描case的类型"})
    private String caseType;

    @ColumnWidth(13)
    @ExcelProperty({"kconf创建人"})
    private String caseCreator;

    @ColumnWidth(13)
    @ExcelProperty({"kconf最后修改人"})
    private String caseUpdater;

    @ColumnWidth(15)
    @ExcelProperty({"kconf关联硬编码域名"})
    private String caseLinkUrl;

    // 超链接
    @ColumnWidth(75)
    @ExcelProperty({"链接"})
    private String hyperlink;

}
