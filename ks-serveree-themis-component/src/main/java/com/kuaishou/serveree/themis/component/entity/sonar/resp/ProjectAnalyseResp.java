package com.kuaishou.serveree.themis.component.entity.sonar.resp;

import java.util.List;

import com.kuaishou.serveree.themis.component.entity.sonar.Analyse;
import com.kuaishou.serveree.themis.component.entity.sonar.Paging;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/10/21 4:11 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectAnalyseResp {

    private Paging paging;
    private List<Analyse> analyses;

}
