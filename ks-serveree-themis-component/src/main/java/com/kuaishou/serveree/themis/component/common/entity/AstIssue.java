package com.kuaishou.serveree.themis.component.common.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.kuaishou.serveree.themis.component.config.mybatis.KsLocalDateTimeTypeHandler;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-02-24
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AstIssue {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private Long pipelineId;

    private Long buildId;

    private String repoUrl;

    private String branch;

    private String commitId;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * rule key
     */
    private String ruleKey;

    private String description;

    private String message;

    private String projectName;

    @ApiModelProperty(value = "创建时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "修改时间")
    @TableField(typeHandler = KsLocalDateTimeTypeHandler.class)
    private LocalDateTime gmtModified;

    private String gavLocation;

    private Integer projectId;

    private Integer lineNo;

    private Integer columnNo;

    private Integer type;

}
