package com.kuaishou.serveree.themis.component.client.kim;

import java.lang.reflect.InvocationTargetException;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.entity.kim.BaseMessage;
import com.kuaishou.serveree.themis.component.entity.kim.MarkDownMsg;
import com.kuaishou.serveree.themis.component.entity.kim.OpenApiConfig;

/**
 * <AUTHOR>
 * @since 2021/8/9 6:34 下午
 */
@Component
public class KimApi {

    @Autowired
    private OpenApiConfig openApiConfig;

    public String sendText(String username, String content) throws Exception {
        Map<String, String> map = Maps.newHashMap();
        map.put("content", content);
        BaseMessage markDownMsg = fromUser(username, MarkDownMsg.class).withConfig(openApiConfig);
        String result = markDownMsg.send2People(map);
        return result;
    }

    public static <T extends BaseMessage> T fromUser(String username, Class<T> t)
            throws NoSuchMethodException, IllegalAccessException, InvocationTargetException, InstantiationException {
        T instance = t.getDeclaredConstructor().newInstance();
        instance.setUsername(username);
        return instance;
    }

}
