package com.kuaishou.serveree.themis.component.service.impl;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfileRuleRelation;
import com.kuaishou.serveree.themis.component.common.mappers.CheckProfileRuleRelationMapper;
import com.kuaishou.serveree.themis.component.service.CheckProfileRuleRelationService;
import com.kuaishou.serveree.themis.component.service.CheckProfileService;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-19
 */
@Service
public class CheckProfileRuleRelationServiceImpl
        extends ServiceImpl<CheckProfileRuleRelationMapper, CheckProfileRuleRelation>
        implements CheckProfileRuleRelationService {

    @Autowired
    private CheckProfileService checkProfileService;

    @Autowired
    private CheckProfileRuleRelationMapper checkProfileRuleRelationMapper;

    @Override
    public List<CheckProfileRuleRelation> listActiveByProfileName(String profileName) {
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .eq(CheckProfileRuleRelation::getProfileName, profileName)
                .eq(CheckProfileRuleRelation::getDeleted, false);
        return this.list(queryWrapper);
    }

    @Override
    public List<CheckProfileRuleRelation> listActiveByProfileName(String profileName, String severity) {
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .eq(CheckProfileRuleRelation::getProfileName, profileName)
                .eq(StringUtils.isNotEmpty(severity), CheckProfileRuleRelation::getRuleSeverity, severity)
                .eq(CheckProfileRuleRelation::getDeleted, false);
        return this.list(queryWrapper);
    }

    @Override
    public List<CheckProfileRuleRelation> listInProfileNames(List<String> profileNames) {
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .in(CheckProfileRuleRelation::getProfileName, profileNames);
        return this.list(queryWrapper);
    }

    @Override
    public Map<String, Long> groupCountByProfileNameInProfileNames(Collection<String> profileNames) {
        final String countKey = "count(0)";
        final String profileKey = "profile_name";
        final String deleted = "deleted";
        QueryWrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(profileKey + "," + countKey);
        queryWrapper.in(profileKey, profileNames);
        queryWrapper.eq(deleted, false);
        queryWrapper.groupBy(profileKey);
        List<Map<String, Object>> mapsList = this.listMaps(queryWrapper);
        if (CollectionUtils.isEmpty(mapsList)) {
            return Collections.emptyMap();
        }
        Map<String, Long> profileNameMap = Maps.newHashMap();
        String key = "";
        Long val = 0L;
        for (Map<String, Object> stringObjectMap : mapsList) {
            for (Entry<String, Object> entry : stringObjectMap.entrySet()) {
                if (countKey.equals(entry.getKey())) {
                    val = (Long) entry.getValue();
                }
                if (profileKey.equals(entry.getKey())) {
                    key = (String) entry.getValue();
                }
            }
            profileNameMap.put(key, val);
        }
        return profileNameMap;
    }

    @Override
    public List<CheckProfileRuleRelation> listAllByProfileNameAndRuleKeys(String profileName,
            List<String> ruleKeyList) {
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .eq(CheckProfileRuleRelation::getProfileName, profileName)
                .in(CheckProfileRuleRelation::getRuleKey, ruleKeyList)
                .eq(CheckProfileRuleRelation::getDeleted, false);
        return this.list(queryWrapper);
    }

    @Override
    public List<CheckProfileRuleRelation> listByRuleKey(String ruleKey) {
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .eq(CheckProfileRuleRelation::getRuleKey, ruleKey)
                .eq(CheckProfileRuleRelation::getDeleted, false);
        return this.list(queryWrapper);
    }

    @Override
    public void deleteByProfileName(String profileName) {
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .eq(CheckProfileRuleRelation::getProfileName, profileName);
        checkProfileRuleRelationMapper.delete(queryWrapper);
    }

    @Override
    public void deleteInProfileNames(List<String> profileNames) {
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .in(CheckProfileRuleRelation::getProfileName, profileNames);
        checkProfileRuleRelationMapper.delete(queryWrapper);
    }

    @Override
    public void updateRelationToDelete(String profileName, String userName) {
        LocalDateTime now = LocalDateTime.now();
        List<CheckProfileRuleRelation> ruleRelationList = listActiveByProfileName(profileName);
        for (CheckProfileRuleRelation ruleRelation : ruleRelationList) {
            ruleRelation.setUpdater(userName);
            ruleRelation.setGmtModified(now);
            ruleRelation.setDeleted(true);
        }
        this.updateBatchById(ruleRelationList);
    }

    @Override
    public List<CheckProfileRuleRelation> listAllByProfileNameAndRuleKeysAllStatus(String profileName,
            List<String> ruleKeyList) {
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .eq(CheckProfileRuleRelation::getProfileName, profileName)
                .in(CheckProfileRuleRelation::getRuleKey, ruleKeyList);
        return this.list(queryWrapper);
    }

    @Override
    public List<String> listAllDistinctProfile() {
        QueryWrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct profile_name");
        return this.list(queryWrapper).stream().map(CheckProfileRuleRelation::getProfileName)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, List<String>> groupRuleKeyListByProfileNameInProfileNames(List<String> profileNames) {
        if (profileNames.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, List<CheckProfileRuleRelation>> profile2RuleRelationListMap =
                groupRuleRelationListByProfileNameInProfileNames(profileNames);
        // List<CheckProfileRuleRelation> --> List<String>
        return profile2RuleRelationListMap.entrySet().stream().collect(
                Collectors.toMap(Entry::getKey, e -> e.getValue().stream()
                        .map(CheckProfileRuleRelation::getRuleKey).collect(Collectors.toList()), (existing, replacement) -> existing));
    }

    @Override
    public Map<String, List<CheckProfileRuleRelation>> groupRuleRelationListByProfileNameInProfileNames(
            List<String> profileNames) {
        if (CollectionUtils.isEmpty(profileNames)) {
            return Collections.emptyMap();
        }
        // 若不存在关联规则，添加键值对 <name, emptyList>
        Map<String, List<CheckProfileRuleRelation>> res = Maps.newHashMapWithExpectedSize(profileNames.size());
        for (String profileName : profileNames) {
            res.put(profileName, Collections.emptyList());
        }
        // 查询规则集关联的规则
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .in(CheckProfileRuleRelation::getProfileName, profileNames)
                .eq(CheckProfileRuleRelation::getDeleted, false);
        List<CheckProfileRuleRelation> profileRuleRelationList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(profileRuleRelationList)) {
            return res;
        }
        // 根据 profileName 分组
        Map<String, List<CheckProfileRuleRelation>> map = profileRuleRelationList.stream()
                .collect(Collectors.groupingBy(CheckProfileRuleRelation::getProfileName));

        res.putAll(map);

        return res;
    }

    @Override
    public List<CheckProfileRuleRelation> listActiveByProfileNamesAndRuleKeys(Collection<String> profileNames,
            Collection<String> ruleKeys) {
        Wrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<CheckProfileRuleRelation>().lambda()
                .in(CollectionUtils.isNotEmpty(profileNames), CheckProfileRuleRelation::getProfileName, profileNames)
                .in(CollectionUtils.isNotEmpty(ruleKeys), CheckProfileRuleRelation::getRuleKey, ruleKeys)
                .eq(CheckProfileRuleRelation::getDeleted, false);
        return this.list(queryWrapper);
    }

    @Override
    public Set<String> listActiveRuleKeysByProfileNames(Collection<String> profileNames) {
        if (CollectionUtils.isEmpty(profileNames)) {
            return Collections.emptySet();
        }
        QueryWrapper<CheckProfileRuleRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("distinct rule_key")
                .lambda().in(CheckProfileRuleRelation::getProfileName, profileNames)
                .eq(CheckProfileRuleRelation::getDeleted, false);
        return list(queryWrapper).stream().map(CheckProfileRuleRelation::getRuleKey).collect(Collectors.toSet());
    }

    @Override
    public Set<String> listActiveRuleKeysWithAncestorsByProfileName(String profileName) {
        List<CheckProfile> allAncestorProfiles = checkProfileService.findAllAncestorProfiles(profileName);
        return listActiveRuleKeysByProfileNames(
                allAncestorProfiles.stream().map(CheckProfile::getProfileName).collect(Collectors.toList())
        );
    }
}
