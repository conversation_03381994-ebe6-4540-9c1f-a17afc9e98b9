package com.kuaishou.serveree.themis.component.client.sonar.api;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.entity.sonar.Issue;
import com.kuaishou.serveree.themis.component.entity.sonar.Project;
import com.kuaishou.serveree.themis.component.entity.sonar.req.CopyQualityProfilesReq;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasuresComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.QualityProfileReq;
import com.kuaishou.serveree.themis.component.entity.sonar.req.SonarSearchRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.BranchListResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.CopyQualityProfilesResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.DuplicationListResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.GetWebHooksResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.HistoryMeasureResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.IssuesResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentTreeResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasuresComponentTreeResponse;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MetricListResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProfileCreateResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProfileInheritanceResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProfileProjectsResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProjectAnalyseResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProjectCreateResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProjectsResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.QualityProfileResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.RulesDetailResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.SourceLinesResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.UsersResp;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.SearchRuleRequest;
import com.kuaishou.serveree.themis.component.vo.response.SearchRulesResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;

/**
 * <AUTHOR>
 * @since 2021/7/2 11:11 上午
 */
public interface SonarCommonApi {

    Logger logger = LoggerFactory.getLogger(SonarCommonApi.class);

    String sonarUrl();

    Integer timeout();

    String basicAuth();

    default ProjectsResp getProjects(Integer pageNum, Integer pageSize) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("p", pageNum);
        reqMap.put("ps", pageSize);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/components/search_projects?f=analysisDate")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/components/search_projects?f=analysisDate", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询项目列表返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, ProjectsResp.class);
    }

    default ProjectAnalyseResp searchAnalyses(String component, String from, String to) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("project", component);
        reqMap.put("from", from);
        reqMap.put("to", to);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/project_analyses/search?ps=1")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/project_analyses/search?ps=1", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询项目分析返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, ProjectAnalyseResp.class);
    }

    default HistoryMeasureResp getHistoryMeasure(String component, String metrics, String from, String to) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("component", component);
        if (StringUtils.isNotEmpty(from)) {
            reqMap.put("from", from);
        }
        if (StringUtils.isNotEmpty(to)) {
            reqMap.put("to", to);
        }
        reqMap.put("metrics", metrics);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/measures/search_history?ps=1000")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/measures/search_history?ps=1000", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询项目指标返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, HistoryMeasureResp.class);
    }

    default HistoryMeasureResp getAllHistoryMeasure(String branch, String component, String metrics, String from,
            String to, Integer page, Integer pageSize) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("branch", branch);
        reqMap.put("component", component);
        reqMap.put("metrics", metrics);
        if (Objects.nonNull(page)) {
            reqMap.put("p", page);
        }
        if (Objects.nonNull(pageSize)) {
            reqMap.put("ps", pageSize);
        }
        if (StringUtils.isNotBlank(from)) {
            reqMap.put("from", from);
        }
        if (StringUtils.isNotBlank(to)) {
            reqMap.put("to", to);
        }
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/measures/search_history")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/measures/search_history", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询项目指标返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, HistoryMeasureResp.class);
    }

    default MeasureComponentResp measuresComponent(String projectKey, String branch, String additionalFields,
            String metricKeys) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("component", projectKey);
        reqMap.put("metricKeys", metricKeys);
        if (StringUtils.isNotEmpty(branch)) {
            reqMap.put("branch", branch);
        }
        if (StringUtils.isNotEmpty(additionalFields)) {
            reqMap.put("additionalFields", additionalFields);
        }
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/measures/component")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/measures/component", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询项目度量数据返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, MeasureComponentResp.class);
    }

    default MeasureComponentTreeResp measuresComponentTree(MeasureComponentTreeRequest request) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("component", request.getComponent());
        reqMap.put("metricKeys", request.getMetricKeys());
        request.setMetricSort(request.getMetricKeys().split(",")[0]);
        reqMap.put("metricSort", request.getMetricSort());
        if (request.getPage() != 0) {
            reqMap.put("p", request.getPage());
        }
        if (request.getPageSize() != 0) {
            reqMap.put("ps", request.getPageSize());
        } else {
            // CHECKSTYLE:OFF
            reqMap.put("ps", 500);
            // CHECKSTYLE:OFF
        }
        if (StringUtils.isNotEmpty(request.getAdditionalFields())) {
            reqMap.put("additionalFields", request.getAdditionalFields());
        } else {
            reqMap.put("additionalFields", "metrics");
        }
        if (StringUtils.isNotEmpty(request.getAsc())) {
            reqMap.put("asc", request.getAsc());
        } else {
            reqMap.put("asc", "false");
        }
        if (StringUtils.isNotEmpty(request.getMetricSortFilter())) {
            reqMap.put("metricSortFilter", request.getMetricSortFilter());
        } else {
            reqMap.put("metricSortFilter", "withMeasuresOnly");
        }
        if (StringUtils.isNotEmpty(request.getStrategy())) {
            reqMap.put("strategy", request.getStrategy());
        } else {
            reqMap.put("strategy", "leaves");
        }
        if (request.getMetricPeriodSort() != 0) {
            reqMap.put("metricPeriodSort", request.getMetricPeriodSort());
        }
        if (StringUtils.isNotEmpty(request.getQ())) {
            reqMap.put("q", request.getQ());
        }
        if (StringUtils.isNotEmpty(request.getQualifiers())) {
            reqMap.put("qualifiers", request.getQualifiers());
        }
        if (StringUtils.isNotEmpty(request.getS())) {
            reqMap.put("s", request.getS());
        } else {
            reqMap.put("s", "metric");
        }
        if (StringUtils.isNotEmpty(request.getBranch())) {
            reqMap.put("branch", request.getBranch());
        }
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/measures/component_tree")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/measures/component_tree", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务[/api/measures/component_tree]返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, MeasureComponentTreeResp.class);
    }

    default IssuesResp searchIssues(SonarSearchRequest request) {
        Map<String, Object> reqMap = new HashMap<>();
        if (StringUtils.isNotEmpty(request.getComponentKeys())) {
            reqMap.put("componentKeys", request.getComponentKeys());
        }
        reqMap.put("sinceLeakPeriod", request.isSinceLeakPeriod());
        if (StringUtils.isNotEmpty(request.getFrom())) {
            reqMap.put("createdAfter", request.getFrom());
        }
        if (StringUtils.isNotEmpty(request.getTo())) {
            reqMap.put("createdBefore", request.getTo());
        }
        if (request.getResolved() != null) {
            reqMap.put("resolved", request.getResolved());
        }
        if (StringUtils.isNotEmpty(request.getTypes())) {
            reqMap.put("types", request.getTypes());
        }
        if (StringUtils.isNotEmpty(request.getCreatedAt())) {
            reqMap.put("createdAt", request.getCreatedAt());
        }
        if (StringUtils.isNotEmpty(request.getStatuses())) {
            reqMap.put("statuses", request.getStatuses());
        }
        if (StringUtils.isNotEmpty(request.getSeverities())) {
            reqMap.put("severities", request.getSeverities());
        }
        if (StringUtils.isNotEmpty(request.getBranch())) {
            reqMap.put("branch", request.getBranch());
        }
        if (request.getPage() != 0) {
            reqMap.put("p", request.getPage());
        }
        if (request.getPageSize() != 0) {
            reqMap.put("ps", request.getPageSize());
        } else {
            // CHECKSTYLE:OFF
            reqMap.put("ps", 500);
            // CHECKSTYLE:OFF
        }
        if (StringUtils.isNotEmpty(request.getAdditionalFields())) {
            reqMap.put("additionalFields", request.getAdditionalFields());
        }
        if (StringUtils.isNotEmpty(request.getFacets())) {
            reqMap.put("facets", request.getFacets());
        }
        if (StringUtils.isNotEmpty(request.getRules())) {
            reqMap.put("rules", request.getRules());
        }
        if (StringUtils.isNotBlank(request.getResolutions())) {
            reqMap.put("resolutions", request.getResolutions());
        }
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/issues/search")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/issues/search", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询项目问题返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, IssuesResp.class);
    }

    default IssuesResp searchClosedIssues(String componentKeys, String branch) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("componentKeys", componentKeys);
        if (StringUtils.isNotEmpty(branch)) {
            reqMap.put("branch", branch);
        }
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/issues/search?statuses=CLOSED&s=CLOSE_DATE&asc=false")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/issues/search?statuses=CLOSED&s=CLOSE_DATE&asc=false", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询项目问题返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, IssuesResp.class);
    }

    default ProjectCreateResp createProject(String projectName, String projectKey, String visibility) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("name", projectName);
        reqMap.put("project", projectKey);
        reqMap.put("visibility", visibility);

        String resultResp = HttpRequest
                .post(sonarUrl() + "/api/projects/create")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/projects/create", reqMap, resultResp);
            throw ThemisException.builder().code(HttpStatus.HTTP_INTERNAL_ERROR)
                    .message("调用sonar服务 [api/projects/create] 返回异常，resp is " + resultResp).build();
        }
        ProjectCreateResp projectCreateResp = JSONUtils.deserialize(resultResp, ProjectCreateResp.class);
        if (null == projectCreateResp.getProject()) {
            logError("/api/projects/create", reqMap, resultResp);
            // 项目key已经创建, 重复创建
            if (resultResp.contains("key already exists")) {
                throw new ThemisException(ResultCodeConstant.SONAR_PROJECTKEY_ALREADY_EXISTS);
            }
            throw ThemisException.builder().code(HttpStatus.HTTP_INTERNAL_ERROR)
                    .message("调用sonar服务 [api/projects/create] 返回异常，resp is " + resultResp).build();
        }
        return projectCreateResp;
    }

    default void addProjectQualityprofile(String language, String projectKey, String qualityProfile) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("language", language);
        reqMap.put("project", projectKey);
        reqMap.put("qualityProfile", qualityProfile);

        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/qualityprofiles/add_project")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("/api/qualityprofiles/add_project", reqMap, resp.body());
            throw ThemisException.builder().code(HttpStatus.HTTP_INTERNAL_ERROR)
                    .message("调用sonar服务 [api/qualityprofiles/add_project] 返回异常，resp is " + resp.body()).build();
        }
    }

    default void createUserGroup(String groupName) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("name", groupName);

        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/user_groups/create")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("api/user_groups/create", reqMap, resp.body());
        }
    }

    default void addGroupPermission(String groupName, String permission, String projectKey) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("groupName", groupName);
        reqMap.put("permission", permission);
        reqMap.put("projectKey", projectKey);

        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/permissions/add_group")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("/api/permissions/add_group", reqMap, resp.body());
            throw ThemisException.builder().code(HttpStatus.HTTP_INTERNAL_ERROR)
                    .message("调用sonar服务 [api/permissions/add_group] 返回异常，resp is " + resp.body()).build();
        }
    }

    /**
     * 删除sonar project
     *
     * @param projectKey String
     */
    default void deleteProject(String projectKey) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("project", projectKey);
        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/projects/delete")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("api/projects/delete", reqMap, resp.body());
        }
    }

    /**
     * 批量删除sonar project
     *
     * @param projectKeys List
     */
    default void deleteProjects(List<String> projectKeys) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("projects", StringUtils.join(projectKeys, ","));

        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/projects/bulk_delete")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("api/projects/bulk_delete", reqMap, resp.body());
        }
    }

    /**
     * 模糊查询 project
     *
     * @param projectKey String 模糊键
     * @param pageNum Integer
     * @param pageSize Integer
     * @return ProjectsResp
     */
    default ProjectsResp searchProjects(String projectKey, Integer pageNum, Integer pageSize, String projects) {
        Map<String, Object> reqMap = new HashMap<>();
        if (StringUtils.isNotEmpty(projectKey)) {
            reqMap.put("q", projectKey);
        }
        if (pageNum != null) {
            reqMap.put("p", pageNum);
        }
        if (pageSize != null) {
            reqMap.put("ps", pageSize);
        }
        if (StringUtils.isNotEmpty(projects)) {
            reqMap.put("projects", projects);
        }
        HttpRequest httpRequest = HttpRequest
                .get(sonarUrl() + "/api/projects/search")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout()); //超时，毫秒
        String resultResp = httpRequest.execute().body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/projects/search", reqMap, resultResp);
            throw new RuntimeException("调用[/api/projects/search]返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, ProjectsResp.class);
    }

    /**
     * 获取sonar用户列表
     *
     * @param pageNum 页号
     * @param pageSize 大小
     * @return UsersResp
     */
    default UsersResp getUsers(Integer pageNum, Integer pageSize) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("p", pageNum);
        reqMap.put("ps", pageSize);
        HttpResponse resp = HttpRequest
                .get(sonarUrl() + "/api/users/search")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        String resultData = resp.body();
        if (!resp.isOk() || StringUtils.isEmpty(resultData)) {
            logError("/api/users/search", reqMap, resultData);
            throw ThemisException.builder().code(HttpStatus.HTTP_INTERNAL_ERROR)
                    .message("调用sonar服务 [api/users/search] 返回异常，resp is " + resultData).build();
        }
        return JSONUtils.deserialize(resultData, UsersResp.class);
    }

    /**
     * 创建sonar用户
     *
     * @param login String
     * @param name String
     * @param email String
     */
    default void createUser(String login, String name, String email) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("login", login);
        reqMap.put("name", name);
        reqMap.put("email", email);
        reqMap.put("local", "false"); // 默认非本地用户

        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/users/create")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("/api/users/create", reqMap, resp.body());
        } else {
            logger.info("[success]create sonar user: login={}, name={}, email={}]", login, name, email);
        }
    }

    default GetWebHooksResp getWebHooks(String component) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("component", component);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/webhooks/list")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/webhooks/list", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询项目hook指标返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, GetWebHooksResp.class);
    }

    default QualityProfileResp getQualityProfile(QualityProfileReq qualityProfileReq) {
        Map<String, Object> reqMap = BeanUtil.beanToMap(qualityProfileReq, false, true);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/qualityprofiles/search")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        logger.info("getQualityProfile qualityProfileReq is {}, resultResp is {}",
                JSONUtils.serialize(qualityProfileReq), JSONUtils.serialize(resultResp));
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/qualityprofiles/search", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询项目profile指标返回异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, QualityProfileResp.class);
    }

    default void updateVisibility(String projectKey, String visibility) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("project", projectKey);
        reqMap.put("visibility", visibility);
        HttpResponse response = HttpRequest
                .post(sonarUrl() + "/api/projects/update_visibility")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!response.isOk()) {
            logError("/api/projects/update_visibility", reqMap, response.body());
            throw new RuntimeException("调用sonar服务更改项目可见性异常，resp is " + response);
        }
    }

    default BranchListResp getProjectBranchesList(String projectKey) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("project", projectKey);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/project_branches/list")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/project_branches/list", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务获取所有的分支列表异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, BranchListResp.class);
    }

    default MetricListResp getMetricList(Integer pageNum, Integer pageSize) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("p", pageNum);
        reqMap.put("ps", pageSize);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/metrics/search")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/metrics/search", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务获取metric列表异常，resp is " + resultResp);
        }
        return JSONUtils.deserialize(resultResp, MetricListResp.class);
    }

    default SourceLinesResp listSourceLines(String key, Integer from, Integer to) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("key", key);
        reqMap.put("from", from);
        reqMap.put("to", to);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/sources/lines")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/sources/lines", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用sonar服务获取代码源文件异常", resultResp);
        }
        return JSONUtils.deserialize(resultResp, SourceLinesResp.class);
    }

    default RulesDetailResp getRuleDetail(String key, String organization, String actives) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("key", key);
        if (StringUtils.isNotEmpty(organization)) {
            reqMap.put("organization", organization);
        }
        if (StringUtils.isEmpty(actives)) {
            reqMap.put("actives", "true");
        } else {
            reqMap.put("actives", actives);
        }
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/rules/show")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/rules/show", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用sonar服务获取规则详情异常", resultResp);
        }
        return JSONUtils.deserialize(resultResp, RulesDetailResp.class);
    }

    default SearchRulesResponse searchRules(SearchRuleRequest request) {
        Map<String, Object> reqMap = new HashMap<>();
        //sonar集群的质量阀，默认可以不传
        if (StringUtils.isNotEmpty(request.getQprofile())) {
            reqMap.put("qprofile", request.getQprofile());
        }
        if (StringUtils.isNotEmpty(request.getF())) {
            reqMap.put("f", request.getF());
        } else {
            reqMap.put("f", "isTemplate,name,lang,langName,severity,status,sysTags,tags,templateKey,actives,params");
        }
        if (request.getP() == null) {
            reqMap.put("p", 1);
        } else {
            reqMap.put("p", request.getP());
        }
        if (request.getPs() == null) {
            reqMap.put("ps", 100);
        } else {
            reqMap.put("ps", request.getPs());
        }
        if (StringUtils.isNotEmpty(request.getS())) {
            reqMap.put("s", request.getS());
        } else {
            reqMap.put("s", "name");
        }
        //默认查询激活的
        if (StringUtils.isNotEmpty(request.getActivation())) {
            reqMap.put("activation", request.getActivation());
        } else {
            reqMap.put("activation", true);
        }
        //默认查询Java语言
        if (StringUtils.isNotEmpty(request.getLanguages())) {
            reqMap.put("languages", request.getLanguages());
        } else {
            reqMap.put("languages", "java");
        }
        //默认查询bug和漏洞
        if (StringUtils.isNotEmpty(request.getTypes())) {
            reqMap.put("types", request.getTypes());
        }
        if (StringUtils.isNotEmpty(request.getFacets())) {
            reqMap.put("facets", request.getFacets());
        } else {
            reqMap.put("facets", "types,severities");
        }
        if (StringUtils.isNotEmpty(request.getSeverities())) {
            reqMap.put("severities", request.getSeverities());
        }
        if (StringUtils.isNotBlank(request.getRuleKey())) {
            reqMap.put("rule_key", request.getRuleKey());
        }
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/rules/search")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/rules/search", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用sonar服务搜索规则列表异常", resultResp);
        }
        return JSONUtils.deserialize(resultResp, SearchRulesResponse.class);
    }

    default ProfileCreateResp createProfile(String language, String profileName) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("language", language);
        reqMap.put("name", profileName);

        String resultResp = HttpRequest
                .post(sonarUrl() + "/api/qualityprofiles/create")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/qualityprofiles/create", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用sonar服务创建质量配置异常", resultResp);
        }
        return JSONUtils.deserialize(resultResp, ProfileCreateResp.class);
    }

    default void changeProfileParent(String language, String profileName, String parentProfileName) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("language", language);
        reqMap.put("qualityProfile", profileName);
        reqMap.put("parentQualityProfile", parentProfileName);

        HttpResponse response = HttpRequest
                .post(sonarUrl() + "/api/qualityprofiles/change_parent")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!response.isOk()) {
            logError("/api/qualityprofiles/change_parent", reqMap, response.body());
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用sonar服务绑定规则集父子关系异常", response);
        }
    }

    default void deleteProfile(String language, String profileName) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("language", language);
        reqMap.put("qualityProfile", profileName);

        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/qualityprofiles/delete")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("/api/qualityprofiles/delete", reqMap, resp.body());
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用sonar 删除质量配置异常", resp);
        }
    }

    default void activeRule(String profileKey, String ruleKey, String severity) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("rule", ruleKey);
        reqMap.put("key", profileKey);
        String sonarStatus = CheckIssueSeverity.querySonarStatus(severity);
        if (StringUtils.isNotEmpty(sonarStatus)) {
            reqMap.put("severity", sonarStatus);
        }
        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/qualityprofiles/activate_rule")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("api/qualityprofiles/activate_rule", reqMap, resp.body());
        }
    }

    default void deActiveRule(String profileKey, String ruleKey) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("rule", ruleKey);
        reqMap.put("key", profileKey);
        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/qualityprofiles/deactivate_rule")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("api/qualityprofiles/deactivate_rule", reqMap, resp.body());
        }
    }

    default MeasuresComponentTreeResponse measuresComponentTree(MeasuresComponentTreeRequest request) {
        Map<String, Object> reqMap = new HashMap<>();
        Integer page = request.getPage();
        reqMap.put("p", page == null ? 1 : page);
        Integer pageSize = request.getPageSize();
        reqMap.put("ps", pageSize == null ? 500 : pageSize);
        reqMap.put("asc", request.isAsc());
        String metricSort = request.getMetricSort();
        if (StringUtils.isEmpty(metricSort)) {
            metricSort = request.getMetricKeys();
        }
        reqMap.put("metricSort", metricSort);
        String s = request.getS();
        if (StringUtils.isEmpty(s)) {
            s = "metric";
        }
        reqMap.put("s", s);
        String metricSortFilter = request.getMetricSortFilter();
        if (StringUtils.isEmpty(metricSortFilter)) {
            metricSortFilter = "withMeasuresOnly";
        }
        reqMap.put("metricSortFilter", metricSortFilter);
        reqMap.put("component", request.getComponent());
        reqMap.put("metricKeys", request.getMetricKeys());
        String strategy = request.getStrategy();
        if (StringUtils.isEmpty(strategy)) {
            strategy = "leaves";
        }
        reqMap.put("strategy", strategy);
        String branch = request.getBranch();
        if (StringUtils.isNotEmpty(branch)) {
            reqMap.put("branch", branch);
        }
        String resultResp = HttpRequest
                .post(sonarUrl() + "/api/measures/component_tree")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/measures/component_tree", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用sonar获取度量数据component_tree出现异常",
                    resultResp);
        }
        return JSONUtils.deserialize(resultResp, MeasuresComponentTreeResponse.class);
    }

    default Issue doTransition(String issue, String transition) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("issue", issue);
        reqMap.put("transition", transition);
        String resultResp = HttpRequest
                .post(sonarUrl() + "/api/issues/do_transition")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/issues/do_transition", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用sonar进行issue打标失败", resultResp);
        }
        return JSONUtils.deserialize(resultResp, Issue.class);
    }

    default ProfileProjectsResp getProfileProjects(String profileKey, int page, int pageSize) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("key", profileKey);
        reqMap.put("p", page);
        if (pageSize > 500) {
            pageSize = 500;
        }
        reqMap.put("ps", pageSize);
        String resultResp = HttpRequest
                .post(sonarUrl() + "/api/qualityprofiles/projects")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/qualityprofiles/projects", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "获取profileKey项目失败", resultResp);
        }
        return JSONUtils.deserialize(resultResp, ProfileProjectsResp.class);
    }

    default CopyQualityProfilesResp copyQualityProfiles(CopyQualityProfilesReq req) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("fromKey", req.getFromKey());
        reqMap.put("toName", req.getToName());
        String resultResp = HttpRequest
                .post(sonarUrl() + "/api/qualityprofiles/copy")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/qualityprofiles/copy", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "copy quality profile失败", resultResp);
        }
        return JSONUtils.deserialize(resultResp, CopyQualityProfilesResp.class);
    }

    default DuplicationListResp getFileDuplication(String key, String sonarBranch) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("key", key);
        reqMap.put("branch", sonarBranch);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/duplications/show")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/duplications/show", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR, "调用sonar服务[/api/duplications/show]异常",
                    resultResp);
        }
        return JSONUtils.deserialize(resultResp, DuplicationListResp.class);
    }

    default ProfileInheritanceResp getProfileInheritance(String language, String qualityProfile) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("language", language);
        reqMap.put("qualityProfile", qualityProfile);

        String resultResp = HttpRequest
                .post(sonarUrl() + "/api/qualityprofiles/inheritance")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/qualityprofiles/inheritance", reqMap, resultResp);
            throw new ThemisException(ResultCodeConstant.API_RESP_ERROR,
                    "调用sonar服务[/api/qualityprofiles/inheritance]异常", resultResp);
        }
        return JSONUtils.deserialize(resultResp, ProfileInheritanceResp.class);
    }

    /**
     * 根据 projectKey 精确查找
     */
    default Project searchProjectByKey(String projectKey) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("p", 1);
        reqMap.put("ps", 1);
        // reqMap.put("filter", URLEncoder.encode("query=" + projectKey, StandardCharsets.UTF_8.toString()));
        reqMap.put("filter", "query=" + projectKey);
        String resultResp = HttpRequest
                .get(sonarUrl() + "/api/components/search_projects?f=analysisDate")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute()
                .body();
        if (StringUtils.isEmpty(resultResp)) {
            logError("/api/components/search_projects?f=analysisDate", reqMap, resultResp);
            throw new RuntimeException("调用sonar服务查询指定项目返回异常，resp is " + resultResp);
        }
        ProjectsResp resp = JSONUtils.deserialize(resultResp, ProjectsResp.class);
        if (resp == null || CollectionUtils.isEmpty(resp.getComponents())) {
            return null;
        }
        return resp.getComponents().get(0);
    }

    default void deleteProjectBranch(String projectKey, String branch) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.put("project", projectKey);
        reqMap.put("branch", branch);
        HttpResponse resp = HttpRequest
                .post(sonarUrl() + "/api/project_branches/delete")
                .header(Header.AUTHORIZATION, basicAuth())
                .form(reqMap)
                .timeout(timeout())//超时，毫秒
                .execute();
        if (!resp.isOk()) {
            logError("api/project_branches/delete", reqMap, resp.body());
        }
    }

    default void logError(String apiPath, Object req, String resp) {
        logger.error("调用sonar服务[{}]异常 ，req: {}, resp: {}, sonarUrl: {}, ", apiPath, req, resp, sonarUrl());
    }
}
