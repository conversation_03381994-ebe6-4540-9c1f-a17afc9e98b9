package com.kuaishou.serveree.themis.component.service.platform.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.process.ProcessChangeType;
import com.kuaishou.serveree.themis.component.entity.issue.DefaultTransitions;
import com.kuaishou.serveree.themis.component.entity.issue.IssueWorkflow;
import com.kuaishou.serveree.themis.component.entity.issue.Transition;
import com.kuaishou.serveree.themis.component.entity.kim.IssueTransitionNoticeContext;
import com.kuaishou.serveree.themis.component.service.IssueChangesService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryBaseService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.platform.IssueTransitionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;
import com.kuaishou.serveree.themis.component.service.platform.notice.CheckRepoNoticeActionService;
import com.kuaishou.serveree.themis.component.utils.IssueUtils;
import com.kuaishou.serveree.themis.component.utils.NumberUtils;
import com.kuaishou.serveree.themis.component.utils.PlatformSonarInfoUtils;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SonarIssueVo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-02-10
 */
@Slf4j
abstract class AbstractIssueTransitionService implements IssueTransitionService {

    @Autowired
    protected PlatformPermissionService platformPermissionService;

    @Autowired
    protected IssueSummaryService summaryService;

    @Autowired
    protected IssueSummaryBaseService issueSummaryBaseService;

    @Autowired
    protected IssueWorkflow workflow;

    @Autowired
    protected IssueChangesService issueChangesService;

    @Autowired
    protected CheckRepoNoticeActionService checkRepoNoticeActionService;

    @Autowired
    protected PlatformSonarInfoUtils platformSonarInfoUtils;

    @Transactional
    @Override
    public SonarIssueVo transition(IssueTransitionRequest request, String userName) {
        // 禁止页面点击解决issue方式
        if (DefaultTransitions.RESOLVE.equals(request.getTransition())) {
            throw new ThemisException(ResultCodeConstant.ISSUE_ONLY_SUPPORT_RESOLVED_BY_CODE);
        }
        // 查询记录
        IssueSummary issueSummary = getIssueSummary(request);
        // 不需要进行状态流转
        if (!workflow.needTransition(issueSummary.getStatus(), request.getTransition())) {
            return covertToSonarIssueVo(issueSummary);
        }
        // 根据操作的动作从状态机中获取下一个流转的状态
        Transition transition = workflow.doManualTransition(issueSummary.newIssue(), request.getTransition());
        // 执行状态流转
        doTransition(issueSummary, transition, userName);
        // 后续操作
        afterTransition(request, issueSummary);
        // 发送状态变更消息
        sendIssueTransitionNotice(request, issueSummary, transition, userName);
        // 返回
        return covertToSonarIssueVo(issueSummary);
    }

    protected void doTransition(IssueSummary issueSummary, Transition transition, String userName) {
        // 更新summary的状态和change的动作
        issueChangesService.saveOneChange(issueSummary, ProcessChangeType.STATUS, transition.to(), userName);
        summaryService.updateIssueStatus(issueSummary, transition.to());
        // 更改 summary base 表中 issue 的状态
        issueSummaryBaseService.updateIssueStatusByUniqKey(IssueUtils.getIssueUniqkey(issueSummary), transition.to(),
                userName);
    }

    private void sendIssueTransitionNotice(IssueTransitionRequest request, IssueSummary summary, Transition transition,
            String userName) {
        checkRepoNoticeActionService.sendIssueTransitionNotice(IssueTransitionNoticeContext.builder()
                .gitProjectId(summary.getGitProjectId())
                .operator(userName)
                .beforeStatus(transition.from())
                .afterStatus(transition.to())
                .issueId(summary.getId())
                .scanMode(scanMode())
                .executionReferType(summary.getExecutionReferType())
                .kspBuildId(request.getKspBuildId())
                .build()
        );
    }

    protected abstract ScanModeEnum scanMode();

    protected IssueSummary getIssueSummary(IssueTransitionRequest request) {
        IssueSummary issueSummary;
        if (NumberUtils.isPositive(request.getIssueId())) {
            issueSummary = summaryService.getById(request.getIssueId());
        } else {
            issueSummary = summaryService.getByGitBranchUniqId(
                    request.getGitProjectId(), request.getGitBranch(), request.getIssueUniqId(), scanMode().getCode());
        }
        if (issueSummary == null) {
            throw new ThemisException(ResultCodeConstant.ISSUE_NOT_EXIST);
        }
        return issueSummary;
    }

    protected void afterTransition(IssueTransitionRequest request, IssueSummary issueSummary) {
    }

    protected SonarIssueVo covertToSonarIssueVo(IssueSummary issueSummary) {
        String issueKey = IssueUtils.getIssueUniqkey(issueSummary);
        String lastModifier =
                issueSummaryBaseService.getModifierByIssueUniqKeys(List.of(issueKey)).get(issueKey);
        SonarIssueVo sonarIssueVo = SonarIssueVo.convertFromIssueSummary(issueSummary,
                workflow.outTransitions(issueSummary.newIssue()).stream()
                        .map(Transition::key)
                        .collect(Collectors.toList()), lastModifier);
        sonarIssueVo.setComponentKey(
                platformSonarInfoUtils.getFinalProjectKey(sonarIssueVo.getProjectId().intValue()) + ":"
                        + sonarIssueVo.getLocation());
        return sonarIssueVo;
    }
}
