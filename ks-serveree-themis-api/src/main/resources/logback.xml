<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="max.history" value="90" />

    <property name="CONSOLE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p %X{kslog} %X{remoteIp} %X{visitorId} %t \\(%F:%L\\) - %X{traceId} %X{outtraceid} %msg%n"/>


    <!-- stdout appender定义 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <charset>UTF-8</charset>
            <!--<pattern>%d [%thread] %-5level %logger{68} - %msg%n</pattern>-->
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    <appender name="DEBUGOUT" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <encoder>
            <charset>UTF-8</charset>
            <!--<pattern>[%level][%d][%thread %logger{68} %line] %msg%n</pattern>-->
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 日志文件 appender定义 -->
    <appender name="ThemisApiInfoAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印info日志 -->
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <File>./themis-api.info.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./themis-api.info.log.%d
            </fileNamePattern>
            <maxHistory>90</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <!--<pattern>%d [%thread] %-5level %logger{36} %line %msg%n</pattern>-->
        </encoder>
    </appender>



    <appender name="ThemisApiWarnAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
            <level>warn</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <File>./themis-api.warn.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./themis-api.warn.log.%d
            </fileNamePattern>
            <maxHistory>90</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>

        <encoder>
            <charset>UTF-8</charset>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <!--<pattern>%d [%thread] %-5level %logger{36} %msg%n</pattern>-->
        </encoder>
    </appender>

    <appender name="ThemisApiERRORAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印错误日志 -->
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <File>./themis-api.error.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./themis-api.error.log.%d
            </fileNamePattern>
            <maxHistory>90</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>

        <encoder>
            <charset>UTF-8</charset>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <!--<pattern>%d [%thread] %-5level %logger{36} %msg%n</pattern>-->
        </encoder>
    </appender>


    <appender name="ThemisApiSQLAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>./themis-api.sql.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./themis-api.sql.log.%d
            </fileNamePattern>
            <maxHistory>90</maxHistory>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>

        <encoder>
            <charset>UTF-8</charset>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 异步日志appender -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="ThemisApiSQLAppender" />
        <!-- 设置异步阻塞队列的大小，为了不丢失日志建议设置的大一些，单机压测时100000是没问题的，应该不用担心OOM -->
        <queueSize>10000</queueSize>
        <!-- 设置丢弃DEBUG、TRACE、INFO日志的阀值，不丢失 -->
        <discardingThreshold>0</discardingThreshold>
        <!-- 设置队列入队时非阻塞，当队列满时会直接丢弃日志，但是对性能提升极大 -->
        <neverBlock>true</neverBlock>
    </appender>

    <appender name="Sentry" class="io.sentry.logback.SentryAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <logger name="com.kuaishou.serveree.themis.component.intercept.LogMybatisInterceptor"
            additivity="false">
        <level value="INFO" />
        <appender-ref ref="ASYNC" />
        <appender-ref ref="ThemisApiERRORAppender" />
        <appender-ref ref="DEBUGOUT" />
        <appender-ref ref="Sentry" />
    </logger>

    <logger name="com.kuaishou" additivity="false">
        <level value="INFO" />
        <appender-ref ref="ThemisApiInfoAppender" />
        <appender-ref ref="ThemisApiERRORAppender" />
        <appender-ref ref="ThemisApiWarnAppender" />
        <appender-ref ref="DEBUGOUT" />
        <appender-ref ref="Sentry" />
    </logger>

    <!-- root 放在最后 -->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>

</configuration>

