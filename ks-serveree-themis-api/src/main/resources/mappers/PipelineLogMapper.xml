<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kuaishou.test.dao.PipelineLogRepository">

    <select id="listForSchedule" resultType="com.kuaishou.test.domain.PipelineLog">
        select id ,creator,status,pipeline_id,queue_key
        from pipeline_log
        where pipeline_id = #{pipelineId} and status = 0
        order by id,create_time
    </select>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ left join ]}} -->
    <select id="listForScheduleParallel" resultType="com.kuaishou.test.domain.PipelineLog">
        select a.id,a.creator,a.status,a.pipeline_id,a.queue_key
        from pipeline_log a
                 left join (select mr_id from pipeline_log where pipeline_id = #{pipelineId} and status in (1,2)) b on a.mr_id = b.mr_id
        where a.pipeline_id = #{pipelineId} and a.status = 0 and b.mr_id is null
        order by a.id,a.create_time
    </select>
    <!-- 没有正在执行的pipeline 的最大id的记录设置状态为待执行，其他的设置状态为取消-->
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update , left join ]}} -->
    <update id="fixPipelineLogCreated">
        UPDATE pipeline_log t1,(
            select a.id, if(a.id = b.id, 1, 5) as status
            from pipeline_log a,
            (
            select max(a.id) as id, a.pipeline_id as pipeline_id
            from pipeline_log a
            left join pipeline b on a.pipeline_id = b.id and b.deleted = 0
            where a.pipeline_id not in (
            select pipeline_id
            from pipeline_log
            where status = 2
            and deleted = 0
            )
            and a.deleted = 0
            and a.status in (0, 1)
            and b.id is not null and
            (JSON_EXTRACT(b.custom, "$.parallel") != 1 or JSON_EXTRACT(b.custom, "$.parallel") is null)
            group by a.pipeline_id
            ) b
            where a.pipeline_id = b.pipeline_id
            and a.deleted = 0
            and a.status in (0, 1)
            ) t2
        set t1.status = t2.status
        where t1.id = t2.id and t1.status = 0 and deleted = 0
    </update>
    <!-- 并行流水线-符合条件的记录设置为待执行：1-并行流水线；2-所属流水线待执行或执行中的记录小于5;3-触发执行的用户在当前流水线下不能有执行中或待执行的记录-->
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update , left join ]}} -->
    <update id="fixPipelineLogCreatedParallel">
        update pipeline_log t1,(
            select min(if(a.status = 0 and c.id is null, a.id, null)) as readyLogId
            from pipeline_log a
            left join pipeline b on a.pipeline_id = b.id and b.deleted = 0
            left join pipeline_log c on a.pipeline_id = c.pipeline_id and a.creator = c.creator and c.status in (1, 2) and c.deleted = 0
            where b.id is not null
            and JSON_EXTRACT(b.custom, "$.parallel") = 1
            and 2 >= a.status
            and a.deleted = 0
            group by a.pipeline_id
            having 10 > sum(if(a.status = 1 or a.status = 2, 1, 0))
            and readyLogId > 0
            ) t2
        set t1.status = 1
        where t1.status = 0
          and t1.id = t2.readyLogId
    </update>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update ]}} -->
    <update id="cancelPipelineLogCreated">
        update pipeline_log set status = 5,update_time = unix_timestamp()*1000
        <if test="username != null">,updater=#{username}</if>
        where status in(0,1) and deleted = 0 and pipeline_id = #{pipelineId} and id != #{id}
        <if test="mrId != null"> and mr_id=#{mrId}</if>
    </update>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update ]}} -->
    <update id="cancelPipelineLogCreatedParallel">
        update pipeline_log set status = 5,update_time = unix_timestamp()*1000
        <if test="username != null">,updater=#{username}</if>
        where status in(0,1) and deleted = 0 and pipeline_id = #{pipelineId} and id != #{id}
        <if test="mrId != null"> and mr_id=#{mrId}</if>
    </update>

    <select id="queryPipelineLogForExecute" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log where status = 1
                                     and (pipeline_id,pipeline_version) in (select pipeline_id,version from pipeline where deleted = 0 and status in (1,2) and target_type in(1,4))
        order by create_time,id
            limit #{limit}
    </select>
    <select id="queryPipelineLogForExecuteByPipeline" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log where status = 1
                                     and (pipeline_id,pipeline_version) in (select pipeline_id,version from pipeline where deleted = 0 and status in (1,2) and target_type in(1,4))
                                     and pipeline_id = #{pipelineId}
        order by create_time,id
    </select>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update ]}} -->
    <update id="updateToRun">
        update pipeline_log set status = 2,start_time = #{startTime},end_time = 0 where status = 1 and id = #{id}
    </update>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update ]}} -->
    <update id="updateToReady">
        update pipeline_log set status = 1 where status = 0 and id = #{id}
    </update>

    <select id="queryForEnd" resultType="com.kuaishou.test.dto.PipelineLogStatusDto">
        select pipeline_log_id            as pipelineLogId,
               max(end_time)              as endTime,
               sum(if(status = 3 or status =7, 0, 1)) as notSuccessCnt,
               sum(if(status = 4,1,0))    as failCnt,
               sum(if(status = 5,1,0))    as cancelCnt,
               sum(if(status in (1,2,6,11,14),1,0))    as runningCnt
        from pipeline_job_log
        where pipeline_log_id in (select id from pipeline_log where status = 2)
        group by pipeline_log_id
        having runningCnt = 0 and (notSuccessCnt = 0 or failCnt > 0 or cancelCnt > 0)
            limit 20
    </select>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update ]}} -->
    <update id="updateToEnd">
        update pipeline_log set status = #{endStatus}, end_time = #{endTime} where id = #{id} and status = 2
    </update>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update ]}} -->
    <update id="updateToNotifySuccess">
        update pipeline_log set notify_time = #{now} where deleted = 0 and id = #{id} and notify_time = 0
    </update>

    <select id="listTotal" parameterType="com.kuaishou.test.param.PipelineLogParam" resultType="java.lang.Integer">
        select count(*) from pipeline_log
        <include refid="listWhereSql"/>
    </select>
    <select id="list" parameterType="com.kuaishou.test.param.PipelineLogParam"
            resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log
        <include refid="listWhereSql"/>
        order by id desc
        limit #{start},#{limit}
    </select>
    <select id="listIds" parameterType="com.kuaishou.test.param.PipelineLogParam"
            resultType="com.kuaishou.test.domain.PipelineLog">
        select id from pipeline_log
        <include refid="listWhereSql"/>
        order by id desc
    </select>
    <sql id="listWhereSql">
        where deleted = 0
        <if test="branchId != null">
            and pipeline_id in (
            select id from pipeline
            where target_id = #{branchId} and target_type = 1 and id = pipeline_id and status = 1
            <if test="pipelineDeleted == null">and deleted = 0</if>
            )
        </if>
        <if test="repoId != null and svnBranchByRepo !=null and svnBranchByRepo == true">
            and pipeline_id in (
            select id from pipeline
            where target_type = 4 and id = pipeline_id and deleted= 0 and status = 1
            and target_id in (select id from branch where repo_id = #{repoId} and deleted = 0 and type = 11)
            )
        </if>
        <if test="pipelineId != null">and pipeline_id = #{pipelineId}</if>
        <if test="integrationId != null">and integration_id = #{integrationId}</if>
        <if test="commitHash != null">and commit_hash = #{commitHash}</if>
        <if test="creator != null">and creator=#{creator}</if>
        <if test="mrId != null">and mr_id=#{mrId}</if>
        <if test="pipelineIds != null and pipelineIds.size() > 0">
            and pipeline_id in (<foreach collection="pipelineIds" item="item" index="index" separator=",">#{item}</foreach>)
        </if>
        <if test="statusList != null and statusList.size() > 0">
            and status in (
            <foreach collection="statusList" item="statusItem" index="index" separator=",">
                #{statusItem}
            </foreach>
            )
        </if>
        <if test="status != null">
            and status = #{status}
        </if>
        <if test="keyword != null">
            and (
            commit_hash like concat('%',#{keyword},'%')
            or creator like concat('%',#{keyword},'%')
            or JSON_EXTRACT(trigger_param, "$.last_commit.message") like concat('%',#{keyword},'%')
            )
        </if>
    </sql>

    <select id="listRecentlyMaxIds" parameterType="com.kuaishou.test.param.PipelineLogParam"
            resultType="java.lang.Long">
        select max(id) from pipeline_log
        <include refid="listWhereSql"></include>
        group by pipeline_id
    </select>
    <select id="listRecentlyMaxIdsByPipeline" parameterType="com.kuaishou.test.param.PipelineLogParam"
            resultType="com.kuaishou.test.domain.PipelineLog">
        select max(id) as id ,pipeline_id as pipelineId from pipeline_log
        <include refid="listWhereSql"></include>
        group by pipeline_id
    </select>

    <select id="listRecentlyByIds" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log where id in (
        <foreach collection="ids" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </select>

    <select id="listRecentlyByHash" resultType="java.lang.Long">
        select max(id) from pipeline_log
        where pipeline_id in (
        <foreach collection="pipelineIdList" item="item" index="index" separator=",">
            #{item}
        </foreach>
        ) and commit_hash in (
        <foreach collection="commitHashList" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
        and status != 5
        group by pipeline_id,commit_hash
    </select>
    <select id="listRecentlyByMr" resultType="java.lang.Long">
        select max(id) from pipeline_log
        where pipeline_id in (
        <foreach collection="pipelineIdList" item="item" index="index" separator=",">
            #{item}
        </foreach>
        ) and mr_id = #{mrId} and status != 5
        group by pipeline_id
    </select>

    <select id="statByStatus" resultType="com.kuaishou.test.domain.PipelineLog">
        select status,count(*) as id from pipeline_log
        where pipeline_id = #{pipelineId} and deleted = 0
        <if test="userName != null">
            and creator = #{userName}
        </if>
        group by status
    </select>
    <select id="statByStatuses" resultType="com.kuaishou.test.domain.PipelineLog">
        select pipeline_id,status,count(*) as id from pipeline_log
        where pipeline_id in (<foreach collection="pipelineIds" item="item" index="index" separator=",">#{item}</foreach>)and deleted = 0
        group by status,pipeline_id
    </select>
    <select id="listRunning" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log
        where pipeline_id = #{pipelineId} and deleted = 0 and status = 2
    </select>

    <select id="listRunningByCommit" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log
        where pipeline_id = #{pipelineId} and deleted = 0 and status = 2 and commit_hash = #{commitHash} and trigger_type = 3
    </select>

    <select id="listRunningByMr" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log
        where pipeline_id = #{pipelineId} and deleted = 0 and status = 2 and mr_id =#{mrId} and trigger_type=#{triggerType}
    </select>


    <select id="listByLocalbuildId" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log where deleted = 0 and trigger_type = 5 and mr_id in (
        <foreach collection="mrIds" item="item" index="index" separator=",">
            #{item}
        </foreach>
        )
    </select>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ left join ]}} -->
    <select id="listAdmins" resultType="com.kuaishou.test.dto.PipelineLogAdminDto"
            parameterType="com.kuaishou.test.dto.PipelineLogAdminDto">
        select a.id as id,
        a.status as status,
        a.trigger_type as triggerType,
        a.creator as creator,
        a.create_time as createTime,
        a.start_time as startTime,
        a.end_time as endTime,
        a.pipeline_id as pipelineId,
        a.pipeline_version as pipelineVersion,
        b1.version as pipelineCurrentVersion,
        b.name as pipelineName,
        b.status as pipelineStatus,
        b.target_type as pipelineTargetType,
        b.deleted as pipelineDeleted,
        b.ksp_pipeline_id as kspPipelineId,
        b.ksp_pipeline_status as kspPipelineStatus,
        c.id as branchId,
        c.name as branchName,
        c.repo_id as repoId,
        ifnull(f.path,g.path) as repoPath,
        e.type as repoType,
        d.group_id as groupId,
        a.fail_reason as failReason
        from pipeline_log a
        left join pipeline b on a.pipeline_id = b.pipeline_id and a.pipeline_version = b.version and b.status in (1,2)
        left join pipeline b1 on a.pipeline_id = b1.id
        left join branch c on b.target_id = c.id and b.target_branch_type = c.type
        left join repo_group_relation d on c.repo_id = d.repo_id
        left join repo e on c.repo_id = e.id
        left join gitlab_project_config f on e.project_id = f.project_id and e.type = 1
        left join svn g on e.project_id = g.id and e.type = 2
        where b1.id is not null
        <if test="id != null">and a.id = #{id}</if>
        <if test="status != null">and a.status = #{status}</if>
        <if test="triggerType != null">and a.trigger_type = #{triggerType}</if>
        <if test="creator != null">and a.creator = #{creator}</if>
        <if test="pipelineId != null">and a.pipeline_id = #{pipelineId}</if>
        <if test="branchId != null">and c.id = #{branchId}</if>
        <if test="repoId != null">and c.repo_id = #{repoId}</if>
        <if test="groupId != null">and d.group_id = #{groupId}</if>
        <if test="repoPath != null">and ifnull(f.path,g.path) like concat('%',#{repoPath},'%')</if>
        <if test="kspPipelineStatus!=null">and b.ksp_pipeline_status=#{kspPipelineStatus}</if>
        <if test="kspPipelineId!=null">and b.ksp_pipeline_id=#{kspPipelineId}</if>
        <if test="repoType!=null">and e.type=#{repoType}</if>
        <if test="ids != null">
            and a.id in (<foreach collection="ids" item="item" index="index" separator=",">#{item}</foreach>)
        </if>
        <if test="createTime != null">
            and a.create_time > #{createTime} and (a.notify_time = 0 or a.notify_time > #{createTime})
        </if>
        order by a.id desc
        limit #{limit}
    </select>
    <select id="listNeedNotifyPipelineLog" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log where deleted = 0 and status in (3,4,5) and notify_time = 0
        order by create_time,id
            limit #{limit}
    </select>
    <select id="listByPipelineLogIds" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log
        where deleted = 0 and id in (<foreach collection="ids" item="item" index="index" separator=",">#{item}</foreach>)
    </select>


    <select id="statLogStatus" parameterType="com.kuaishou.test.dto.PipelineLogAdminDto"
            resultType="com.kuaishou.test.dto.pipeline.PipelineLogStatusStatDto">
        select date_format(from_unixtime(create_time/1000),'%Y%m%d') as dayTime,status,count(*) as cnt
        from pipeline_log
        where create_time between #{startTime} and #{endTime}
        and (notify_time = 0  or create_time between #{startTime} and #{endTime})
        <if test="groupId != null or repoId != null or groupIds != null">
            and pipeline_id in (
            select id
            from pipeline
            where target_id in (
            select id
            from branch
            where id is not null
            <if test="groupId != null">
                and repo_id in (
                select repo_id
                from repo_group_relation
                where group_id = #{groupId}
                )
            </if>
            <if test="repoId != null">
                and repo_id = #{repoId}
            </if>
            <if test="groupIds != null">
                and repo_id in (
                select repo_id
                from repo_group_relation
                where group_id in (${groupIds})
                )
            </if>
            ) and target_type in (1, 4)
            and id = pipeline_id and status = 1
            )
        </if>
        group by dayTime,status
        order by dayTime,status
    </select>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ left join ]}} -->
    <select id="listForCancel" resultType="java.lang.Long">
        select a.id from pipeline_log a
                             left join pipeline b on a.pipeline_id = b.pipeline_id and a.pipeline_version = b.version
        where a.status in (0,1) and (b.id is null or b.deleted = 1)
    </select>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update ]}} -->
    <update id="updateCancelById">
        update pipeline_log set status = 5 where id = #{id} and status in (0,1)
    </update>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update ]}} -->
    <update id="updateCancelByIds">
        update pipeline_log set status = 5
        where status = 0 and id in
        (
        <foreach collection="ids" item="item" index="index" separator=",">#{item}</foreach>
        )
    </update>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ update ]}} -->
    <update id="updateForCancel">
        update pipeline_log set status = 5, updater=#{updater} ,end_time=#{currentTime}, update_time=#{currentTime}
        where id = #{id} and status = #{lastStatus}
    </update>

    <select id="listAllErrorByTime" resultType="com.kuaishou.test.domain.PipelineLog">
        select id,pipeline_id,pipeline_version,status from pipeline_log
        where status = 4 and end_time between #{startTime} and #{endTime}
          and notify_time between (#{startTime} - 3600000) and (#{endTime} + 3600000)
    </select>

    <select id="listRunningByPipelineId" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log where pipeline_id = #{pipelineId} and status = 2
        <if test="id != null">and id != #{id}</if>
        order by create_time desc
    </select>
    <!-- Noncompliant@+1 {{该SQL内包含不被允许的语法: [ left join ]}} -->
    <select id="loadJavaModuleDeploy" resultType="com.kuaishou.test.dto.pipeline.PipelineJavaDeployInputData">
        select a.id,c.input as javaInput,d.input as laneDeployInput
        from pipeline_log a
        left join pipeline b on b.id = a.pipeline_id and b.deleted = 0 and b.status = 1 and  b.target_type = 1 and b.target_id = #{branchId}
        left join pipeline_job_log c on c.pipeline_log_id = a.id and c.template_id in (
        <foreach collection="javaIds" item="item" index="index" separator=",">#{item}</foreach>
        )
        left join pipeline_job_log d on d.pipeline_log_id = a.id and d.template_id in (
        <foreach collection="laneDeployIds" item="item" index="index" separator=",">#{item}</foreach>
        )
        where b.id is not null and c.id is not null and d.id is not null and a.status = 3
        order by id desc limit 100
    </select>

    <select id="selectLatestRunLogByStatus" resultType="com.kuaishou.test.domain.PipelineLog">
        select * from pipeline_log where id in (
        select max(id) as id from pipeline_log where status = #{status} and pipeline_id in
        <foreach collection="pipelineIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        group by pipeline_id
        order by id
        )
    </select>

    <delete id="cleanHistoryByIds">
        delete from pipeline_log where id in (
        <foreach collection="ids" item="item" index="index" separator=",">#{item}</foreach>
        )
    </delete>
</mapper>