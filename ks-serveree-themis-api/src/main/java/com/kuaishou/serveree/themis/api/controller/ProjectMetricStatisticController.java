package com.kuaishou.serveree.themis.api.controller;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.plugin.ComplexityService;
import com.kuaishou.serveree.themis.component.vo.request.ComplexityReportRequest;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-02-26
 */
@RestController
@RequestMapping("/project/metric/statistic")
public class ProjectMetricStatisticController {

    @Resource
    private ComplexityService complexityService;

    @NeedAuth
    @PostMapping("/complexity/report")
    public ThemisResponse<Boolean> complexityReport(@RequestBody @Validated ComplexityReportRequest reportData) {
        return ThemisResponse.success(complexityService.dataReport(reportData));
    }
}
