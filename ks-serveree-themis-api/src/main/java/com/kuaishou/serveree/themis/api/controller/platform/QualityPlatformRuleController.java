package com.kuaishou.serveree.themis.api.controller.platform;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.converter.CheckRuleConverter;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRuleService;
import com.kuaishou.serveree.themis.component.vo.request.AddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.DeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ListRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.SearchRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.UpdateRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.AddRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.DeleteRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.SearchRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.SearchRuleResponseVo.CheckRuleVo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.UpdateRuleResponseVo;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/9/6 11:22 上午
 */
@Slf4j
@RestController
@RequestMapping("platform/rule")
public class QualityPlatformRuleController {

    @Autowired
    private PlatformRuleService platformRuleService;

    @PostMapping("/search")
    @NeedAuth
    public ThemisResponse<SearchRuleResponseVo> search() {
        return ThemisResponse.success(platformRuleService.search(new SearchRuleRequestVo()));
    }

    @PostMapping("/update")
    @NeedAuth
    public ThemisResponse<UpdateRuleResponseVo> update(@RequestBody UpdateRuleRequestVo requestVo) {
        return ThemisResponse.success(platformRuleService.update(requestVo));
    }

    @PostMapping("/delete")
    @NeedAuth
    public ThemisResponse<DeleteRuleResponseVo> delete(@RequestBody DeleteRuleRequestVo requestVo) {
        return ThemisResponse.success(platformRuleService.delete(requestVo));
    }

    @PostMapping("/add")
    @NeedAuth
    public ThemisResponse<AddRuleResponseVo> add(@RequestBody AddRuleRequestVo requestVo) {
        return ThemisResponse.success(platformRuleService.add(requestVo));
    }

    @GetMapping("/project/pipeline/rules")
    public ThemisResponse<List<CheckRuleVo>> projectPipelineRules(Integer gitProjectId,
            @RequestParam(required = false, defaultValue = "") String language,
            @RequestParam(required = false, defaultValue = "") String scanner) {
        return ThemisResponse.success(platformRuleService.projectPipelineRules(gitProjectId, language, scanner));
    }

    @GetMapping("/profile/rules")
    public ThemisResponse<List<CheckRuleVo>> profileRules(@RequestParam String profileName,
            @RequestParam(required = false, defaultValue = "") String scanner) {
        return ThemisResponse.success(platformRuleService.list(
                ListRuleRequestVo.builder()
                        .profileName(profileName)
                        .scanner(scanner)
                        .selected(true)
                        .onlySelf(false)
                        .page(1)
                        .pageSize(1 << 10)
                        .build(), "system"
        ).getRules().stream().map(CheckRuleConverter.INSTANCE::ruleInfoResponseVo2CheckRuleVo).collect(Collectors.toList()));
    }

}
