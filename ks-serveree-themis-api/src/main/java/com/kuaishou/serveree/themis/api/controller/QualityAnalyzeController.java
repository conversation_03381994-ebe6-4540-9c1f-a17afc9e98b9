package com.kuaishou.serveree.themis.api.controller;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.service.QualityAnalyzeService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.AddDependencyConfigRequest;
import com.kuaishou.serveree.themis.component.vo.request.QualityAnalyzeDetailInfoRequest;
import com.kuaishou.serveree.themis.component.vo.request.QualityAnalyzeIssueListRequest;
import com.kuaishou.serveree.themis.component.vo.request.ReportDependencyAnalyzeRequest;
import com.kuaishou.serveree.themis.component.vo.response.CheckInfoResponse;
import com.kuaishou.serveree.themis.component.vo.response.DefaultSettingResponse;
import com.kuaishou.serveree.themis.component.vo.response.DependencyConfigResponse;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeDetailInfoResponse;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeIssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeResultResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/1/27 3:30 下午
 */
@Slf4j
@RestController
@RequestMapping("analyze")
public class QualityAnalyzeController {

    @Autowired
    private QualityAnalyzeService qualityAnalyzeService;

    @Autowired
    private KsRedisLock ksRedisLock;

    @GetMapping("/detail")
    public ThemisResponse<QualityAnalyzeDetailInfoResponse> analyzeDetail(QualityAnalyzeDetailInfoRequest request) {
        return qualityAnalyzeService.analyzeDetail(request);
    }

    @GetMapping("/issue/list")
    public ThemisResponse<QualityAnalyzeIssueListResponse> analyzeIssueList(QualityAnalyzeIssueListRequest request) {
        return qualityAnalyzeService.analyzeIssueList(request);
    }

    @GetMapping("/result")
    public ThemisResponse<QualityAnalyzeResultResponse> analyzeResult(Long buildId) {
        return qualityAnalyzeService.analyzeResult(buildId);
    }

    @GetMapping("/default/setting")
    public ThemisResponse<DefaultSettingResponse> getDefaultSettings(String repoUrl) {
        if (StringUtils.isEmpty(repoUrl)) {
            return ThemisResponse.fail(ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(),
                    "repoUrl" + ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getMessage());
        }
        boolean defaultProject = false;
        Map<Integer, String> typeAndLevelMap = Maps.newHashMap();
        if (qualityAnalyzeService.defaultProject(repoUrl)) {
            defaultProject = true;
            typeAndLevelMap = qualityAnalyzeService.getDefaultCheckConfig(repoUrl);
        }
        return ThemisResponse.success(DefaultSettingResponse.builder().hitProject(defaultProject)
                .typeAndLevelConfig(typeAndLevelMap).build());
    }

    @PostMapping("/dependency/report")
    @NeedAuth
    public void reportDependencyAnalyze(@RequestBody ReportDependencyAnalyzeRequest reportRequest) {
        String redisKey = KsRedisPrefixConstant.LOCAL_ANALYZE_SPIN_LOCK_PREFIX + reportRequest.getTaskId();
        ksRedisLock.spinLock(redisKey, TimeUnit.SECONDS.toMillis(60));
        log.info("receive dependency report request, is {}", JSONUtils.serialize(reportRequest));
        try {
            qualityAnalyzeService.reportDependencyAnalyze(reportRequest);
        } catch (Exception e) {
            log.error("local dependency report error! request is {}", JSONUtils.serialize(reportRequest), e);
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    @GetMapping("/dependency/checkInfo")
    public ThemisResponse<CheckInfoResponse> checkRepoInfo(String repoUrl) {
        log.info("receive checkInfo request, repoUrl is {}", repoUrl);
        try {
            return ThemisResponse.success(qualityAnalyzeService.checkRepoInfo(repoUrl));
        } catch (Exception e) {
            log.error("checkRepoInfo error, requestUrl is {}", repoUrl);
            return ThemisResponse.success(CheckInfoResponse.builder().checkUnusedDependencyAnalyze(false).build());
        }
    }

    @GetMapping("/dependency/config/detail")
    public ThemisResponse<DependencyConfigResponse> dependencyConfig(String repoUrl) {
        return qualityAnalyzeService.dependencyConfig(repoUrl);
    }

    @SuppressWarnings("rawtypes")
    @GetMapping("/dependency/config/add")
    public ThemisResponse addDependencyConfig(AddDependencyConfigRequest request) {
        try {
            return qualityAnalyzeService.addDependencyConfig(request);
        } catch (DuplicateKeyException e) {
            return ThemisResponse.fail(ResultCodeConstant.DUPLICATE_KEY_ERROR);
        } catch (Exception e) {
            return ThemisResponse.fail(ResultCodeConstant.ADD_WHITE_LIST_ERROR);
        }
    }

}
