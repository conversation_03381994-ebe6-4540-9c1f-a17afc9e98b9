package com.kuaishou.serveree.themis.api.controller.platform.v2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.service.platform.PlatformIssueService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.NumberUtils;
import com.kuaishou.serveree.themis.component.vo.request.IssueCycleComplexityRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueDuplicationRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueListRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionBpmRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.response.IssueCycleComplexityResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueDuplicationResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SonarIssueVo;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/17 3:40 下午C
 */
@Slf4j
@RestController
@RequestMapping("/v2/platform/issue")
public class QualityPlatformIssueV2Controller {

    @Autowired
    private PlatformIssueService platformIssueService;

    @Autowired
    private PlatformPermissionService platformPermissionService;


    @PostMapping("duplication")
    public ThemisResponse<IssueDuplicationResponse> duplication(@RequestBody IssueDuplicationRequest request) {
        return ThemisResponse.success(platformIssueService.duplication(request, SsoUserInfo.getUserName()));
    }

    @PostMapping("cycleComplexity")
    public ThemisResponse<IssueCycleComplexityResponse> cycleComplexity(@RequestBody
            IssueCycleComplexityRequest request) {
        return ThemisResponse.success(platformIssueService.cycleComplexity(request, SsoUserInfo.getUserName()));
    }

    @PostMapping("list")
    public ThemisResponse<IssueListResponse> list(@RequestBody IssueListRequest request) {
        return ThemisResponse.success(platformIssueService.list(request));
    }

    /**
     * issue打标 走bpm审批
     * 前端调用
     */
    @PostMapping("transition/bpm/apply")
    public ThemisResponse<Void> issueTransitionByBpm(@RequestBody @Validated IssueTransitionBpmRequest request) {
        log.info("issue transition by bpm, request is : {}", JSONUtils.serialize(request));
        platformIssueService.issueTransitionByBpm(request, SsoUserInfo.getUserName());
        return ThemisResponse.success(null);
    }

    @PostMapping("transition")
    public ThemisResponse<SonarIssueVo> transition(@RequestBody IssueTransitionRequest request) {
        // 参数校验
        Assert.notNull(request.getGitProjectId(), () -> new ThemisException(
                ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "gitProjectId参数不能为空"));
        Assert.notNull(request.getGitBranch(), () -> new ThemisException(
                ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "branch参数不能为空"));
        Assert.isTrue(NumberUtils.isPositive(request.getIssueId())
                || StringUtils.isNotBlank(request.getIssueUniqId()), () -> new ThemisException(
                ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "issueId和issueUniqId不能同时为空"));
        Assert.notNull(request.getTransition(), () -> new ThemisException(
                ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "transition不能为空"));
        // 权限检查
        platformPermissionService.checkKdevGitPermission(request.getGitProjectId(),  SsoUserInfo.getUserName());
        return ThemisResponse.success(platformIssueService.transition(request, SsoUserInfo.getUserName()));
    }

}
