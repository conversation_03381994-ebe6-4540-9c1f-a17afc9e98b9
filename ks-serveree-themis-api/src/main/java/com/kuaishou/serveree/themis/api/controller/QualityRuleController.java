package com.kuaishou.serveree.themis.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.QualityRuleService;
import com.kuaishou.serveree.themis.component.vo.response.QualityRuleResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/7/29 2:38 下午
 */
@Slf4j
@RestController
@RequestMapping("rule")
public class QualityRuleController {

    @Autowired
    private QualityRuleService qualityRuleService;

    @NeedAuth
    @GetMapping("list")
    public ThemisResponse<QualityRuleResponse> ruleList(int type) {
        return ThemisResponse.success(new QualityRuleResponse(qualityRuleService.listQualityRules(type)));
    }

}
