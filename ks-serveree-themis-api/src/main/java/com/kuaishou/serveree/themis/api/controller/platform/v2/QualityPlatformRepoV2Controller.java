package com.kuaishou.serveree.themis.api.controller.platform.v2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoSettingService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoMeasuresHistoryRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoMeasuresOverviewRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoScanHistoryRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSearchRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSettingUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSettingsRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSkyeyeDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoStarRequest;
import com.kuaishou.serveree.themis.component.vo.response.RepoCreateResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoInfoResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoLanguageInfoResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoMeasuresHistoryResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoMeasuresOverviewResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoProfileUpdateResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoScanHistoryResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSearchResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSettingResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSettingUpdateResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSettingsResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSkyeyeDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoStarResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/23 5:50 下午
 */
@Slf4j
@RestController
@RequestMapping("/v2/platform/repo")
public class QualityPlatformRepoV2Controller {

    @Autowired
    private PlatformRepoService platformRepoService;
    @Autowired
    private PlatformRepoSettingService platformRepoSettingService;


    @Autowired
    private KsRedisLock ksRedisLock;


    @PostMapping("create")
    public ThemisResponse<RepoCreateResponse> create(@RequestBody RepoCreateRequest searchRequest) {
        final String redisKey = KsRedisPrefixConstant.PLATFORM_REPO_CREATE_LOCK + searchRequest.getGitProjectId();
        try {
            ksRedisLock.spinLock(redisKey);
            return ThemisResponse.success(platformRepoService.create(searchRequest, SsoUserInfo.getUserName()));
        } catch (Exception e) {
            log.error("repo create error searchRequest is {}", JSONUtils.serialize(searchRequest), e);
            throw e;
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    @PostMapping("/update")
    public ThemisResponse<RepoCreateResponse> update(@RequestBody RepoCreateRequest searchRequest) {
        final String redisKey = KsRedisPrefixConstant.PLATFORM_REPO_UPDATE_LOCK + searchRequest.getGitProjectId();
        try {
            ksRedisLock.spinLock(redisKey);
            return ThemisResponse.success(platformRepoService.update(searchRequest, SsoUserInfo.getUserName()));
        } catch (Exception e) {
            log.error("repo update error searchRequest is {}", JSONUtils.serialize(searchRequest), e);
            throw e;
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    @PostMapping("/delete")
    public ThemisResponse<RepoCreateResponse> delete(@RequestBody RepoCreateRequest searchRequest) {
        final String redisKey = KsRedisPrefixConstant.PLATFORM_REPO_DELETE_LOCK + searchRequest.getGitProjectId();
        try {
            ksRedisLock.spinLock(redisKey);
            return ThemisResponse.success(platformRepoService.delete(searchRequest, SsoUserInfo.getUserName()));
        } catch (Exception e) {
            log.error("repo delete error searchRequest is {}", JSONUtils.serialize(searchRequest), e);
            throw e;
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    @PostMapping("profile/update")
    public ThemisResponse<RepoProfileUpdateResponse> profileUpdate(
            @RequestBody RepoProfileUpdateRequest updateRequest) {
        final String redisKey = KsRedisPrefixConstant.PLATFORM_REPO_CREATE_LOCK + updateRequest.getGitProjectId();
        try {
            ksRedisLock.spinLock(redisKey);
            return ThemisResponse.success(platformRepoService.updateProfile(updateRequest, SsoUserInfo.getUserName()));
        } catch (Exception e) {
            log.error("repo profileUpdate error updateRequest is {}", JSONUtils.serialize(updateRequest), e);
            throw new ThemisException(-1, "更新出现异常" + e.getMessage());
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    @PostMapping("search")
    public ThemisResponse<RepoSearchResponse> search(@RequestBody RepoSearchRequest searchRequest) {
        return ThemisResponse.success(platformRepoService.search(searchRequest, SsoUserInfo.getUserName()));
    }

    @PostMapping("detail")
    public ThemisResponse<RepoDetailResponse> detail(@RequestBody RepoDetailRequest searchRequest) {
        return ThemisResponse.success(platformRepoService.detail(searchRequest, SsoUserInfo.getUserName()));
    }

    @PostMapping("star")
    public ThemisResponse<RepoStarResponse> star(@RequestBody RepoStarRequest searchRequest) {
        return ThemisResponse.success(platformRepoService.star(searchRequest, SsoUserInfo.getUserName()));
    }

    /**
     * 给扫描器
     */
    @PostMapping("settings")
    public ThemisResponse<RepoSettingsResponse> settings(@RequestBody RepoSettingsRequest request) {
        return ThemisResponse.success(platformRepoService.settings(request));
    }

    @PostMapping("setting/update")
    public ThemisResponse<RepoSettingUpdateResponse> updateRepoSetting(@RequestBody RepoSettingUpdateRequest request) {
        final String redisKey = KsRedisPrefixConstant.PLATFORM_REPO_UPDATE_LOCK + request.getGitProjectId();
        try {
            ksRedisLock.spinLock(redisKey);
            return ThemisResponse.success(platformRepoSettingService.updateRepoSetting(request, SsoUserInfo.getUserName()));
        } catch (Exception e) {
            log.error("repo setting update error, updateRequest is {}", JSONUtils.serialize(request), e);
            throw e;
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    /**
     * 给扫描平台前端
     */
    @PostMapping("setting")
    public ThemisResponse<RepoSettingResponse> getRepoSetting(@RequestBody RepoDetailRequest request) {
        return ThemisResponse.success(platformRepoSettingService.getRepoSetting(request, SsoUserInfo.getUserName()));
    }

    @GetMapping("info")
    public ThemisResponse<RepoInfoResponse> info(Integer gitProjectId, String branch) {
        return ThemisResponse.success(platformRepoService.info(gitProjectId, branch));
    }

    @PostMapping("skyeye/detail")
    public ThemisResponse<RepoSkyeyeDetailResponse> skyeyeDetail(@RequestBody RepoSkyeyeDetailRequest request) {
        return ThemisResponse.success(platformRepoService.skyeyeDetail(request, SsoUserInfo.getUserName()));
    }

    @PostMapping("measures/history")
    public ThemisResponse<RepoMeasuresHistoryResponse> measuresHistory(
            @RequestBody RepoMeasuresHistoryRequest request) {
        return ThemisResponse.success(platformRepoService.measuresHistory(request, SsoUserInfo.getUserName()));
    }

    @PostMapping("measures/overview")
    public ThemisResponse<RepoMeasuresOverviewResponse> measuresOverview(
            @RequestBody RepoMeasuresOverviewRequest request) {
        return ThemisResponse.success(platformRepoService.measuresOverview(request, SsoUserInfo.getUserName()));
    }

    @PostMapping("scan/history")
    public ThemisResponse<RepoScanHistoryResponse> scanHistory(@RequestBody RepoScanHistoryRequest request) {
        return ThemisResponse.success(platformRepoService.scanHistory(request, SsoUserInfo.getUserName()));
    }

    @PostMapping("/language")
    public ThemisResponse<RepoLanguageInfoResponse> repoBranchLanguageInfo(@RequestBody RepoDetailRequest request) {
        return ThemisResponse.success(platformRepoSettingService.getLanguageInfo(request, SsoUserInfo.getUserName()));
    }
}
