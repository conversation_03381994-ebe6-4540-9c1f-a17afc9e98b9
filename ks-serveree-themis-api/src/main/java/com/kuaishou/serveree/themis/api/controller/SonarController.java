package com.kuaishou.serveree.themis.api.controller;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.framework.util.PerfUtils;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.entity.sonar.HookPayload;
import com.kuaishou.serveree.themis.component.entity.sonar.HookProperties;
import com.kuaishou.serveree.themis.component.entity.sonar.Project;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MetricComponentRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MetricComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MetricIssueRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.gitMetricRequest.IssuesRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.gitMetricRequest.MeasureRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.gitMetricRequest.MeasureTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.IssuesResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentTreeResp;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.sonar.SonarProjectService;
import com.kuaishou.serveree.themis.component.service.sonar.SonarService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.PluginScanStartRequest;
import com.kuaishou.serveree.themis.component.vo.request.SettingsValidateRequest;
import com.kuaishou.serveree.themis.component.vo.request.SonarProjectCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.SonarStuckPointRequest;
import com.kuaishou.serveree.themis.component.vo.request.sonar.PluginPipelineReportRequest;
import com.kuaishou.serveree.themis.component.vo.response.PipelineReportResponse;
import com.kuaishou.serveree.themis.component.vo.response.PluginScanStartResponse;
import com.kuaishou.serveree.themis.component.vo.response.SonarStuckPointResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.NeedCompileResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.ParallelExecuteResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SettingsValidateResponse;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-05-26
 */
@RestController
@RequestMapping("sonar")
@Slf4j
public class SonarController {

    @Autowired
    private SonarProjectService sonarProjectService;

    @Autowired
    private SonarService sonarService;

    @Autowired
    private KsRedisLock ksRedisLock;
    @Resource
    private PCheckBaseService pCheckBaseService;

    @NeedAuth
    @PostMapping("/project/create")
    public ThemisResponse<?> createSonarProject(@RequestBody @Valid SonarProjectCreateRequest sponsorRequest) {
        Project sonarProject = sonarProjectService.createSonarProject(sponsorRequest);
        return ThemisResponse.success(sonarProject.getKey());
    }

    @PostMapping(value = "/global_hook")
    public void globalHook(@RequestBody HookPayload payload) {
        log.info("receive sonar global hook,paylood is {}", JSONUtils.serialize(payload));
        sonarService.hook(payload, null, true);
    }

    @PostMapping(value = "/hook")
    public void projectHook(@RequestBody HookPayload payload) {
        sonarService.hook(payload, null, false);
    }

    @PostMapping(value = "/custom/hook")
    public void customHook(@RequestBody HookPayload payload) {
        sonarService.customHook(payload);
    }

    @GetMapping("/measure/component")
    public ThemisResponse<MeasureComponentResp> measureComponent(MeasureComponentRequest request) {
        MeasureComponentResp resp = sonarService.measureComponent(request);
        return ThemisResponse.success(resp);
    }

    @PostMapping(value = "parser")
    public void checkParser(@RequestBody HookPayload payload) {
        sonarService.checkParser(payload);
    }

    @PostMapping(value = "/process/hook")
    public void processCheckHook(@RequestBody HookPayload payload) {
        String key = payload.getProject().getKey();
        String redisKey = KsRedisPrefixConstant.SONAR_PROCESS_HOOK_LOCK_PREFIX + key;
        HookProperties properties = payload.getProperties();
        if (properties != null) {
            redisKey += ":" + properties.getBranch();
        }
        try {
            ksRedisLock.spinLock(redisKey);
            sonarService.processHook(payload);
        } catch (Exception e) {
            log.error("processCheckHook error", e);
            throw new RuntimeException(e);
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    @PostMapping(value = "/plugin/stuck/point/result")
    public ThemisResponse<SonarStuckPointResponse> pluginStuckPoint(@RequestBody SonarStuckPointRequest request) {
        return sonarService.pluginStuckPoint(request);
    }

    @PostMapping(value = "/plugin/scan/start")
    public ThemisResponse<PluginScanStartResponse> pluginScanStart(@RequestBody @Validated PluginScanStartRequest request) {
        return ThemisResponse.success(sonarService.pluginScanStart(request));
    }

    @PostMapping("/plugin/stuck/point/hook")
    public void stuckPointHook(@RequestBody HookPayload payload) {
        sonarService.stuckPointHook(payload);
    }

    @PostMapping("/sonarMeasures")
    public MeasureComponentResp sonarMeasures(@RequestBody MeasureComponentRequest request) {
        return sonarService.sonarMeasures(request);
    }

    @PostMapping("/measure/tree")
    public MeasureComponentTreeResp sonarMeasureTree(@RequestBody MeasureComponentTreeRequest request) {
        return sonarService.sonarMeasureTree(request);
    }

    @PostMapping("/metric/measures")
    public MeasureComponentResp metricSonarMeasures(@RequestBody MeasureRequest request) {
        MetricComponentRequest req = new MetricComponentRequest();
        req.setGitProjectId(request.getGitProjectId());
        req.setMeasureComponent(request.getComponentRequest());
        return sonarService.metricMeasures(req);
    }

    @PostMapping("/metric/measuresTree")
    public MeasureComponentTreeResp metricSonarMeasureTree(@RequestBody MeasureTreeRequest request) {
        MetricComponentTreeRequest req = new MetricComponentTreeRequest();
        req.setGitProjectId(request.getGitProjectId());
        req.setMeasureComponentTree(request.getTreeRequest());
        return sonarService.metricMeasuresTree(req);
    }

    @PostMapping("/metric/issue/search")
    public IssuesResp metricIssueSearch(@RequestBody IssuesRequest request) {
        MetricIssueRequest req = new MetricIssueRequest();
        req.setGitProjectId(request.getGitProjectId());
        req.setSearchRequest(request.getTotalIssueRequest());
        return sonarService.sonarSearchIssues(req);
    }

    @PostMapping("/settings/validate")
    public ThemisResponse<SettingsValidateResponse> settingsValidate(@RequestBody SettingsValidateRequest request) {
        return sonarService.settingsValidate(request);
    }

    @GetMapping("/local/parallelScanCount")
    public ThemisResponse<Integer> localParallelScanCount(Integer gitProjectId) {
        return sonarService.getLocalParallelScanCount(gitProjectId);
    }

    @GetMapping("/needCompile")
    public ThemisResponse<NeedCompileResponse> needCompile(Long kspBuildId) {
        return sonarService.needCompile(kspBuildId);
    }

    @GetMapping("/project/canProcessParallelExecute")
    public ThemisResponse<ParallelExecuteResponse> canProcessParallelExecute(Integer gitProjectId) {
        return ThemisResponse.success(sonarService.canProcessParallelExecute(gitProjectId));
    }

    @PostMapping("/pipeline/report")
    public ThemisResponse<PipelineReportResponse> pipelineReport(@RequestBody PluginPipelineReportRequest request) {
        log.info("[MavenScannerNew]流水线扫描结果上报，request: {}", request);
        long start = System.currentTimeMillis();
        String redisKey = KsRedisPrefixConstant.SONAR_PROCESS_HOOK_LOCK_PREFIX + request.getGitProjectId() + ":"
                + request.getGitBranch();
        try {
            ksRedisLock.spinLock(redisKey);
            PipelineReportResponse resp = sonarService.pipelineReport(request);
            PerfUtils.perf("serveree.themis.scan_report_cost", "sonar_pipeline")
                    .millis(System.currentTimeMillis() - start).logstash();
            return ThemisResponse.success(resp);
        } catch (Exception e) {
            // 记录执行失败
            pCheckBaseService.updateBaseStatusByKspBuildId(request.getKspBuildId(), TaskStatusEnum.FAIL);
            log.error("sonar pipeline report error, request is: {}", JSONUtils.serialize(request), e);
            throw new RuntimeException(e);
        } finally {
            ksRedisLock.unlock(redisKey);
            log.info("sonar pipeline report, time cost : {} ms", System.currentTimeMillis() - start);
        }
    }

}
