package com.kuaishou.serveree.themis.api.controller.platform.v2;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.service.openapi.IhrOpenApi.IhrUserInfo;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCommonService;
import com.kuaishou.serveree.themis.component.vo.request.GitBranchListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.GitRepoSearchRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.LabelListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.LanguageListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ScannerListRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.CreateLanguageListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.GitBranchListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.GitProjectListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.GitRepoSearchResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.LabelListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.LanguageListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ScannerListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/4/7 5:33 下午
 */
@Slf4j
@RestController
@RequestMapping("/v2/platform/common")
public class QualityPlatformCommonV2Controller {

    @Autowired
    private PlatformCommonService platformCommonService;

    @GetMapping("/language/list")
    public ThemisResponse<LanguageListResponseVo> languageList(LanguageListRequestVo requestVo) {
        return ThemisResponse.success(platformCommonService.languageList(requestVo));
    }

    @GetMapping("/label/list")
    public ThemisResponse<LabelListResponseVo> labelList(LabelListRequestVo requestVo) {
        return ThemisResponse.success(platformCommonService.labelList(requestVo));
    }

    @GetMapping("/scanner/list")
    public ThemisResponse<ScannerListResponseVo> scannerList(ScannerListRequestVo requestVo) {
        return ThemisResponse.success(platformCommonService.scannerList(requestVo));
    }

    @GetMapping("/gitRepo/search")
    public ThemisResponse<GitRepoSearchResponseVo> gitRepoSearch(GitRepoSearchRequestVo requestVo) {
        return ThemisResponse.success(platformCommonService.gitRepoSearch(requestVo, SsoUserInfo.getUserName()));
    }

    @GetMapping("/gitBranch/list")
    public ThemisResponse<GitBranchListResponseVo> gitBranchList(GitBranchListRequestVo requestVo) {
        return ThemisResponse.success(platformCommonService.gitBranchList(requestVo));
    }

    @GetMapping("/grayGitProject/list")
    public ThemisResponse<GitProjectListResponseVo> grayGitProjectList() {
        return ThemisResponse.success(platformCommonService.grayGitProjectList());
    }

    @GetMapping("/repo/create/language/list")
    public ThemisResponse<CreateLanguageListResponseVo> createRepoLanguageList() {
        return ThemisResponse.success(platformCommonService.createRepoLanguageList());
    }

    @GetMapping("/ihr/user/search")
    public ThemisResponse<List<IhrUserInfo>> fuzzySearchUser(@RequestParam("userName") String userName) {
        return ThemisResponse.success(platformCommonService.searchUserByUserName(userName));
    }

}
