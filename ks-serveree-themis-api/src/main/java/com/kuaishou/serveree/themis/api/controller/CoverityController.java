package com.kuaishou.serveree.themis.api.controller;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.platform.CoverityService;
import com.kuaishou.serveree.themis.component.vo.request.coverity.CoverityAgentRegisterReq;
import com.kuaishou.serveree.themis.component.vo.request.coverity.CoverityAgentTaskReport;
import com.kuaishou.serveree.themis.component.vo.request.coverity.CoverityAgentTaskStatusReportReq;
import com.kuaishou.serveree.themis.component.vo.request.coverity.CoverityAnalyseTaskSubmitReq;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.coverity.CoverityAgentStatusVo;
import com.kuaishou.serveree.themis.component.vo.response.coverity.CoverityAgentTaskVo;
import com.kuaishou.serveree.themis.component.vo.response.coverity.CoverityAgentVo;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-07-05
 */
@Slf4j
@RequestMapping("/coverity")
@RestController
public class CoverityController {

    @Resource
    private CoverityService coverityService;

    /**
     * FUNC_DES: 物理机agent注册
     */
    @NeedAuth
    @PostMapping("/agent/register")
    public ThemisResponse<Long> agentRegister(@RequestBody @Validated CoverityAgentRegisterReq req) {
        Long agentId = coverityService.registerAgent(req);
        log.info("[Coverity][Agent]接收到agent注册请求: {}，agentId: {}", req, agentId);
        return ThemisResponse.success(agentId);
    }

    /**
     * FUNC_DES: 物理机agent心跳上报
     */
    @NeedAuth
    @PostMapping("/agent/heartbeat/{agentId}")
    public ThemisResponse<CoverityAgentVo> agentHeartbeat(@PathVariable("agentId") long agentId) {
        log.info("[Coverity][Agent]接收到agent心跳信号,agentId: {}", agentId);
        return ThemisResponse.success(coverityService.agentHeartbeat(agentId));
    }

    /**
     * FUNC_DES: 流水线cov-build后，提交任务到物理机执行cov-analyse
     */
    @NeedAuth
    @PostMapping("/pipeline/task/analyse/submit")
    public ThemisResponse<Long> pipelineTaskSubmit(@RequestBody @Validated CoverityAnalyseTaskSubmitReq req) {
        Long taskId = coverityService.submitCovAnalyseTask(req);
        log.info("[Coverity][Pipeline]接收到cov-analyse任务提交: {}, taskId: {}", req, taskId);
        return ThemisResponse.success(taskId);
    }

    /**
     * FUNC_DES: 物理机agent抢占任务
     */
    @NeedAuth
    @GetMapping("/agent/task/fetch")
    public ThemisResponse<CoverityAgentTaskVo> fetchAgentTask(@RequestParam("agentId") long agentId) {
        CoverityAgentTaskVo taskVo = coverityService.fetchAgentTask(agentId);
        log.info("[Coverity][Agent]接收到agent抢占任务请求: agentId: {}, 返回值: {}", agentId, taskVo);
        return ThemisResponse.success(taskVo);
    }

    /**
     * FUNC_DES: 物理机agent-task状态上报，返回 任务是否继续执行
     */
    @NeedAuth
    @PostMapping("/agent/task/heartbeat")
    public ThemisResponse<Boolean> agentTaskHeartbeat(@RequestBody @Validated CoverityAgentTaskStatusReportReq req) {
        log.info("[Coverity][Agent]接收到agentTask心跳信号: {}", req);
        return ThemisResponse.success(coverityService.agentTaskHeartbeat(req));
    }

    /**
     * FUNC_DES: 任务执行成功上报结果
     */
    @NeedAuth
    @PostMapping("/agent/task/report/{taskId}")
    public ThemisResponse<Boolean> agentTaskReport(@PathVariable long taskId,
            @RequestBody @Validated CoverityAgentTaskReport report) {
        log.info("[Coverity][Agent]接收到agentTask结果上报, taskId: {}, req : {}", taskId, report);
        return ThemisResponse.success(coverityService.agentTaskReport(taskId, report));
    }

    /**
     * FUNC_DES: 重试某个task（重置任务状态）
     */
    @NeedAuth
    @PostMapping("/agent/task/retry/{taskId}")
    public ThemisResponse<Boolean> agentTaskRetry(@PathVariable("taskId") long taskId) {
        log.info("[Coverity][Agent]接收到agentTask重试请求，taskId: {}", taskId);
        return ThemisResponse.success(coverityService.resetAgentTask(taskId));
    }

    /**
     * FUNC_DES: 获取物理机agent及其任务状态
     */
    @GetMapping("/agent/status/{agentId}")
    public ThemisResponse<CoverityAgentStatusVo> getCoverityAgentStatus(@PathVariable("agentId") long agentId) {
        CoverityAgentStatusVo statusVo = coverityService.getCoverityAgentStatus(agentId);
        log.info("[Coverity][Agent]接收到agent状态请求: agentId: {}, 返回值: {}", agentId, statusVo);
        return ThemisResponse.success(statusVo);
    }
}
