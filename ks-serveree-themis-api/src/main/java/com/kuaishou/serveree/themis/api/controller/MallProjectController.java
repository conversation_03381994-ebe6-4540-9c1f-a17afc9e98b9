package com.kuaishou.serveree.themis.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.ProjectService;
import com.kuaishou.serveree.themis.component.vo.request.ProjectTeamRequest;
import com.kuaishou.serveree.themis.component.vo.response.ProjectTeamResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/1/5 10:43 上午
 * 为电商团队定制化开发
 */
@Slf4j
@RestController
@RequestMapping("project")
public class MallProjectController {

    @Autowired
    private ProjectService projectService;

    @PostMapping("/teaminfo")
    public ThemisResponse<ProjectTeamResponse> projectTeamBatchInfo(@RequestBody ProjectTeamRequest projectTeamRequest) {
        return ThemisResponse.success(projectService.projectTeamBatchInfo(projectTeamRequest));
    }

}
