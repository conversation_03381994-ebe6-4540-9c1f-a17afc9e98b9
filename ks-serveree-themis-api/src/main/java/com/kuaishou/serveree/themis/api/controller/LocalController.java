package com.kuaishou.serveree.themis.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.framework.scope.TraceContextUtil;
import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.serveree.themis.component.entity.kafka.ScanIssueSummaryNotify;
import com.kuaishou.serveree.themis.component.service.LocalAnalysisService;
import com.kuaishou.serveree.themis.component.service.LocalService;
import com.kuaishou.serveree.themis.component.service.kafka.PlatformTeamIssueBuildService;
import com.kuaishou.serveree.themis.component.vo.request.ProjectBatchInitRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProjectGroupProfileBatchUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProjectProfileBatchUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.SkyeyeProfileInitRequest;
import com.kuaishou.serveree.themis.component.vo.request.SonarProfileInitRequest;
import com.kuaishou.serveree.themis.component.vo.request.UpdateLanguageRequest;
import com.kuaishou.serveree.themis.component.vo.response.ProjectGroupProfileBatchUpdateResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/2/3 2:43 下午
 */
@Slf4j
@RestController
@RequestMapping("local")
public class LocalController {

    @Autowired
    private LocalService localService;

    @Autowired
    private LocalAnalysisService localAnalysisService;

    @Autowired
    private PlatformTeamIssueBuildService platformTeamIssueBuildService;

    @GetMapping("/analyze/rule/del/cache")
    public ThemisResponse<Boolean> delCache() {
        return localService.delAnalyzeRuleCache();
    }

    @GetMapping("/analyze/dependency/common/cache/del")
    public ThemisResponse<Long> delDependencyAnalyzeCommonCache() {
        return localService.delDependencyAnalyzeCommonCache();
    }

    @GetMapping("/dependency/data/perf")
    public ThemisResponse<Boolean> perfLocalDependencyData() {
        localAnalysisService.perfLocalDependencyData();
        return ThemisResponse.success(true);
    }

    @GetMapping("/check/repo/update/projectId")
    public ThemisResponse<Integer> updateProjectId() {
        return localService.updateProjectId();
    }

    @GetMapping("/check/repo/update/linkUrl")
    public ThemisResponse<Integer> updateLinkUrl() {
        return localService.updateLinkUrl();
    }

    @GetMapping("/platform/skyeye/init")
    public ThemisResponse<Integer> skyeyeRuleInit() {
        return localService.skyeyeInit();
    }

    @GetMapping("/platform/skyeye/profile/init")
    public ThemisResponse<Integer> skyeyeProfileInit() {
        return localService.skyeyeDefaultProfile();
    }

    @PostMapping("/platform/skyeye/profileAndRule/init")
    public ThemisResponse<Integer> skyeyeProfileAndRuleInit(@RequestBody SkyeyeProfileInitRequest initRequest) {
        return localService.skyeyeProfileAndRuleInit(initRequest);
    }

    @GetMapping("/platform/sonar/rule/init")
    public ThemisResponse<Integer> sonarRuleInit(String profileName, String scanner, String language) {
        return localService.sonarRuleInit(profileName, scanner, language);
    }

    @GetMapping("/platform/sonar/rule/sync")
    public ThemisResponse<Integer> sonarRuleSync() {
        return localService.sonarRuleSync();
    }

    @GetMapping("/platform/sonar/label/init")
    public ThemisResponse<Integer> sonarLabelInit(String profileName) {
        return localService.sonarLabelInit(profileName);
    }

    @PostMapping("/platform/sonar/profile/init")
    public ThemisResponse<Integer> sonarProfileInit(@RequestBody SonarProfileInitRequest initRequest) {
        return localService.sonarProfileInit(initRequest);
    }

    @GetMapping("/platform/issue/clean")
    public ThemisResponse<Integer> issueClean() {
        return localService.issueClean();
    }

    @PostMapping("/platform/project/batch/init")
    public ThemisResponse<Integer> platformBatchInitProject(@RequestBody ProjectBatchInitRequest initRequest) {
        return localService.platformBatchInitProject(initRequest);
    }

    @PostMapping("/platform/project/group/profile/batch/update")
    public ThemisResponse<ProjectGroupProfileBatchUpdateResponse> platformProjectGroupBatchUpdateProfile(
            @RequestBody ProjectGroupProfileBatchUpdateRequest request) {
        return localService.platformBatchUpdateProjectGroupProfile(request);
    }

    @PostMapping("/platform/project/profile/batch/update")
    public ThemisResponse<Integer> platformRepoBatchUpdateProfile(@RequestBody
    ProjectProfileBatchUpdateRequest updateRequest) {
        return localService.platformBatchUpdateProjectProfile(updateRequest);
    }

    @GetMapping("/sonar/group/fix")
    public ThemisResponse<Integer> fixSonarGroup() {
        return localService.fixSonarGroup();
    }

    @GetMapping("/kem/java/transfer")
    public ThemisResponse<Integer> transferKemJava(String projectIds) {
        return localService.transferKemJava(projectIds);
    }

    @GetMapping("/kem/java/issue/fix")
    public ThemisResponse<Integer> fixIssueStatus() {
        return localService.fixIssueStatus();
    }

    @GetMapping("/platform/fix/executionReferType")
    public ThemisResponse<Integer> executionReferTypeFix(String source, Integer executionReferType) {
        return localService.executionReferTypeFix(source, executionReferType);
    }

    @GetMapping("/platform/notice/init")
    public ThemisResponse<Integer> platformNoticeInit() {
        return localService.platformNoticeInit();
    }

    @GetMapping("/kformat/rule/init")
    public ThemisResponse<Integer> kformatRuleInit() {
        return localService.kformatRuleInit();
    }

    @GetMapping("/plugin/sonar/issue/link/fix")
    public ThemisResponse<Integer> pluginSonarIssueLinkFix() {
        return localService.pluginSonarIssueLinkFix();
    }

    @GetMapping("/plugin/java/summary/issue/link/fix")
    public ThemisResponse<Integer> javaSummaryIssueLinkFix() {
        return localService.javaSummaryIssueLinkFix();
    }

    @GetMapping("/plugin/java/summary/issue/change/resolve")
    public ThemisResponse<Integer> javaSummaryIssueChangeResolve() {
        return localService.javaSummaryIssueChangeResolve();
    }

    @GetMapping("/sonar/java/setting/sync")
    public ThemisResponse<Integer> sonarJavaSettingSync() {
        return localService.sonarJavaSettingSync();
    }

    @GetMapping("/platform/fix/repoId")
    public ThemisResponse<Integer> platformFixRepoId() {
        return localService.platformFixRepoId();
    }

    @GetMapping("/platform/fix/history/issueSeverity")
    public ThemisResponse<Integer> platformFixHistoryIssueSeverity(String profile, String rule, String severity) {
        return localService.platformFixHistoryIssueSeverity(profile, rule, severity);
    }

    @GetMapping("/platform/delete/duplicateBranch")
    public ThemisResponse<Integer> duplicateBranch() {
        return localService.platformFixRepoBranchId();
    }

    @GetMapping("/issue/summary/fillup/commonUniqId")
    public ThemisResponse<Integer> fillUpCommonUniqId() {
        return localService.fillUpCommonUniqId();
    }

    @GetMapping("/group/panel/data")
    public ThemisResponse<Integer> oneMonthAgoGroupPanelData() {
        return localService.oneMonthAgoGroupPanelData();
    }

    @PostMapping("/team/build/issue")
    public ThemisResponse<String> buildTeamIssue(@RequestBody ScanIssueSummaryNotify notify) {
        platformTeamIssueBuildService.buildTeamIssue(notify);
        return ThemisResponse.success("success");
    }

    @GetMapping("/platform/issue/writeSummaryBase")
    public ThemisResponse<Integer> writeIssueSummaryBase() {
        return localService.writeIssueSummaryBase();
    }

    @PostMapping("/platform/repo/updateLanguage")
    public ThemisResponse<Integer> updateLanguage(@RequestBody UpdateLanguageRequest request) {
        return localService.updateLanguage(request);
    }

    @PostMapping("/platform/repo/compareDiffJdkVersion")
    public ThemisResponse<List<Integer>> compareDiffJdkVersion(@RequestBody UpdateLanguageRequest request) {
        return localService.compareDiffJdkVersion(request);
    }

    @GetMapping("/issue/copyAndChangeColume")
    public ThemisResponse<Integer> copyAndChangeColumn() {
        return localService.copyAndChangeColumn();
    }

    @GetMapping("/issue/swapColumnValues")
    public ThemisResponse<Integer> swapColumnValues() {
        return localService.swapColumnValues();
    }

    @GetMapping("/send/kafka/msg")
    public ThemisResponse<String> sendPrtMsg(@RequestParam("topic") String topic, @RequestParam("msg") String msg) {
        KafkaProducers.sendString(topic, msg);
        return ThemisResponse.success(TraceContextUtil.getLaneId());
    }

    @PutMapping("/batch/update/profile/ruleCount")
    public ThemisResponse<Boolean> batchUpdateCheckProfileRuleCount() {
        return ThemisResponse.success(localService.batchUpdateCheckProfileRuleCount());
    }

    @PutMapping("/rule/skip")
    public ThemisResponse<Boolean> updateRuleSkipControl(String ruleKey, Boolean canSkip) {
        return ThemisResponse.success(localService.updateRuleSkipAndClearCache(ruleKey, canSkip));
    }

    @PutMapping("/project/coverity/scanner/profile/pipeline/init")
    public ThemisResponse<Object> initCoverityScannerPipelineProfile() {
        return ThemisResponse.success(localService.initCoverityScannerPipelineProfile());
    }
}
