package com.kuaishou.serveree.themis.api.controller.platform;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoService;
import com.kuaishou.serveree.themis.component.vo.request.RepoLatestCommitRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoMeasuresSearchRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSettingsRequest;
import com.kuaishou.serveree.themis.component.vo.response.RepoLatestCommitResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoMeasuresSearchResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSettingsResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/23 5:50 下午
 */
@Slf4j
@RestController
@RequestMapping("platform/repo")
public class QualityPlatformRepoController {

    @Autowired
    private PlatformRepoService platformRepoService;

    @PostMapping("measures/search")
    @NeedAuth
    public ThemisResponse<RepoMeasuresSearchResponse> measuresSearch(
            @RequestBody RepoMeasuresSearchRequest searchRequest) {
        return ThemisResponse.success(platformRepoService.measuresSearch(searchRequest));
    }

    @PostMapping("latestCommit")
    @NeedAuth
    public ThemisResponse<RepoLatestCommitResponse> latestCommit(
            @RequestBody RepoLatestCommitRequest latestCommitRequest) {
        return ThemisResponse.success(platformRepoService.latestCommit(latestCommitRequest));
    }

    /**
     * 给扫描器端
     */
    @PostMapping("settings")
    public ThemisResponse<RepoSettingsResponse> settings(@RequestBody RepoSettingsRequest request) {
        return ThemisResponse.success(platformRepoService.settings(request));
    }

}
