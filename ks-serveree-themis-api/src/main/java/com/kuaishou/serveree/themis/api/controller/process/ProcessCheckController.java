package com.kuaishou.serveree.themis.api.controller.process;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.process.ProcessCheckService;
import com.kuaishou.serveree.themis.component.vo.request.ProcessDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProcessIssueListRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProcessSponsorRequest;
import com.kuaishou.serveree.themis.component.vo.response.ProcessDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.ProcessIssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.ProcessSponsorResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/10/20 8:11 下午
 */
@Slf4j
@RestController
@RequestMapping("process")
public class ProcessCheckController {

    @Autowired
    private ProcessCheckService processCheckService;

    @PostMapping("sponsor")
    @NeedAuth
    public ThemisResponse<ProcessSponsorResponse> sponsor(@RequestBody ProcessSponsorRequest sponsorRequest) {
        ProcessSponsorResponse response = processCheckService.sponsor(sponsorRequest);
        return ThemisResponse.success(response);
    }

    @GetMapping("result/detail")
    public ThemisResponse<ProcessDetailResponse> resultDetail(ProcessDetailRequest detailRequest) {
        ProcessDetailResponse response = processCheckService.resultDetail(detailRequest);
        return ThemisResponse.success(response);
    }

    @GetMapping("result/issue/list")
    public ThemisResponse<ProcessIssueListResponse> issueList(ProcessIssueListRequest issueListRequest) {
        ProcessIssueListResponse response = processCheckService.issueList(issueListRequest);
        return ThemisResponse.success(response);
    }

}
