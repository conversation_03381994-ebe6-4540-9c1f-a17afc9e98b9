package com.kuaishou.serveree.themis.api.controller;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.constant.kdev.MrCheckpointEnum;
import com.kuaishou.serveree.themis.component.service.kdev.MrCheckpointExecutor;
import com.kuaishou.serveree.themis.component.service.kdev.MrStuckPointService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.kdev.KdevPipelineCallbackRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrCheckpointResultRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckGetOrCreateProjectRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckIssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckPointSponsorRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckResultRequest;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.kdev.MrCheckpointResultVo;
import com.kuaishou.serveree.themis.component.vo.response.kdev.MrCheckpointSponsorResponse;
import com.kuaishou.serveree.themis.component.vo.response.kdev.MrStuckIssueTransitionResponse;
import com.kuaishou.serveree.themis.component.vo.response.kdev.MrStuckRepoDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.kdev.MrStuckResultVo;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SonarIssueVo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-16
 *
 * Mr集成代码扫描相关功能Controller
 */
@Slf4j
@RestController
@RequestMapping("/mr/stuck")
public class MrStuckPointController {

    @Resource
    private MrStuckPointService mrStuckPointService;

    @Resource
    private MrCheckpointExecutor mrCheckpointExecutor;

    /**
     * 发起Mr卡点检查（由kdev CR调用）
     */
    @PostMapping("/code/scan/sponsor")
    public ThemisResponse<List<MrCheckpointSponsorResponse>> sponsor(@RequestBody @Validated MrStuckPointSponsorRequest request) {
        log.info("mr stuck point pipeline sponsor, request is : {}", JSONUtils.serialize(request));
        return ThemisResponse.success(mrCheckpointExecutor.executeCheck(request));
    }

    /**
     * 获取指定卡点项、指定文件内的issue（前端调用）
     */
    @PostMapping("/checkpoint/result")
    public ThemisResponse<MrCheckpointResultVo> getCheckpointResult(@RequestBody @Validated
    MrCheckpointResultRequest request) {
        log.info("get mr checkpoint result, request is : {}", JSONUtils.serialize(request));
        return ThemisResponse.success(mrCheckpointExecutor.getCheckpointResult(request));
    }

    /**
     * 获取卡点扫描出的issue（前端调用）
     * @param request
     * @return
     */
    @PostMapping("/code/scan/result")
    public ThemisResponse<MrStuckResultVo> getStuckResult(@RequestBody @Validated MrStuckResultRequest request) {
        log.info("get mr stuck scan result, request is : {}", JSONUtils.serialize(request));
        // 用新逻辑处理
        MrCheckpointResultVo resultData = mrCheckpointExecutor.getCheckpointResult(
                MrCheckpointResultRequest.builder()
                        .gitProjectId(request.getGitProjectId())
                        .mrId(request.getMrId()).commitId(request.getCommitId())
                        .checkpointName(StringUtils.defaultString(request.getCheckpointName(), MrCheckpointEnum.CodeScan.name()))
                        .build()
        );
        // 做一下结构转换
        return ThemisResponse.success(new MrStuckResultVo(
                resultData.getProjectId(),
                resultData.getMrId(),
                resultData.getCommitId(),
                resultData.getIssue().stream().collect(Collectors.groupingBy(SonarIssueVo::getLocation))
        ));
    }

    /**
     * 在kdev配置开启代码扫描卡点，页面展示默认配置，可以点击更改配置跳转到代码扫描平台
     * 其实就是查询项目项目是否存在，如果不存在则常见默认项目
     * 暂时只支持Java项目
     * 前端调用
     */
    @PostMapping("/code/scan/project/init")
    public ThemisResponse<MrStuckRepoDetailResponse> getOrCreateProject(@RequestBody @Validated
    MrStuckGetOrCreateProjectRequest request) {
        return ThemisResponse.success(mrStuckPointService.getOrCreateProject(request));
    }

    /**
     * 卡点流水线执行结束（成功、失败） 回调
     */
    @PostMapping("/code/scan/pipeline/finish/callback")
    public void pipelineExecutingCallback(@RequestBody KdevPipelineCallbackRequest request) {
        log.info("mr stuck point pipeline callback is {}", JSONUtils.serialize(request));
        mrStuckPointService.finishKdevPipelineExecute(request.getPipelineLog());
    }

    /**
     * mr扫描结果页，进行issue打标
     * 前端调用
     */
    @PostMapping("/issue/transition")
    public ThemisResponse<MrStuckIssueTransitionResponse> issueTransition(@RequestBody @Validated
    MrStuckIssueTransitionRequest request) {
        log.info("mr stuck point issue transition, request is : {}", JSONUtils.serialize(request));
        return ThemisResponse.success(mrCheckpointExecutor.issueTransition(request, SsoUserInfo.getUserName()));
    }

}
