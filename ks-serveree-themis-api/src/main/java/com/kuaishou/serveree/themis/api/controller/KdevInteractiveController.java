package com.kuaishou.serveree.themis.api.controller;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.KdevInteractiveService;
import com.kuaishou.serveree.themis.component.vo.request.CommitReportUrlRequest;
import com.kuaishou.serveree.themis.component.vo.response.CustomBuildParamVo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR>
 * @since 2021/12/15 11:04 上午
 */
@RestController
@RequestMapping("kdev/interactive")
public class KdevInteractiveController {

    @Autowired
    private KdevInteractiveService kdevInteractiveService;

    @GetMapping("customBuildParam/{buildId}")
    public ThemisResponse<CustomBuildParamVo> getCustomBuildParam(@PathVariable("buildId") long buildId) {
        return ThemisResponse.success(kdevInteractiveService.getCustomBuildParam(buildId));
    }

    @PostMapping("/check/report/url")
    public ThemisResponse<Map<String, String>> requireCheckReportUrl(@RequestBody CommitReportUrlRequest param) {
        return ThemisResponse.success(kdevInteractiveService.getCommitCheckReportUrl(param));
    }

}
