package com.kuaishou.serveree.themis.api.controller;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.PARAMS_CAN_NOT_EMPTY;

import java.io.IOException;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.service.statics.LocalStringMatchScanService;
import com.kuaishou.serveree.themis.component.vo.request.SponsorCodeScanRequest;
import com.kuaishou.serveree.themis.component.vo.response.CodeScanPlanInfo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.kdev.SponsorCodeScanResponse;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/11/4 2:05 下午
 */
@Slf4j
@RestController
@RequestMapping("scan")
public class CodeScanController {

    @Autowired
    private LocalStringMatchScanService localStringMatchScanService;

    @GetMapping("/download/scanIssue/excel")
    public void downloadScanIssueExcel(HttpServletResponse response) throws IOException {
        localStringMatchScanService.downloadScanIssueExcel(response);
    }

    @GetMapping("/codeScan/sponsor")
    public ThemisResponse<SponsorCodeScanResponse> sponsorCodeScan(SponsorCodeScanRequest request) {
        SponsorCodeScanResponse response = localStringMatchScanService.sponsorCodeScan(request);
        return ThemisResponse.success(response);
    }

    @GetMapping("/download/interface/scanIssue/excel")
    public void downloadInterfaceScanIssueExcel(HttpServletRequest httpServletRequest, HttpServletResponse response)
            throws IOException {
        String parameterName = "scanPlanId";
        String scanPlanId = httpServletRequest.getParameter(parameterName);
        if (StringUtils.isEmpty(scanPlanId)) {
            throw new ThemisException(PARAMS_CAN_NOT_EMPTY.getCode(),
                    parameterName + PARAMS_CAN_NOT_EMPTY.getMessage()
            );
        }
        localStringMatchScanService.downloadInterfaceScanIssueExcel(Long.parseLong(scanPlanId), response);
    }

    @GetMapping("download/interface/scanPlan/user/list")
    public ThemisResponse<List<CodeScanPlanInfo>> userCodeScanPlanList(String userName) {
        Assert.notEmpty(userName, () -> new ThemisException(-1, "userName不能为空"));
        List<CodeScanPlanInfo> codeScanPlanInfos = localStringMatchScanService.userCodeScanPlanList(userName);
        return ThemisResponse.success(codeScanPlanInfos);
    }

}
