package com.kuaishou.serveree.themis.api.controller.platform.v2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.entity.platform.MetricRankResult;
import com.kuaishou.serveree.themis.component.entity.platform.ReportDetail;
import com.kuaishou.serveree.themis.component.service.platform.PlatformSkyeyeReportService;
import com.kuaishou.serveree.themis.component.vo.request.FileDuplicationRequest;
import com.kuaishou.serveree.themis.component.vo.request.FileIssueRequest;
import com.kuaishou.serveree.themis.component.vo.request.FileMaintainabilityRequest;
import com.kuaishou.serveree.themis.component.vo.request.FileMatchRequest;
import com.kuaishou.serveree.themis.component.vo.request.MetricRankRequest;
import com.kuaishou.serveree.themis.component.vo.request.RuleMatchRequest;
import com.kuaishou.serveree.themis.component.vo.response.FileDuplicationResp;
import com.kuaishou.serveree.themis.component.vo.response.FileIssueResp;
import com.kuaishou.serveree.themis.component.vo.response.FileMaintainabilityResp;
import com.kuaishou.serveree.themis.component.vo.response.FileMatchResp;
import com.kuaishou.serveree.themis.component.vo.response.RuleMatchResp;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-07-25
 */
@Slf4j
@RestController
@RequestMapping("/v2/platform/skyeye")
public class QualityPlatformSkyeyeV2Controller {

    @Autowired
    private PlatformSkyeyeReportService platformSkyeyeReportService;

    @GetMapping("reporter/detail")
    public ThemisResponse<ReportDetail> detail(Long baseId) {
        return ThemisResponse.success(platformSkyeyeReportService.getReportDetail(baseId));
    }

    @PostMapping("matched/files")
    public ThemisResponse<FileMatchResp> matchedFile(@RequestBody FileMatchRequest request) {
        return ThemisResponse.success(platformSkyeyeReportService.fileMatch(request));
    }

    @PostMapping("rules/issuesNum")
    public ThemisResponse<RuleMatchResp> matchedRule(@RequestBody RuleMatchRequest request) {
        return ThemisResponse.success(platformSkyeyeReportService.ruleMatch(request));
    }

    @PostMapping("file/issues")
    public ThemisResponse<FileIssueResp> fileIssues(@RequestBody FileIssueRequest request) {
        return ThemisResponse.success(platformSkyeyeReportService.fileIssueSearch(request));
    }

    @PostMapping("file/maintainability")
    public ThemisResponse<FileMaintainabilityResp> fileMaintainability(@RequestBody FileMaintainabilityRequest request) {
        return ThemisResponse.success(platformSkyeyeReportService.fileMaintainability(request));
    }

    @PostMapping("file/duplication")
    public ThemisResponse<FileDuplicationResp> fileDuplication(@RequestBody FileDuplicationRequest request) {
        return ThemisResponse.success(platformSkyeyeReportService.fileDuplication(request));
    }

    @PostMapping("metric/rank")
    public ThemisResponse<MetricRankResult> metricRank(@RequestBody MetricRankRequest request) {
        return ThemisResponse.success(platformSkyeyeReportService.metricRank(request));
    }
}
