package com.kuaishou.serveree.themis.api.ww;

import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-11-24
 */
public class KconfTest {

    private static final Kconf<Person> KCONF = Kconfs.ofJson("key", new Person(), Person.class).build();

    // 找到所有 xxx.set
    // 找到所有 xxx.get 且 xxx 是 Kconf<T> 类型的 tree，对应的语句是 T t = xxx.get();
    // 将这些tree保存到 Map<> m1 中，key 是 Tree 对象，值是 变量名 比如 上式中的 't'
    // 准备一个新的 Map<Tree, String> m2
    // 遍历Map<Tree, String> m1 -- <tree, 't'>
    // 拿到 tree，拿到这个tree所在的方法体，遍历方法中 此 tree 对应的语句 之后 的语句
    //      如果遇到 V v = t.get(); 形式的语句，对应的tree 是 t1，则把 <t1，'v'> 加入 Map<Tree, String> m2
    //      如果遇到 xxxx.xxxx(t); 即方法调用形式的语句，则 把对应方法节点 t2，t 所在位置参数名 v2 ，<t2, v2> 加入 m2 中
    // 如果 m2 不空
    // 则 用 m2 替换 m1，重复上述过程

    private KconfTestService service = new KconfTestService();

    private void method1() {
        // 有个漏洞，这条语句检测不出来
        KCONF.get().setName("ABc");
        Person person = KCONF.get();
        person.setName("Abc");
        person.getCity().setCityName("city");
        City city = person.getCity();
        city.setCityName("city");

        City d = city;

        method2(person);

        Person p1 = person;

        method21(p1);

        method3("aaa", person.getCity());

        method31("aaa", d);

        method4(person.getCity().getDepartment());

        method3Deep("aaa", p1.getCity());

        service.setName(d.getDepartment());
    }

    private void method2(Person person) {
        person.setName("aaaa");
    }

    private void method21(Person person) {
        person.setName("aaaa");
    }

    private void method3(String str, City city) {
        city.getDepartment().setDeptName("ddd");
        Department department = city.getDepartment();
        try {
            city.setDepartment(new Department());
        } finally {

        }
        department.setDeptName("Ssss");
    }

    private void method31(String str, City city) {
        city.getDepartment().setDeptName("ddd");
        try {
            city.setDepartment(new Department());
        } finally {

        }
    }

    private void method3Deep(String str, City city) {
        method5(city.getDepartment());
    }

    private void method4(Department department) {
        department.setDeptName("aaaa");
    }

    private void method5(Department department) {
        department.setDeptName("aaaa");
    }
}

class Person {
    private String name;

    private City city;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public City getCity() {
        return city;
    }

    public void setCity(City city) {
        this.city = city;
    }
}

class City {
    private String cityName;

    private Department department;

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Department getDepartment() {
        return department;
    }

    public void setDepartment(Department department) {
        this.department = department;
    }
}

