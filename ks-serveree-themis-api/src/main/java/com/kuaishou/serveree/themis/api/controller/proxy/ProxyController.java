package com.kuaishou.serveree.themis.api.controller.proxy;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.proxy.SonarClusterProxy;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.proxy.ProxyLoginInfoVo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-12-15
 */
@Slf4j
@RestController
@RequestMapping("/proxy")
public class ProxyController {
    @Autowired
    private SonarClusterProxy clusterProxy;

    @GetMapping("/login")
    public ThemisResponse<ProxyLoginInfoVo> getLoginId(Long projectId) {
        if (projectId == null) {
            return ThemisResponse.fail(ResultCodeConstant.INVALID_PARAMS);
        }
        return ThemisResponse.<ProxyLoginInfoVo>builder()
                .status(ResultCodeConstant.SUCCESS.getCode())
                .message(ResultCodeConstant.SUCCESS.getMessage())
                .data(clusterProxy.getSonarLoginIdByProjectId(projectId))
                .build();
    }

    @ResponseBody
    @RequestMapping(value = "/redirect/**", method = {RequestMethod.GET, RequestMethod.POST})
    public void redirectSonarApi(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json;charset=UTF-8");
        // TODO 过渡逻辑：过滤接口/api/sources/lines，上报数据走gitlab查源代码，其他走sonar查；后续统一调整为gitlab获取源代码
//        if ("/api/sources/lines".equals(request.getRequestURI())) {
//            try {
//                Integer executionReferType = Integer.parseInt(request.getParameter("executionReferType"));
//                if (executionReferType == 3) {
//                    // 上报数据走gitlab查源代码
//                    String result = JSONUtils.serialize(codeScanReportController.getCodeSourceLines(
//                            request.getParameter("key"),
//                            Integer.parseInt(request.getParameter("from")),
//                            Integer.parseInt(request.getParameter("to")),
//                            request.getParameter("branch")));
//                    response.getWriter().write(result);
//                } else {
//                    response.getWriter().write(clusterProxy.proxyRedirectHandler(request));
//                }
//            } catch (Exception e) {
//                log.error("上报数据查看源文件失败", e);
//            }
//            return;
//        }
//        response.getWriter().write(clusterProxy.proxyRedirectHandler(request));
    }
}
