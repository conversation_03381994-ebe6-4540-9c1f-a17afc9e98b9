package com.kuaishou.serveree.themis.api.controller.platform.v2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.service.CheckProfileService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformProfileService;
import com.kuaishou.serveree.themis.component.vo.request.AllParentProfileRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.AllParentProfileResponseVo;
import com.kuaishou.serveree.themis.component.vo.request.ChangeParentProfileRequest;
import com.kuaishou.serveree.themis.component.vo.request.PlatformPermissionGrantRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileAddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCopyRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCreateRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileGetByNameRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileSearchRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.PlatformPermissionGrantResponse;
import com.kuaishou.serveree.themis.component.vo.response.ProfileAddRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileCopyResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileCreateResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileDeleteResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileDeleteRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileGetByNameResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileSearchResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/4/7 5:40 下午
 */
@Slf4j
@RestController
@RequestMapping("/v2/platform/profile")
public class QualityPlatformProfileV2Controller {

    @Autowired
    private PlatformProfileService platformProfileService;

    @Autowired
    private CheckProfileService checkProfileService;

    @Autowired
    private PlatformPermissionService platformPermissionService;

    @PostMapping("/create")
    public ThemisResponse<ProfileCreateResponseVo> createRuleProfile(@RequestBody ProfileCreateRequestVo requestVo) {
        return ThemisResponse.success(platformProfileService.create(requestVo, SsoUserInfo.getUserName()));
    }

    @PostMapping("/listAllParentProfiles")
    public ThemisResponse<AllParentProfileResponseVo> listAllParentProfiles(@RequestBody AllParentProfileRequestVo requestVo) {
        return ThemisResponse.success(platformProfileService.listAllParentProfiles(requestVo, SsoUserInfo.getUserName()));
    }

    @PostMapping("/search")
    public ThemisResponse<ProfileSearchResponseVo> searchProfile(@RequestBody ProfileSearchRequestVo requestVo) {
        return ThemisResponse.success(platformProfileService.search(requestVo, SsoUserInfo.getUserName()));
    }

    @PostMapping("/delete")
    public ThemisResponse<ProfileDeleteResponseVo> deleteProfile(@RequestBody ProfileDeleteRequestVo requestVo) {
        return ThemisResponse.success(platformProfileService.delete(requestVo, SsoUserInfo.getUserName()));
    }

    @PostMapping("/copy")
    public ThemisResponse<ProfileCopyResponseVo> copyProfile(@RequestBody ProfileCopyRequestVo requestVo) {
        return ThemisResponse.success(platformProfileService.copyProfile(requestVo, SsoUserInfo.getUserName()));
    }

    @PostMapping("/addRule")
    public ThemisResponse<ProfileAddRuleResponseVo> addRule(@RequestBody ProfileAddRuleRequestVo requestVo) {
        checkProfileEditPermission(requestVo.getProfileName());
        return ThemisResponse.success(platformProfileService.addRule(requestVo, SsoUserInfo.getUserName()));
    }

    @PostMapping("/updateRule")
    public ThemisResponse<ProfileAddRuleResponseVo> updateRule(@RequestBody ProfileAddRuleRequestVo requestVo) {
        checkProfileEditPermission(requestVo.getProfileName());
        return ThemisResponse.success(platformProfileService.addRule(requestVo, SsoUserInfo.getUserName()));
    }

    @PostMapping("/deleteRule")
    public ThemisResponse<ProfileDeleteRuleResponseVo> deleteRule(@RequestBody ProfileDeleteRuleRequestVo requestVo) {
        checkProfileEditPermission(requestVo.getProfileName());
        return ThemisResponse.success(platformProfileService.deleteRule(requestVo, SsoUserInfo.getUserName()));
    }

    @GetMapping("/getByProfileName")
    public ThemisResponse<ProfileGetByNameResponseVo> getByProfileName(ProfileGetByNameRequestVo requestVo) {
        return ThemisResponse.success(platformProfileService.getByProfileName(requestVo));
    }

    @PostMapping("/grant/permission/edit")
    public ThemisResponse<PlatformPermissionGrantResponse> grantProfileEditPermission(
            @RequestBody PlatformPermissionGrantRequestVo requestVo) {
        checkProfileGrantPermission(requestVo.getProfileName());
        return ThemisResponse.success(
                platformProfileService.grantProfileEditPermission(requestVo, SsoUserInfo.getUserName()));
    }

    @PostMapping("/changeParent")
    public ThemisResponse<Boolean> changeParentProfile(@Validated @RequestBody ChangeParentProfileRequest requestVo) {
        checkProfileGrantPermission(requestVo.getProfileName());
        return ThemisResponse.success(platformProfileService.changeParentProfile(requestVo));
    }

    private void checkProfileEditPermission(String profileName) {
        CheckProfile checkProfile = checkProfileService.getNonnullByName(profileName);
        if (!platformPermissionService.hasProfileEditPermission(SsoUserInfo.getUserName(), checkProfile)) {
            throw new ThemisException(ResultCodeConstant.NO_PERMISSION);
        }
    }

    private void checkProfileGrantPermission(String profileName) {
        CheckProfile checkProfile = checkProfileService.getNonnullByName(profileName);
        platformPermissionService.checkUserNameEquals(checkProfile.getCreator(), SsoUserInfo.getUserName());
    }
}
