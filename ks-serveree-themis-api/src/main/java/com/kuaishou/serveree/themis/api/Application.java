package com.kuaishou.serveree.themis.api;

import org.apache.shardingsphere.spring.boot.ShardingSphereAutoConfiguration;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

import com.kuaishou.infra.boot.KsSpringApplication;
import com.kuaishou.infra.boot.autoconfigure.KsBootApplication;

import lombok.Data;

/**
 * KsBoot 应用启动类，默认扫描当前 package
 * <p>
 * Write the code. Change the world.
 *
 * <AUTHOR>
 */
@KsBootApplication(scanBasePackages = {
        "com.kuaishou.serveree.themis.api.*",
        "com.kuaishou.serveree.themis.component.*",
        "com.kuaishou.serveree.themis.security.*",
        "cn.hutool.extra.spring",
        "com.kuaishou.id.*",
        "com.kuaishou.filter"
}, exclude = ShardingSphereAutoConfiguration.class)
@MapperScan(value = "com.kuaishou.serveree.themis.component.common.mappers")
@EnableScheduling
@EnableAspectJAutoProxy(exposeProxy = true)
public class Application {

    public static void main(String[] args) {

        KsSpringApplication.run(Application.class, args);
    }

    public void test() {

    }
}

@Data
class Person {
    private Address address;
}
class Address {

    private static String location = "https://abc.shkwai.com/dfkal/we/4fg;s/fsldf/fls/sss.svg"; // Noncompliant

    private static String domain = "play-safe.app"; // Noncompliant


    void test(Person person) {
        if (person.getAddress() != null) {
            System.out.println("not null");
        }
        System.out.println(person.getAddress().toString().hashCode());
    }
}
