package com.kuaishou.serveree.themis.api.warmup;

import org.springframework.core.Ordered;

import com.kuaishou.framework.kafka.topic.FrameworkLogTopic;
import com.kuaishou.framework.warmup.WarmupAble;
import com.kuaishou.infra.boot.warmup.SpringWarmuper;

/**
 * 执行 Warmup 资源，执行完毕后才正式对外提供服务
 * <p>
 * Write the code. Change the world.
 *
 * <AUTHOR>
 */
public class ApplicationWarmuper implements SpringWarmuper {

    @Override
    public WarmupAble[] warmupAbles() {
        return new WarmupAble[] {FrameworkLogTopic.PERF_LOG};
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }

}