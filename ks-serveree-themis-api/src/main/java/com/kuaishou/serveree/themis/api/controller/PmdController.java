package com.kuaishou.serveree.themis.api.controller;

import static com.kuaishou.serveree.themis.component.constant.ResultCodeConstant.INVALID_PARAMS;

import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.entity.pmd.PmdPipelineReportRequest;
import com.kuaishou.serveree.themis.component.service.pmd.PmdService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.response.PipelineReportResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-10-17
 */
@Slf4j
@RestController
@RequestMapping("/pmd")
public class PmdController {

    @Resource
    private PmdService pmdService;

    @Resource
    private KsRedisLock ksRedisLock;

    @PostMapping("/pipeline/report")
    public ThemisResponse<PipelineReportResponse> reportPmdCyclo(@RequestBody @Validated PmdPipelineReportRequest request) {
        // 参数校验
        checkReportParam(request);
        String redisKey = KsRedisPrefixConstant.PLATFORM_PIPELINE_PMD_REPORT_PREFIX + request.getGitProjectId() + ":"
                + request.getBranch();
        try {
            ksRedisLock.spinLock(redisKey);
            return ThemisResponse.success(pmdService.report(request));
        } catch (Exception e) {
            log.error("pmd pipeline report error, request is: {}", JSONUtils.serialize(request), e);
            throw new RuntimeException(e);
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    private void checkReportParam(PmdPipelineReportRequest reportData) {
        if (Objects.isNull(reportData) || CollectionUtils.isEmpty(reportData.getFiles())) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "报告数据为空");
        }
        Long kspBuildId = reportData.getKspBuildId();
        if (Objects.isNull(kspBuildId) || kspBuildId < 1) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "无效的KspBuildId:" + kspBuildId);
        }
        Integer gitProjectId = reportData.getGitProjectId();
        if (Objects.isNull(gitProjectId) || gitProjectId < 1) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "无效的gitProjectId:" + gitProjectId);
        }
        String branch = reportData.getBranch();
        if (StringUtils.isBlank(branch)) {
            throw new ThemisException(INVALID_PARAMS.getCode(), "无效的branch:" + branch);
        }
    }
}
