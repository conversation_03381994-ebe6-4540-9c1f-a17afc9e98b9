package com.kuaishou.serveree.themis.api.controller.platform.v2;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.service.platform.PlatformFileMeasureService;
import com.kuaishou.serveree.themis.component.vo.request.FileDuplicationRequest;
import com.kuaishou.serveree.themis.component.vo.request.PipelineFileMeasureRequest;
import com.kuaishou.serveree.themis.component.vo.response.FileContentDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueCycleComplexityResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-04-27
 */
@Slf4j
@RestController
@RequestMapping("/v2/platform/file/measures")
public class QualityPlatformFileMeasuresV2Controller {

    @Autowired
    private PlatformFileMeasureService platformFileMeasureService;

    @PostMapping("/duplications")
    public ThemisResponse<FileContentDetailResponse> fileDuplications(@RequestBody FileDuplicationRequest request) {
        return ThemisResponse.success(platformFileMeasureService.getFileContentDetail(request));
    }

    /**
     * 流水线扫描，文件指定指标，结果列表
     */
    @PostMapping("/list")
    public ThemisResponse<IssueCycleComplexityResponse> pipelineMeasureList(@RequestBody @Validated PipelineFileMeasureRequest request) {
        checkUserLogin();
        return ThemisResponse.success(platformFileMeasureService.pageByMetricKey(request));
    }

    private void checkUserLogin() {
        if (StringUtils.isEmpty(SsoUserInfo.getUserName())) {
            throw new ThemisException(ResultCodeConstant.NO_PERMISSION);
        }
    }
}
