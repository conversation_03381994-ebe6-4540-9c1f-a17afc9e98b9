package com.kuaishou.serveree.themis.api.controller.platform;

import static com.kuaishou.serveree.themis.component.constant.sonar.SonarConstants.ISSUE_OPENED_STATUS_STRING;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.SonarIssueService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformIssueService;
import com.kuaishou.serveree.themis.component.vo.request.IssueSearchRequest;
import com.kuaishou.serveree.themis.component.vo.request.SearchIssueRequest;
import com.kuaishou.serveree.themis.component.vo.response.IssueSearchResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SonarIssueVo;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/17 3:40 下午C
 */
@Slf4j
@RestController
@RequestMapping("platform/issue")
public class QualityPlatformIssueController {

    @Autowired
    private PlatformIssueService platformIssueService;
    @Autowired
    private SonarIssueService sonarIssueService;

    @PostMapping("search")
    @NeedAuth
    public ThemisResponse<IssueSearchResponse> report(@RequestBody IssueSearchRequest searchRequest) {
        return ThemisResponse.success(platformIssueService.search(searchRequest));
    }

    // TODO 迁移到 CodeScanReportController 后删除
    @PostMapping("search/files")
    @NeedAuth
    public ThemisResponse<Map<String, List<SonarIssueVo>>> searchIssues(@RequestBody SearchIssueRequest request) {
        if (StringUtils.isEmpty(request.getStatuses())) {
            request.setStatuses(ISSUE_OPENED_STATUS_STRING);
        }
        return ThemisResponse.success(sonarIssueService.searchCrRelatedIssues(request));
    }

    // TODO 迁移到 CodeScanReportController 后删除
    @GetMapping("check/exists")
    @NeedAuth
    public ThemisResponse<Integer> checkExists(Long kspBuildId) {
        return ThemisResponse.success(sonarIssueService.checkExistsOrIssueExists(kspBuildId));
    }

}
