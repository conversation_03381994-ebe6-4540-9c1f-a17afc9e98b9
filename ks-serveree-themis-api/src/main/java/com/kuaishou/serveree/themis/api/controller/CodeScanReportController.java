package com.kuaishou.serveree.themis.api.controller;

import static com.kuaishou.serveree.themis.component.constant.sonar.SonarConstants.ISSUE_NO_RESOLVED_STRING;
import static com.kuaishou.serveree.themis.component.constant.sonar.SonarConstants.ISSUE_OPENED_STATUS_STRING;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.base.Joiner;
import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.RulesDetailResp;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.SonarIssueService;
import com.kuaishou.serveree.themis.component.service.SourceCodeService;
import com.kuaishou.serveree.themis.component.service.pipeline.SonarRuleService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.request.SearchIssueRequest;
import com.kuaishou.serveree.themis.component.vo.request.SearchRuleRequest;
import com.kuaishou.serveree.themis.component.vo.response.ReportBaseResp;
import com.kuaishou.serveree.themis.component.vo.response.SearchIssuesResponse;
import com.kuaishou.serveree.themis.component.vo.response.SearchRulesResponse;
import com.kuaishou.serveree.themis.component.vo.response.SourceLinesResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SonarIssueVo;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-01-12
 */
@Slf4j
@RestController
@RequestMapping("/code/scan/report")
public class CodeScanReportController {
    @Autowired
    private SonarIssueService sonarIssueService;

    @Autowired
    private Map<String, SonarRuleService> ruleServiceMap;

    @Autowired
    private PCheckBaseService pCheckBaseService;

    @Autowired
    private SourceCodeService sourceCodeService;

    @Autowired
    private PlatformPermissionService platformPermissionService;

    /**
     * 流水线执行扫描插件入口查询issue
     * 查询指定插件/类型的扫描结果
     */
    @GetMapping("/issues/search")
    public ThemisResponse<SearchIssuesResponse> searchIssues(SearchIssueRequest request) {
        if (Objects.isNull(request.getKspBuildId())) {
            return ThemisResponse.fail(ResultCodeConstant.INVALID_PARAMS);
        }
        if (request.getP() == null) {
            request.setP(1L);
        }
        if (request.getPs() == null) {
            request.setPs(100L);
        }
        if (StringUtils.isEmpty(request.getStatuses())) {
            request.setStatuses(ISSUE_NO_RESOLVED_STRING);
        }
        // 特殊逻辑 当指定了issueKeys时 查询全部状态
        if (StringUtils.isNotEmpty(request.getIssueKeys())) {
            request.setStatuses(Joiner.on(",").join(CheckIssueStatus.allIssueStatus()));
        }
        return ThemisResponse.success(sonarIssueService.searchIssues(request));
    }

    /**
     * commit列表入口查询issue
     * 查询本次提交触发流水线执行了代码扫描的issue扫描结果
     * 结果包含所有类别的issue，不同类别前端分tab展示
     */
    @GetMapping("commit/issues/search")
    public ThemisResponse<SearchIssuesResponse> searchCommitIssues(SearchIssueRequest request) {
        if (Objects.isNull(request.getKspBuildId())) {
            return ThemisResponse.fail(ResultCodeConstant.INVALID_PARAMS);
        }
        if (request.getP() == null) {
            request.setP(1L);
        }
        if (request.getPs() == null) {
            request.setPs(100L);
        }
        return ThemisResponse.success(sonarIssueService.searchCommitIssues(request));
    }

    @PostMapping("/issue/transition")
    public ThemisResponse<SonarIssueVo> issueTransition(@RequestBody @Validated IssueTransitionRequest request) {
        // 参数校验
        Assert.isTrue(com.kuaishou.serveree.themis.component.utils.NumberUtils.isPositive(request.getIssueId()),
                () -> new ThemisException(ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "issueId不能为空"));
        Assert.notNull(request.getTransition(), () -> new ThemisException(
                ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "transition不能为空"));
        Assert.isTrue(com.kuaishou.serveree.themis.component.utils.NumberUtils.isPositive(request.getKspBuildId()),
                () -> new ThemisException(ResultCodeConstant.PARAMS_CAN_NOT_EMPTY.getCode(), "kspBuildId不能为空"));
        // 权限检查
        platformPermissionService.checkKdevGitPermission(
                pCheckBaseService.getByBuildId(request.getKspBuildId()).getProjectId(),  SsoUserInfo.getUserName()
        );
        return ThemisResponse.success(sonarIssueService.issueTransition(request, SsoUserInfo.getUserName()));
    }

    @GetMapping("/rules/search")
    public ThemisResponse<SearchRulesResponse> searchRules(SearchRuleRequest request) {
        request.setSeverities(CheckIssueSeverity.getSonarStatusByThemisKey(request.getSeverities()));
        return ThemisResponse.success(ruleServiceMap.get(String.valueOf(1)).searchRules(request));
    }

    @GetMapping("/rules/show")
    public ThemisResponse<RulesDetailResp> showRuleDetail(String key, String organization, String actives,
            Integer executionReferType) {
        if (StringUtils.isEmpty(key)) {
            return ThemisResponse.fail(ResultCodeConstant.INVALID_PARAMS);
        }
        executionReferType = Objects.isNull(executionReferType) ? 1 : executionReferType;
        return ThemisResponse.success(
                ruleServiceMap.get(String.valueOf(executionReferType)).showRuleDetail(key, organization, actives));
    }

    @GetMapping("/base")
    public ThemisResponse<ReportBaseResp> getReportBaseInfo(Long kspBuildId) {
        if (kspBuildId == null) {
            return ThemisResponse.fail(ResultCodeConstant.INVALID_PARAMS);
        }
        return ThemisResponse.success(pCheckBaseService.getReportBaseInfo(kspBuildId));
    }

    @GetMapping("/sources/lines")
    public SourceLinesResponse getCodeSourceLines(String key, Integer from, Integer to, String branch) {
        String[] keyArgs = key.split(":");
        Integer projectId = Integer.parseInt(keyArgs[0]);
        String filePath = keyArgs[1];
        String[] branchArg = branch.split("||");
        Long kspBuildId = Long.parseLong(branchArg[1]);
        from = Objects.isNull(from) ? NumberUtils.INTEGER_ONE : from;
        to = Objects.isNull(to) ? Integer.MAX_VALUE : to;
        return sourceCodeService.requireSourceLines(projectId, filePath, from, to, kspBuildId);
    }

    /**
     * cr集成代码扫描入口查询issue
     * 查询cr单diff最新patch的issue扫描结果
     * 查询结果包含所有类型的issue（sonar，checkstyle...）
     * 查询结果为终态（标注后的状态）是开启状态（open，reopen）的issue
     */
    @PostMapping("cr/issues/search")
    @NeedAuth
    public ThemisResponse<Map<String, List<SonarIssueVo>>> searchCrIssues(@RequestBody SearchIssueRequest request) {
        if (StringUtils.isEmpty(request.getStatuses())) {
            request.setStatuses(ISSUE_OPENED_STATUS_STRING);
        }
        return ThemisResponse.success(sonarIssueService.searchCrRelatedIssuesV2(request));
    }

    /**
     * 根据kspBuildId查询是否存在issue
     */
    @GetMapping("cr/check/exists")
    @NeedAuth
    public ThemisResponse<Integer> checkExists(Long kspBuildId) {
        return ThemisResponse.success(sonarIssueService.checkExistsOrIssueExists(kspBuildId));
    }
}
