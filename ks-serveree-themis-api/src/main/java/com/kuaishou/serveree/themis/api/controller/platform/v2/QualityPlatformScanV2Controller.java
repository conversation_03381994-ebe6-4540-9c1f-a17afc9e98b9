package com.kuaishou.serveree.themis.api.controller.platform.v2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.service.QualityCheckService;
import com.kuaishou.serveree.themis.component.vo.request.ScanResultRecordRequest;
import com.kuaishou.serveree.themis.component.vo.request.ScheduleScanRequest;
import com.kuaishou.serveree.themis.component.vo.response.CheckRecordResponse;
import com.kuaishou.serveree.themis.component.vo.response.ScheduleScanResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-08-23
 */
@Slf4j
@RestController
@RequestMapping("/v2/platform/scan")
public class QualityPlatformScanV2Controller {

    @Autowired
    private QualityCheckService qualityCheckService;

    @GetMapping("/record/latest")
    public ThemisResponse<CheckRecordResponse> latestCheckRecord(ScanResultRecordRequest request) {
        return ThemisResponse.success(qualityCheckService.latestCheckRecord(request));
    }

    @PostMapping("/schedule/once")
    public ThemisResponse<ScheduleScanResponse> scheduleOnce(@RequestBody ScheduleScanRequest request) {
        return ThemisResponse.success(qualityCheckService.scheduleOnce(request, SsoUserInfo.getUserName()));
    }
}
