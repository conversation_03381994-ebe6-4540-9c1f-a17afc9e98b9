package com.kuaishou.serveree.themis.api.controller.platform;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.platform.PlatformProfileService;
import com.kuaishou.serveree.themis.component.vo.request.ProfileBaseInfoRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileBaseInfoResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/4/7 5:40 下午
 */
@Slf4j
@RestController
@RequestMapping("platform/profile")
public class QualityPlatformProfileController {

    @Autowired
    private PlatformProfileService platformProfileService;

    @PostMapping("/baseInfo")
    public ThemisResponse<ProfileBaseInfoResponseVo> baseInfo(@RequestBody ProfileBaseInfoRequestVo requestVo) {
        return ThemisResponse.success(platformProfileService.baseInfo(requestVo));
    }

}
