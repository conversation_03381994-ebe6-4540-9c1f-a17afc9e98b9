package com.kuaishou.serveree.themis.api.controller.platform.v2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRuleService;
import com.kuaishou.serveree.themis.component.vo.request.ListRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.RuleDetailRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.ListRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.RuleDetailResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/9/6 11:22 上午
 */
@Slf4j
@RestController
@RequestMapping("/v2/platform/rule")
public class QualityPlatformRuleV2Controller {

    @Autowired
    private PlatformRuleService platformRuleService;

    @PostMapping("/list")
    public ThemisResponse<ListRuleResponseVo> list(@RequestBody ListRuleRequestVo requestVo) {
        return ThemisResponse.success(platformRuleService.list(requestVo, SsoUserInfo.getUserName()));
    }

    @PostMapping("/detail")
    public ThemisResponse<RuleDetailResponseVo> detail(@RequestBody RuleDetailRequestVo requestVo) {
        return ThemisResponse.success(platformRuleService.detail(requestVo));
    }

    @GetMapping("/sys")
    public ThemisResponse<String> sysSonarSeverity() {
        platformRuleService.sysSonarSeverity();
        return ThemisResponse.success("success");
    }
}
