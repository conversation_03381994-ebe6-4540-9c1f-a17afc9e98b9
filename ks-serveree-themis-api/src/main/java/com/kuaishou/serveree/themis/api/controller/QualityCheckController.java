package com.kuaishou.serveree.themis.api.controller;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.service.QualityCheckService;
import com.kuaishou.serveree.themis.component.vo.request.CheckSponsorRequest;
import com.kuaishou.serveree.themis.component.vo.response.QualityCheckLogResponse;
import com.kuaishou.serveree.themis.component.vo.response.QualityCheckResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/11/2 7:45 下午
 */
@Slf4j
@RestController
@RequestMapping("check")
public class QualityCheckController {

    @Autowired
    private QualityCheckService qualityCheckService;


    @PostMapping("/sponsor")
    @NeedAuth
    public ThemisResponse<Long> sponsor(@RequestBody CheckSponsorRequest sponsorRequest) {
        Long taskId = qualityCheckService.sponsorCheck(sponsorRequest);
        if (taskId == null) {
            return ThemisResponse.fail(ResultCodeConstant.SAVE_FAIL);
        }
        return ThemisResponse.success(taskId);
    }

    @GetMapping("/result")
    @NeedAuth
    public ThemisResponse<QualityCheckResponse> result(Long taskId, String sourceId) {
        return qualityCheckService.checkResult(taskId, sourceId);
    }

    @GetMapping("/log/read")
    @NeedAuth
    public ThemisResponse<QualityCheckLogResponse> taskLog(Long taskId, String sourceId) {
        return qualityCheckService.taskLog(taskId, sourceId);
    }

    @GetMapping("/{taskId}/log/download")
    public void downloadLogFile(HttpServletResponse servletRes, @PathVariable("taskId") Long taskId, String sourceId) {
        qualityCheckService.downloadTaskLog(servletRes, taskId, sourceId);
    }

    @GetMapping("/pipeline/result")
    public ThemisResponse<QualityCheckResponse> pipelineCheckResult(Long pipelineId, Long buildId) {
        return qualityCheckService.pipelineCheckResult(pipelineId, buildId);
    }
}
