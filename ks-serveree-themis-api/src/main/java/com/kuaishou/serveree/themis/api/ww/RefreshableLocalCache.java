package com.kuaishou.serveree.themis.api.ww;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-08
 */
@Slf4j
public class RefreshableLocalCache<K, V> {
    /**
     * map最大容量，由于没有过期和淘汰策略，且大部分都会初始化（大概200多），控制最大容量为500
     */
    private static final int MAP_MAX_SIZE = 500;
    /**
     * 数据缓存，使用map存储数据
     */
    private volatile ConcurrentHashMap<K, V> cache;
    private volatile ScheduledThreadPoolExecutor executor;
    private Supplier<Map<K, V>> supplier;
    private String threadName;
    private int refreshInterval;
    private TimeUnit timeUnit;

    /**
     * @param supplier 数据提供器
     * @param threadName 线程名称
     * @param refreshInterval 刷新频率，如果小于等于0，则只调用一次，不刷新
     * @param timeUnit 刷新频率时间单位
     */
    public RefreshableLocalCache(Supplier<Map<K, V>> supplier,
            String threadName, int refreshInterval, TimeUnit timeUnit) {
        this.supplier = supplier;
        this.threadName = threadName;
        this.refreshInterval = refreshInterval;
        this.timeUnit = timeUnit;
    }

    /**
     * 刷新数据
     */
    private void refresh() {
        ScheduledThreadPoolExecutor localObj = executor;
        if (localObj != null) {
            return;
        }
        synchronized (this) {
            if (localObj != null) {
                return;
            }
            // 使用CustomizableThreadFactory替代手动创建线程
            ThreadFactory threadFactory = new CustomizableThreadFactory(threadName + "-");
            localObj = new ScheduledThreadPoolExecutor(1, threadFactory);
            executor = localObj;
            executor.scheduleWithFixedDelay(() -> {
                log.info("缓存刷新开始，threadName:{}", threadName);
                cache = convertToConcurrentHashMap(supplier.get());
                log.info("缓存刷新完成，threadName:{}", threadName);
            }, refreshInterval, refreshInterval, timeUnit);
        }
        return;
    }

    /**
     * map转为ConcurrentHashMap,过滤掉key和value为null的值
     *
     * @param map 原map
     * @return ConcurrentHashMap
     */
    public ConcurrentHashMap<K, V> convertToConcurrentHashMap(Map<K, V> map) {
        map.remove(null);
        Iterator<Map.Entry<K, V>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<K, V> entry = iterator.next();
            if (entry.getValue() == null) {
                iterator.remove();
            }
        }
        return new ConcurrentHashMap<>(map);
    }

    public static void main(String[] args) {
        Map<Integer, Student> map = new HashMap<>();
        map.put(1, new Student());
        Student student = map.get(2);
        // 修复空指针问题
        if (student != null) {
            System.out.println(student.getName());
        }
        System.out.println(map.get(1).getName());
        Student student2 = map.get(2);
        if (student2 != null) {
            System.out.println(student2.getName());
        }
        String name = null;
        System.out.println("你属撒");
        // 修复空指针问题
        if (name != null) {
            System.out.println(name.hashCode());
        }

        List<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(2);
        list.add(3);
        for (int i = 0; i < list.size(); i++) {
            System.out.println(list.get(i));
        }
        name = null;
        // 修复空指针问题
        if (name != null) {
            System.out.println(name.hashCode());
        }
    }

    @Data
    public static class Student {

        private int age = 3;
        private String name = "hello";
    }
}
