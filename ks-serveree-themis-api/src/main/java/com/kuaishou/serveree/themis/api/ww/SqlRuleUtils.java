package com.kuaishou.serveree.themis.api.ww;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-04-14
 */
@Slf4j
public class SqlRuleUtils {

    private static class Interval {
        private double lowerBound;
        private double upperBound;
        private boolean lowerInclusive;
        private boolean upperInclusive;
        private String[] values;

        public Interval(double lowerBound, double upperBound) {
            this(lowerBound, upperBound, false, false);
        }

        public Interval(double lowerBound, double upperBound, boolean lowerInclusive, boolean upperInclusive) {
            this.lowerBound = lowerBound;
            this.upperBound = upperBound;
            this.lowerInclusive = lowerInclusive;
            this.upperInclusive = upperInclusive;
        }

        public Interval(String[] values) {
            this.values = values;
            this.lowerBound = 0.0;
            this.upperBound = 0.0;
            this.lowerInclusive = false;
            this.upperInclusive = false;
        }

        // 是反选集的子集就会有冲突
        // 反选集合如果不是一段区间而是两端，那么取反且没交集就有冲突
        public boolean isDisjointFrom(Interval other) {
            if (other.lowerBound >= other.upperBound) {
                return this.lowerBound >= other.lowerBound || this.upperBound <= other.upperBound;
            }

            return this.upperBound <= other.upperBound && this.lowerBound >= other.lowerBound;
        }
    }
}


class BDCDD {
    private double lowerBound;
    private double upperBound;
    private boolean lowerInclusive;
    private boolean upperInclusive;
    private String[] values;

    public BDCDD(double lowerBound, double upperBound) {
        this(lowerBound, upperBound, false, false);
    }

    public BDCDD(double lowerBound, double upperBound, boolean lowerInclusive, boolean upperInclusive) {
        this.lowerBound = lowerBound;
        this.upperBound = upperBound;
        this.lowerInclusive = lowerInclusive;
        this.upperInclusive = upperInclusive;
    }

    public BDCDD(String[] values) {
        this.values = values;
        this.lowerBound = 0.0;
        this.upperBound = 0.0;
        this.lowerInclusive = false;
        this.upperInclusive = false;
    }

    // 是反选集的子集就会有冲突
    // 反选集合如果不是一段区间而是两端，那么取反且没交集就有冲突
    public boolean isDisjointFrom(BDCDD other) {
        if (other.lowerBound >= other.upperBound) {
            return this.lowerBound >= other.lowerBound || this.upperBound <= other.upperBound;
        }

        return this.upperBound <= other.upperBound && this.lowerBound >= other.lowerBound;
    }
}