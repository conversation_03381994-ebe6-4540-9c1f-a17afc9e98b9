package com.kuaishou.serveree.themis.api.controller;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.entity.plugin.changedFiles.ChangedFilesResponse.ChangedFile;
import com.kuaishou.serveree.themis.component.service.plugin.SonarPluginService;
import com.kuaishou.serveree.themis.component.vo.request.IncrementChangedFilesRequest;
import com.kuaishou.serveree.themis.component.vo.response.IncrementChangedFilesResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-08-30
 */
@RestController
@RequestMapping("/common/plugin")
public class SonarPluginController {

    @Autowired
    private SonarPluginService sonarPluginService;

    @PostMapping(value = "/increment/changed")
    public ThemisResponse<List<String>> getChangedFiles(
            @RequestBody IncrementChangedFilesRequest request) {
        IncrementChangedFilesResponse changedFilesResponse = sonarPluginService.getChangedFiles(request);
        if (changedFilesResponse == null || changedFilesResponse.getChangedFiles() == null) {
            return null;
        }
        // 只返回需要扫描的文件的路径
        return ThemisResponse.success(
                changedFilesResponse.getChangedFiles().stream()
                        .map(ChangedFile::getFilePath).collect(Collectors.toList())
        );
    }
}
