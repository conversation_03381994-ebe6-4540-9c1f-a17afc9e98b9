package com.kuaishou.serveree.themis.api.ww;

import java.util.Map;

import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;

import lombok.Data;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2025-01-17
 */
public class CustomRuleDemoV1 {
    @Data
    static class ExpressStateIcon {

        /**
         * 标题
         */
        private String title;

        /**
         * B端的ICON
         */
        private String bdIconUrl;

        /**
         * B端的ICON
         */
        private String bdHighLightIconUrl;

        /**
         * C端的ICON
         */
        private String cdIconUrl;

        /**
         * C端的ICON
         */
        private String cdHighLightIconUrl;
    }

    @Data
    static class ExpressStateIconConf {

        /**
         * 默认图标
         */
        private ExpressStateIcon defaultIcon;

        /**
         * 物流状态
         */
        private Map<String, ExpressStateIcon> expressStateIcon;
    }


    private static Kconf<ExpressStateIconConf> indExpressStateIconDefine =
            Kconfs.ofJson("kwaishop.scmPackageService.indExpressStateIcon", new ExpressStateIconConf(),
                    ExpressStateIconConf.class).build();

    public static void aaa() {
        ExpressStateIconConf expressStateIconConf = indExpressStateIconDefine.get();
        if (expressStateIconConf != null) {
            ExpressStateIcon defaultIcon = expressStateIconConf.getDefaultIcon();
            Map<String, ExpressStateIcon> expressStateIconMap =
                    expressStateIconConf.getExpressStateIcon();
        }
    }

    public static void aabba() {
        ExpressStateIconConf expressStateIconConf = indExpressStateIconDefine.get();
        if (expressStateIconConf != null) {
            ExpressStateIcon defaultIcon = expressStateIconConf.getDefaultIcon();
            Map<String, ExpressStateIcon> expressStateIconMap =
                    expressStateIconConf.getExpressStateIcon();
        }
    }
}
