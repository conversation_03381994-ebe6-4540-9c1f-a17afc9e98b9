package com.kuaishou.serveree.themis.api.controller.platform;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.quality.CheckModeEnum;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCheckActionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformSkyeyeReportService;
import com.kuaishou.serveree.themis.component.vo.request.CheckActionRequest;
import com.kuaishou.serveree.themis.component.vo.request.DataAppendRequest;
import com.kuaishou.serveree.themis.component.vo.request.PipelineReportRequest;
import com.kuaishou.serveree.themis.component.vo.request.SkyeyeLocalReportRequest;
import com.kuaishou.serveree.themis.component.vo.response.CheckActionResponse;
import com.kuaishou.serveree.themis.component.vo.response.DataAppendResponse;
import com.kuaishou.serveree.themis.component.vo.response.PipelineReportResponse;
import com.kuaishou.serveree.themis.component.vo.response.SkyeyeLocalReportResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/8/27 6:20 下午
 */
@Slf4j
@RestController
@RequestMapping("platform/check")
public class QualityPlatformCheckActionController {

    @Resource(name = "PlatformCheckActionServiceImpl")
    private PlatformCheckActionService platformCheckActionService;

    @Resource(name = "PlatformCheckActionCoverModeServiceImpl")
    private PlatformCheckActionService coverModeActionService;

    @Autowired
    private PlatformSkyeyeReportService platformSkyeyeReportService;

    @Autowired
    private KsRedisLock ksRedisLock;

    @PostMapping("action")
    @NeedAuth
    public ThemisResponse<CheckActionResponse> action(@RequestBody CheckActionRequest reportRequest) {
        String branch = reportRequest.getBranch();
        Integer gitProjectId = reportRequest.getGitProjectId();
        Assert.notEmpty(branch, () -> new ThemisException(-1, "branch must not be empty"));
        Assert.notNull(gitProjectId, () -> new ThemisException(-1, "gitProjectId must not be null"));
        final String redisKey =
                KsRedisPrefixConstant.QUALITY_PLATFORM_CHECK_ACTION_PREFIX + ":" + gitProjectId + ":" + branch;
        try {
            ksRedisLock.spinLock(redisKey);
            if (reportRequest.getMode() == null || reportRequest.getMode().equals(CheckModeEnum.DELTA_MODE.getMode())) {
                return ThemisResponse.success(platformCheckActionService.action(reportRequest));
            } else {
                return ThemisResponse.success(coverModeActionService.action(reportRequest));
            }
        } catch (Exception e) {
            log.error("platform check action error", e);
            return ThemisResponse.fail(ResultCodeConstant.CHECK_ACTION_FAIL.getCode(),
                    ResultCodeConstant.CHECK_ACTION_FAIL.getMessage() + "原因是：" + e.getMessage());
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    @PostMapping("pipeline/report")
    @NeedAuth
    public ThemisResponse<PipelineReportResponse> pipelineReport(@RequestBody PipelineReportRequest reportRequest) {
        checkArgs(reportRequest);
        final String redisKey = KsRedisPrefixConstant.QUALITY_PLATFORM_PIPELINE_CHECK_REPORT_PREFIX + ":"
                + reportRequest.getGitProjectId() + ":" + reportRequest.getBranch();
        try {
            ksRedisLock.spinLock(redisKey);
            return ThemisResponse.success(platformCheckActionService.pipelineReport(reportRequest));
        } catch (Exception e) {
            log.error("platform pipeline report error", e);
            return ThemisResponse.fail(ResultCodeConstant.CHECK_ACTION_FAIL.getCode(),
                    ResultCodeConstant.CHECK_ACTION_FAIL.getMessage() + "原因是：" + e.getMessage());
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    private void checkArgs(PipelineReportRequest reportRequest) {
        Integer gitProjectId = reportRequest.getGitProjectId();
        Assert.notNull(gitProjectId, () -> new ThemisException(-1, "gitProjectId must not be null"));

        String branch = reportRequest.getBranch();
        Assert.notEmpty(branch, () -> new ThemisException(-1, "branch must not be empty"));

        Long kspBuildId = reportRequest.getKspBuildId();
        Assert.notNull(kspBuildId, () -> new ThemisException(-1, "kspBuildId must not be null"));

        String commitId = reportRequest.getCommitId();
        Assert.notEmpty(commitId, () -> new ThemisException(-1, "commitId must not be empty"));

        String repoUrl = reportRequest.getRepoUrl();
        Assert.notEmpty(repoUrl, () -> new ThemisException(-1, "repoUrl must not be empty"));

        String sponsor = reportRequest.getSponsor();
        Assert.notEmpty(sponsor, () -> new ThemisException(-1, "sponsor must not be empty"));
    }

    @PostMapping("skyeye/local/report")
    public ThemisResponse<SkyeyeLocalReportResponse> skyeyeLocalReport(
            @RequestBody SkyeyeLocalReportRequest reportRequest) {
        Long baseId = reportRequest.getBaseId();
        Assert.notNull(baseId, () -> new ThemisException(-1, "baseId must not be empty"));
        final String redisKey = KsRedisPrefixConstant.PLATFORM_SKYEYE_REPORT + ":" + baseId;
        try {
            ksRedisLock.spinLock(redisKey);
            return ThemisResponse.success(platformSkyeyeReportService.dataReport(reportRequest));
        } catch (Exception e) {
            log.error("platform skyeye local report action error", e);
            return ThemisResponse.fail(ResultCodeConstant.CHECK_ACTION_FAIL.getCode(),
                    ResultCodeConstant.CHECK_ACTION_FAIL.getMessage() + "原因是：" + e.getMessage());
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    @PostMapping("data/append")
    public ThemisResponse<DataAppendResponse> dataAppend(@RequestBody DataAppendRequest reportRequest) {
        Long baseId = reportRequest.getBaseId();
        Assert.notNull(baseId, () -> new ThemisException(-1, "baseId must not be empty"));
        final String redisKey = KsRedisPrefixConstant.PLATFORM_DATA_APPEND + ":" + baseId;
        try {
            ksRedisLock.spinLock(redisKey);
            return ThemisResponse.success(platformCheckActionService.dataAppend(reportRequest));
        } catch (Exception e) {
            log.error("platform skyeye local report action error", e);
            return ThemisResponse.fail(ResultCodeConstant.CHECK_ACTION_FAIL.getCode(),
                    ResultCodeConstant.CHECK_ACTION_FAIL.getMessage() + "原因是：" + e.getMessage());
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }


}
