package com.kuaishou.serveree.themis.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.QualityTaskKspSyncService;
import com.kuaishou.serveree.themis.component.vo.request.QualityCheckKspSyncRequest;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-05-16
 */
@RestController
@RequestMapping("sync")
@Slf4j
public class QualityTaskKspSyncController {

    @Autowired
    private QualityTaskKspSyncService qualityCheckService;

    @PostMapping("/ksp/result")
    public ThemisResponse<?> handleSyncResultEvent(@RequestBody QualityCheckKspSyncRequest request) {
        log.info("[天琴流水线回调]Java检查插件执行结束，pipelineId:{}, buildId:{}", request.getPipelineId(), request.getBuildId());
        return qualityCheckService.syncKspResult(request.getPipelineId(), request.getBuildId());
    }
}
