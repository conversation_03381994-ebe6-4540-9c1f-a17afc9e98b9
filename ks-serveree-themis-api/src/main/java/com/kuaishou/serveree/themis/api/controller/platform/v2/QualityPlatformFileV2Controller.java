package com.kuaishou.serveree.themis.api.controller.platform.v2;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.service.platform.PlatformFileService;
import com.kuaishou.serveree.themis.component.vo.request.FileDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.FileSegmentsRequest;
import com.kuaishou.serveree.themis.component.vo.response.FileDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.FileSegmentsResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/7/19 3:49 PM
 */
@Slf4j
@RestController
@RequestMapping("/v2/platform/file")
public class QualityPlatformFileV2Controller {

    @Autowired
    private PlatformFileService platformFileService;

    @PostMapping("segments")
    public ThemisResponse<FileSegmentsResponse> fileSegments(@Validated @RequestBody FileSegmentsRequest request) {
        return ThemisResponse.success(platformFileService.fileSegments(request));
    }

    @PostMapping("detail")
    public ThemisResponse<FileDetailResponse> fileDetail(@RequestBody FileDetailRequest request) {
        return ThemisResponse.success(platformFileService.fileDetail(request, SsoUserInfo.getUserName()));
    }

}
