package com.kuaishou.serveree.themis.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.entity.plugin.ReportData;
import com.kuaishou.serveree.themis.component.service.PluginCheckService;
import com.kuaishou.serveree.themis.component.vo.request.AstIssuesRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.AstIssueVo;
import com.kuaishou.serveree.themis.component.vo.response.ProjectLoadConfigVo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR>
 * @since 2022/1/18 2:37 下午
 */
@RestController
@RequestMapping("plugin")
public class PluginCheckController {

    @Autowired
    private PluginCheckService pluginCheckService;

    @GetMapping("project/config/load")
    public ThemisResponse<ProjectLoadConfigVo> projectAndCommonConfig(Integer projectId) {
        return pluginCheckService.projectAndCommonConfig(projectId);
    }

    @PostMapping("report")
    public void pluginReport(@RequestBody ReportData reportData) {
        pluginCheckService.report(reportData);
    }

    @GetMapping("issues")
    public ThemisResponse<List<AstIssueVo>> issues(AstIssuesRequestVo astIssuesRequestVo) {
        return ThemisResponse.success(pluginCheckService.issues(astIssuesRequestVo));
    }

}
