package com.kuaishou.serveree.themis.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.sonar.cache.SonarCacheBO;
import com.kuaishou.serveree.themis.component.service.sonar.cache.SonarCacheConverter;
import com.kuaishou.serveree.themis.component.service.sonar.cache.SonarCacheQueryParam;
import com.kuaishou.serveree.themis.component.service.sonar.cache.SonarCacheService;
import com.kuaishou.serveree.themis.component.vo.request.cache.SonarCacheQueryParamRequest;
import com.kuaishou.serveree.themis.component.vo.request.cache.SonarCacheReportParamRequest;
import com.kuaishou.serveree.themis.component.vo.response.SonarCacheResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-12
 */
@RestController
@RequestMapping("sonar/cache")
@Slf4j
public class SonarCacheController {
    @Autowired
    private SonarCacheService sonarCacheService;

    private final SonarCacheConverter cacheConverter = SonarCacheConverter.INSTANCE;


    @PostMapping(value = "/query")
    public ThemisResponse<SonarCacheResponse> queryCacheInfo(@RequestBody SonarCacheQueryParamRequest request) {
        SonarCacheQueryParam queryParam = cacheConverter.queryParamDto2Bo(request);
        SonarCacheBO bo = sonarCacheService.queryCache(queryParam);
        sonarCacheService.reportHitCacheRecord(bo, queryParam, request.getExecutor());
        return ThemisResponse.success(cacheConverter.renderVo(bo));
    }

    @PostMapping(value = "/report")
    public ThemisResponse<Boolean> reportCacheInfo(@RequestBody SonarCacheReportParamRequest request) {
        SonarCacheBO bo = cacheConverter.dto2Bo(request);
        sonarCacheService.reportCache(bo);
        return ThemisResponse.success(true);
    }


}
