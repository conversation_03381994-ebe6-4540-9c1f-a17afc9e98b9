package com.kuaishou.serveree.themis.api.controller;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.service.factory.ReportFactory;
import com.kuaishou.serveree.themis.component.service.impl.AnalyzePlanReportServiceImpl;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.AnalyzeReportRequest;
import com.kuaishou.serveree.themis.security.annotation.NeedAuth;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/12/16 8:43 下午呀
 */
@Slf4j
@RestController
@RequestMapping("report")
public class AnalyzeReportController {

    @Autowired
    private ReportFactory reportFactory;

    @Autowired
    private AnalyzePlanReportServiceImpl analyzePlanReportService;

    @Autowired
    private KsRedisLock ksRedisLock;

    @RequestMapping
    @NeedAuth
    public void report(@RequestBody AnalyzeReportRequest request) {
        Integer projectId = request.getProjectId();
        String branch = request.getBranch();
        final String redisKey = KsRedisPrefixConstant.ANALYZE_REPORT_LOCK_PREFIX + projectId + ":" + branch;
        try {
            ksRedisLock.spinLock(redisKey);
            log.info("receive report data, repoUrl is {}, buildId is {}", request.getRepoUrl(), request.getBuildId());
            reportFactory.getService(request).report(request);
        } catch (Exception e) {
            log.error("AnalyzeReportController report spin lock error, request is {}", JSONUtils.serialize(request), e);
        } finally {
            ksRedisLock.unlock(redisKey);
        }
    }

    @GetMapping("/ruleplugin/whitelist")
    @NeedAuth
    public Map<String, List<String>> rulePluginWhiteList() {
        return reportFactory.getService(null).rulePluginWhiteListMap();
    }

    @GetMapping("/issue/download")
    public void staticIssueDownload(HttpServletResponse servletRes, Long executeId) throws IOException {
        analyzePlanReportService.staticIssueDownload(servletRes, executeId);
    }
}
