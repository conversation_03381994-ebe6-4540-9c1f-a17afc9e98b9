package com.kuaishou.serveree.themis.api.controller.platform;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCommonService;
import com.kuaishou.serveree.themis.component.vo.response.GitProjectListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2022/4/7 5:33 下午
 */
@Slf4j
@RestController
@RequestMapping("/platform/common")
public class QualityPlatformCommonController {

    @Autowired
    private PlatformCommonService platformCommonService;

    @Autowired
    private PCheckBaseService pCheckBaseService;

    @GetMapping("/grayGitProject/list")
    public ThemisResponse<GitProjectListResponseVo> grayGitProjectList() {
        return ThemisResponse.success(platformCommonService.grayGitProjectList());
    }

    @GetMapping("/set/check/failed/{kspBuildId}")
    public ThemisResponse<Boolean> setCheckFailed(@PathVariable("kspBuildId") Long kspBuildId) {
        return ThemisResponse.success(pCheckBaseService.updateBaseStatusByKspBuildId(kspBuildId, TaskStatusEnum.FAIL));
    }

    @GetMapping("/java/project/id")
    public ThemisResponse<Integer> getJavaProjectIdBySshUrl(@RequestParam("sshUrl") String sshUrl) {
        return ThemisResponse.success(platformCommonService.getJavaProjectIdBySshUrl(sshUrl));
    }

}
