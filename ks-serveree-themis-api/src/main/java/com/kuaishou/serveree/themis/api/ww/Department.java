package com.kuaishou.serveree.themis.api.ww;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
public class Department {
    private String deptName;

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
}

@EqualsAndHashCode(callSuper = true)
@Data
class Address extends Department {
    private String address;
}

@EqualsAndHashCode(callSuper = false)
@Data
class BBB extends Department {
    private String address;
}

@EqualsAndHashCode(callSuper = true)
@Data
class CCC extends Department {
    private String address;
}