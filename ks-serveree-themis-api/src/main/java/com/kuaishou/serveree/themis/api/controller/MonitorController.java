package com.kuaishou.serveree.themis.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.MonitorService;
import com.kuaishou.serveree.themis.component.vo.request.MonitorReportRequest;

/**
 * <AUTHOR>
 * @since 2021/7/5 10:43 上午
 */
@RestController
@RequestMapping("monitor")
public class MonitorController {

    @Autowired
    private MonitorService monitorService;

    @RequestMapping("/report")
    public void report(@RequestBody MonitorReportRequest reportRequest) {
        monitorService.report(reportRequest);
    }

}
