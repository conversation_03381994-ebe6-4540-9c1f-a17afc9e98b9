package com.kuaishou.serveree.themis.api.controller.idea;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.idea.SonarIdeaService;
import com.kuaishou.serveree.themis.component.vo.request.idea.CheckVersionRequest;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.idea.CheckVersionResponse;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/4/11 5:49 PM
 */
@RestController
@RequestMapping("idea")
@Slf4j
public class IdeaController {

    @Autowired
    private SonarIdeaService sonarIdeaService;

    @GetMapping("/init/platform/repo")
    public void initPlatformRepo(Integer gitProjectId, String jdkVersion) {
        sonarIdeaService.initPlatformRepo(gitProjectId, jdkVersion);
    }

    @PostMapping("/sonar/checkVersion")
    public ThemisResponse<CheckVersionResponse> checkSonarIdeaPluginVersion(CheckVersionRequest checkVersionRequest) {
        CheckVersionResponse checkVersionResponse = sonarIdeaService.checkPluginVersion(checkVersionRequest);
        return ThemisResponse.success(checkVersionResponse);
    }

}
