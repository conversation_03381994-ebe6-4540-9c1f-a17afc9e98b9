package com.kuaishou.serveree.themis.api.controller;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.kuaishou.serveree.themis.component.service.openapi.OpenApiService;
import com.kuaishou.serveree.themis.component.vo.request.openapi.PipelineIssueSearchRequest;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.openapi.PipelineIssueSearchResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-12-20
 */
@RequestMapping("/openapi")
@RestController
public class OpenapiController {

    @Resource
    private OpenApiService openApiService;

    @PostMapping("/issue/pipelineIssues/groupBySeverityAndStatus")
    public ThemisResponse<PipelineIssueSearchResponse> searchPipelineIssuesGroupBySeverityAndStatus(@RequestBody @Validated
            PipelineIssueSearchRequest request) {
        return ThemisResponse.success(openApiService.searchPipelineIssuesGroupBySeverityAndStatus(request));
    }
}
