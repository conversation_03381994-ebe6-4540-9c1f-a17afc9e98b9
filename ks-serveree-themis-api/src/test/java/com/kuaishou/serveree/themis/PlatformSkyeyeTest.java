package com.kuaishou.serveree.themis;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.mappers.CheckFileIssueMapper;
import com.kuaishou.serveree.themis.component.common.mappers.CheckFileMeasuresMapper;
import com.kuaishou.serveree.themis.component.entity.platform.MetricRankResult;
import com.kuaishou.serveree.themis.component.entity.platform.ReportDetail;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckFileIssueService;
import com.kuaishou.serveree.themis.component.service.CheckFileMeasuresService;
import com.kuaishou.serveree.themis.component.service.CheckFileService;
import com.kuaishou.serveree.themis.component.service.CheckMeasuresSnapshotService;
import com.kuaishou.serveree.themis.component.service.LocalService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformSkyeyeReportService;
import com.kuaishou.serveree.themis.component.vo.request.FileDuplicationRequest;
import com.kuaishou.serveree.themis.component.vo.request.FileIssueRequest;
import com.kuaishou.serveree.themis.component.vo.request.FileMaintainabilityRequest;
import com.kuaishou.serveree.themis.component.vo.request.FileMatchRequest;
import com.kuaishou.serveree.themis.component.vo.request.MetricRankRequest;
import com.kuaishou.serveree.themis.component.vo.request.RuleMatchRequest;
import com.kuaishou.serveree.themis.component.vo.response.FileDuplicationResp;
import com.kuaishou.serveree.themis.component.vo.response.FileIssueResp;
import com.kuaishou.serveree.themis.component.vo.response.FileMaintainabilityResp;
import com.kuaishou.serveree.themis.component.vo.response.FileMatchResp;
import com.kuaishou.serveree.themis.component.vo.response.RuleMatchResp;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-07-13
 */
public class PlatformSkyeyeTest extends SpringBaseTest {

    @Autowired
    PlatformSkyeyeReportService platformSkyeyeReportService;

    @Autowired
    CheckBaseService checkBaseService;

    @Autowired
    CheckFileMeasuresService checkFileMeasuresService;

    @Autowired
    CheckFileIssueService checkFileIssueService;

    @Autowired
    CheckMeasuresSnapshotService checkMeasuresSnapshotService;

    @Autowired
    CheckFileMeasuresMapper checkFileMeasuresMapper;

    @Autowired
    CheckFileService checkFileService;

    @Autowired
    CheckFileIssueMapper checkFileIssueMapper;

    @Autowired
    private LocalService localService;

    // 获取本地报告的详情数据
    @Test
    public void testReportDetail() {
        CheckBase checkBase = checkBaseService.getById(613);
        System.out.println(checkBase.toString());
        ReportDetail reportDetail = platformSkyeyeReportService.getReportDetail(checkBase.getId());
        System.out.println(reportDetail);

    }

    @Test
    public void testFileMatch() {
        FileMatchRequest fileMatchRequest = new FileMatchRequest();
        fileMatchRequest.setCheckBaseId(576);
        fileMatchRequest.setPartFileName("src/pc/route");
        FileMatchResp fileMatchResp = platformSkyeyeReportService.fileMatch(fileMatchRequest);
        System.out.println(fileMatchResp.toString());
    }

    @Test
    public void testRuleMatch() {
        RuleMatchRequest ruleMatchRequest = new RuleMatchRequest();
        ruleMatchRequest.setCheckBaseId(576);
        ruleMatchRequest.setPartRuleKey("mpor");
        RuleMatchResp ruleMatchResp = platformSkyeyeReportService.ruleMatch(ruleMatchRequest);
        System.out.println(ruleMatchResp.toString());
    }

    @Test
    public void testFileIssueSearch() {
        FileIssueRequest fileIssueRequest = new FileIssueRequest();
        fileIssueRequest.setCheckBaseId(576L);
        fileIssueRequest.setRuleKey("@typescript-eslint/no-unused-vars");
        fileIssueRequest.setFileId(371L);
        FileIssueResp fileIssueResp = platformSkyeyeReportService.fileIssueSearch(fileIssueRequest);
        System.out.println(fileIssueResp);
    }

    @Test
    public void testFileMaintainability() {
        FileMaintainabilityRequest request = new FileMaintainabilityRequest();
        request.setCheckBaseId(576);
        request.setFileId(376);
        FileMaintainabilityResp resp = platformSkyeyeReportService.fileMaintainability(request);
        System.out.println(resp);
    }

    @Test
    public void testFileDuplication() {
        FileDuplicationRequest request = new FileDuplicationRequest();
        request.setCheckBaseId(576);
        request.setFileId(397);
        FileDuplicationResp fileDuplication = platformSkyeyeReportService.fileDuplication(request);
        System.out.println(fileDuplication);
    }

    @Test
    public void testMetricRank1() {
        MetricRankRequest rankRequest = new MetricRankRequest();

        CheckBase checkBase = checkBaseService.getById(576);

        rankRequest.setCheckBaseId(checkBase.getId());
        rankRequest.setMetricKey("file_issue_count");
        rankRequest.setTop(11);

        MetricRankResult rankResult = platformSkyeyeReportService.metricRank(rankRequest);
        System.out.println(rankResult);
    }

    @Test
    public void testMetricRank2() {
        MetricRankRequest rankRequest = new MetricRankRequest();

        CheckBase checkBase = checkBaseService.getById(576);

        rankRequest.setCheckBaseId(checkBase.getId());
        rankRequest.setMetricKey("rule_key");
        rankRequest.setTop(5);

        MetricRankResult rankResult = platformSkyeyeReportService.metricRank(rankRequest);
        System.out.println(rankResult);
    }

    @Test
    public void testMetricRank3() {
        MetricRankRequest rankRequest = new MetricRankRequest();

        CheckBase checkBase = checkBaseService.getById(608);

        rankRequest.setCheckBaseId(checkBase.getId());
        rankRequest.setMetricKey("folder_maintainability");
        rankRequest.setTop(20);
        rankRequest.setFolderLevel(3);

        MetricRankResult rankResult = platformSkyeyeReportService.metricRank(rankRequest);
        System.out.println(rankResult);
    }

    @Test
    public void testMetricRank4() {
        MetricRankRequest rankRequest = new MetricRankRequest();

        CheckBase checkBase = checkBaseService.getById(576);

        rankRequest.setCheckBaseId(checkBase.getId());
        rankRequest.setMetricKey("folder_issue_distribution");
        rankRequest.setFolderLevel(4);

        MetricRankResult rankResult = platformSkyeyeReportService.metricRank(rankRequest);
        System.out.println(rankResult);
    }

    @Test
    public void testMetricRank5_1() {
        MetricRankRequest rankRequest = new MetricRankRequest();

        CheckBase checkBase = checkBaseService.getById(608);

        rankRequest.setCheckBaseId(checkBase.getId());
        rankRequest.setMetricKey("script_line_count");
        rankRequest.setTop(10);

        MetricRankResult rankResult = platformSkyeyeReportService.metricRank(rankRequest);
        System.out.println(rankResult);
    }

    @Test
    public void testMetricRank5_2() {
        MetricRankRequest rankRequest = new MetricRankRequest();

        CheckBase checkBase = checkBaseService.getById(613);

        rankRequest.setCheckBaseId(checkBase.getId());
        rankRequest.setMetricKey("style_line_count");
        rankRequest.setTop(10);

        MetricRankResult rankResult = platformSkyeyeReportService.metricRank(rankRequest);
        System.out.println(rankResult);
    }

    @Test
    public void testMetricRank5_3() {
        MetricRankRequest rankRequest = new MetricRankRequest();

        CheckBase checkBase = checkBaseService.getById(608);

        rankRequest.setCheckBaseId(checkBase.getId());
        rankRequest.setMetricKey("maintainability_index");
        rankRequest.setTop(10);

        MetricRankResult rankResult = platformSkyeyeReportService.metricRank(rankRequest);
        System.out.println(rankResult);
    }

    @Test
    public void testSkyeyeInit() {
        localService.skyeyeInit();
    }

    @Test
    public void testInit() {
        ThemisResponse<Integer> themisResponse = localService.executionReferTypeFix("admin-check", 1);

    }

}
