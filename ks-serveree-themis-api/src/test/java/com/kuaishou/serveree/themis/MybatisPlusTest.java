package com.kuaishou.serveree.themis;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.service.CheckProfileRuleRelationService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2021-05-18
 */
public class MybatisPlusTest extends SpringBaseTest {
    @Autowired
    private TaskConfigService taskConfigService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private CheckProfileRuleRelationService checkProfileRuleRelationService;

    @Test
    public void test01() {
        Long pipelienId = 0L;
        Long buildId = 0L;


        Map<Long, TaskConfig> taskConfMap = taskConfigService.getTasksBySponsorPipelineInfo(pipelienId, buildId)
                .stream().collect(Collectors.toMap(TaskConfig::getTaskId, Function.identity()));

        List<Long> taskIdList = Lists.newArrayList(taskConfMap.keySet());

        Map<Long, Task> taskMap = taskService.listByIds(taskIdList).stream()
                .collect(Collectors.toMap(Task::getId, Function.identity()));

    }

}
