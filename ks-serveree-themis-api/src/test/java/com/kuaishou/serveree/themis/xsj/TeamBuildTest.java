package com.kuaishou.serveree.themis.xsj;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.retry.support.RetryTemplate;
import org.testng.collections.Lists;

import com.kuaishou.serveree.themis.SpringBaseTest;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.common.exception.TeamTaskException;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckModeEnum;
import com.kuaishou.serveree.themis.component.constant.team.TeamIssueBuEnum;
import com.kuaishou.serveree.themis.component.entity.kafka.ScanIssueSummaryNotify;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PCheckIssueService;
import com.kuaishou.serveree.themis.component.service.factory.TeamIssueServiceFactory;
import com.kuaishou.serveree.themis.component.service.kafka.impl.PlatformTeamIssueBuildServiceImpl;
import com.kuaishou.serveree.themis.component.service.openapi.AbstractTaskTeamIssueService;
import com.kuaishou.serveree.themis.component.service.openapi.TeamOpenApi;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.core.lang.Assert;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-03-24 09:54
 **/
public class TeamBuildTest extends SpringBaseTest {

    @Resource
    private TeamOpenApi teamOpenApi;

    @Resource
    private TeamIssueServiceFactory factory;

    @Resource
    private RetryTemplate retryTemplate;

    @Resource
    private PlatformTeamIssueBuildServiceImpl platformTeamIssueBuildService;

    @Resource
    private PCheckBaseService pCheckBaseService;
    @Resource
    private IssueSummaryService issueSummaryService;
    @Resource
    private PCheckIssueService pCheckIssueService;

    @Test
    public void testBuildLinkRecordIssue() {
        Long pBaseId = 119395L;
        List<String> uniqueIds = Lists.newArrayList("43297lfd382264l2092041493l533533052l1427787661l147839895", "43297lf784631cl564956685l201201075l80699793l1667715686");
        List<PCheckIssue> needBuildIssue = pCheckIssueService.queryIssueByUniqueIdAndBaseId(uniqueIds, pBaseId);

        PCheckBase pCheckBase = pCheckBaseService.getById(pBaseId);
        ScanIssueSummaryNotify notify = ScanIssueSummaryNotify.builder()
                .buildId(pCheckBase.getKspBuildId())
                .projectId(pCheckBase.getProjectId())
                .branch(pCheckBase.getBranch())
                .repoUrl(pCheckBase.getRepoUrl())
                .pBaseId(pBaseId)
                .scanMode(ScanModeEnum.PROCESS.getCode())
                .checkMode(CheckModeEnum.COVER_MODE.getMode())
                .build();
        platformTeamIssueBuildService.buildLinkRecordIssue(needBuildIssue, notify, "********");
    }

    @Test
    public void testConsumerBuildTeamIssue() {
        Long pBaseId = 142108L;
        PCheckBase pCheckBase = pCheckBaseService.getById(pBaseId);
        ScanIssueSummaryNotify notify = ScanIssueSummaryNotify.builder()
                .buildId(pCheckBase.getKspBuildId())
                .projectId(pCheckBase.getProjectId())
                .branch(pCheckBase.getBranch())
                .repoUrl(pCheckBase.getRepoUrl())
                .pBaseId(pBaseId)
                .scanMode(ScanModeEnum.PROCESS.getCode())
                .checkMode(CheckModeEnum.COVER_MODE.getMode())
                .build();
        platformTeamIssueBuildService.buildTeamIssue(notify);
    }

    @Test
    public void testCreateAndDependency() throws IOException {
        String teamId = "********";
        String operator = "xieshijie";
        // TeamOpenApi.SimpleTaskModel result = retryTemplate.execute(context -> teamOpenApi.taskInfo(teamId, operator).execute().body()).getResult();
        TeamOpenApi.ApiResponse<TeamOpenApi.SimpleTaskModel> body = teamOpenApi.taskInfo(teamId, operator).execute().body();
        System.out.println(JSONUtils.serialize(body.getResult()));
    }

    @Test
    public void testLinkApi() {
        String teamId = "********";

        AbstractTaskTeamIssueService apiService = factory.createFactory(TeamIssueBuEnum.API_MANAGE_BU.getDesc());
//        apiService.linkSingleIssueToTeam(teamId, "xieshijie", "缺陷001 by spring auto");
    }

    private String createAndDependency(String teamId, String operator) {
        try {
            // 查询team信息
            TeamOpenApi.ApiResponse<TeamOpenApi.SimpleTaskModel> teamInfo = teamOpenApi.taskInfo(teamId, operator).execute().body();
            Assert.isTrue(validateResponse(teamInfo), () -> new TeamTaskException(teamInfo));
            TeamOpenApi.SimpleTaskModel taskModel = teamInfo.getResult();
            String projectId = taskModel.getProjectId();
            String sectionId = taskModel.getSectionId();
            // 查询当前任务组下的分类，并获取目标分类
            TeamOpenApi.ApiResponse<List<TeamOpenApi.TaskClassModel>> taskClasses = teamOpenApi.listTaskClasses(projectId, operator).execute().body();
            if (!validateResponse(taskClasses)) {
                throw new TeamTaskException(taskClasses);
            }
            // 查询
        } catch (TeamTaskException e) {

        } catch (Exception e) {
            logger.error("createAndDependency meet exception! msg == {}", e.getMessage(), e);
            return subBuildMsg(e.getMessage());
        }
        return subBuildMsg("success");
    }

    private boolean validateResponse(TeamOpenApi.ApiResponse response) {
        if (Objects.isNull(response)) {
            return false;
        }
        boolean validateCode = response.getCode() == 200 || response.getCode() == 0;

        return validateCode && Objects.nonNull(response.getResult());
    }

    /**
     * 截取信息的前255，方法堆栈信息过大导致sql超越长度
     */
    private String subBuildMsg(String message) {
        return message.substring(0, Math.min(message.length(), 255));
    }

    private String queryTargetClass(List<TeamOpenApi.TaskClassModel> taskClasses) {
        return null;
    }
}
