package com.kuaishou.serveree.themis;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.api.controller.process.ProcessCheckController;
import com.kuaishou.serveree.themis.component.common.mappers.PCheckIssueMapper;
import com.kuaishou.serveree.themis.component.service.check.custom.entity.CustomRulePair;
import com.kuaishou.serveree.themis.component.service.check.custom.entity.DiffInfo;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.DiffContent;
import com.kuaishou.serveree.themis.component.vo.request.ProcessDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProcessIssueListRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProcessSponsorRequest;
import com.kuaishou.serveree.themis.component.vo.response.ProcessDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.ProcessIssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.ProcessSponsorResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR>
 * @since 2021/10/22 5:44 下午
 */
public class ProcessCheckTest extends SpringBaseTest {

    @Autowired
    private ProcessCheckController processCheckController;

    @Autowired
    private PCheckIssueMapper pCheckIssueMapper;

    @Test
    public void testSponsor() {
        ProcessSponsorRequest processSponsorRequest = new ProcessSponsorRequest();
        processSponsorRequest.setProjectId(23221);
        processSponsorRequest.setBranch("feature__lxx_checkstyle_test");
        processSponsorRequest.setSponsorType(2);
        processSponsorRequest.setCommitId("weiuqgdukahkudhaudh124019741094890");
        processSponsorRequest.setMrId(2251);
        processSponsorRequest.setKCheckLevel(3);
        processSponsorRequest.setSponsor("lixiaoxin");
        CustomRulePair customRulePair = new CustomRulePair();
        customRulePair.setRuleId("Z001");
        List<CustomRulePair> customRulePairs = Lists.newArrayList();
        customRulePairs.add(customRulePair);
        List<DiffContent> diffContentList = Lists.newArrayList();
//        DiffContent diffContent = DiffContent.builder()
//                .lineContent("SQLUpdateBuilder sql = new SQLUpdateBuilder")
//                .build();
//        DiffContent diffContent1 = DiffContent.builder()
//                .lineContent("// SQLUpdateBuilder sql = new SQLUpdateBuilder")
//                .build();
//        diffContentList.add(diffContent);
//        diffContentList.add(diffContent1);
        DiffInfo diffInfo = DiffInfo.builder()
                .filePath("a.java")
                .diffContentList(diffContentList)
                .build();
        List<DiffInfo> diffInfos = Lists.newArrayList();
        diffInfos.add(diffInfo);
        processSponsorRequest.setCustomRulePairs(customRulePairs);
        processSponsorRequest.setDiffInfos(diffInfos);
        ThemisResponse<ProcessSponsorResponse> sponsor = processCheckController.sponsor(processSponsorRequest);
        System.out.println(JSONUtils.serialize(sponsor));
    }

    @Test
    public void testDetail() {
        ProcessDetailRequest processDetailRequest = new ProcessDetailRequest();
        processDetailRequest.setProjectId(23221);
        processDetailRequest.setMrId(2251);
        processDetailRequest.setCommitId("weiuqgdukahkudhaudh124019741094890");
        ThemisResponse<ProcessDetailResponse> detailResponse =
                processCheckController.resultDetail(processDetailRequest);
        System.out.println(JSONUtils.serialize(detailResponse));
    }

    @Test
    public void testMapper() {
        Integer integer = pCheckIssueMapper.issueGroupByCount(464L, Lists.newArrayList(1, 2));
        System.out.println(integer);

    }

    @Test
    public void testMapperList() {
        List<String> strings = pCheckIssueMapper.issueGroupByLocationList(464L, Lists.newArrayList(1, 2), 1, 20);
        System.out.println(JSONUtils.serialize(strings));
    }

    @Test
    public void testSearch() {
        ProcessIssueListRequest processIssueListRequest = new ProcessIssueListRequest();
        processIssueListRequest.setPage(1);
        processIssueListRequest.setPageSize(20);
        processIssueListRequest.setSelectTab("k-check");
        processIssueListRequest.setProjectId(23221);
        processIssueListRequest.setMrId(2251);
        processIssueListRequest.setCommitId("weiuqgdukahkudhaudh124019741094890");
        long l = System.currentTimeMillis();
        ThemisResponse<ProcessIssueListResponse> responseThemisResponse =
                processCheckController.issueList(processIssueListRequest);
        long l1 = System.currentTimeMillis();
        System.out.println("======" + (l1 - l));
        System.out.println(JSONUtils.serialize(responseThemisResponse));
    }

    @Test
    public void testSponsorBuild() {
        ProcessSponsorRequest processSponsorRequest = new ProcessSponsorRequest();
        processSponsorRequest.setProjectId(23221);
        processSponsorRequest.setBranch("feature__lxx_checkstyle_test");
        processSponsorRequest.setSponsorType(1);
        processSponsorRequest.setCommitId("weiuqgdukahkudhaudh124019741094890");
        processSponsorRequest.setBuildId(122314L);
        processSponsorRequest.setBaseCheckType(2);
        processSponsorRequest.setSponsor("lixiaoxin");
        ThemisResponse<ProcessSponsorResponse> sponsor = processCheckController.sponsor(processSponsorRequest);
        System.out.println(JSONUtils.serialize(sponsor));
    }

    @Test
    public void testDetailBuild() {
        ProcessDetailRequest processDetailRequest = new ProcessDetailRequest();
        processDetailRequest.setBuildId(122314L);
        ThemisResponse<ProcessDetailResponse> detailResponse =
                processCheckController.resultDetail(processDetailRequest);
        System.out.println(JSONUtils.serialize(detailResponse));
    }

    @Test
    public void testSearchBuild() {
        ProcessIssueListRequest processIssueListRequest = new ProcessIssueListRequest();
        processIssueListRequest.setPage(1);
        processIssueListRequest.setPageSize(20);
        processIssueListRequest.setSelectTab("k-check");
        processIssueListRequest.setBuildId(12314L);
        long l = System.currentTimeMillis();
        ThemisResponse<ProcessIssueListResponse> responseThemisResponse =
                processCheckController.issueList(processIssueListRequest);
        long l1 = System.currentTimeMillis();
        System.out.println("======" + (l1 - l));
        System.out.println(JSONUtils.serialize(responseThemisResponse));
    }



}
