package com.kuaishou.serveree.themis;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.gitlab.api.GitlabAPI;
import org.gitlab.api.models.GitlabProject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kuaishou.serveree.themis.component.common.entity.ScanCase;
import com.kuaishou.serveree.themis.component.common.entity.ScanIssue;
import com.kuaishou.serveree.themis.component.common.entity.ThemisDependencyAnalysisRepo;
import com.kuaishou.serveree.themis.component.constant.statics.ScanCaseType;
import com.kuaishou.serveree.themis.component.entity.statics.KconfCaseExtend;
import com.kuaishou.serveree.themis.component.service.ScanCaseService;
import com.kuaishou.serveree.themis.component.service.ScanIssueService;
import com.kuaishou.serveree.themis.component.service.ThemisDependencyAnalysisRepoService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

/**
 * <AUTHOR>
 * @since 2021/4/18 3:57 下午
 */
public class ImportRepoInfoTest extends SpringBaseTest {

    @Autowired
    private ThemisDependencyAnalysisRepoService themisDependencyAnalysisRepoService;

    @Autowired
    private ScanCaseService scanCaseService;

    @Autowired
    private GitlabAPI gitlabApi;

    @Autowired
    private ScanIssueService scanIssueService;

    @Test
    public void importExcel() throws FileNotFoundException {
        FileInputStream fileInputStream =
                new FileInputStream("/Users/<USER>/Desktop/1.xlsx");
        List<Map<Integer, String>> projectIds = EasyExcel.read(fileInputStream, null, null).sheet().doReadSync();
        List<ThemisDependencyAnalysisRepo> repoList = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        for (Map<Integer, String> projectStrMap : projectIds) {
            String projectIdStr = projectStrMap.get(0);
            if (StringUtils.isEmpty(projectIdStr)) {
                continue;
            }
            ThemisDependencyAnalysisRepo themisDependencyAnalysisRepo = new ThemisDependencyAnalysisRepo();
            themisDependencyAnalysisRepo
                    .setCollectDate(now.toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            themisDependencyAnalysisRepo.setProjectId(Integer.valueOf(projectIdStr));
            themisDependencyAnalysisRepo.setUpdatedTime(now);
            themisDependencyAnalysisRepo.setCreatedTime(now);
            repoList.add(themisDependencyAnalysisRepo);
        }
        themisDependencyAnalysisRepoService.saveBatch(repoList);
    }

    @Test
    public void testNewest() {
        String newestDate = themisDependencyAnalysisRepoService.newestCollectDate("java");
        System.out.println(newestDate);
    }

    @Test
    public void testInsertHardCodeDomain() throws FileNotFoundException {
        FileInputStream fileInputStream = new FileInputStream("/Users/<USER>/Downloads/硬编码域名列表.xlsx");
        List<Map<Integer, String>> domainMapList = EasyExcel.read(fileInputStream, null, null).sheet().doReadSync();

        Set<ScanCase> scanCaseSet = Sets.newHashSet();

        for (Map<Integer, String> integerStringMap : domainMapList) {
            ScanCase scanCase = new ScanCase();
            scanCase.setCaseStr(integerStringMap.get(0));
            scanCase.setGmtCreate(LocalDateTime.now());
            scanCase.setGmtModified(LocalDateTime.now());
            scanCase.setCaseType(ScanCaseType.HARD_CODE_DOMAIN_CASE.getType());
            scanCaseSet.add(scanCase);
        }
        scanCaseService.saveBatch(scanCaseSet);
    }

    @Test
    public void testInsertKconfHard() throws FileNotFoundException {
        FileInputStream fileInputStream = new FileInputStream("/Users/<USER>/Desktop/域名.xlsx");
        List<Map<Integer, String>> domainMapList = EasyExcel.read(fileInputStream, null, null).sheet().doReadSync();

        Set<ScanCase> scanCaseSet = Sets.newHashSet();
        for (int i = 0; i < domainMapList.size(); i++) {
            if (i < 1) {
                continue;
            }
            Map<Integer, String> integerStringMap = domainMapList.get(i);
            ScanCase scanCase = new ScanCase();
            scanCase.setCaseStr(integerStringMap.get(1));
            scanCase.setGmtCreate(LocalDateTime.now());
            scanCase.setGmtModified(LocalDateTime.now());
            scanCase.setCaseType(ScanCaseType.KCONF_KEY_CASE.getType());
            KconfCaseExtend kconfCaseExtend = new KconfCaseExtend();
            kconfCaseExtend.setCreator(integerStringMap.get(2));
            kconfCaseExtend.setLastUpdater(integerStringMap.get(3));
            kconfCaseExtend.setHardCodeDomainStr(integerStringMap.get(4));
            scanCase.setExtendStr(JSONUtils.serialize(kconfCaseExtend));
            scanCaseSet.add(scanCase);
        }
        scanCaseService.saveBatch(scanCaseSet);
    }

    @Test
    public void testGitProjectInfo() throws IOException {

        FileInputStream fileInputStream =
                new FileInputStream("/Users/<USER>/Desktop/repoInfo/repoid_checkjson(1).xlsx");
        List<Map<Integer, String>> domainMapList = EasyExcel.read(fileInputStream, null, null).sheet().doReadSync();

        List<Map<Integer, String>> mapList = Lists.newArrayList();

        for (int i = 0; i < domainMapList.size(); i++) {
            String request = domainMapList.get(i).get(2);
            GitlabProject project = gitlabApi.getProject(Integer.valueOf(request));
            System.out.println(project.getWebUrl());
        }

    }

    @Test
    public void testInsert267() throws FileNotFoundException {
        FileInputStream fileInputStream = new FileInputStream("/Users/<USER>/Desktop/11111111.xlsx");
        List<Map<Integer, String>> domainMapList = EasyExcel.read(fileInputStream, null, null).sheet().doReadSync();

        Set<ScanIssue> scanIssues = Sets.newHashSet();
        for (Map<Integer, String> integerStringMap : domainMapList) {

            ScanIssue scanIssue = new ScanIssue();
            scanIssue.setId(Long.valueOf(integerStringMap.get(0)));
            scanIssue.setScanPlanId(Long.valueOf(integerStringMap.get(1)));
            scanIssue.setGroupId(Integer.valueOf(integerStringMap.get(4)));
            scanIssue.setProjectId(Integer.valueOf(integerStringMap.get(5)));
            scanIssue.setIllegalStr(integerStringMap.get(6));
            scanIssue.setLocation(integerStringMap.get(7));
            scanIssue.setStartLine(Integer.valueOf(integerStringMap.get(8)));
            scanIssue.setExtendStr(integerStringMap.get(9));
            scanIssue.setCaseType(Integer.valueOf(integerStringMap.get(10)));
            scanIssue.setHttpRepoUrl(integerStringMap.get(11));
            scanIssue.setCodeStr(integerStringMap.get(12));
            scanIssues.add(scanIssue);
        }
        scanIssueService.saveBatch(scanIssues);
    }


}
