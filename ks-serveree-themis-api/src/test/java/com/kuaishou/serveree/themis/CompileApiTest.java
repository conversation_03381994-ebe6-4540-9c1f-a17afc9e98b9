package com.kuaishou.serveree.themis;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.client.compile.CompileApi;
import com.kuaishou.serveree.themis.component.entity.compile.DependencyCheckResult;
import com.kuaishou.serveree.themis.component.entity.compile.DependencyKimNoticeRequest;

/**
 * <AUTHOR>
 * @since 2022/11/24 9:51 AM
 */
public class CompileApiTest extends SpringBaseTest {

    @Autowired
    private CompileApi compileApi;

    @Test
    public void testSendNotice() {
        DependencyKimNoticeRequest request = DependencyKimNoticeRequest.newBuilder()
                .ciJobId(10085L)
                .ciTaskId(1008611L)
                .userList(Lists.newArrayList("lixiaoxin"))
                .resultItems(Lists.newArrayList(
                        DependencyCheckResult.newBuilder()
                                .type(0)
                                .ruleId(100)
                                .context(
                                        "sdk禁止发布全局切面的代码逻辑（[问题详情](www.baidu.com)）,component禁止发布全局切面的代码逻辑（[问题详情](www"
                                                + ".baidu.com)）")
                                .build())
                )
                .build();
        compileApi.sendDependencyKimNotice(request);
    }

    @Test
    public void testCheckRootPomVersion() {
        System.out.println(
                compileApi.checkReleaseRootPomVersion(1400, "m4", "47978bbb6e65455f781f87ee25ff5152ba881d47", "pom.xml"));
    }

}
