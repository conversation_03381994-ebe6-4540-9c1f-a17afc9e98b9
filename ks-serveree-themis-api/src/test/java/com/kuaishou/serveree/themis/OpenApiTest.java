package com.kuaishou.serveree.themis;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.component.entity.kdev.JavaPipelineCreator;
import com.kuaishou.serveree.themis.component.entity.kdev.KdevResponse;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineCancelRequest;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineCreateRequest;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineTriggerRequest;
import com.kuaishou.serveree.themis.component.service.kdev.MrCheckpointBo;
import com.kuaishou.serveree.themis.component.service.openapi.IhrOpenApi;
import com.kuaishou.serveree.themis.component.service.openapi.IhrOpenApi.IhrUserInfo;
import com.kuaishou.serveree.themis.component.service.openapi.KdevOpenApi;
import com.kuaishou.serveree.themis.component.service.openapi.KdevOpenApi.PipelineCreateResponse;
import com.kuaishou.serveree.themis.component.service.openapi.KdevOpenApi.PipelineTriggerResponse;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-09-05
 */
public class OpenApiTest extends SpringBaseTest {

    @Autowired
    private IhrOpenApi ihrOpenApi;

    @Autowired
    private KdevOpenApi kdevOpenApi;

    @Autowired
    private JavaPipelineCreator javaPipelineCreator;

    @Test
    public void testIhrOpenApi() throws IOException {
        List<IhrUserInfo> userInfoList = ihrOpenApi.getPersonsLimited("wangwei", true).execute().body().getData();
        System.out.println(JSONUtils.serialize(userInfoList));
    }

    @Test
    public void testKdevOpenApiCreatePipeline() throws IOException {
        // 创建流水线
        PipelineCreateRequest createRequest = javaPipelineCreator.buildPipelineCreateRequest(1400);
        KdevResponse<PipelineCreateResponse> body = kdevOpenApi.pipelineCreate(createRequest).execute().body();
        System.out.println(JSONUtils.serialize(body));
    }

    @Test
    public void testKdevOpenApiTriggerPipeline() throws IOException {
        // 触发流水线
        MrCheckpointBo sponsorRequest = new MrCheckpointBo();
        sponsorRequest.setGitProjectId(1400);
        sponsorRequest.setRepoUrl("****************************************:kdev-test/ks-serveree-themis.git");
        sponsorRequest.setRepoLanguage("java");
        sponsorRequest.setMrId(4);
        sponsorRequest.setSourceBranch("main_v1");
        sponsorRequest.setTargetBranch("main");
        sponsorRequest.setStuckSeverity("严重");
        sponsorRequest.setCommitId("1d50486e23648e1e87e2b6bf69c7bd818fa897b0");
        sponsorRequest.setOnlyDiff(false);

        PipelineTriggerRequest triggerRequest =
                javaPipelineCreator.buildPipelineTriggerRequest(sponsorRequest, 84714, Collections.emptyList());
        KdevResponse<PipelineTriggerResponse> body = kdevOpenApi.pipelineTrigger(triggerRequest).execute().body();
        System.out.println(JSONUtils.serialize(body));
    }

    @Test
    public void testKdevOpenApiCancelPipeline() throws IOException {
        // 取消流水线
        PipelineCancelRequest cancelRequest = javaPipelineCreator.buildPipelineCancelRequest(84714L, 51050L);
        KdevResponse body = kdevOpenApi.pipelineCancel(cancelRequest).execute().body();
        System.out.println(JSONUtils.serialize(body));
    }

}
