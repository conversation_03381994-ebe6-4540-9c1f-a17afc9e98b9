package com.kuaishou.serveree.themis;


import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.api.controller.CodeScanReportController;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.RulesDetailResp;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.SearchIssueRequest;
import com.kuaishou.serveree.themis.component.vo.response.ReportBaseResp;
import com.kuaishou.serveree.themis.component.vo.response.SearchIssuesResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR>
 * @since 2022/4/12 5:43 下午
 */
public class CodeScanReportControllerTest extends SpringBaseTest {

    @Autowired
    private CodeScanReportController codeScanReportController;

    @Test
    public void testGetIssues() {
        SearchIssueRequest searchIssueRequest = new SearchIssueRequest();
        searchIssueRequest.setKspBuildId(12L);
        searchIssueRequest.setExecutionReferType(ProcessExecutionReferType.SKY_EYE.getType());
        ThemisResponse<SearchIssuesResponse> searchIssuesResponseThemisResponse =
                codeScanReportController.searchIssues(searchIssueRequest);
        System.out.println(JSONUtils.serialize(searchIssuesResponseThemisResponse));
    }

    @Test
    public void testBase() {
        ThemisResponse<ReportBaseResp> reportBaseInfo = codeScanReportController.getReportBaseInfo(194264L);
        System.out.println(JSONUtils.serialize(reportBaseInfo));
    }

    @Test
    public void testRuleDetail() {
        ThemisResponse<RulesDetailResp> ruleDetail =
                codeScanReportController.showRuleDetail("@skyeye/no-internal-url", null, null, null);
        System.out.println(JSONUtils.serialize(ruleDetail));
    }

}
