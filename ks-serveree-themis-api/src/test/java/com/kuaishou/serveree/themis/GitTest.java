package com.kuaishou.serveree.themis;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.eclipse.jgit.api.Git;
import org.gitlab.api.GitlabAPI;
import org.gitlab.api.models.GitlabCommit;
import org.gitlab.api.models.GitlabGroup;
import org.gitlab.api.models.GitlabProject;
import org.gitlab.api.models.GitlabRepositoryTree;
import org.gitlab.api.models.GitlabUser;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.Uninterruptibles;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.git.SelfGitApi;
import com.kuaishou.serveree.themis.component.client.kdev.KdevApi;
import com.kuaishou.serveree.themis.component.common.entity.ScanPlan;
import com.kuaishou.serveree.themis.component.entity.git.BlameInfo;
import com.kuaishou.serveree.themis.component.entity.git.BlameInfoRequest;
import com.kuaishou.serveree.themis.component.entity.git.GetUserProjectsRequest;
import com.kuaishou.serveree.themis.component.entity.git.SelfGitDetailRequest;
import com.kuaishou.serveree.themis.component.entity.git.SelfGitProject;
import com.kuaishou.serveree.themis.component.entity.statics.ScanProjectInfo;
import com.kuaishou.serveree.themis.component.entity.statics.ScanProjectInfo.ScanGroupInfo;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.ScanPlanService;
import com.kuaishou.serveree.themis.component.service.kdev.MrCheckpointBo;
import com.kuaishou.serveree.themis.component.service.kdev.impl.MrCheckpointLogbackService;
import com.kuaishou.serveree.themis.component.service.plugin.SonarPluginService;
import com.kuaishou.serveree.themis.component.utils.JGitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.IncrementChangedFilesRequest;
import com.kuaishou.serveree.themis.component.vo.response.IncrementChangedFilesResponse;
import com.kuaishou.serveree.themis.component.vo.response.SourceLinesResponse.SourceLine;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2020/10/29 4:11 下午
 */
@Slf4j
public class GitTest extends SpringBaseTest {

    @Autowired
    private GitlabAPI gitlabApi;

    @Autowired
    private GitOperations gitOperations;

    @Autowired
    private SelfGitApi selfGitApi;

    @Autowired
    private JGitUtils jGitUtils;

    @Autowired
    private ScanPlanService scanPlanService;

    @Autowired
    private KdevApi kdevApi;

    @Autowired
    private PCheckBaseService pCheckBaseService;

    @Autowired
    private SonarPluginService sonarPluginService;

    @Test
    public void test01() {
        List<GitlabProject> groupProjects = gitlabApi.getGroupProjects(2428);
        List<String> nameList = groupProjects.stream().map(GitlabProject::getName).collect(Collectors.toList());
        System.out.println(JSONUtils.serialize(nameList));
    }

    @Test
    public void test02() {
        GitlabProject cacheProject = gitOperations.getCacheProject(39374);
        System.out.println(JSONUtils.serialize(cacheProject));
    }

    @Test
    public void testGetProject() throws IOException {
        GitlabProject serveree = gitlabApi.getProject("serveree", "ks-serveree-themis");
        List<GitlabRepositoryTree> repositoryTrees =
                gitlabApi.getRepositoryTree(serveree, "/", "master", false);
        System.out.println("======" + JSONUtils.serialize(repositoryTrees));
        Date createdAt = serveree.getCreatedAt();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(createdAt.toInstant(), ZoneId.systemDefault());
        System.out.println(localDateTime);
        System.out.println(JSONUtils.serialize(serveree));
    }

    @Test
    public void test03() throws IOException {
        List<GitlabGroup> groups = gitlabApi.getGroups();
        System.out.println(groups.size());
    }

    @Test
    public void testGetAllProjects() {
        List<GitlabProject> allProjects = gitlabApi.getAllProjects();
        System.out.println(allProjects.size());
    }

    @Test
    public void testGitCache() {
        GitlabProject gitlabProject = gitOperations.getCacheProjectByGroupAndRepoName("kdev-test", "java-example-test");
        System.out.println(JSONUtils.serialize(gitlabProject));
    }

    @Test
    public void test04_getAllUsers() throws InterruptedException {

        List<GitlabUser> allUsers = gitOperations.getAllUsers();
        System.out.println(
                "获取allUsers.size(): " + allUsers.size());
        System.out.println(
                "allUsers username: " + allUsers.stream().map(GitlabUser::getUsername).collect(Collectors.toList()));
        System.out.println(
                "allUsers name: " + allUsers.stream().map(GitlabUser::getName).collect(Collectors.toList()));
        System.out.println(
                "allUsers email: " + allUsers.stream().map(GitlabUser::getEmail).collect(Collectors.toList()));
    }

    @Test
    public void test05() throws IOException {
        List<GitlabUser> list = gitlabApi.findUsers("niuyanan");
        //        String s = selfGitApi.listAllUsers("");
        System.out.println("findUsers email: " + list.stream().map(GitlabUser::getEmail).collect(Collectors.toList()));
        System.out.println("findUsers name : " + list.stream().map(GitlabUser::getName).collect(Collectors.toList()));
        System.out.println(
                "findUsers username: " + list.stream().map(GitlabUser::getUsername).collect(Collectors.toList()));
    }

    @Test
    public void testGetCommitId() {
        List<GitlabCommit> lastCommitsByUrl =
                gitOperations.getLastCommitsByUrl("*************************:serveree/ks-serveree-themis.git",
                        "feature_sonar_check_parser");
        System.out.println(JSONUtils.serialize(lastCommitsByUrl));
    }

    @Test
    public void testAllGroups() {
        Set<Integer> set =
                Sets.newHashSet(21088, 6338, 1953, 20574, 16106, 21089, 20580, 12148, 12523, 20579, 15666, 20577, 21203,
                        20578, 12981, 8433, 22480, 24069, 9477, 22028, 21843, 19398, 8911, 9273, 23040, 1788, 15283,
                        594, 19847, 22117, 21988, 8779, 9316, 18524, 21346, 22795, 24315, 22240, 22327, 11399, 23885,
                        22162, 3290, 5026, 5419, 21087, 22042, 3292, 24090, 22034, 23880, 25221, 23273, 25417, 23549,
                        19012, 19849, 15084, 15873, 12375, 6656, 11867, 24282, 24621, 25039, 25246, 23042, 4072, 1209,
                        14611, 21889, 21137, 23050, 8053, 23503, 20602, 21862, 16117, 19308, 23346, 24436, 19382, 797,
                        1381, 7138, 13612, 16765, 19162, 19390, 7509, 24073, 13611, 23412, 18682, 21136, 21616, 22002,
                        21926, 13607, 21336, 14661, 21655, 23147, 21823, 23991, 22220, 13610, 19165, 23732, 23801,
                        23467, 21442, 24849, 13570, 24023, 13608, 21646, 21396, 21674, 13613, 24175, 21564, 21615, 9313,
                        21948, 21950, 20732, 23088, 24963, 24759, 9160, 14755, 14754, 932, 21441, 8210, 22840, 14056,
                        25492, 6638, 24765, 23267, 21451, 24474, 23185, 21757, 20201, 10569, 2268, 20881, 20319, 21462,
                        535, 9311, 7462, 11453, 17861, 11944, 11557, 10874, 13058, 2869, 21412, 8414, 15265, 15867,
                        19487, 11558, 24667, 3405, 4754, 11230, 12151, 25293, 16846, 17032, 7664, 24108, 13433, 17034,
                        20348, 22466, 23538, 18747, 24529, 6248, 6375, 16761, 14025, 444, 14332, 20841, 24709, 23240,
                        2598, 23476, 19347, 23390, 6379, 25478, 25112, 22963, 23579, 23692, 2578, 2596, 25464, 6381,
                        12149, 12735, 6377, 6378, 6380, 23612, 25465, 18432, 2597, 14286, 20261, 19489, 15662, 24903,
                        7135, 21153, 24901, 24902, 21047, 24248, 23711, 24015, 23373, 20310, 23411, 23056, 3068, 23461,
                        25169, 19938, 3479, 4549, 2953, 7592, 2211, 7214, 4664, 4194, 6536, 2208, 6751, 7903, 14532,
                        11390, 24827, 9659, 9026, 25049, 3318, 5928, 11409, 6768, 23908, 10041, 2209, 22336, 7768, 1629,
                        11900, 18999, 19058, 19203, 23376, 17781, 19197, 19205, 20712, 22115, 20711, 23349, 14706,
                        21730, 15645, 17694, 19861, 25451, 17322, 14880, 14911, 19752);
        Set<Integer> set1 = Sets.newHashSet();
        Set<Integer> set2 = Sets.newHashSet();
        for (Integer integer : set) {
            try {
                List<GitlabProject> gitlabProjects = selfGitApi.listAllProjectsByGroupIdWithSubGroups(integer);
                set1.addAll(gitlabProjects.stream().map(GitlabProject::getId).collect(Collectors.toList()));
            } catch (Exception e) {
                e.printStackTrace();
                set2.add(integer);
            }

        }
        System.out.println(set1.size());
        System.out.println(set2);
        //        List<GitlabProject> realProjects = Lists.newArrayList();
        //        for (GitlabProject gitlabProject : gitlabProjects) {
        //            if (skipThisProject(gitlabProject)) {
        //                continue;
        //            }
        //            log.warn("addd=====");
        //            realProjects.add(gitlabProject);
        //        }
        //        System.out.println(JSONUtils
        //                .serialize(realProjects.stream().map(GitlabProject::getSshUrl).collect(Collectors.toList())));
        //        System.out.println("============");
        //        System.out.println(JSONUtils
        //                .serialize(realProjects.stream().map(GitlabProject::getId).collect(Collectors.toList())));

    }

    @Test
    public void testFinalProjectIds() {
        Set<Integer> projectIdSet =
                Sets.newHashSet(65857, 65858, 65859, 65860, 65861, 65862, 65863, 65871, 65874, 65877, 65883, 65884,
                        65885, 65886, 65887, 65888, 65890, 65894, 65895, 65910, 65911, 65934, 65935, 65936, 65937,
                        65938, 65939, 65940, 65942, 65943, 65949, 65950, 65951, 65952, 65953, 65954, 65957, 65958,
                        65960, 65963, 65968, 65969, 65971, 65981, 65987, 65992, 65995, 65998, 65999, 66000, 66001,
                        66002, 66005, 66007, 66038, 66052);
        System.out.println(projectIdSet.size());

        List<Integer> projectIds = Lists.newArrayList();

        for (Integer integer : projectIdSet) {
            try {
                GitlabProject project = gitlabApi.getProject(integer);
                Uninterruptibles.sleepUninterruptibly(100, TimeUnit.MILLISECONDS);
                if (skipThisProject(project)) {
                    continue;
                }
                projectIds.add(integer);
            } catch (IOException e) {
                log.error("参数error", e);
            }
        }
        System.out.println(projectIds.size());
        System.out.println(JSONUtils.serialize(projectIds));
    }

    private boolean skipThisProject(GitlabProject gitlabProject) {
        if ("user".equals(gitlabProject.getNamespace().getKind())) {
            log.warn("{} is user project id ,skip", gitlabProject.getWebUrl());
            return true;
        }
        if (!gitOperations.isJavaMavenProject(gitlabProject)) {
            return true;
        }
        return false;
    }

    @Test
    public void testA() {
        GitlabProject projectByUrl = gitOperations.getProjectByUrl(
                "*************************:plateco-dev/kwaishop-business/kwaishop-shop/kwaishop-workbench"
                        + "-center.git");
        System.out.println(projectByUrl);
    }

    @Test
    public void testJava() {
        String mainLanguage = gitOperations.getMainLanguage(1400);
        assert mainLanguage.equals("java");
        boolean javaProject = gitOperations.isJavaProject(1400);
        System.out.println(javaProject);
    }

    @Test
    public void testGetGroups() {
        GitlabProject project = gitOperations.getProject(49931);
        System.out.println(JSONUtils.serialize(project));
        System.out.println("=============================");
    }

    @Test
    public void testJGit() {
        boolean master = jGitUtils.cloneProject(
                "*************************:plateco-dev/kwaishop-selection-delivery/kwaishop-delivery/kwaishop-onx"
                        + "-engine-service.git",
                new File("/Users/<USER>/Desktop/周三需要上线的sql1122/"), "master");
        System.out.println(master);

    }

    @Test
    public void testGroupId() throws IOException {
        List<Integer> groupIdList = Lists.newArrayList();
        groupIdList.add(489);
        groupIdList.add(3620);
        groupIdList.add(57);

        List<ScanPlan> scanPlans = scanPlanService.listActivePlans();
        for (ScanPlan scanPlan : scanPlans) {
            String projectInfo = scanPlan.getProjectInfo();
            ScanProjectInfo scanProjectInfo = JSONUtils.deserialize(projectInfo, ScanProjectInfo.class);
            if (scanProjectInfo == null) {
                continue;
            }
            for (ScanGroupInfo scanGroupInfo : scanProjectInfo.getScanGroupInfos()) {
                groupIdList.add(scanGroupInfo.getGroupId());
            }
        }
        for (Integer integer : groupIdList) {
            GitlabGroup group = gitlabApi.getGroup(integer);
            System.out.println(JSONUtils.serialize(group));
        }

    }

    @Test
    public void testGetRowFile() {
        AtomicInteger line = new AtomicInteger(1);
        List<SourceLine> sources = new ArrayList<>();
        try {
            byte[] content = gitlabApi.getRawFileContent(356, "67187d00e30c8c6c042bc4c9b394815f142ac540",
                    "src/main/java/com/liufei13/liufeidev/serivce/BigFile.java");
            try (
                    BufferedReader br = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(content)))
            ) {
                br.lines().forEach(e -> sources.add(SourceLine.builder().line(line.getAndIncrement()).code(e).build()));
            }
        } catch (IOException e) {
            log.error("gitlab get source file failed", e);
        }
        System.out.println(JSONUtils.serialize(sources));
    }

    @Test
    public void getUserProjects() {
        GetUserProjectsRequest request = GetUserProjectsRequest.builder()
                .page(1)
                .pageSize(10)
                .simple(true)
                .search("kdev")
                .username("lixiaoxin")
                .build();
        List<GitlabProject> gitlabProjects = selfGitApi.getUserProjects(request);
        System.out.println(JSONUtils.serialize(gitlabProjects));
    }

    @Test
    public void testGetRawFileContent() {
        String fileName =
                "ks-serveree-cr-api/src/main/java/com/kuaishou/serveree/cr/api/controller/MRDiffController.java";
        try {
            byte[] rawFileContent = gitlabApi.getRawFileContent(23221, "f01cbb86", fileName);
            String content = Arrays.toString(rawFileContent);
            String highlightContent = selfGitApi.getHighlightContent(fileName, content);
            System.out.println(highlightContent);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testBlameInfo() {
        BlameInfoRequest request = BlameInfoRequest.builder()
                .gitProjectId(24049)
                .filePath("ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/controller"
                        + "/AnalyzeReportController.java")
                .ref("safa")
                .build();
        List<BlameInfo> fileBlameInfo = selfGitApi.getFileBlameInfo(request);
        System.out.println(JSONUtils.serialize(fileBlameInfo));
    }

    @Resource
    private MrCheckpointLogbackService logbackCheckpointChecker;

    @Test
    public void testHL() throws IOException {
        String cachedHighlightContent =
                gitOperations.getCachedHighlightContent(24157, "3d9d66deae92c98e273e31e2997fe6781df208d6",
                        "kspay-channel-component/src/main/java/com/kuaishou/kspay/channel/dataReport/service/impl"
                                + "/ChannelDataReportServiceImpl.java");
        System.out.println(cachedHighlightContent);

        MrCheckpointBo mrCheckpointBo = new MrCheckpointBo();
        mrCheckpointBo.setGitProjectId(1400);
        mrCheckpointBo.setCommitId("41b8257acc05a541167b4f1abe8920c882fb0171");
        mrCheckpointBo.setCheckpointName("LogbackCheck");

        logbackCheckpointChecker.check(mrCheckpointBo);
        System.in.read();
    }


    @Test
    public void testSelfGitDetail() {
        SelfGitDetailRequest selfGitDetailRequest = new SelfGitDetailRequest();
        selfGitDetailRequest.setGitProjectId(13418);
        selfGitDetailRequest.setStatistics(true);
        SelfGitProject projectDetail = selfGitApi.getProjectDetail(selfGitDetailRequest);
        System.out.println(JSONUtils.serialize(projectDetail));
    }

    @Test
    public void validateProjectIds() {
        ArrayList<Integer> integers =
                Lists.newArrayList(31672, 31458, 41420, 11521, 25558, 3690, 29712, 41420, 5093, 6053, 51124, 33675,
                        40489, 3882,
                        36451, 38858, 550, 26065, 19866, 9108, 40551, 7773, 43932, 8047, 4374, 40255, 40591, 306, 5202,
                        10632,
                        17037, 32305, 31672, 6162, 43439, 35570, 6892, 34798, 40662, 38420, 48298, 14497, 32169, 40209,
                        40091,
                        41933, 48117, 34705, 31816, 55454, 43038, 40194);
        for (Integer integer : integers) {
            try {
                GitlabProject project = gitlabApi.getProject(integer);
            } catch (IOException e) {
                log.error("getProject error , projectId is {}", integer);
            }
        }
    }

    @Test
    public void testUrl() {
        List<Integer> projectIds = Lists.newArrayList(640, 1929, 3992, 66851, 1064, 26803, 14649, 27195, 3901,
                2110, 2111, 2112, 1740, 1742, 2127, 11113);
        List<String> sshUrls = Lists.newArrayList();
        for (Integer projectId : projectIds) {
            GitlabProject project = gitOperations.getProject(projectId);
            String sshUrl = project.getSshUrl();
            sshUrls.add(sshUrl);
        }
        System.out.println(sshUrls.size());
        System.out.println(JSONUtils.serialize(sshUrls));
    }

    @Test
    public void filterJavaAndMaven() {

        List<Integer> gitProjectIds =
                Lists.newArrayList(43253, 64450, 65057, 46648, 68923, 26065, 33435, 16277, 14346, 5169, 58150, 44110,
                        63409,
                        64925, 11919, 65154, 69695, 103, 51101, 60743, 69626, 50286, 68749, 67810, 30789, 59466, 65249,
                        18755,
                        69432, 25378, 50542, 65411, 66394, 69138, 70231, 64491, 61829, 67849, 50000, 65940, 66827, 8356,
                        31613,
                        36988, 24067, 33824, 56887, 65549, 52881, 5524, 47669, 359, 57228, 64354, 52631, 65486, 48353,
                        45801,
                        55931, 70100, 11091, 53647, 69836, 6664, 65999, 43316, 42271, 47991, 65354, 4224, 48392, 474,
                        66058,
                        45303, 68486, 65624, 37780, 66074, 65350, 25108, 42105, 51260, 8674, 64614, 70217, 67012, 57191,
                        64724,
                        61378, 31332, 48560, 65402, 8981, 64353, 39854, 69047, 65727, 65535, 59209, 18338, 65282, 31892,
                        66922,
                        29875, 69263, 50101, 23559, 66046, 68345, 4189, 41541, 7007, 51648, 39322, 64923, 8615, 47190,
                        39799,
                        38838, 64941, 35699, 65783, 66078, 67788, 61984, 65180, 24903, 66381, 40741, 67617, 63156,
                        69833, 69285,
                        14232, 68414, 47456, 65257, 66030, 68546, 65188, 37992, 37014, 42388, 68138, 25558, 45045,
                        50034, 46191,
                        40669, 68873, 4857, 68821, 45908, 56466, 65018, 41602, 66447, 52397, 68395, 61720, 64451, 64409,
                        1062,
                        68993, 61335, 68335, 3987, 58149, 65629, 56752, 32565, 4785, 64732, 42954, 13880, 67434, 59440,
                        67997,
                        25369, 68125, 34466, 39496, 66249, 49295, 39167, 64977, 58050, 63598, 28064, 65453, 52331, 2321,
                        16241,
                        66047, 66029, 64698, 61326, 24906, 67805, 35746, 25134, 24860, 68981, 63304, 68504, 56852,
                        58844, 65463,
                        53225, 65373, 41138, 65464, 60698, 20055, 68051, 48345, 41726, 56336, 66554, 70000, 12082,
                        67712, 31896,
                        60906, 69052, 25885, 5202, 40551, 47713, 64380, 45821, 65041, 32389, 48851, 32445, 66340, 61095,
                        67442,
                        25070, 41942, 4117, 5444, 3174, 35215, 69937, 59137, 65723, 51754, 18562, 19199, 65630, 65010,
                        62785,
                        35192, 65189, 64355, 62879, 61927, 39200, 17848, 45113, 51477, 65460, 52648, 54302, 42539,
                        69485, 69301,
                        48266, 23657, 65011, 69239, 40132, 65528, 51128, 47664, 65221, 2709, 66187, 65523, 69072, 64015,
                        19866,
                        64780, 55531, 65225, 41044, 69730, 51245, 22286, 6478, 25420, 66028, 4374, 12948, 69906, 65447,
                        39374,
                        66397, 8635, 65304, 63433, 34705, 43213, 11552, 43093, 64924, 36934, 69761, 58148, 37027, 57623,
                        43932,
                        64393, 65479, 47932, 63527, 63464, 43254, 40937, 64741, 12952, 32996, 902, 65623, 66841, 69999,
                        17265,
                        35191, 17037, 64844, 66073, 65191, 65605, 26931, 51852, 64810, 66188, 67495, 20980, 25093,
                        65037, 65462,
                        64687, 12653, 69820, 62830, 69363, 16709, 40677, 65668, 69474, 65769, 67112, 19660, 65371,
                        65339, 64585,
                        56228, 64891, 64905, 63181, 65810, 44902, 44098, 25046, 69823, 57365, 65231, 15917, 63160, 768,
                        65072,
                        13984, 64954, 61334, 63550, 35571, 48832, 56762, 33432, 57844, 43860, 65751, 67587, 69793, 8803,
                        42205,
                        68905, 63399, 26602, 60715, 68036, 66929, 66185, 64898, 65875, 52248, 25600, 65569, 46652,
                        63496, 64758,
                        12600, 44077, 31478, 21102, 33677, 66851, 68177, 18623, 36268, 16254, 47123, 64852, 67671,
                        66128, 65873,
                        61491, 15157, 31128, 65367, 69917, 65578, 63337, 47706, 65066, 65566, 39296, 51700, 64425,
                        21581, 65150,
                        32533, 41491, 11286, 64723, 37493, 69677, 7438, 22611, 28424, 55665, 55832, 65134, 37394, 68027,
                        20642,
                        48361, 51684, 47466, 13474, 52007, 64324, 21687, 60513, 43689, 30499, 68011, 64519, 64893,
                        65408, 30320,
                        66414, 55454, 70261, 40450, 64821, 1701, 64982, 16682, 64382, 68862, 3935, 43478, 66043, 14277,
                        57176,
                        69831, 68680, 4073, 31818, 48298, 65118, 37214, 33524, 69068, 20437, 64719, 38393, 64945, 42548,
                        26922,
                        65785, 66475, 24162, 7761, 64904, 67477, 30796, 67181, 40255, 65794, 65778, 24223, 1633, 63301,
                        50213,
                        61873, 13660, 10448, 68927, 51188, 29390, 24812, 65459, 51406, 64538, 42063, 58291, 21659,
                        61324, 47930,
                        35053, 66109, 67559, 61642, 64703, 46104, 7231, 10398, 34976, 20809, 67282, 53611, 67075, 47746,
                        64870,
                        36094, 14199, 36159, 34633, 342, 27209, 7507, 4493, 10632, 50718, 65326, 51346, 66147, 61297,
                        38419,
                        69481, 47193, 6162, 15246, 64951, 51226, 48388, 6854, 40501, 65942, 65030, 60921, 69483, 17034,
                        69743,
                        30644, 65804, 65587, 69808, 44938, 69213, 69125, 68671, 44559, 8582, 44802, 2541, 65561, 66640,
                        68180,
                        47392, 37874, 33670, 306, 7263, 7455, 53112, 10858, 57434, 30577, 66465, 25820, 69538, 63634,
                        38309,
                        55202, 30791, 5899, 64511, 47357, 64739, 41923, 65603, 69680, 45513, 65478, 61064, 57274, 64694,
                        38261,
                        69799, 47880, 17492, 48055, 65802, 69137, 68612, 69388, 64684, 59609, 66889, 57839, 66510,
                        56279, 4753,
                        64352, 29603, 67303, 53484, 35889, 61528, 30951, 20675, 52755, 7872, 58890, 60199, 69214, 20632,
                        49889,
                        8309, 69290, 170, 69832, 20214, 53426, 48294, 64960, 36289, 65640, 14337, 8229, 42881, 39938,
                        33864,
                        42538, 41916, 65649, 4703, 69658, 44047, 56430, 11327, 62656, 25647, 52106, 37814, 61672, 64389,
                        63493,
                        19158, 65952, 20522, 3895, 10837, 65317, 65012, 26066, 68712, 59063, 51443, 903, 31369, 64422,
                        17591,
                        14546, 64851, 65467, 69423, 33127, 22550, 68244, 65217, 11953, 48105, 19888, 41965, 64401,
                        63454, 64675,
                        70220, 57377, 68592, 57843, 65392, 68380, 31309, 6520, 23739, 39873, 28703, 44159, 52900, 38357,
                        63320,
                        67543, 65161, 20569, 66218, 13618, 67713, 45914, 22538, 69025, 11521, 58061, 47434, 55570,
                        58940, 68048,
                        68268, 56388, 10812, 7770, 31111, 69164, 68598, 61666, 33525, 35199, 39441, 21721, 1012, 69858,
                        64913,
                        57505, 56297, 47860, 45030, 51900, 67456, 56137, 1156, 68347, 65256, 51954, 19388, 66103, 69951,
                        64859,
                        64777, 65251, 63225, 66870, 70221, 60433, 61660, 66976, 69952, 15489, 62964, 69991, 64316,
                        64754, 15700,
                        66736, 46109, 64520, 67458, 67187, 65223, 62415, 65771, 25294, 11915, 69981, 6675, 65331, 58155,
                        7773,
                        65527, 47683, 46838, 65287, 26968, 32520, 24425, 34330, 43406, 65277, 31458, 67375, 69911,
                        26803, 64788,
                        64837, 57826, 24759, 64916, 64622, 36179, 65862, 49618, 64962, 67170, 33974, 62491, 69643,
                        38615, 56825,
                        18528, 7720, 20885, 58574, 48122, 65014, 14356, 68978, 67246, 64730, 68726, 40963, 41624, 65198,
                        66673,
                        1646, 50339, 64521, 51464, 8519, 27334, 65122, 65508, 49540, 4604, 30690, 4697, 68760, 37772,
                        40941,
                        25143, 58755, 55203, 17990, 65450, 10405, 65910, 60449, 62316, 37662, 66190, 21543, 37307,
                        25824, 25030,
                        66869, 39789, 64886, 65580, 67759, 64363, 68704, 11207, 47506, 32176, 45533, 68723, 48924,
                        47373, 42034,
                        65110, 68136, 5399, 59445, 67862, 41126, 13317, 65172, 65936, 44537, 14443, 550, 65487, 35275,
                        16532,
                        7811, 65239, 47853, 70197, 52932, 60727, 65766, 65714, 68834, 64651, 48819, 63948, 65401, 66619,
                        7494,
                        50602, 53678, 58146, 65404, 67821, 65390, 58467, 65285, 49394, 67365, 33436, 39337, 64307,
                        42302, 69568,
                        68487, 67300, 65211, 11024, 20266, 33907, 67284, 39218, 44432, 38633, 27221, 48573, 65366,
                        48749, 37529,
                        68624, 37654, 66553, 46476, 69014, 24931, 65173, 40285, 65075, 34693, 24248, 64668, 64362,
                        47579, 65825,
                        55805, 10891, 68818, 65457, 27521, 69733, 5894, 33288, 36226, 65735, 65106, 67801, 45334, 31684,
                        66082,
                        65016, 64446, 5281, 52462, 64727, 49003, 65147, 15957, 53021, 66880, 57549, 22258, 69718, 65473,
                        60534,
                        62684, 66199, 65633, 58105, 68478, 25172, 69786, 44135, 44996, 64184, 64361, 8935, 8957, 35098,
                        69647,
                        67986, 55420, 66236, 55921, 36414, 52926, 2919, 9889, 47429, 69785, 52635, 64371, 7941, 42162,
                        64828,
                        68909, 64682, 65264, 34806, 68516, 11650, 22326, 10626, 65475, 66558, 30699, 64405, 13895,
                        10746, 68155,
                        13617, 65047, 68226, 25575, 60093, 66975, 69006, 57083, 57965, 23592, 46055, 64946, 25646,
                        36849, 26277,
                        6127, 4858, 62654, 65056, 40424, 60197, 24928, 65434, 68672, 46114, 69824, 64686, 70301, 24114,
                        56938,
                        40489, 44799, 65113, 7165, 11554, 65599, 38720, 123, 7456, 38276, 51829, 65884, 49418, 64507,
                        32142,
                        69415, 60264, 35750, 68744, 66758, 3725, 51963, 55806, 64581, 37205, 49216, 67315, 65358, 65369,
                        42186,
                        70098, 25362, 55768, 69303, 13639, 35960, 57924, 62053, 29111, 65089, 66225, 35620, 69183,
                        65565, 9912,
                        59716, 51732, 10425, 8071, 54865, 69132, 51449, 23994, 64729, 64855, 67083, 68944, 64783, 14489,
                        57904,
                        65071, 66626, 9239, 38811, 46929, 5912, 64957, 53721, 41422, 65598, 6806, 63252, 66045, 36924,
                        29524,
                        36408, 5471, 30858, 45310, 16932, 65951, 66745, 23968, 35059, 51159, 65992, 45748, 24423, 65482,
                        58936,
                        61410, 65894, 41619, 68124, 45373, 66637, 69803, 55664, 43464, 26397, 67142, 69834, 37234,
                        68023, 57581,
                        38990, 24939, 33094, 42877, 46676, 20554, 4708, 34105, 52138, 15809, 69770, 65195, 11935, 30228,
                        60919,
                        47181, 48860, 68225, 55416, 67803, 65170, 58728, 70243, 67717, 41816, 66121, 64833, 26690,
                        64984, 31339,
                        40163, 27053, 43856, 33092, 65958, 70002, 64857, 53471, 69949, 56238, 46241, 84, 65015, 10802,
                        22189,
                        1424, 25951, 53869, 64861, 62314, 64755, 65307, 48867, 53235, 44565, 15053, 56536, 66622, 43413,
                        35298,
                        54811, 65957, 32305, 55513, 33816, 8672, 67604, 66522, 69839, 24648, 69390, 14340, 66997, 69268,
                        26856,
                        57058, 63491, 64300, 28234, 55781, 25279, 65320, 65885, 54228, 13563, 27474, 52520, 56434,
                        68290, 65658,
                        58887, 50214, 38156, 68597, 58033, 65524, 25257, 65585, 24366, 68437, 65279, 69373, 46849, 5282,
                        30979,
                        56846, 34905, 66337, 65237, 34646, 10422, 68732, 64245, 66842, 55265, 41004, 41405, 48073,
                        65163, 45112,
                        7716, 65128, 39527, 66500, 864, 64557, 65345, 58279, 65349, 21121, 62298, 66393, 40505, 67182,
                        64565,
                        46891, 21091, 18900, 70200, 37506, 42788, 38965, 39569, 39533, 37613, 69355, 65792, 65187,
                        65643, 65586,
                        38391, 69741, 65273, 65341, 42228, 12360, 47188, 39195, 69915, 65694, 31187, 53525, 69046,
                        70119, 32827,
                        64764, 801, 58678, 69181, 64510, 65270, 64890, 30950, 2681, 63427, 64900, 33805, 24538, 22530,
                        68406,
                        51884, 48960, 64969, 32382, 65593, 18878, 64823, 57969, 31833, 14999, 65476, 49775, 17661,
                        33250, 37340,
                        34770, 49355, 44041, 61911, 48895, 67704, 64722, 56006, 68633, 41534, 68629, 56356, 55914,
                        65013, 6109,
                        25349, 43673, 65483, 40372, 63131, 37547, 65410, 69849, 67030, 27268, 65406, 43275, 57652,
                        26901, 57498,
                        63060, 51160, 42659, 3502, 69709, 68015, 56231, 8489, 69304, 45922, 6844, 65082, 36116, 64812,
                        31845,
                        53871, 51439, 5009, 48220, 40270, 50631, 15639, 57165, 31696, 37759, 62137, 65800, 64863, 40082,
                        53776,
                        56367, 11971, 18346, 39072, 70088, 7309, 37309, 64239, 19334, 13244, 24183, 56822, 41339, 64649,
                        44629,
                        65592, 24715, 31459, 65346, 3919, 36401, 67999, 65144, 52405, 67635, 33124, 60601, 8698, 59106,
                        23327,
                        28394, 64410, 43350, 65332, 13312, 4320, 25151, 9006, 31928, 15340, 37502, 880, 60910, 60633,
                        65330,
                        67643, 60292, 17538, 38183, 40096, 12993, 62214, 63229, 64959, 60941, 53070, 31356, 51084,
                        65255, 64996,
                        41387, 9426, 37770, 64643, 9380, 67662, 64598, 39402, 37387, 68722, 22647, 60908, 65088, 66933,
                        54863,
                        64679, 57741, 44176, 64689, 20943, 66184, 65425, 34733, 45208, 2878, 45955, 54820, 25981, 40056,
                        48109,
                        34542, 66996, 31107, 38199, 9888, 65953, 65206, 66364, 59548, 22033, 66942, 10625, 64842, 65051,
                        67260,
                        18191, 67059, 60545, 65433, 64733, 66768, 64872, 68657, 66355, 21161, 1727, 36551, 4357, 65093,
                        41419,
                        5886, 55968, 69760, 64921, 8710, 65382, 44671, 61698, 64827, 45568, 4511, 32788, 57018, 48600,
                        30698,
                        69840, 26834, 45599, 67648, 69817, 51259, 65160, 45916, 360, 58051, 66577, 44163, 64988, 46775,
                        64720,
                        69187, 19359, 56226, 57158, 25563, 65725, 67308, 69740, 65512, 48412, 55453, 65689, 61808,
                        68376, 8042,
                        67806, 540, 67663, 18862, 70219, 68313, 31836, 65389, 29730, 26673, 65577, 39771, 6443, 13261,
                        21137,
                        66743, 39265, 48209, 65120, 67353, 65600, 66822, 12734, 44097, 59042, 43351, 70329, 30991, 2153,
                        51253,
                        65235, 65791, 64784, 50427, 9127, 12307, 67073, 51693, 68149, 4400, 3025, 67955, 68068, 62231,
                        24214,
                        13175, 66138, 58254, 67981, 15267, 21437, 44846, 66974, 3858, 3454, 64671, 65497, 41562, 3631,
                        24734,
                        25959, 67010, 65663, 55702, 47457, 65560, 53368, 22231, 50179, 36288, 64911, 7772, 41425, 26025,
                        35697,
                        68716, 8161, 38202, 52370, 34275, 65175, 65949, 9848, 69149, 46802, 65062, 64453, 48047, 14323,
                        45598,
                        64506, 59325, 67623, 69178, 43439, 66116, 69506, 67153, 66438, 55812, 46795, 66469, 47838,
                        20669, 18079,
                        23747, 18007, 69627, 65132, 69636, 65556, 20947, 17456, 50360, 66608, 54660, 65421, 28699,
                        19609, 42164,
                        30502, 33783, 65441, 58468, 45114, 53123, 39037, 65162, 65939, 62713, 56751, 66712, 47619,
                        32121, 64490,
                        69144, 26869, 67988, 67397, 51912, 64967, 14183, 45433, 66535, 70161, 13679, 67305, 48966,
                        45574, 43565,
                        6898, 64699, 64756, 68002, 16147, 68926, 67552, 43114, 35523, 23209, 22298, 68113, 18103, 27791,
                        24222,
                        69932, 51587, 58055, 31612, 17107, 65651, 31371, 57842, 65363, 64818, 64705, 6890, 64013, 69571,
                        64676,
                        68441, 57580, 4737, 30232, 58814, 60775, 64825, 63084, 50011, 65702, 16723, 64840, 52993, 38392,
                        44115,
                        66633, 64888, 51941, 66765, 64918, 45768, 62296, 53662, 55864, 64776, 24824, 35555, 33095, 9001,
                        26021,
                        33747, 63731, 3988, 58049, 65199, 35908, 64820, 45543, 20965, 64972, 65644, 54980, 64871, 40662,
                        43450,
                        67707, 69976, 66580, 29156, 62474, 67622, 68751, 24224, 41291, 55598, 66486, 64428, 65084,
                        68965, 68561,
                        69710, 53504, 69577, 29933, 42366, 67773, 33565, 31487, 19248, 34124, 57695, 58255, 68693,
                        64775, 65935,
                        64385, 64489, 17550, 41749, 61265, 18814, 58837, 23543, 69746, 28018, 51590, 69958, 65465, 6762,
                        64436,
                        64403, 67544, 63158, 50477, 46076, 69980, 29229, 20511, 64975, 40610, 66000, 65758, 31816,
                        22929, 14375,
                        65820, 27444, 64958, 42961, 45669, 8614, 32654, 38898, 68204, 30481, 28743, 64847, 64881, 68838,
                        66534,
                        13356, 27693, 68974, 35502, 68247, 26156, 63332, 64638, 53495, 66112, 64880, 64659, 21125,
                        51929, 23843,
                        36319, 68107, 28624, 65847, 38204, 33818, 53493, 14775, 28514, 8938, 58343, 69433, 41385, 63230,
                        67668,
                        64391, 65094, 27274, 64654, 1733, 46526, 13754, 67343, 41677, 35875, 22200, 21064, 68419, 57584,
                        65642,
                        65789, 7194, 61549, 65594, 65364, 52713, 41418, 69727, 34934, 49344, 22250, 64284, 65662, 65283,
                        66767,
                        70099, 59193, 64188, 46805, 64854, 65184, 66818, 27943, 38398, 51649, 62521, 66801, 64773, 7460,
                        38420,
                        42839, 26999, 61489, 32535, 16819, 65536, 56399, 61856, 47111, 65824, 18555, 8559, 49847, 62294,
                        46297,
                        67457, 17041, 55585, 51284, 63649, 61740, 1759, 38094, 32252, 27250, 63484, 68217, 4254, 24861,
                        63421,
                        64701, 24502, 47335, 64469, 12088, 65574, 54138, 56616, 68046, 60201, 61061, 6440, 48821, 13247,
                        30710,
                        63163, 3689, 64998, 64789, 65438, 44567, 65430, 28973, 68339, 38162, 8974, 59082, 65488, 42180,
                        61622,
                        5955, 33470, 43358, 69067, 2697, 65176, 19267, 65061, 64146, 49059, 12520, 57059, 67774, 61300,
                        39097,
                        46103, 48225, 37561, 64704, 56761, 6008, 56276, 30947, 57847, 69790, 56389, 63482, 49914, 62661,
                        65496,
                        67804, 814, 32231, 43544, 68747, 50048, 66973, 43193, 58064, 24683, 64421, 35758, 66805, 32621,
                        24134,
                        58816, 4662, 60383, 744, 65384, 66437, 30503, 64356, 45445, 68645, 11378, 63261, 58803, 49046,
                        16153,
                        6643, 35851, 40230, 49957, 7810, 66132, 62456, 8244, 35985, 64408, 6030, 40845, 64414, 20090,
                        65254,
                        68377, 65340, 64906, 41661, 66866, 43868, 36276, 65676, 65370, 50428, 4573, 50632, 42724, 31814,
                        64749,
                        17815, 65448, 13194, 64696, 58151, 11717, 43978, 68625, 65477, 69425, 45055, 17574, 59191,
                        13917, 64530,
                        53458, 58790, 65582, 31555, 37935, 65076, 15527, 62454, 767, 18749, 64647, 66033, 67256, 36686,
                        27725,
                        50823, 61018, 30231, 15400, 12763, 15504, 60413, 68337, 65583, 64771, 30229, 67605, 63447,
                        63584, 64910,
                        64074, 64657, 27756, 49564, 46537, 65058, 36194, 22952, 65296, 65121, 55797, 6575, 21143, 64211,
                        55600,
                        65405, 69261, 41245, 5457, 31409, 69784, 65149, 48464, 55677, 21964, 53233, 65469, 51485, 65413,
                        57913,
                        68029, 38545, 56568, 3655, 64416, 27801, 64765, 638, 17959, 29209, 68899, 44649, 64766, 68238,
                        48884,
                        64877, 68473, 67257, 58598, 28088, 3946, 65351, 64980, 65178, 66211, 25380, 64937, 54468, 67556,
                        64617,
                        21872, 67474, 65280, 40037, 65827, 65966, 36567, 44904, 48512, 48645, 69154, 33583, 53469,
                        18611, 24167,
                        67918, 40591, 29942, 48751, 65784, 51187, 36451, 45616, 50700, 65432, 66877, 57962, 39933,
                        56324, 67833,
                        65829, 70229, 4859, 53412, 43075, 17514, 35228, 64990, 52053, 22211, 67005, 68054, 10728, 30770,
                        35776,
                        65315, 66777, 65597, 64778, 68615, 70017, 31939, 14923, 65078, 46357, 28353, 68799, 46451,
                        57798, 62010,
                        37059, 44969, 51808, 33192, 46429, 69933, 11525, 55555, 6467, 69982, 22792, 67772, 63315, 56419,
                        65591,
                        58102, 6208, 25178, 47103, 19614, 27452, 33471, 58170, 64144, 45376, 65124, 65506, 67744, 65843,
                        58999,
                        41020, 48281, 49332, 64523, 48141, 55815, 53582, 66042, 64467, 47040, 64850, 47819, 61436,
                        70274, 49603,
                        14035, 70055, 68894, 48067, 34677, 51633, 52036, 67062, 65859, 65004, 63029, 41833, 19880,
                        50699, 53443,
                        15423, 61336, 36681, 46305, 1132, 54531, 47641, 35841, 37369, 70247, 68940, 27653, 66076, 16098,
                        64424,
                        69560, 70227, 70139, 67396, 53527, 67454, 37806, 68111, 65707, 67738, 68538, 14448, 65795,
                        26906, 69372,
                        60490, 45014, 17505, 66215, 66440, 52246, 68148, 1702, 67651, 33232, 52736, 15314, 68096, 67441,
                        64994,
                        60790, 46942, 68620, 46050, 14758, 65137, 68169, 41857, 17392, 54677, 12346, 40012, 35389,
                        64885, 13302,
                        60507, 68996, 64563, 64610, 63171, 15374, 65590, 14468, 64662, 64781, 65196, 46139, 67808,
                        67838, 63913,
                        69931, 2201, 37517, 49568, 69305, 57378, 14608, 65002, 64920, 45465, 69515, 43989, 59345, 68651,
                        25312,
                        41679, 22959, 41531, 56311, 69821, 65823, 53199, 47988, 52738, 64014, 69528, 57440, 39602,
                        43841, 52478,
                        63580, 39874, 53571, 65036, 45588, 64864, 11586, 65054, 64793, 68937, 64831, 64378, 45187,
                        48564, 58526,
                        66986, 52079, 43079, 67871, 60431, 6192, 54552, 68307, 66071, 65764, 65567, 41786, 65863, 65435,
                        44089,
                        29712, 68254, 31264, 48121, 65427, 63941, 29250, 65965, 68820, 67946, 42979, 65493, 24155,
                        48777, 64090,
                        44583, 31488, 65168, 64515, 65639, 68117, 59462, 66449, 29683, 43345, 42404, 31442, 35953,
                        17982, 17737,
                        64772, 18320, 36404, 28372, 18080, 450, 49545, 41132, 26129, 46348, 43729, 25864, 30697, 52012,
                        43038,
                        67048, 64790, 3060, 70306, 21695, 69838, 54400, 64895, 68352, 63840, 65333, 69954, 41417, 37451,
                        58652,
                        61845, 51853, 64791, 48074, 69152, 4559, 25686, 46175, 58869, 61844, 67525, 46286, 64151, 65529,
                        31080,
                        18730, 31408, 30918, 64782, 66542, 55808, 69180, 68090, 27476, 20389, 67168, 51727, 29497,
                        65064, 66569,
                        69935, 56361, 44668, 6819, 58154, 64803, 20326, 67443, 32257, 511, 55970, 16468, 63732, 65414,
                        7445,
                        65266, 50128, 47695, 65065, 51447, 26263, 44249, 68880, 402, 40492, 48031, 44913, 65695, 52401,
                        4146,
                        68231, 29941, 50617, 70255, 64768, 29617, 64845, 67389, 65628, 65908, 65204, 37329, 69828,
                        17138, 12410,
                        60445, 64949, 64661, 64973, 48609, 46466, 16910, 31223, 43717, 62868, 65860, 70036, 13588,
                        32798, 12855,
                        67760, 7118, 63569, 47368, 23707, 61805, 29087, 16313, 20324, 67594, 3966, 25250, 65606, 69825,
                        4012,
                        65436, 52396, 22155, 68841, 9352, 5585, 68123, 37712, 51372, 57369, 18894, 44100, 65909, 21141,
                        69938,
                        65665, 11108, 42092, 55512, 8047, 4359, 51851, 67700, 64370, 69632, 64761, 68416, 30580, 65772,
                        41873,
                        65214, 45201, 67120, 68108, 55688, 40611, 67297, 32351, 65091, 65877, 64894, 66826, 32395,
                        65514, 17655,
                        65238, 69539, 44420, 43076, 53660, 64605, 63000, 17925, 12942, 21381, 58741, 9390, 2642, 66007,
                        24564,
                        43337, 20052, 68809, 31718, 46051, 32023, 64991, 40348, 65788, 37792, 37276, 43139, 32718,
                        47521, 69237,
                        36450, 40933, 48607, 5105, 30831, 17583, 58074, 67864, 54296, 4256, 70083, 34380, 67616, 68021,
                        67710,
                        14742, 47652, 46124, 2918, 39882, 69516, 46404, 55352, 69822, 7998, 26578, 60746, 64392, 55584,
                        62816,
                        32195, 64599, 3263, 70110, 23609, 67867, 25029, 66659, 65294, 23901, 23420, 66652, 815, 65034,
                        8217,
                        59047, 39362, 35134, 61246, 55957, 70105, 68913, 66379, 51650, 67985, 47250, 64767, 7396, 49020,
                        13557,
                        28797, 64939, 69493, 38079, 65793, 66002, 64848, 35714, 34088, 64884, 12446, 6536, 60492, 4243,
                        30379,
                        64210, 42848, 15988, 33869, 65215, 33194, 66065, 24068, 67095, 48745, 46662, 48335, 50015,
                        67861, 69294,
                        65770, 61994, 40194, 66641, 64915, 4057, 36978, 31462, 25073, 45679, 63781, 67399, 38940, 48839,
                        23168,
                        63755, 65362, 22003, 67581, 68086, 66687, 56885, 6010, 65713, 58119, 31377, 45401, 36753, 64527,
                        64411,
                        69646, 58600, 48789, 66562, 60932, 69953, 12394, 29068, 24046, 9074, 46585, 62473, 58891, 64950,
                        65790,
                        64298, 20546, 64373, 63168, 64455, 48088, 67143, 43362, 64609, 61757, 25800, 62581, 7195, 30711,
                        64368,
                        15241, 65803, 64846, 66702, 51339, 44528, 32361, 62814, 46642, 62683, 49369, 23796, 65271,
                        50731, 22274,
                        66288, 64387, 45991, 64952, 10737, 27792, 47564, 64442, 60709, 60512, 66122, 22737, 38397,
                        69184, 69731,
                        22385, 8550, 41830, 67870, 5991, 68658, 48400, 38629, 62138, 15937, 37840, 47018, 25223, 64666,
                        46112,
                        65499, 55711, 12988, 66502, 65060, 64731, 31359, 25822, 53025, 27456, 29768, 11214, 69576,
                        29638, 25865,
                        65446, 38815, 66906, 52766, 5760, 66086, 6752, 65712, 43391, 5656, 69222, 34916, 53121, 64909,
                        66585,
                        13467, 64770, 45083, 41388, 10557, 51131, 64763, 15999, 49380, 52973, 24221, 67267, 35544,
                        50680, 65085,
                        64400, 12098, 40150, 45305, 3766, 64556, 14813, 27020, 55279, 65185, 65126, 66762, 52696, 6384,
                        25381,
                        21385, 41046, 25154, 33812, 39794, 64858, 69343, 67685, 15166, 69104, 5264, 46746, 2823, 62396,
                        65200,
                        58315, 29347, 37476, 65208, 67310, 66847, 38081, 38181, 64708, 40193, 14047, 64572, 56303,
                        14699, 30861,
                        7975, 9773, 64402, 65693, 14435, 11917, 69765, 56632, 62060, 47312, 9475, 10838, 3037, 65228,
                        65969,
                        65588, 48144, 36368, 65067, 65190, 31825, 23303, 7699, 35124, 21571, 64430, 27431, 53803, 66509,
                        63676,
                        62763, 43939, 70071, 3456, 65589, 49460, 70211, 40606, 62011, 67586, 11556, 69479, 5999, 56771,
                        66208,
                        37060, 67610, 69945, 64797, 55752, 66925, 16832, 35256, 36611, 4050, 23774, 50399, 33541, 65400,
                        29588,
                        69133, 6331, 29261, 65806, 65092, 51124, 64834, 68696, 64794, 69030, 1743, 65177, 23489, 30164,
                        20375,
                        20674, 51897, 65844, 64047, 43467, 64966, 64901, 65272, 66661, 64423, 52154, 64853, 44697,
                        33696, 43904,
                        33897, 32507, 59732, 65080, 31270, 12028, 69313, 58992, 65086, 69908, 24664, 24157, 32408, 5719,
                        62944,
                        61525, 3707, 57374, 32481, 56085, 58662, 37702, 8316, 40831, 63322, 13011, 66863, 51634, 64832,
                        26493,
                        65572, 43962, 51487, 66766, 64596, 62220, 36282, 65850, 39662, 5048, 63167, 66018, 57419, 25989,
                        40569,
                        52858, 64822, 32833, 68067, 64650, 23701, 52706, 3882, 29878, 37916, 70095, 64429, 43612, 52586,
                        47503,
                        44320, 40336, 57967, 63112, 30855, 66072, 57484, 64917, 44415, 61777, 28425, 68563, 21388,
                        65151, 11975,
                        47725, 69919, 64673, 70297, 65943, 35779, 34519, 50342, 65581, 64726, 65886, 15240, 65652,
                        37015, 54593,
                        64938, 35974, 57804, 67152, 13254, 50046, 40209, 41153, 13994, 22288, 63103, 26870, 41735,
                        67655, 32488,
                        46569, 32197, 70124, 8089, 28428, 64760, 66368, 12351, 25434, 53439, 43265, 62096, 31595, 24131,
                        64137,
                        55350, 47189, 43080, 65782, 44988, 62799, 36329, 69717, 19893, 16150, 47015, 68530, 24229,
                        19780, 41503,
                        53063, 35278, 69918, 38844, 68450, 67887, 37776, 62074, 46465, 10946, 18465, 48758, 65083,
                        53668, 10468,
                        44973, 64808, 49552, 47684, 23219, 24006, 37728, 3394, 16888, 53323, 33998, 13843, 58896, 62009,
                        10787,
                        21486, 48734, 17452, 41565, 59874, 11610, 5967, 65461, 35798, 52417, 53481, 64826, 67812, 66453,
                        65267,
                        69251, 33685, 68423, 65212, 6856, 54530, 65596, 47411, 13972, 68332, 35185, 65938, 551, 65507,
                        4371,
                        40286, 43423, 55886, 4119, 44446, 64656, 64936, 30230, 23708, 38943, 31906, 69916, 1186, 66270,
                        70230,
                        66551, 1758, 68069, 3648, 2498, 40445, 64838, 65309, 54125, 62742, 42662, 56774, 43479, 51417,
                        67854,
                        8717, 67761, 9178, 34790, 68363, 59843, 14766, 69782, 64718, 65998, 22586, 65213, 68687, 34918,
                        17519,
                        52936, 32677, 65269, 46265, 68128, 52630, 65738, 48054, 36059, 51765, 48555, 48669, 66183,
                        35839, 64433,
                        15806, 22232, 64907, 66164, 6305, 58813, 65081, 61302, 65602, 40858, 68740, 43456, 68652, 53729,
                        65858,
                        65338, 64399, 43228, 64272, 34383, 12411, 54859, 45589, 16251, 64592, 11925, 65481, 63486,
                        68887, 58369,
                        49029, 68398, 27536, 43086, 349, 61060, 39884, 49680, 20357, 23263, 31357, 38533, 30716, 38155,
                        6211,
                        22315, 30887, 18739, 48445, 34534, 30399, 66119, 65281, 41431, 40676, 64978, 51223, 49206,
                        58066, 34613,
                        70086, 57432, 65636, 51518, 64874, 3443, 42000, 6129, 14450, 52579, 30263, 65494, 13767, 68458,
                        21186,
                        50681, 65672, 10810, 37793, 65485, 3645, 65492, 4479, 64892, 13452, 34789, 17652, 2132, 30949,
                        42606,
                        43120, 65458, 65313, 61920, 7495, 11073, 65455, 65415, 51097, 68911, 59075, 57062, 38065, 56739,
                        46110,
                        49281, 65310, 67755, 69870, 67688, 10642, 15031, 40869, 25703, 64632, 64942, 29429, 30956,
                        34798, 66177,
                        65136, 3549, 24666, 15388, 64564, 16084, 44214, 66077, 9397, 66763, 470, 46053, 66085, 64364,
                        65818,
                        57127, 64413, 1824, 618, 44197, 46513, 11745, 70057, 65573, 30990, 69524, 58152, 59575, 65278,
                        67189,
                        64152, 64717, 17474, 40197, 64806, 36755, 64420, 68326, 65073, 64750, 67836, 17110, 64953,
                        21379, 64485,
                        3817, 44177, 69100, 64860, 66539, 4876, 32719, 15114, 64824, 57851, 11742, 45976, 62359, 22462,
                        51289,
                        43563, 34735, 26793, 5260, 20644, 30484, 67051, 56074, 31372, 64836, 64728, 7321, 4383, 35490,
                        48441,
                        39503, 64866, 65911, 25024, 67647, 48467, 68846, 68115, 64751, 14362, 68246, 64968, 65466,
                        26480, 8622,
                        53530, 42394, 14658, 64931, 60076, 29951, 66508, 26452, 66224, 62762, 65456, 66032, 64929, 5280,
                        65218,
                        64971, 64736, 10723, 38374, 54615, 51714, 39155, 64690, 69278, 48797, 56239, 62987, 67251,
                        66120, 49744,
                        65650, 57553, 67311, 51711, 52568, 25741, 20048, 43225, 24049, 43577, 36608, 57214, 67017, 3326,
                        12787,
                        2146, 57705, 67570, 69708, 64725, 64899, 53509, 46054, 33162, 68193, 57406, 67620, 46113, 24299,
                        63494,
                        61278, 68997, 67160, 67373, 68259, 64461, 69139, 64970, 41362, 2074, 49096, 8714, 44665, 22766,
                        48925,
                        25566, 70296, 13522, 58986, 46088, 48935, 65874, 69943, 65963, 69055, 68551, 52455, 69734,
                        67088, 35402,
                        64697, 22553, 38626, 22482, 65182, 53181, 66217, 54183, 69950, 22996, 65687, 41818, 64562,
                        64903, 1618,
                        67003, 64443, 56895, 24515, 31093, 5748, 49799, 65828, 65100, 65407, 60472, 65656, 7881, 64533,
                        22794,
                        58054, 65761, 57261, 66048, 41691, 48876, 9704, 60610, 24230, 4507, 64441, 5669, 64665, 65019,
                        55878,
                        64383, 64944, 36989, 66629, 55423, 40527, 66737, 66062, 66044, 53570, 27876, 64372, 9809, 16520,
                        25307,
                        58657, 67976, 10851, 64752, 65838, 68293, 48143, 55835, 59611, 30772, 400, 40875, 14545, 66481,
                        65437,
                        66729, 52457, 67863, 64829, 67293, 66576, 65050, 29462, 69898, 10510, 3626, 52203, 11996, 67492,
                        66231,
                        36766, 18966, 20460, 69519, 65941, 68422, 28319, 66104, 43407, 49267, 58918, 65156, 29709,
                        65787, 51201,
                        3550, 53659, 45204, 64830, 66820, 11948, 61003, 56633, 69719, 56177, 70155, 69845, 48483, 50316,
                        7409,
                        65368, 65052, 64928, 66734, 51127, 16896, 6294, 26319, 30315, 16974, 68545, 7284, 64655, 65625,
                        15268,
                        14587, 68083, 64512, 40321, 41855, 64795, 64514, 46886, 62817, 49802, 61480, 18740, 51001,
                        51790, 3123,
                        62948, 59313, 21469, 64431, 45641, 35326, 45980, 16960, 68076, 67398, 67646, 45783, 65861,
                        57057, 65960,
                        70167, 31768, 53523, 65129, 65934, 39142, 17935, 10462, 68205, 22989, 11615, 69685, 38272,
                        36499, 15571,
                        30957, 66005, 34483, 8349, 64896, 52684, 70054, 68725, 3473, 65305, 67524, 53282, 45584, 68817,
                        65502,
                        19253, 45377, 64678, 19582, 60553, 46210, 51712, 4584, 64976, 52519, 68870, 63875, 45444, 67886,
                        11710,
                        60950, 43663, 65472, 48866, 63621, 37610, 28716, 65006, 60777, 12283, 2229, 68613, 64691, 64961,
                        66357,
                        63783, 64786, 44286, 44921, 4070, 24346, 4342, 64908, 69396, 43648, 63597, 26693, 3567, 65511,
                        26344,
                        36058, 58779, 65275, 23703, 18275, 65324, 1280, 65020, 69697, 51688, 65328, 67238, 68110, 8795,
                        65069,
                        67529, 2140, 62941, 50471, 59074, 64813, 1953, 31097, 37305, 66757, 65454, 67509, 65848, 46459,
                        32340,
                        68724, 37283, 69819, 66754, 69747, 42871, 63680, 65252, 55776, 33004, 64868, 36324, 66924,
                        65748, 26215,
                        28121, 65584, 19897, 68898, 68104, 41420, 47317, 37477, 67331, 31672, 27063, 49483, 30916,
                        33894, 3835,
                        30945, 55745, 35787, 22264, 31678, 61346, 69764, 60273, 26307, 31611, 47025, 44459, 34243,
                        37862, 33367,
                        9615, 65001, 38396, 53672, 68964, 49472, 38343, 64762, 37037, 64196, 66795, 52424, 69569, 61822,
                        57174,
                        20700, 1738, 40091, 49976, 68703, 42033, 21219, 14893, 28084, 65031, 67053, 53226, 68265, 43412,
                        57508,
                        65112, 53040, 64867, 25483, 65439, 50122, 66171, 50322, 42740, 64381, 57921, 30383, 31202,
                        55122, 68353,
                        26611, 27195, 9316, 25074, 39700, 14339, 57232, 65888, 15884, 52782, 70056, 14117, 64653, 16940,
                        58825,
                        31013, 40597, 8507, 68329, 7787, 63910, 54535, 17816, 54157, 68766, 50344, 9414, 51996, 64799,
                        9563,
                        68170, 53506, 70212, 65337, 69069, 51075, 7808, 64841, 65059, 65017, 66118, 42066, 55873, 65595,
                        67898,
                        65627, 69306, 59040, 8075, 64835, 67910, 65372, 67708, 68621, 11149, 63476, 59592, 64660, 45089,
                        69742,
                        66839, 24135, 61604, 43297, 25382, 55371, 33948, 64712, 64753, 14487, 65954, 65937, 48698,
                        10530, 67137,
                        69075, 65601, 68907, 44686, 35977, 65146, 67705, 20571, 64518, 49610, 68401, 64974, 68836,
                        68140, 67566,
                        65403, 65169, 65468, 55394, 65261, 20690, 25043, 56307, 67580, 65558, 65165, 41949, 62676,
                        13027, 11579,
                        42518, 33659, 69186, 32894, 20365, 62082, 1199, 64740, 10718, 51276, 16689, 31485, 31090, 65853,
                        31803,
                        65334, 32818, 2575, 40936, 33371, 56041, 67011, 65005, 14771, 48185, 65032, 28629, 1945, 16717,
                        70085,
                        20824, 67649, 65108, 25332, 12044, 65705, 35473, 68719, 69002, 65174, 44905, 57315, 67513,
                        47811, 65361,
                        6053, 59514, 51268, 65890, 11545, 64889, 9280, 51481, 69713, 64930, 24689, 64735, 67885, 65871,
                        60468,
                        67340, 14303, 66101, 4145, 42044, 52028, 64856, 6446, 64700, 54812, 14283, 66493, 51450, 70222,
                        65033,
                        17851, 50282, 64774, 69984, 36729, 65416, 31601, 67672, 45354, 32521, 38858, 5764, 63117, 64595,
                        3900,
                        30218, 59605, 35570, 35937, 64757, 37631, 59405, 56866, 1069, 45313, 33822, 64862, 64415, 47372,
                        36327,
                        57845, 47407, 61102, 65981, 66713, 66430, 47721, 1604, 69059, 52467, 43388, 41347, 69085, 51358,
                        56359,
                        66360, 34169, 36871, 65068, 64849, 27968, 57673, 65429, 39076, 68102, 68727, 69835, 44840,
                        40373, 35958,
                        67064, 65260, 53208, 58024, 27414, 21274, 66087, 51920, 65526, 65822, 18874, 34853, 65480,
                        23092, 64876,
                        64561, 43315, 64366, 70174, 39397, 64072, 70084, 65726, 60436, 69711, 70001, 65442, 53023,
                        49975, 40332,
                        25034, 35252, 65143, 65431, 44708, 23623, 67992, 46052, 68127, 40762, 65053, 64746, 44104,
                        65171, 65856,
                        37565, 69771, 61471, 67796, 14520, 64203, 35513, 49797, 34210, 18024, 67865, 24550, 64947,
                        65449, 62159,
                        66406, 58625, 35799, 43366, 35885, 65325, 66578, 66555, 31186, 63489, 27541, 32228, 68819,
                        50019, 64734,
                        68876, 21453, 64695, 46719, 48117, 889, 41384, 16549, 5134, 49328, 38267, 64427, 64887, 64390,
                        64544,
                        20949, 743, 19756, 3961, 16929, 46763, 59597, 29995, 65268, 37246, 62265, 18682, 4353, 52236,
                        66803,
                        65653, 25210, 32169, 22648, 69529, 29386, 25691, 56848, 47620, 42941, 66537, 9326, 63568, 17847,
                        67775,
                        26916, 64769, 65335, 27151, 24680, 40654, 37463, 5862, 65374, 883, 60407, 24057, 64667, 64721,
                        6907,
                        64693, 65484, 64883, 13313, 15321, 65265, 69934, 47747, 34201, 39867, 4595, 69163, 8271, 67547,
                        4079,
                        48423, 46344, 64445, 64869, 23221, 68956, 12054, 12412, 55456, 64648, 66921, 65040, 57336,
                        16276, 34992,
                        60134, 9128, 52659, 21266, 65452, 65308, 59828, 42918, 65258, 52226, 47632, 70101, 64802, 65074,
                        42142,
                        45782, 40195, 32876, 64814, 50381, 11414, 41595, 69575, 30877, 69998, 3690, 51448, 46677, 16158,
                        32821,
                        66902, 43344, 55125, 18276, 24338, 51636, 14588, 46349, 65471, 64145, 43475, 54222, 63492,
                        66001, 53482,
                        34883, 70128, 25505, 56403, 66844, 65127, 25916, 24548, 19671, 47742, 70315, 303, 46291, 55855,
                        67376,
                        51129, 58628, 53150, 68299, 617, 2131, 59809, 60536, 5233, 66395, 17898, 57402, 6087, 56614,
                        62809,
                        5528, 66365, 53160, 65563, 54543, 62675, 65152, 68255, 21378, 66321, 40658, 19287, 31370, 67296,
                        31404,
                        19494, 37848, 56199, 67514, 34525, 69382, 28733, 65715, 61831, 46352, 67515, 66775, 13021,
                        46340, 23679,
                        68166, 42361, 11486, 27318, 53223, 34430, 34581, 43258, 44447, 43480, 67427, 45566, 65070,
                        66560, 46292,
                        53203, 57708, 65950, 53470, 3699, 3121, 68522, 3939, 65451, 48540, 31214, 65815, 64979, 58902,
                        65412,
                        65292, 65063, 5093, 52653, 64914, 15808, 66137, 65684, 18762, 61806, 15645, 65202, 59150, 37896,
                        50194,
                        41598, 4018, 51782, 10627, 48570, 44550, 34862, 66730, 65857, 48778, 69812, 64759, 51744, 35224,
                        47239,
                        64878, 31810, 50241, 62955, 13463, 68158, 2064, 14098, 66971, 4226, 69827, 20162, 65259, 378,
                        66036,
                        36933, 48184, 51453, 65311, 35561, 8276, 17543, 37926, 65038, 65025, 65111, 69815, 64658, 56083,
                        17812,
                        29184, 58930, 64406, 31046, 56896, 36473, 39866, 34662, 56858, 12018, 65164, 27101, 67727,
                        22500, 42230,
                        66382, 58193, 30028, 49397, 39534, 65570, 55800, 33872
                );
        System.out.println("=======" + gitProjectIds.size());
        gitProjectIds.removeIf(projectId -> {
            GitlabProject gitlabProject = gitOperations.getLocalCacheProject(projectId);
            return !isJavaAndMaven(gitlabProject);
        });
        System.out.println("=======" + gitProjectIds.size());
    }

    private boolean isJavaAndMaven(GitlabProject gitlabProject) {
        try {
            boolean javaProject = gitOperations.isJavaProject(gitlabProject.getId());
            if (!javaProject) {
                return false;
            }
        } catch (Throwable e) {
            return false;
        }
        try {
            List<GitlabRepositoryTree> treeList = gitlabApi.getRepositoryTree(gitlabProject, "/", "master", false);
            boolean hasPomXml = treeList.stream().anyMatch(tree -> tree.getName().equals("pom.xml"));
            if (!hasPomXml) {
                return false;
            }
        } catch (Throwable e) {
            log.error("gitlabApi getRepositoryTree error, projectId is {}", gitlabProject.getId(), e);
            return false;
        }
        return true;
    }

    @Test
    public void testApplyPatch() throws IOException {
        boolean master = jGitUtils.cloneProject(
                "*************************:lfe/market/locallife-selection-platform.git",
                new File("/Users/<USER>/Desktop/lixiaoxin-apply-test/"), "feat/activity-refactor",
                "1d8e950d047dfe610ccc076b4a2b10f0d5bcabaa", "");
        Git open = Git.open(new File("/Users/<USER>/Desktop/lixiaoxin-apply-test"));
        InputStream inputStream = kdevApi.getLocalBuildDiffInputStream(1446453L);
        jGitUtils.applyPatch(open, inputStream);
    }

    @Test
    public void testGetDiffFilesPathByLocalBuildId() {
        List<String> diffFiles = kdevApi.getDiffFilesPathByLocalBuildId(69525, 1445324L, 1446453L);
        System.out.println(JSONUtils.serialize(diffFiles));
    }

    @Test
    public void testGitApiClient() {
        IncrementChangedFilesRequest request = new IncrementChangedFilesRequest();
        // request.setGitProjectId(24049);
        // request.setBranch("master");
        // request.setSourceCommitId("00e0274280442757ea8bfa96a22d8d388f3b5128");
        // request.setCurrentCommitId("00e0274280442757ea8bfa96a22d8d388f3b5128");
        // request.setIncrement(true);
        // request.setMrId(0);
        // request.setKspBuildId(0L);
        // request.setLocalBuildId(0L);
        String json = "{\"currentCommitId\":\"2c12eaf5\",\"gitProjectId\":3443,\"increment\":true,\"branch\":\"master\",\"kspBuildId\":\"39188009\",\"moreProcessModulePath\":\"kuaishou-kmovie-api\",\"mrId\":0,\"localBuildId\":0,\"sourceCommitId\":\"13477b5b092d78432aeb2b6b5cb11d53c61a2c6b\"}";
        request = JSONUtils.deserialize(json, IncrementChangedFilesRequest.class);
        IncrementChangedFilesResponse changedFiles = sonarPluginService.getChangedFiles(request);
        Assert.assertNotNull(changedFiles);
        System.out.println(JSONUtils.serialize(changedFiles));
    }

    @Test
    public void testGetMrDiff() {
        System.out.println(
                sonarPluginService.getCachedMrDiffList(24049, 355, "7ade77ac8d74cbbf77590a828f5a2ab1937543ff"));
    }

}
