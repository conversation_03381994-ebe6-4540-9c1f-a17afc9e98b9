package com.kuaishou.serveree.themis;

import static com.kuaishou.serveree.themis.component.constant.platform.Index.DUPLICATION_BLOCK_COUNT;
import static com.kuaishou.serveree.themis.component.constant.platform.Index.DUPLICATION_LINE_COUNT;
import static com.kuaishou.serveree.themis.component.constant.platform.Index.LINE_COUNT;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.entity.platform.DuplicationDetail;
import com.kuaishou.serveree.themis.component.entity.platform.DuplicationFileDetail;
import com.kuaishou.serveree.themis.component.entity.platform.FileDetail;
import com.kuaishou.serveree.themis.component.entity.platform.FileDuplication;
import com.kuaishou.serveree.themis.component.entity.platform.FileFunction;
import com.kuaishou.serveree.themis.component.entity.platform.FileInfo;
import com.kuaishou.serveree.themis.component.entity.platform.FileIssue;
import com.kuaishou.serveree.themis.component.entity.platform.FileMeasure;
import com.kuaishou.serveree.themis.component.service.platform.PlatformSkyeyeReportService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.SkyeyeLocalReportRequest;

import cn.hutool.core.io.FileUtil;

/**
 * <AUTHOR>
 * @since 2022/7/15 2:47 PM
 */
public class SkyeyeTest extends SpringBaseTest {

    @Autowired
    private PlatformSkyeyeReportService platformSkyeyeReportService;

    @Test
    public void testReport() {
        FileDetail fileDetail0 = getFileDetail0();
        FileDetail fileDetail1 = getFileDetail1();
        FileDetail fileDetail2 = getFileDetail2();
        FileInfo fileInfo = FileInfo.builder()
                .fileList(Lists.newArrayList(fileDetail0, fileDetail1, fileDetail2))
                .build();

        DuplicationFileDetail duplicationFileDetail0 = DuplicationFileDetail.builder()
                .path("src/pc/page/brief/components/ImageTextSpread/order.ts")
                .startLine(12)
                .endLine(32)
                .startOffset(12)
                .endOffset(14)
                .build();

        DuplicationFileDetail duplicationFileDetail1 = DuplicationFileDetail.builder()
                .path("src/pc/routes.ts")
                .startLine(13)
                .endLine(33)
                .startOffset(13)
                .endOffset(15)
                .build();

        DuplicationFileDetail duplicationFileDetail2 = DuplicationFileDetail.builder()
                .path("src/pc/page/brief/components/ImageTextSpread/Card.tsx")
                .startLine(14)
                .endLine(34)
                .startOffset(14)
                .endOffset(16)
                .build();

        DuplicationDetail duplicationDetail0 = DuplicationDetail.builder()
                .content(";\nimport { QueryParams } from '../index';\nimport less from './index.module.less';\n\ntype "
                        + "IDateRange = [Moment, Moment] | null;\n\ninterface IProps {\n    defaultTimezone: "
                        + "string;\n    queryParams: QueryParams;\n    onChange: (changeValues: "
                        + "Partial<QueryParams>, timezone: string) => void;\n    onUploadDrawerShow: () => "
                        + "void;\n    materialTypeOptionList")
                .fileList(Lists.newArrayList(duplicationFileDetail0, duplicationFileDetail1))
                .build();
        DuplicationDetail duplicationDetail1 = DuplicationDetail.builder()
                .content(";\n}\n\nconst QueryForm: React.FunctionComponent<IProps> = (props) => {\n    const { "
                        + "defaultTimezone, queryParams, onChange } = props;\n    const intl = useIntl();\n  "
                        + "  const rangeNameList = React.useMemo(\n        () => [QuickRange.today, "
                        + "QuickRange.lastday, QuickRange.last7day, QuickRange.last30day, QuickRange"
                        + ".last3month],\n        [],\n    );\n    const { createTimeRange } = queryParams ||"
                        + " {};\n    const [timezone, setTimezone] = React.useState(defaultTimezone);\n    "
                        + "const [createRange, setCreateRange] = React.useState<IDateRange>([\n        "
                        + "utc2Moment(createTimeRange?.[0], defaultTimezone),\n        utc2Moment"
                        + "(createTimeRange?.[1], defaultTimezone),\n    ]);\n\n    const "
                        + "handleCreateRangeChange = React.useCallback(\n        (values: RangeValue<Moment>,"
                        + " formatString?: [string, string]) => {\n            if (values?.[0] && values?"
                        + ".[1]) {\n                const start = values[0].clone();\n                const "
                        + "end = values[1].clone();\n                setCreateRange([start, end]);\n         "
                        + "       onChange(\n                    {\n                        createTimeRange: "
                        + "[start.startOf('day').valueOf(), end.endOf('day').valueOf()],\n                   "
                        + " },\n                    timezone,\n                );\n            } else {\n    "
                        + "            setCreateRange(null);\n                onChange(\n                    "
                        + "{\n                        createTimeRange: null,\n                    },\n       "
                        + "             timezone,\n                );\n            }\n        },\n        "
                        + "[onChange, timezone],\n    );\n\n    const handleTimezoneChange = React"
                        + ".useCallback(\n        (newTimezone: string) => {\n            const "
                        + "newCreateRange: IDateRange = createRange\n                ? [changeMomentTimezone"
                        + "(createRange[0], newTimezone), changeMomentTimezone(createRange[1], newTimezone)"
                        + "]\n                : null;\n            setCreateRange(newCreateRange);\n         "
                        + "   setTimezone(newTimezone);\n            // 返回具有时区的moment对象\n            onChange"
                        + "(\n                {\n                    createTimeRange: newCreateRange\n       "
                        + "                 ? [newCreateRange[0].startOf('day').valueOf(), newCreateRange[1]"
                        + ".endOf('day').valueOf()]\n                        : null,\n                },\n   "
                        + "             newTimezone,\n            );\n        },\n        [createRange, "
                        + "onChange],\n    );\n\n    return (\n        <div className={less.formWrapper}>\n  "
                        + "          <div className={less.formItem}>\n                <Button "
                        + "type=\"primary\" style={{ width: 80 }} onClick={props.onUploadDrawerShow}>\n      "
                        + "              <FormattedMessage defaultMessage=\"Upload\" />\n                "
                        + "</Button>\n            </div>\n            {")
                .fileList(Lists.newArrayList(duplicationFileDetail0, duplicationFileDetail2))
                .build();
        FileDuplication fileDuplication = FileDuplication.builder()
                .duplicationList(Lists.newArrayList(duplicationDetail0, duplicationDetail1))
                .build();
        SkyeyeLocalReportRequest reportRequest = SkyeyeLocalReportRequest.builder()
                .baseId(725333L)
                .fileInfo(fileInfo)
                .duplicationInfo(fileDuplication)
                .build();
        String serialize = JSONUtils.serialize(reportRequest);
        System.out.println(serialize);
        platformSkyeyeReportService.dataReport(reportRequest);
    }

    private FileDetail getFileDetail2() {
        List<FileMeasure> fileMeasures = Lists.newArrayList();
        fileMeasures.add(FileMeasure.builder()
                .metricKey(LINE_COUNT.getKey())
                .metricValue("122")
                .build());

        fileMeasures.add(FileMeasure.builder()
                .metricKey(DUPLICATION_LINE_COUNT.getKey())
                .metricValue("12")
                .build());

        fileMeasures.add(FileMeasure.builder()
                .metricKey(DUPLICATION_BLOCK_COUNT.getKey())
                .metricValue("12")
                .build());

        List<FileFunction> fileFunctions = Lists.newArrayList();
        fileFunctions.add(FileFunction.builder()
                .functionName("myfunction1")
                .complexity(12)
                .startLine(12)
                .endLine(43)
                .startOffset(211)
                .endOffset(233)
                .lineCount(123)
                .logicLineCount(231)
                .halsteadVolume(214)
                .build());

        fileFunctions.add(FileFunction.builder()
                .functionName("myfunction2")
                .complexity(13)
                .startLine(15)
                .endLine(42)
                .startOffset(231)
                .endOffset(433)
                .lineCount(124)
                .logicLineCount(221)
                .halsteadVolume(211)
                .build());

        List<FileIssue> fileIssues = Lists.newArrayList();
        fileIssues.add(FileIssue.builder()
                .ruleKey("@typescript-eslint/no-unused-vars")
                .type("CODE_SMELL")
                .severity("COMMON")
                .startLine(111)
                .endLine(123)
                .startOffset(222)
                .endOffset(333)
                .message("'store' is defined but never used.")
                .build());
        fileIssues.add(FileIssue.builder()
                .ruleKey("@typescript-eslint/no-unused-vars")
                .type("CODE_SMELL")
                .severity("COMMON")
                .startLine(111)
                .endLine(123)
                .startOffset(2422)
                .endOffset(3323)
                .message("'nextState' is defined but never used. Allowed unused args must match /^_/u.")
                .build());
        fileIssues.add(FileIssue.builder()
                .ruleKey("import/newline-after-import")
                .type("CODE_SMELL")
                .severity("COMMON")
                .startLine(111)
                .endLine(1223)
                .startOffset(2232)
                .endOffset(3331)
                .message("Expected 1 empty line after require statement not followed by another require.")
                .build());
        return FileDetail.builder()
                .path("src/pc/page/brief/components/ImageTextSpread/Card.tsx")
                .fileMeasures(fileMeasures)
                .functions(fileFunctions)
                .issueList(fileIssues)
                .build();
    }

    private FileDetail getFileDetail1() {
        List<FileMeasure> fileMeasures = Lists.newArrayList();
        fileMeasures.add(FileMeasure.builder()
                .metricKey(LINE_COUNT.getKey())
                .metricValue("1222")
                .build());

        fileMeasures.add(FileMeasure.builder()
                .metricKey(DUPLICATION_LINE_COUNT.getKey())
                .metricValue("123")
                .build());

        fileMeasures.add(FileMeasure.builder()
                .metricKey(DUPLICATION_BLOCK_COUNT.getKey())
                .metricValue("124")
                .build());

        List<FileFunction> fileFunctions = Lists.newArrayList();
        fileFunctions.add(FileFunction.builder()
                .functionName("myfunction3")
                .complexity(12)
                .startLine(12)
                .endLine(43)
                .startOffset(211)
                .endOffset(233)
                .lineCount(123)
                .logicLineCount(231)
                .halsteadVolume(214)
                .build());

        fileFunctions.add(FileFunction.builder()
                .functionName("myfunction4")
                .complexity(13)
                .startLine(15)
                .endLine(42)
                .startOffset(231)
                .endOffset(433)
                .lineCount(124)
                .logicLineCount(221)
                .halsteadVolume(211)
                .build());

        List<FileIssue> fileIssues = Lists.newArrayList();
        fileIssues.add(FileIssue.builder()
                .ruleKey("@typescript-eslint/no-unused-vars")
                .type("CODE_SMELL")
                .severity("COMMON")
                .startLine(111)
                .endLine(123)
                .startOffset(222)
                .endOffset(333)
                .message("'store' is defined but never used.")
                .build());
        fileIssues.add(FileIssue.builder()
                .ruleKey("@typescript-eslint/no-unused-vars")
                .type("CODE_SMELL")
                .severity("COMMON")
                .startLine(111)
                .endLine(123)
                .startOffset(2422)
                .endOffset(3323)
                .message("'nextState' is defined but never used. Allowed unused args must match /^_/u.")
                .build());
        fileIssues.add(FileIssue.builder()
                .ruleKey("import/newline-after-import")
                .type("CODE_SMELL")
                .severity("COMMON")
                .startLine(111)
                .endLine(1223)
                .startOffset(2232)
                .endOffset(3331)
                .message("Expected 1 empty line after require statement not followed by another require.")
                .build());
        return FileDetail.builder()
                .path("src/pc/routes.ts")
                .fileMeasures(fileMeasures)
                .functions(fileFunctions)
                .issueList(fileIssues)
                .build();
    }

    private FileDetail getFileDetail0() {
        List<FileMeasure> fileMeasures = Lists.newArrayList();
        fileMeasures.add(FileMeasure.builder()
                .metricKey(LINE_COUNT.getKey())
                .metricValue("1222")
                .build());

        fileMeasures.add(FileMeasure.builder()
                .metricKey(DUPLICATION_LINE_COUNT.getKey())
                .metricValue("1323")
                .build());

        fileMeasures.add(FileMeasure.builder()
                .metricKey(DUPLICATION_BLOCK_COUNT.getKey())
                .metricValue("1244")
                .build());

        List<FileFunction> fileFunctions = Lists.newArrayList();
        fileFunctions.add(FileFunction.builder()
                .functionName("myfunction5")
                .complexity(12)
                .startLine(12)
                .endLine(43)
                .startOffset(211)
                .endOffset(233)
                .lineCount(123)
                .logicLineCount(231)
                .halsteadVolume(214)
                .build());

        fileFunctions.add(FileFunction.builder()
                .functionName("myfunction6")
                .complexity(13)
                .startLine(15)
                .endLine(42)
                .startOffset(231)
                .endOffset(433)
                .lineCount(124)
                .logicLineCount(221)
                .halsteadVolume(211)
                .build());

        List<FileIssue> fileIssues = Lists.newArrayList();
        fileIssues.add(FileIssue.builder()
                .ruleKey("@typescript-eslint/no-unused-vars")
                .type("CODE_SMELL")
                .severity("COMMON")
                .startLine(111)
                .endLine(123)
                .startOffset(222)
                .endOffset(333)
                .message("'store' is defined but never used.")
                .build());
        fileIssues.add(FileIssue.builder()
                .ruleKey("@typescript-eslint/no-unused-vars")
                .type("CODE_SMELL")
                .severity("COMMON")
                .startLine(111)
                .endLine(123)
                .startOffset(2422)
                .endOffset(3323)
                .message("'nextState' is defined but never used. Allowed unused args must match /^_/u.")
                .build());
        fileIssues.add(FileIssue.builder()
                .ruleKey("import/newline-after-import")
                .type("CODE_SMELL")
                .severity("COMMON")
                .startLine(111)
                .endLine(1223)
                .startOffset(2232)
                .endOffset(3331)
                .message("Expected 1 empty line after require statement not followed by another require.")
                .build());
        return FileDetail.builder()
                .path("src/pc/page/brief/components/ImageTextSpread/order.ts")
                .fileMeasures(fileMeasures)
                .functions(fileFunctions)
                .issueList(fileIssues)
                .build();
    }

    @Test
    public void testReportJson() {
        String s = FileUtil.readUtf8String("/Users/<USER>/Desktop/param.json");
        SkyeyeLocalReportRequest reportRequest = JSONUtils.deserialize(s, SkyeyeLocalReportRequest.class);
        reportRequest.setBaseId(616L);
        platformSkyeyeReportService.dataReport(reportRequest);
    }

}
