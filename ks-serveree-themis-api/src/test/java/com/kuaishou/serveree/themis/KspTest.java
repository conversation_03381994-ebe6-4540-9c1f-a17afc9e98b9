package com.kuaishou.serveree.themis;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.fasterxml.jackson.core.type.TypeReference;
import com.kuaishou.serveree.themis.component.client.ksp.KspApi;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineDataRun;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineGetProductVersionsRequest;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineGetProductVersionsResponse;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineGetResultRequest;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineGetResultResponse;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.response.ReturnMessageVo;

import cn.hutool.http.HttpResponse;

/**
 * <AUTHOR>
 * @since 2020/11/16 4:04 下午
 */
public class KspTest extends SpringBaseTest {

    @Autowired
    private KspApi kspApi;

    @Test
    public void test01() throws Exception {
        KspPipelineDataRun pipelineParamCovers = kspApi.getPipelineParamCovers(527703L);
        System.out.println(JSONUtils.serialize(pipelineParamCovers));
    }

    @Test
    public void test02() {
        HttpResponse pipelineResult = kspApi.getPipelineResultV4(
                KspPipelineGetResultRequest.builder()
                        .kspBuildId(44067849L)
                        .kspPipelineId(788015L)
                        .build()
        );
        String body = pipelineResult.body();
        ReturnMessageVo<KspPipelineGetResultResponse> deserialize = JSONUtils.deserialize(body,
                new TypeReference<ReturnMessageVo<KspPipelineGetResultResponse>>() {
                });
        System.out.println(JSONUtils.serialize(deserialize));
        KspPipelineGetResultResponse data = deserialize.getData();
        System.out.println(data);
    }

    @Test
    public void test03() {
        KspPipelineGetProductVersionsResponse productVersions = kspApi.getProductVersions(
                KspPipelineGetProductVersionsRequest.builder()
                        .buildId(925336L)
                        .build()
        );
        System.out.println(JSONUtils.serialize(productVersions));
    }
}
