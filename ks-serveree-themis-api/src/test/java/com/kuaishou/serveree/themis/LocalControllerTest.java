package com.kuaishou.serveree.themis;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import org.gitlab.api.GitlabAPI;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.kuaishou.serveree.themis.api.controller.LocalController;
import com.kuaishou.serveree.themis.component.constant.platform.CheckProfileType;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformLanguageEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.entity.platform.LanguageSetting;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.ProjectBatchInitRequest;
import com.kuaishou.serveree.themis.component.vo.request.ProjectProfileBatchUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.SonarProfileInitRequest;

/**
 * <AUTHOR>
 * @since 2021/2/3 3:42 下午
 */
public class LocalControllerTest extends SpringBaseTest {

    @Autowired
    private LocalController localController;

    @Autowired
    private GitlabAPI gitlabApi;

    @Test
    public void test01() {
        localController.delCache();
    }

    @Test
    public void initRule() {
        localController.skyeyeRuleInit();
    }

    @Test
    public void initProfileRuleRelation() {
        localController.skyeyeProfileInit();
    }

    @Test
    public void testRuleClean() {
        localController.issueClean();
    }

    @Test
    public void test() {
        List<RepoCreateRequest> createRequests = Lists.newArrayList();

        RepoCreateRequest createRequest = new RepoCreateRequest();
        createRequest.setScanner(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner());
        createRequest.setGitProjectId(35851);
        createRequest.setBranch("master");
        createRequest.setRepoUrl("*************************:serveree/ks-pipeline-compile.git");
        LanguageSetting languageSetting = new LanguageSetting();
        languageSetting.setLanguage(PlatformLanguageEnum.JAVA.getName());
        languageSetting.setVersion("8");
        createRequest.setLanguageSetting(languageSetting);
        createRequest.setTriggerCron("0 0 0 0 0 0");
        createRequest.setProfileName("lxx-test-01");
        createRequests.add(createRequest);

        RepoCreateRequest createRequest1 = new RepoCreateRequest();
        createRequest1.setScanner(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner());
        createRequest1.setGitProjectId(24049);
        createRequest1.setBranch("master");
        createRequest1.setRepoUrl("*************************:serveree/ks-serveree-themis.git");
        LanguageSetting languageSetting1 = new LanguageSetting();
        languageSetting1.setLanguage(PlatformLanguageEnum.JAVA.getName());
        languageSetting1.setVersion("8");
        createRequest1.setLanguageSetting(languageSetting1);
        createRequest1.setTriggerCron("0 0 0 0 0 0");
        createRequest1.setProfileName("lxx-test-01");
        createRequests.add(createRequest1);

        ProjectBatchInitRequest projectBatchInitRequest = new ProjectBatchInitRequest();
        projectBatchInitRequest.setRepoCreateRequestList(createRequests);
        localController.platformBatchInitProject(projectBatchInitRequest);
    }

    @Test
    public void testInitRule() {
        localController.sonarRuleInit("AYSDaa9JR1HZdrX-N7Ur", PlatformScannerEnum.SONAR_SCANNER_NEW.getScanner(), "go");
    }

    @Test
    public void testSonarLabelInit() {
        localController.sonarLabelInit("992de0e7c0dd46ffa98c171d8e7d3185");
    }

    @Test
    public void testSonarProfileInit() {
        SonarProfileInitRequest sonarProfileInitRequest = new SonarProfileInitRequest();
        sonarProfileInitRequest.setKey("AX29xQ-insuXAVGTRTeU");
        sonarProfileInitRequest.setName("Sonar way");
        localController.sonarProfileInit(sonarProfileInitRequest);
    }

    @Test
    public void testAdInit() {
        List<Integer> needInitProjectIds =
                Lists.newArrayList(31672, 31458, 41420, 11521, 25558, 3690, 29712, 41420, 5093, 6053, 51124, 33675,
                        40489, 3882, 36451, 38858, 550, 26065, 19866, 9108, 40551, 7773, 43932, 8047, 4374, 40255,
                        40591, 306, 5202, 10632, 17037, 32305, 31672, 6162, 43439, 35570, 6892, 34798, 40662, 38420,
                        48298, 14497, 32169, 40209, 40091, 41933, 48117, 34705, 31816, 55454, 43038, 40194);

        List<RepoCreateRequest> createRequests = Lists.newArrayList();

        for (Integer needInitProjectId : needInitProjectIds) {
            RepoCreateRequest createRequest = new RepoCreateRequest();
            createRequest.setScanner(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner());
            createRequest.setGitProjectId(needInitProjectId);
            createRequest.setBranch("master");
            String sshUrl;
            try {
                sshUrl = gitlabApi.getProject(needInitProjectId).getSshUrl();
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            createRequest.setRepoUrl(sshUrl);
            LanguageSetting languageSetting = new LanguageSetting();
            languageSetting.setLanguage(PlatformLanguageEnum.JAVA.getName());
            languageSetting.setVersion("11");
            createRequest.setLanguageSetting(languageSetting);
            createRequest.setTriggerCron("0 0 00 ? * MON,WED,FRI,SUN,SAT,THU,TUE");
            createRequest.setProfileName("Sonar way [kuaishou]");
            createRequests.add(createRequest);
        }

        ProjectBatchInitRequest projectBatchInitRequest = new ProjectBatchInitRequest();
        projectBatchInitRequest.setRepoCreateRequestList(createRequests);

        System.out.println(JSONUtils.serialize(projectBatchInitRequest));

    }

    @Test
    public void testKformatInit() {
        localController.kformatRuleInit();
    }

    @Test
    public void issueLinkFix() {
        localController.pluginSonarIssueLinkFix();
    }

    @Test
    public void linkFix() {
        localController.javaSummaryIssueLinkFix();
    }

    @Test
    public void testFixAdIssue() {
        localController.javaSummaryIssueChangeResolve();
    }

    @Test
    public void testSonarJavaSettingSync() {
        localController.sonarJavaSettingSync();
    }

    @Test
    public void testDeleteDuplicateBranch(){
        localController.duplicateBranch();
    }

    @Test
    public void testGroupPanelData() {
        localController.oneMonthAgoGroupPanelData();
    }

    @Test
    public void testBatchUpdateProfile() {
        HashSet<Integer> gitProjectIds =
                Sets.newHashSet(43439, 40489, 18338, 52668, 56751, 61102, 63732, 7773, 31810, 32305, 40551, 69415,
                        69658, 74303, 77525, 25558, 3690, 41422, 5093, 29712, 6467, 25558, 6053, 43412, 49976, 49975,
                        51124, 7773, 43439, 36451, 32305);
        String profileName = "de2072ea995d49c1993167cc829c99d2"; // 商业化资金服务端
        Integer profileType = CheckProfileType.PIPELINE.getType();
        List<RepoProfileUpdateRequest> profileUpdateRequestList = new ArrayList<>();
        for (Integer gitProjectId : gitProjectIds) {
            profileUpdateRequestList.add(
                    RepoProfileUpdateRequest.builder()
                            .gitProjectId(gitProjectId)
                            .profileName(profileName)
                            .profileType(profileType)
                            .build()
            );
        }
        System.out.println(JSONUtils.serialize(profileUpdateRequestList));
        ProjectProfileBatchUpdateRequest request = new ProjectProfileBatchUpdateRequest();
        request.setProfileUpdateRequestList(profileUpdateRequestList);
        localController.platformRepoBatchUpdateProfile(request);
    }

}
