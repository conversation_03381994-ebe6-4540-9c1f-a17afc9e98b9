package com.kuaishou.serveree.themis;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.component.entity.quality.QualityRule;
import com.kuaishou.serveree.themis.component.service.QualityRuleService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

/**
 * <AUTHOR>
 * @since 2021/7/29 5:27 下午
 */
public class RuleTest extends SpringBaseTest {

    @Autowired
    private QualityRuleService qualityRuleService;

    @Test
    public void testRuleList(){
        List<QualityRule> qualityRules = qualityRuleService.listQualityRules(4);
        System.out.println(JSONUtils.serialize(qualityRules));
    }

}
