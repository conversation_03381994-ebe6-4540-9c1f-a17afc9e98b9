package com.kuaishou.serveree.themis;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Maps;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisLock;
import com.kuaishou.serveree.themis.component.constant.redis.KsRedisPrefixConstant;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

/**
 * <AUTHOR>
 * @since 2021/1/15 5:01 下午
 */
public class RedisTest extends SpringBaseTest {

    @Autowired
    private KsRedisClient ksRedisClient;

    @Autowired
    private KsRedisLock ksRedisLock;

    private static final DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Test
    public void test01() {
        long pipelineId = 1006414;
        Long buildId = 10026L;
        int ONE_HOUR_SECONDS = 60 * 60;
        String kspPipelineName = "aaaaafaf";

        // 检查时长大于目前涵盖的stage
        String timingKey = KsRedisPrefixConstant.PLUGIN_CHECK_MORE_TIME_TOTAL_KEY + pipelineId + ":" + buildId;
        // 每10秒请求一次 自增10 会有第一次的10s误差
        ksRedisClient.sync().incrby(timingKey, 10);
        // 为自增时间设置一个最后自增时的1小时失效时间
        ksRedisClient.sync().expire(timingKey, ONE_HOUR_SECONDS);
        String moreTotalTime = ksRedisClient.sync().get(timingKey);
        Long totalTime = Long.valueOf(moreTotalTime);
        String totalKey = KsRedisPrefixConstant.PLUGIN_CHECK_MORE_TIME + ":" + YYYYMMDD.format(LocalDate.now());
        String pipelineHashMap = ksRedisClient.sync().hget(totalKey, kspPipelineName);
        Map<String, Long> buildIdHashMap = Maps.newHashMap();
        if (StringUtils.isNotEmpty(pipelineHashMap)) {
            buildIdHashMap = JSONUtils.deserializeMap(pipelineHashMap, String.class, Long.class);
        }
        buildIdHashMap.put(buildId.toString(), totalTime);
        ksRedisClient.sync().hset(totalKey, kspPipelineName, JSONUtils.serialize(buildIdHashMap));
    }

    @Test
    public void test02() {
        Long aaaaa = ksRedisClient.sync().exists(KsRedisPrefixConstant.PLUGIN_CHECK_MORE_TIME);
        System.out.println(aaaaa);
    }

    @Test
    public void test03() {
        String redisKey = "aaaaa";
        for (int i = 0; i < 100; i++) {
            Long incr = ksRedisClient.sync().incr(redisKey);
            System.out.println(incr);
            String s = ksRedisClient.sync().get(redisKey);
            System.out.println(s);
        }
    }

    @Test
    public void testNx() {
        final String redisKey = KsRedisPrefixConstant.PLUGIN_KIM_NOTICE_PREFIX + 10086 + ":" + "master";

        boolean lock = ksRedisLock.lock(redisKey, TimeUnit.DAYS.toMillis(7));
        System.out.println(lock);
    }

    @Test
    public void testDel(){
        String redisKey = KsRedisPrefixConstant.PLATFORM_CODE_QUALITY_SCORE_INFO + ":" + 44267 + ":" + "master";
        ksRedisLock.forceUnlock(redisKey);
    }
}
