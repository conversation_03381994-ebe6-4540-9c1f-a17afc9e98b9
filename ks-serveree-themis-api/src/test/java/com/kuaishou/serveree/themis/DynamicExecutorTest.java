package com.kuaishou.serveree.themis;

import java.util.concurrent.ExecutorService;

import javax.annotation.Resource;

import org.junit.Test;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-26
 */
public class DynamicExecutorTest extends SpringBaseTest {

    @Resource
    private ExecutorService logExecutor;

    @Test
    public void testDynamicLogExecutor() {
        logExecutor.submit(() -> System.out.println("hello"));
        while (true) {
            System.out.println(logExecutor);
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
