package com.kuaishou.serveree.themis;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Sets;
import org.gitlab.api.GitlabAPI;
import org.gitlab.api.models.GitlabProject;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.client.git.SelfGitApi;
import com.kuaishou.serveree.themis.component.common.entity.Az2DataAllIpList;
import com.kuaishou.serveree.themis.component.common.entity.ScanCase;
import com.kuaishou.serveree.themis.component.common.entity.ScanFilter;
import com.kuaishou.serveree.themis.component.common.entity.ScanPlan;
import com.kuaishou.serveree.themis.component.constant.statics.ScanCaseType;
import com.kuaishou.serveree.themis.component.constant.statics.ScanFilterType;
import com.kuaishou.serveree.themis.component.entity.git.SelfGitDetailRequest;
import com.kuaishou.serveree.themis.component.entity.git.SelfGitProject;
import com.kuaishou.serveree.themis.component.entity.statics.ScanProjectInfo;
import com.kuaishou.serveree.themis.component.entity.statics.ScanProjectInfo.ScanGroupInfo;
import com.kuaishou.serveree.themis.component.service.ScanCaseService;
import com.kuaishou.serveree.themis.component.service.ScanFilterService;
import com.kuaishou.serveree.themis.component.service.ScanPlanService;
import com.kuaishou.serveree.themis.component.service.db.Az2DataAllIpListService;
import com.kuaishou.serveree.themis.component.service.statics.LocalStringMatchScanService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.SponsorCodeScanRequest;

/**
 * <AUTHOR>
 * @since 2021/10/29 4:58 下午
 */
public class StringMatchScanTest extends SpringBaseTest {
    private static final String PATH_CASE = "";
    @Autowired
    private ScanCaseService scanCaseService;

    @Autowired
    private LocalStringMatchScanService localStringMatchScanService;

    @Autowired
    private ScanPlanService scanPlanService;

    @Autowired
    private ScanFilterService scanFilterService;

    @Autowired
    private GitlabAPI gitlabAPI;

    @Autowired
    private SelfGitApi selfGitApi;

    @Test
    public void test01() {
        localStringMatchScanService.startUp();
    }

    @Autowired
    private Az2DataAllIpListService az2DataAllIpListService;

    @Test
    public void insertPlan() {
        System.out.println("cny2022-zhaojinxiang-01.dev.kwaidc.com,************".replace(".", "\\\\."));
        List<Az2DataAllIpList> az2DataAllIpLists = az2DataAllIpListService.listByPDate(LocalDateTime.of(2023,12,5,0,0));
        for (String string : az2DataAllIpLists.stream().map(Az2DataAllIpList::getHostname)
                .distinct()
                .filter(s -> !StringUtils.isBlank(s))
                // .skip(50)
                .limit(1000)
                .map(s -> s.replace(".", "\\\\.")).collect(
                        Collectors.toList())) {
            SponsorCodeScanRequest sponsorCodeScanRequest = new SponsorCodeScanRequest();
            sponsorCodeScanRequest.setScanCases("\"" + string + "\"");
            // sponsorCodeScanRequest.setGitProjectIds("25093");
            sponsorCodeScanRequest.setUserName("lixiaoxin");
            sponsorCodeScanRequest.setAllProjects(true);
            localStringMatchScanService.sponsorCodeScan(sponsorCodeScanRequest);
        }
        localStringMatchScanService.scanInterfacePlans();
        // SponsorCodeScanRequest sponsorCodeScanRequest = new SponsorCodeScanRequest();
        // sponsorCodeScanRequest.setScanCases("getIpv4String");
        // sponsorCodeScanRequest.setGitProjectIds("25093");
        // sponsorCodeScanRequest.setUserName("lixiaoxin");
        // //        sponsorCodeScanRequest.setGitGroupIds();
        // localStringMatchScanService.sponsorCodeScan(sponsorCodeScanRequest);
    }

    @Test
    public void testScanInterface() {
        localStringMatchScanService.scanInterfacePlans();
    }

    @Test
    public void test02() throws IOException {
        GitlabProject project = gitlabAPI.getProject(6710);
        String httpUrl = project.getHttpUrl();
        System.out.println(httpUrl);
    }

    @Test
    public void setScanCase() {
        List<ScanCase> list = new ArrayList<>();
        // 匹配字段 拓展字段 匹配类型
        list.add(new ScanCase("merchant/shop/home", "", 3));
        list.add(new ScanCase("merchant/shop/index", "", 3));
        list.add(new ScanCase("merchant/shop/list", "", 3));
        list.add(new ScanCase("merchant/address/add", "", 3));
        list.add(new ScanCase("merchant/address/edit", "", 3));
        list.add(new ScanCase("merchant/address", "", 3));
        list.add(new ScanCase("merchant/shop/order/new", "", 3));
        list.add(new ScanCase("page/trade-buyer/order/purchase", "", 3));
        list.add(new ScanCase("merchant/shop/detail", "", 3));
        list.add(new ScanCase("merchant/order/detail", "", 3));
        list.add(new ScanCase("merchant/shop/order", "", 3));
        list.add(new ScanCase("merchant/shop/detail/comment", "", 3));
        list.add(new ScanCase("page/kwaishop-c-logistics", "", 3));
        list.add(new ScanCase("merchant/preview/image", "", 3));
        list.add(new ScanCase("merchant/order/comment/multi-additional", "", 3));
        list.add(new ScanCase("merchant/order/comment/multi-add", "", 3));
        scanCaseService.saveOrUpdateBatch(list);
    }

    @Test
    public void setScanCase4() {
        List<ScanCase> list = new ArrayList<>();
        // 匹配字段 拓展字段 匹配类型
        list.add(new ScanCase("eckwai.com", "", 4));
        list.add(new ScanCase("tx-ec.static.yximgs.com", "", 4));
        list.add(new ScanCase("ali-ec.static.yximgs.com", "", 4));
        list.add(new ScanCase("js-ec.static.yximgs.com ", "", 4));
        scanCaseService.saveOrUpdateBatch(list);
    }

    @Test
    public void testPathScan() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());

        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("电商");
        // 配置user项目
        scanProjectInfo.setScanUserProjects(Lists.newArrayList(34));
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();

        ScanGroupInfo scanGroupInfo = new ScanGroupInfo();
        scanGroupInfo.setLogicGroupName("后端：plateco-dev");
        scanGroupInfo.setGroupId(2428);
        scanGroupInfoList.add(scanGroupInfo);

        ScanGroupInfo scanGroupInfo1 = new ScanGroupInfo();
        scanGroupInfo1.setLogicGroupName("前端：plateco-dev-fe");
        scanGroupInfo1.setGroupId(3926);
        scanGroupInfoList.add(scanGroupInfo1);

        ScanGroupInfo scanGroupInfo2 = new ScanGroupInfo();
        scanGroupInfo2.setLogicGroupName("客户端：merchant");
        scanGroupInfo2.setGroupId(12651);
        scanGroupInfoList.add(scanGroupInfo2);

        ScanGroupInfo scanGroupInfo0 = new ScanGroupInfo();
        scanGroupInfo0.setLogicGroupName("客户端：gifshow-android");
        scanGroupInfo0.setGroupId(0);
        scanGroupInfo0.setProjectIds(Lists.newArrayList(34));
        scanGroupInfoList.add(scanGroupInfo0);

        ScanGroupInfo scanGroupInfo3 = new ScanGroupInfo();
        scanGroupInfo3.setLogicGroupName("客户端：merchant");
        scanGroupInfo3.setGroupId(3826);
        scanGroupInfoList.add(scanGroupInfo3);

        ScanGroupInfo scanGroupInfo4 = new ScanGroupInfo();
        scanGroupInfo4.setLogicGroupName("客户端：ios");
        scanGroupInfo4.setGroupId(33);
        scanGroupInfo4.setProjectIds(Lists.newArrayList(97));
        scanGroupInfoList.add(scanGroupInfo4);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.PATH_KEY_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);
    }

    /**
     * 主站前端
     */
    @Test
    public void testPlanInsert() {
        ArrayList<Integer> excludes = Lists.newArrayList(7668, 7781, 32668, 32667, 32671, 3311);
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("主站前端");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();

        ScanGroupInfo scanGroupInfo = new ScanGroupInfo();
        scanGroupInfo.setLogicGroupName("快手小程序");
        scanGroupInfo.setGroupId(6193);
        scanGroupInfo.setProjectIds(Lists.newArrayList(14620, 14045, 14749));
        scanGroupInfo.setExcludeProjectIds(excludes);
        scanGroupInfoList.add(scanGroupInfo);

        ScanGroupInfo scanGroupInfo1 = new ScanGroupInfo();
        scanGroupInfo1.setGroupId(19671);
        scanGroupInfo1.setLogicGroupName("小程序侧边栏");
        scanGroupInfo1.setExcludeProjectIds(excludes);
        scanGroupInfoList.add(scanGroupInfo1);

        ScanGroupInfo scanGroupInfo2 = new ScanGroupInfo();
        scanGroupInfo2.setGroupId(1859);
        scanGroupInfo2.setLogicGroupName("二次元业务");
        scanGroupInfo2.setExcludeProjectIds(excludes);
        scanGroupInfoList.add(scanGroupInfo2);

        ScanGroupInfo scanGroupInfo3 = new ScanGroupInfo();
        scanGroupInfo3.setGroupId(6193);
        scanGroupInfo3.setLogicGroupName("账号/实名");
        scanGroupInfo3.setExcludeProjectIds(excludes);
        scanGroupInfo3.setProjectIds(Lists.newArrayList(29482));
        scanGroupInfoList.add(scanGroupInfo3);

        ScanGroupInfo scanGroupInfo31 = new ScanGroupInfo();
        scanGroupInfo31.setGroupId(6193);
        scanGroupInfo31.setExcludeProjectIds(excludes);
        scanGroupInfo31.setLogicGroupName("IM 2C 服务");
        scanGroupInfo31.setProjectIds(Lists.newArrayList(21328));
        scanGroupInfoList.add(scanGroupInfo31);

        ScanGroupInfo scanGroupInfo4 = new ScanGroupInfo();
        scanGroupInfo4.setGroupId(4175);
        scanGroupInfo4.setLogicGroupName("验证码");
        scanGroupInfo4.setProjectIds(Lists.newArrayList(9064));
        scanGroupInfo4.setExcludeProjectIds(excludes);
        scanGroupInfoList.add(scanGroupInfo4);

        ScanGroupInfo scanGroupInfo6 = new ScanGroupInfo();
        scanGroupInfo6.setGroupId(2776);
        scanGroupInfo6.setLogicGroupName("商业化前端");
        scanGroupInfo6.setProjectIds(Lists.newArrayList(6761, 23207, 11938, 5518));
        scanGroupInfo6.setExcludeProjectIds(excludes);
        scanGroupInfoList.add(scanGroupInfo6);

        ScanGroupInfo scanGroupInfo7 = new ScanGroupInfo();
        scanGroupInfo7.setGroupId(186);
        scanGroupInfo7.setExcludeProjectIds(excludes);
        scanGroupInfo7.setLogicGroupName("同城地推活动");
        scanGroupInfo7.setProjectIds(Lists.newArrayList(31986, 12005, 14005, 7909));
        scanGroupInfoList.add(scanGroupInfo7);

        ScanGroupInfo scanGroupInfo8 = new ScanGroupInfo();
        scanGroupInfo8.setGroupId(312);
        scanGroupInfo8.setExcludeProjectIds(excludes);
        scanGroupInfo8.setLogicGroupName("同城POI");
        scanGroupInfoList.add(scanGroupInfo8);

        ScanGroupInfo scanGroupInfo9 = new ScanGroupInfo();
        scanGroupInfo9.setGroupId(13619);
        scanGroupInfo9.setLogicGroupName("生产&社交");
        scanGroupInfo9.setExcludeProjectIds(excludes);
        scanGroupInfo9.setProjectIds(Lists.newArrayList(32019));
        scanGroupInfoList.add(scanGroupInfo9);

        ScanGroupInfo scanGroupInfo10 = new ScanGroupInfo();
        scanGroupInfo10.setGroupId(2351);
        scanGroupInfo10.setLogicGroupName("生产&社交");
        scanGroupInfo10.setProjectIds(Lists.newArrayList(13025, 8632));
        scanGroupInfo10.setExcludeProjectIds(excludes);
        scanGroupInfoList.add(scanGroupInfo10);

        ScanGroupInfo scanGroupInfo11 = new ScanGroupInfo();
        scanGroupInfo11.setGroupId(720);
        scanGroupInfo11.setLogicGroupName("生产&社交");
        scanGroupInfo11.setProjectIds(Lists.newArrayList(34298, 5247, 32213, 18481));
        scanGroupInfo11.setExcludeProjectIds(excludes);
        scanGroupInfoList.add(scanGroupInfo11);

        ScanGroupInfo scanGroupInfo12 = new ScanGroupInfo();
        scanGroupInfo12.setGroupId(15651);
        scanGroupInfo12.setExcludeProjectIds(excludes);
        scanGroupInfo12.setLogicGroupName("生产&社交");
        scanGroupInfoList.add(scanGroupInfo12);

        ScanGroupInfo scanGroupInfo13 = new ScanGroupInfo();
        scanGroupInfo13.setGroupId(19297);
        scanGroupInfo13.setExcludeProjectIds(excludes);
        scanGroupInfo13.setLogicGroupName("生产&社交");
        scanGroupInfoList.add(scanGroupInfo13);

        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setGroupId(14932);
        scanGroupInfo14.setExcludeProjectIds(excludes);
        scanGroupInfo14.setLogicGroupName("直播");
        scanGroupInfoList.add(scanGroupInfo14);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.HARD_CODE_DOMAIN_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);
    }

    /**
     * 主站后端规则
     */
    @Test
    public void testFilterInsert() {
        List<ScanFilter> scanFilterList = Lists.newArrayList();

        ScanFilter scanFilter = new ScanFilter();
        scanFilter.setFilterType(ScanFilterType.DIRECT.getType());
        List<String> dirList = Lists.newArrayList(
                "/mock/", "/test/", "/tests/", "/sitemap/",
                "/build/", "/dist/", "/dll/", "/demo/", "/.npm/",
                "/node_modules/", "/docker/",
                "/mocker/", "/.yarn/", "/mockData/", "/__ mocks__/", "/.vscode/", "/mockData/",
                "/config/", "/mockBusiness/", "/public/", "/env/", "/doc/", "/docs/");
        scanFilter.setFilterValue(JSONUtils.serialize(dirList));
        scanFilter.setGmtCreate(LocalDateTime.now());
        scanFilter.setGmtModified(LocalDateTime.now());
        scanFilter.setScanPlanId(1234L); // 需要修改
        scanFilterList.add(scanFilter);

        ScanFilter scanFilter1 = new ScanFilter();
        scanFilter1.setFilterType(ScanFilterType.FILE_EXTENSION.getType());
        List<String> dirList1 = Lists.newArrayList();
        dirList1.add(".md");
        dirList1.add(".result");
        dirList1.add(".bak");
        dirList1.add(".MD");
        dirList1.add(".yaml");
        dirList1.add(".xml");
        dirList1.add(".json");
        dirList1.add(".demo");
        dirList1.add(".sh");
        dirList1.add(".es6");
        dirList1.add(".zip");
        dirList1.add(".log");
        dirList1.add(".proto");
        dirList1.add(".d.ts");
        dirList1.add(".txt");
        dirList1.add(".svg");
        dirList1.add(".py");
        dirList1.add(".conf");
        dirList1.add(".wxml");
        dirList1.add(".lua");
        scanFilter1.setFilterValue(JSONUtils.serialize(dirList1));
        scanFilter1.setGmtCreate(LocalDateTime.now());
        scanFilter1.setGmtModified(LocalDateTime.now());
        scanFilter1.setScanPlanId(1234L);// 需要修改
        scanFilterList.add(scanFilter1);

        ScanFilter scanFilter2 = new ScanFilter();
        scanFilter2.setFilterType(ScanFilterType.FILE_NAME.getType());
        List<String> dirList2 = Lists.newArrayList();
        dirList2.add("package-lock.json");
        dirList2.add("nginx.dev.conf");
        dirList2.add("nginx.conf");
        dirList2.add("webpack-config.js”,");
        dirList2.add("yarn.lock");
        dirList2.add("package.json");
        dirList2.add(".gitlab-ci.yml");
        dirList2.add("LICENSE");
        dirList2.add("pom.xml");
        dirList2.add("reviewboardrc");
        dirList2.add("vue.config.js");
        dirList2.add("Dockerfile");
        dirList2.add(".env");
        dirList2.add(".gitmodules");
        dirList2.add("mock.js");
        dirList2.add("vue.config.client.js");
        dirList2.add("mock.json");
        dirList2.add("mock1.json");
        dirList2.add("def.config.js");
        dirList2.add("chrome-linux.tar");
        dirList2.add("vite.config.js");
        dirList2.add("vite.config.ts");
        dirList2.add("mock.ts");
        dirList2.add("README");
        dirList2.add("test.js");
        dirList2.add("mail.js");
        dirList2.add("webpack.js");
        dirList2.add(".yarnrc");
        dirList2.add(".npmrc");
        dirList2.add(".env.development");
        dirList2.add(".testing.env");
        dirList2.add(".umirc.ts");
        dirList2.add("def.js");
        dirList2.add("kfx.js");
        dirList2.add(".Dockerfile");
        dirList2.add(".sentryclirc");
        dirList2.add("sentry.js");
        scanFilter2.setGmtCreate(LocalDateTime.now());
        scanFilter2.setGmtModified(LocalDateTime.now());
        scanFilter2.setScanPlanId(1234L);// 需要修改
        scanFilter2.setFilterValue(JSONUtils.serialize(dirList2));

        scanFilterList.add(scanFilter2);

        ScanFilter scanFilter3 = new ScanFilter();
        scanFilter3.setFilterType(ScanFilterType.CUSTOM_EXCLUDE_STR.getType());
        List<String> dirList3 = Lists.newArrayList();
        dirList3.add(".staging.kuaishou.com");
        dirList3.add("mailto:");
        dirList3.add(".test.gifshow.com");
        dirList3.add("docs.corp.kuaishou.com");
        dirList3.add("kapi.corp.kuaishou.com");
        dirList3.add("wiki.corp.kuaishou.com");
        dirList3.add("npm.corp.kuaishou.com");
        dirList3.add("kcdn.corp.kuaishou.com");
        dirList3.add("wlog.ksapisrv.com");
        dirList3.add("kim-robot.kwailt.com");
        dirList3.add("kim-aliyun.internal");
        dirList3.add("React.Component");
        scanFilter3.setGmtCreate(LocalDateTime.now());
        scanFilter3.setGmtModified(LocalDateTime.now());
        scanFilter3.setScanPlanId(1234L);// 需要修改
        scanFilter3.setFilterValue(JSONUtils.serialize(dirList3));

        scanFilterList.add(scanFilter3);

        scanFilterService.saveBatch(scanFilterList);
    }

    /**
     * 主站后端
     */
    @Test
    public void testPlanZzInsert() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("主站后端");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo1 = new ScanGroupInfo();
        scanGroupInfo1.setGroupId(262);
        scanGroupInfo1.setLogicGroupName("服务号");
        scanGroupInfo1.setProjectIds(Lists.newArrayList(5457, 12054, 13568, 11996, 14098, 14443, 17157));
        scanGroupInfoList.add(scanGroupInfo1);

        ScanGroupInfo scanGroupInfo2 = new ScanGroupInfo();
        scanGroupInfo2.setGroupId(57);
        scanGroupInfo2.setLogicGroupName("oauth");
        scanGroupInfo2.setProjectIds(Lists.newArrayList(902, 903));
        scanGroupInfoList.add(scanGroupInfo2);

        ScanGroupInfo scanGroupInfo3 = new ScanGroupInfo();
        scanGroupInfo3.setGroupId(4494);
        scanGroupInfo3.setLogicGroupName("开放平台");
        scanGroupInfo3.setProjectIds(Lists.newArrayList(40857, 34883, 40505, 42016, 37014, 33659, 35738, 29730, 30086));
        scanGroupInfoList.add(scanGroupInfo3);

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.HARD_CODE_DOMAIN_CASE.getType());
        jobList.add(ScanCaseType.KCONF_KEY_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));
        scanPlanService.save(scanPlan);
    }

    /**
     * 主站后端规则
     */
    @Test
    public void testFilterZzInsert() {

        List<ScanFilter> scanFilterList = Lists.newArrayList();

        ScanFilter scanFilter = new ScanFilter();
        scanFilter.setFilterType(ScanFilterType.DIRECT.getType());
        List<String> dirList = Lists.newArrayList();
        dirList.add("/mock/");
        dirList.add("/test/");
        dirList.add("/frontend/");
        scanFilter.setFilterValue(JSONUtils.serialize(dirList));
        scanFilter.setGmtCreate(LocalDateTime.now());
        scanFilter.setGmtModified(LocalDateTime.now());
        scanFilter.setScanPlanId(13L);// 需要修改
        scanFilterList.add(scanFilter);

        ScanFilter scanFilter1 = new ScanFilter();
        scanFilter1.setFilterType(ScanFilterType.FILE_EXTENSION.getType());
        List<String> dirList1 = Lists.newArrayList();
        dirList1.add(".md");
        dirList1.add(".properties");
        dirList1.add(".json");
        dirList1.add(".sh");
        dirList1.add(".reviewboardrc");
        dirList1.add(".proto");
        dirList1.add(".dat");
        dirList1.add(".csv");
        dirList1.add(".txt");
        dirList1.add(".sh");
        dirList1.add(".gitmodules");
        dirList1.add(".js");
        dirList1.add(".vue");
        dirList1.add(".jsp");
        dirList1.add(".lock");
        dirList1.add(".html");
        dirList1.add(".xml");
        dirList1.add(".yml");
        scanFilter1.setFilterValue(JSONUtils.serialize(dirList1));
        scanFilter1.setGmtCreate(LocalDateTime.now());
        scanFilter1.setGmtModified(LocalDateTime.now());
        scanFilter1.setScanPlanId(13L); // 需要修改
        scanFilterList.add(scanFilter1);

        ScanFilter scanFilter2 = new ScanFilter();
        scanFilter2.setFilterType(ScanFilterType.FILE_NAME.getType());
        List<String> dirList2 = Lists.newArrayList();
        dirList2.add("api-servlet.xml");
        dirList2.add("ServletConfig.java");
        dirList2.add("pom.xml");
        dirList2.add("sentry.properties");
        dirList2.add("*CrossDomain*");
        dirList2.add("web.xml");
        scanFilter2.setGmtCreate(LocalDateTime.now());
        scanFilter2.setGmtModified(LocalDateTime.now());
        scanFilter2.setScanPlanId(13L); // 需要修改
        scanFilter2.setFilterValue(JSONUtils.serialize(dirList2));

        scanFilterList.add(scanFilter2);

        ScanFilter scanFilter3 = new ScanFilter();
        scanFilter3.setFilterType(ScanFilterType.CUSTOM_EXCLUDE_STR.getType());
        List<String> dirList3 = Lists.newArrayList();
        dirList3.add(".corp.kuaishou.com");
        dirList3.add(".staging.kuaishou.com");
        dirList3.add(".test.gifshow.com");
        dirList3.add("@kuaishou.com");
        dirList3.add("@author");
        scanFilter3.setGmtCreate(LocalDateTime.now());
        scanFilter3.setGmtModified(LocalDateTime.now());
        scanFilter3.setScanPlanId(13L); // 需要修改
        scanFilter3.setFilterValue(JSONUtils.serialize(dirList3));

        scanFilterList.add(scanFilter3);

        scanFilterService.saveBatch(scanFilterList);
    }

    /**
     * 主站直播后端
     */
    @Test
    public void testPlanZzLiveInsert() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("主站直播后端");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();

        ScanGroupInfo scanGroupInfo1 = new ScanGroupInfo();
        scanGroupInfo1.setGroupId(13681);
        scanGroupInfo1.setLogicGroupName("主站直播后端");
        scanGroupInfo1.setExcludeProjectIds(Lists.newArrayList(34790, 35889, 11079, 26485, 123, 19225, 7718, 21606));
        scanGroupInfoList.add(scanGroupInfo1);

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.HARD_CODE_DOMAIN_CASE.getType());
        jobList.add(ScanCaseType.KCONF_KEY_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));
        scanPlanService.save(scanPlan);
    }

    /**
     * 主站直播后端规则
     */
    @Test
    public void testFilterZzLiveInsert() {

        List<ScanFilter> scanFilterList = Lists.newArrayList();

        fillScanFilterList(14L, ScanFilterType.FILE_EXTENSION,
                Lists.newArrayList(".md", ".json", ".sh", ".reviewboardrc",
                        ".proto", ".csv", ".txt", ".gitmodules", ".properties"
                ),
                scanFilterList);

        fillScanFilterList(14L, ScanFilterType.FILE_NAME,
                Lists.newArrayList("api-servlet.xml", "ServletConfig.java",
                        "pom.xml", "sentry.properties", "nohup.out", "*web.xml"
                ),
                scanFilterList);

        fillScanFilterList(14L, ScanFilterType.DIRECT,
                Lists.newArrayList("/mock/", "/test/", "/frontend/"),
                scanFilterList);

        fillScanFilterList(14L, ScanFilterType.CUSTOM_EXCLUDE_STR,
                Lists.newArrayList(".staging.kuaishou.com", ".test.gifshow.com",
                        "@kuaishou.com", "@author", ".corp.kuaishou.com", "ksurl.com"
                ),
                scanFilterList);

        scanFilterService.saveBatch(scanFilterList);
    }

    /**
     * 电商后端
     */
    @Test
    public void testPlanDSInsert() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("电商后端");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setGroupId(2428);
        scanGroupInfo14.setLogicGroupName("电商后端");
        scanGroupInfoList.add(scanGroupInfo14);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(4);
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);
    }

    /**
     * 电商后端规则
     */
    @Test
    public void testFilterDSInsert() {

        List<ScanFilter> scanFilterList = Lists.newArrayList();

        fillScanFilterList(214L, ScanFilterType.DIRECT,
                Lists.newArrayList("/test/", "/target/", "/docs/"),
                scanFilterList);

        fillScanFilterList(214L, ScanFilterType.FILE_EXTEND_INCLUDE,
                Lists.newArrayList(".java"),
                scanFilterList);
        fillScanFilterList(214L, ScanFilterType.CUSTOM_EXCLUDE_STR,
                Lists.newArrayList("@kuaishou.com"),
                scanFilterList);

        scanFilterService.saveBatch(scanFilterList);
    }

    /**
     * 快手支付
     */
    @Test
    public void testKSPay() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("快手支付");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setGroupId(809);
        scanGroupInfo14.setLogicGroupName("快手支付");
        scanGroupInfoList.add(scanGroupInfo14);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.HARD_CODE_DOMAIN_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);

        List<ScanFilter> scanFilterList = Lists.newArrayList();

        fillScanFilterList(scanPlan.getId(), ScanFilterType.DIRECT,
                Lists.newArrayList("/test/", "/target/", "/docs/"),
                scanFilterList);

        fillScanFilterList(scanPlan.getId(), ScanFilterType.FILE_EXTEND_INCLUDE,
                Lists.newArrayList(".java"),
                scanFilterList);

        fillScanFilterList(scanPlan.getId(), ScanFilterType.CUSTOM_EXCLUDE_STR,
                Lists.newArrayList("@kuaishou.com"),
                scanFilterList);

        scanFilterService.saveBatch(scanFilterList);

    }

    /**
     * 商业化前端
     */
    @Test
    public void testADFE() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("商业化前端");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setGroupId(2776);
        scanGroupInfo14.setLogicGroupName("商业化前端");
        scanGroupInfoList.add(scanGroupInfo14);

        ScanGroupInfo scanGroupInfo15 = new ScanGroupInfo();
        scanGroupInfo15.setGroupId(15645);
        scanGroupInfo15.setLogicGroupName("商业化前端海外");
        scanGroupInfo15.setProjectIds(
                Lists.newArrayList(41860, 36331, 37659, 43925, 44765, 44766, 39308, 39299, 39309, 41191, 41861));
        scanGroupInfoList.add(scanGroupInfo15);

        ScanGroupInfo scanGroupInfo16 = new ScanGroupInfo();
        scanGroupInfo16.setGroupId(2327);
        scanGroupInfo16.setLogicGroupName("商业化前端部分模块");
        scanGroupInfoList.add(scanGroupInfo16);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.HARD_CODE_DOMAIN_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);

        List<ScanFilter> scanFilterList = Lists.newArrayList();

        fillScanFilterList(scanPlan.getId(), ScanFilterType.DIRECT,
                Lists.newArrayList("/test/", "/mock/", "/.vscode/", "/.npm/", "/.yarn/", "/node_modules/", "/mocker/"),
                scanFilterList);

        fillScanFilterList(scanPlan.getId(), ScanFilterType.FILE_NAME,
                Lists.newArrayList("package-lock.json", "nginx.dev.conf", "nginx.conf",
                        ".gitlab-ci.yml", "package.json", "swagger-lock.json", "mock.json", "yarn.lock", "swetrc.js",
                        "swetrc.json", "Dockerfile", ".gitmodules"),
                scanFilterList);

        fillScanFilterList(scanPlan.getId(), ScanFilterType.FILE_EXTENSION,
                Lists.newArrayList(".md", ".proto", ".d.ts", ".txt", ".svg", ".yml", ".test.js", ".test.js.snap", ".sh",
                        ".log", ".dockerfile", ".sqlite"),
                scanFilterList);

        fillScanFilterList(scanPlan.getId(), ScanFilterType.CUSTOM_EXCLUDE_STR,
                Lists.newArrayList(".staging.kuaishou.com", ".test.gifshow.com", "@kuaishou.com", "@author",
                        "docs.corp.kuaishou.com", "wiki.corp.kuaishou.com", "npm.corp.kuaishou.com",
                        "kcdn.corp.kuaishou.com", "wlog.ksapisrv.com", "kim-robot.kwailt.com", "kim-aliyun.internal",
                        "React.Component"),
                scanFilterList);

        scanFilterService.saveBatch(scanFilterList);

    }

    /**
     * 海外前端团队
     */
    @Test
    public void testKibtFE() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("海外前端团队");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setGroupId(11463);
        scanGroupInfo14.setLogicGroupName("海外前端团队");
        scanGroupInfoList.add(scanGroupInfo14);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.HARD_CODE_DOMAIN_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);

        List<ScanFilter> scanFilterList = Lists.newArrayList();

        fillScanFilterList(scanPlan.getId(), ScanFilterType.DIRECT,
                Lists.newArrayList("/test/", "/mock/"),
                scanFilterList);

        fillScanFilterList(scanPlan.getId(), ScanFilterType.FILE_NAME,
                Lists.newArrayList("package-lock.json", "nginx.dev.conf", "nginx.conf", "yarn.lock", "vue.config.js"),
                scanFilterList);

        fillScanFilterList(scanPlan.getId(), ScanFilterType.FILE_EXTENSION,
                Lists.newArrayList(".md", ".proto", ".d.ts", ".txt", ".svg"),
                scanFilterList);

        scanFilterService.saveBatch(scanFilterList);

    }

    /**
     * 海外server团队
     */
    @Test
    public void testKwai() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("海外server团队");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setGroupId(1055);
        scanGroupInfo14.setLogicGroupName("kwai");
        scanGroupInfoList.add(scanGroupInfo14);

        ScanGroupInfo scanGroupInfo15 = new ScanGroupInfo();
        scanGroupInfo15.setGroupId(18673);
        scanGroupInfo15.setLogicGroupName("kwai-social");
        scanGroupInfoList.add(scanGroupInfo15);

        ScanGroupInfo scanGroupInfo16 = new ScanGroupInfo();
        scanGroupInfo16.setGroupId(551);
        scanGroupInfo16.setLogicGroupName("oversea-server");
        scanGroupInfoList.add(scanGroupInfo16);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.HARD_CODE_DOMAIN_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);

        List<ScanFilter> scanFilterList = Lists.newArrayList();

        fillScanFilterList(scanPlan.getId(), ScanFilterType.DIRECT,
                Lists.newArrayList("/test/", "/docs/", "/target/"),
                scanFilterList);

        fillScanFilterList(scanPlan.getId(), ScanFilterType.FILE_EXTEND_INCLUDE,
                Lists.newArrayList(".java"),
                scanFilterList);

        scanFilterService.saveBatch(scanFilterList);

    }


    private void fillScanFilterList(Long scanPlanId, ScanFilterType filterType, List<String> dirList3,
            List<ScanFilter> scanFilterList) {
        ScanFilter scanFilter3 = new ScanFilter();
        scanFilter3.setFilterType(filterType.getType());
        scanFilter3.setGmtCreate(LocalDateTime.now());
        scanFilter3.setGmtModified(LocalDateTime.now());
        scanFilter3.setScanPlanId(scanPlanId);
        scanFilter3.setFilterValue(JSONUtils.serialize(dirList3));
        scanFilterList.add(scanFilter3);
    }


    @Test
    public void testAdGrpcClientInit() {

        // 初始化扫描计划
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());

        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("reco");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo = new ScanGroupInfo();
        scanGroupInfo.setGroupId(535);
        scanGroupInfo.setLogicGroupName("reco");
        ScanGroupInfo scanGroupInfo1 = new ScanGroupInfo();
        scanGroupInfo1.setGroupId(3405);
        scanGroupInfo1.setLogicGroupName("reco-infra");
        scanGroupInfoList.add(scanGroupInfo);
        scanGroupInfoList.add(scanGroupInfo1);
        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanProjectInfo.setNeedCommitter(true);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> typeList = Lists.newArrayList();
        typeList.add(ScanCaseType.GRPC_CLIENT_OLD_ALL_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(typeList));

        scanPlanService.save(scanPlan);

        // 初始化扫描过滤规则
        List<ScanFilter> scanFilterList = Lists.newArrayList();
        ScanFilter scanFilter = new ScanFilter();
        scanFilter.setFilterType(ScanFilterType.FILE_EXTEND_INCLUDE.getType());
        List<String> dirList = Lists.newArrayList();
        dirList.add(".java");
        dirList.add(".kt");
        scanFilter.setGmtCreate(LocalDateTime.now());
        scanFilter.setGmtModified(LocalDateTime.now());
        scanFilter.setScanPlanId(scanPlan.getId());
        scanFilter.setFilterValue(JSONUtils.serialize(dirList));
        scanFilterList.add(scanFilter);

        ScanFilter scanFilter1 = new ScanFilter();
        scanFilter1.setFilterType(ScanFilterType.IMPORT_ITEM_SCAN.getType());
        scanFilter1.setGmtCreate(LocalDateTime.now());
        scanFilter1.setGmtModified(LocalDateTime.now());
        scanFilter1.setScanPlanId(scanPlan.getId());
        scanFilter1.setFilterValue(JSONUtils.serialize(Lists.newArrayList()));
        scanFilterList.add(scanFilter1);

        scanFilterService.saveBatch(scanFilterList);

    }

    @Test
    public void testAdGrpcClientCase() {
        List<ScanCase> list = new ArrayList<>();
        // 匹配字段 拓展字段 匹配类型
        list.add(new ScanCase("com.kuaishou.framework.rpc.client.Grpc", "",
                ScanCaseType.GRPC_CLIENT_OLD_1_CASE.getType()));
        list.add(new ScanCase("com.kuaishou.framework.rpc.client.Grpc.grpcEx", "",
                ScanCaseType.GRPC_CLIENT_OLD_1_CASE.getType()));
        list.add(new ScanCase("com.kuaishou.framework.rpc.client.Grpc.grpc", "",
                ScanCaseType.GRPC_CLIENT_OLD_1_CASE.getType()));
        list.add(new ScanCase("com.kuaishou.framework.rpc.client.Grpc.grpcHashEx", "",
                ScanCaseType.GRPC_CLIENT_OLD_1_CASE.getType()));
        list.add(new ScanCase("com.kuaishou.framework.rpc.client.Grpc.grpcHash", "",
                ScanCaseType.GRPC_CLIENT_OLD_1_CASE.getType()));
        scanCaseService.saveOrUpdateBatch(list);
    }

    @Test
    public void testAdGrpcClientFilter() {

        List<ScanFilter> scanFilterList = Lists.newArrayList();
        ScanFilter scanFilter = new ScanFilter();
        scanFilter.setFilterType(ScanFilterType.FILE_EXTEND_INCLUDE.getType());
        List<String> dirList = Lists.newArrayList();
        dirList.add(".java");
        dirList.add(".kt");
        scanFilter.setGmtCreate(LocalDateTime.now());
        scanFilter.setGmtModified(LocalDateTime.now());
        scanFilter.setScanPlanId(1234L);
        scanFilter.setFilterValue(JSONUtils.serialize(dirList));
        scanFilterList.add(scanFilter);

        scanFilterService.saveBatch(scanFilterList);
    }

    @Test
    public void testPayCase() {

        List<String> hardCodeList = Lists.newArrayList("2007.kuaishoupay.com",
                "allin.kuaishoupay.com",
                "api.kuaishoupay.com",
                "api1.kuaishoupay.com",
                "api2.kuaishoupay.com",
                "billyk.kuaishoupay.com",
                "bjrz-pay.kuaishoupay.com",
                "c.kuaishoupay.com",
                "dive.kuaishoupay.com",
                "dl-core2.kuaishoupay.com",
                "dyngate-il27-90.kuaishoupay.com",
                "fbfans.kuaishoupay.com",
                "ftp.kuaishoupay.com",
                "gigolo.kuaishoupay.com",
                "gw.kuaishoupay.com",
                "gw-dev.kuaishoupay.com",
                "gw-dev1.kuaishoupay.com",
                "gw-test.kuaishoupay.com",
                "homolog.kuaishoupay.com",
                "idc----eshop-api1----idc.kuaishoupay.com",
                "idc----eshop-api2----idc.kuaishoupay.com",
                "idc----eshop-www----idc.kuaishoupay.com",
                "kixyz.kuaishoupay.com",
                "kspay-activity.kuaishoupay.com",
                "kspay-sf2021.kuaishoupay.com",
                "pay.styz.kuaishoupay.com",
                "qw.kuaishoupay.com",
                "sc4.kuaishoupay.com",
                "sso2.kuaishoupay.com",
                "te2.kuaishoupay.com",
                "test178.kuaishoupay.com",
                "testactivity.kuaishoupay.com",
                "test.kuaishoupay.com",
                "test-rainbow-api.kuaishoupay.com",
                "thirdpart.kuaishoupay.com",
                "twhsob02.kuaishoupay.com",
                "twmemmsf.kuaishoupay.com",
                "www.kuaishoupay.com",
                "www.youlin.kuaishoupay.com",
                "xianning.kuaishoupay.com",
                "yd-hips-console.kuaishoupay.com",
                "yunwei.kuaishoupay.com",
                "zwgk.kuaishoupay.com",
                "gw.kspay.kuaishou.com",
                "gw.kuaishou.com",
                "mpay.ssl.kuaishou.com",
                "pay.ssl.kuaishou.com",
                "pay.ssl.tdyz.kuaishou.com",
                "sogame.ssl.kuaishou.com",
                "wallet-antman.m.kuaishou.com",
                "wallet-gzone.m.kuaishou.com",
                "wallet.m.kuaishou.com",
                "wallet-internal-check.kuaishou.com",
                "wallet-nebula.m.kuaishou.com",
                "wallet-thanos.m.kuaishou.com",
                "hb.gifshow.com",
                "id-card.gifshow.com",
                "bjrz-pay.gifshow.com",
                "js2.a.kwimgs.com");

        List<ScanCase> list = new ArrayList<>();
        for (String s : hardCodeList) {
            list.add(new ScanCase(s, "", ScanCaseType.KSPAY_HARD_CODE_DOMAIN.getType()));
        }
        scanCaseService.saveOrUpdateBatch(list);
    }

    @Test
    public void testPtpInit() throws FileNotFoundException {
        FileInputStream fileInputStream =
                new FileInputStream("/Users/<USER>/Desktop/发票/机器名&ip.xlsx");
        List<Map<Integer, String>> projectIds = EasyExcel.read(fileInputStream, null, null).sheet().doReadSync();
        List<ScanCase> list = Lists.newArrayList();
        for (Map<Integer, String> projectId : projectIds) {
            String s = projectId.get(0);
            list.add(new ScanCase(s, "", ScanCaseType.PTP_TRANSFER_CASE.getType()));
        }

        scanCaseService.saveBatch(list);
    }

    @Test
    public void testInit() {
        List<String> strings = Lists.newArrayList("com.kuaishou.framework.rpc.client.Grpc",
                "com.kuaishou.framework.rpc.client.n.GrpcClient",
                "com.kuaishou.framework.rpc.client.n.primitive.PrimitiveRpcClient",
                "com.kuaishou.framework.rpc.client.n.primitive.PrimitiveGenericRpcClient",
                "com.kuaishou.framework.rpc.client.n.LowLevelGrpcClient",
                "com.kuaishou.framework.rpc.client.n.direct.DirectGrpcClient");
        List<ScanCase> list = Lists.newArrayList();
        for (String string : strings) {
            list.add(new ScanCase(string, "", ScanCaseType.GRPC_CLIENT_OLD_ALL_CASE.getType()));
        }
        scanCaseService.saveBatch(list);
    }

    @Test
    public void test7Init() {
        List<String> strings = Lists.newArrayList(".dev.kwaidc.com",
                "172.29.64",
                "172.29.65",
                "172.29.66",
                "172.29.67",
                "172.29.68",
                "172.29.69",
                "172.29.70",
                "172.29.71",
                "172.29.72",
                "172.29.73",
                "172.29.74",
                "172.29.75",
                "172.29.76",
                "172.29.77",
                "172.29.78",
                "172.29.79",
                "172.29.80",
                "172.29.81",
                "172.29.82",
                "172.29.83",
                "172.29.84",
                "172.29.85",
                "172.29.86",
                "172.29.87",
                "172.29.88",
                "172.29.89",
                "172.29.90",
                "172.29.91",
                "172.29.92",
                "172.29.93",
                "172.29.94",
                "172.29.95");
        List<ScanCase> list = Lists.newArrayList();
        for (String string : strings) {
            list.add(new ScanCase(string, "", ScanCaseType.PTP_TRANSFER_CASE.getType()));
        }
        scanCaseService.saveBatch(list);
    }

    @Test
    public void testInitKsPayPlan() {
        // 初始化扫描计划
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());

        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("快手支付");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo = new ScanGroupInfo();
        scanGroupInfo.setGroupId(809);
        scanGroupInfo.setLogicGroupName("快手支付所有项目");
        scanGroupInfoList.add(scanGroupInfo);
        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> typeList = Lists.newArrayList();
        typeList.add(ScanCaseType.KSPAY_HARD_CODE_DOMAIN.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(typeList));

        scanPlanService.save(scanPlan);
    }

    @Test
    public void initPtpTransfer() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());

        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("Y-tech");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();

        String s = "57348,57349,8198,8199,8200,20489,57355,4110,32783,16400,40977,61459,40983,12315,40989,20509,12320,"
                + "61473,4132,45094,8232,8239,57394,45107,12341,53302,61495,61496,61497,20542,57414,16454,"
                + "4166,24646,12361,24650,24652,24653,41038,57422,49232,41041,49233,8275,36947,45139,49238,"
                + "28765,36958,32865,8290,16484,41060,24678,8294,24699,32898,49284,16517,45192,8329,24717,"
                + "45198,28817,53396,32919,32920,32921,156,158,32929,45218,49319,12457,24746,57515,37036,"
                + "57516,8364,28844,28848,179,20661,49333,20662,182,8375,41145,8379,41157,45258,53452,20685,"
                + "41165,28878,45263,24784,12496,53457,49373,221,4319,12511,20704,61665,49381,45285,49383,"
                + "53479,24807,12526,8440,16633,49404,24832,45312,53510,12550,33031,28935,20743,12551,53512,"
                + "8457,16651,53519,12560,45330,57620,8468,49433,24866,57634,8493,24878,28975,8497,49459,"
                + "20787,33075,20788,33086,16703,53568,33102,53590,29018,49499,49502,24927,41311,41312,37216,"
                + "61793,57699,37222,61799,8552,49513,37226,12652,367,368,45427,8564,41333,57718,45430,37241,"
                + "53627,29054,4480,45440,41345,385,386,387,29059,53635,33159,57736,45448,45449,408,411,8607,"
                + "24995,12710,16807,49578,20906,29099,12721,8626,37298,439,29113,45498,25022,29119,33215,"
                + "33216,29120,29121,37313,29122,49606,53704,12745,12746,45515,8653,8654,8655,8656,8657,"
                + "41426,57813,53722,12764,16861,16868,61925,12775,49644,41453,494,61941,8693,25081,37370,"
                + "49659,20997,16904,21000,57868,45581,49683,57875,37401,33305,45594,37403,8732,41501,543,"
                + "61983,16928,57889,37410,8739,41508,8741,33318,41510,8743,49703,21036,12844,558,45619,563,"
                + "21046,45623,49722,570,571,574,45633,45635,41542,29254,37446,589,4688,592,45649,4690,53848,"
                + "33370,12894,25183,21087,49759,12896,49761,49762,41571,612,57956,41574,8807,57961,33387,"
                + "8811,37485,21105,8818,17011,17012,21110,12918,49787,12924,53885,639,29313,29316,33415,649,"
                + "8844,49806,49807,25231,33426,660,45718,49816,12954,21149,17055,37543,4780,62133,62135,"
                + "45752,45753,33468,8897,12996,58053,49864,58057,45770,45775,25296,25301,728,37594,8924,"
                + "37598,8931,8935,746,8943,8944,49906,13044,13045,29434,13051,4861,4862,41729,62209,21261,"
                + "49935,49937,21270,29467,54044,45853,8993,13092,37672,13097,25391,9008,822,54071,37695,"
                + "37697,37698,9027,49990,62278,17223,9043,41815,861,21341,29534,862,62303,865,58214,875,"
                + "50027,25453,21360,37746,62325,29559,29560,54138,25466,29563,50045,29567,4997,9094,58247,"
                + "25480,58249,58250,58251,9100,21390,45972,29589,17305,5018,45979,62368,9121,929,29608,"
                + "29609,33708,5038,58287,41907,5044,58292,25526,21433,33723,21438,21439,21441,962,41924,"
                + "25543,9166,46032,46035,41939,46037,29661,17373,29662,41962,50155,17388,13292,17389,37869,"
                + "50160,5111,58359,29688,25592,41979,29699,1030,29703,46089,46090,46092,25614,54288,25618,"
                + "54292,54293,54294,54297,62494,58400,25633,13347,54309,17446,9257,5161,5163,42028,5164,"
                + "5165,33838,5166,50223,62511,9267,17460,33845,5174,17464,5180,9277,29758,21567,62528,9281,"
                + "42050,42052,13381,50247,42055,5193,62540,5201,1106,37971,37978,50270,9312,5218,5221,21614,"
                + "33903,13429,21622,33910,62585,62586,17530,17531,33916,13437,46207,17536,54405,46214,21639,"
                + "50311,46215,46216,1161,62602,46220,5261,13458,13462,54423,38040,58532,50343,33963,42156,"
                + "9396,21693,9406,33985,46275,1223,46281,5322,50384,46290,50389,25815,38115,13541,5350,1257,"
                + "13547,17646,38127,17650,1266,38131,5365,21758,1279,42241,54529,17671,25866,34063,34066,"
                + "25875,42262,29975,21783,58647,13592,13593,25883,58654,13598,13599,13600,1312,13601,38180,"
                + "46374,21805,13616,30001,30004,21814,46391,46392,9528,46395,13627,54588,13628,42301,46400,"
                + "13634,1346,21827,5447,9544,13641,9552,34128,9553,46419,1364,42326,46425,42340,58726,46439,"
                + "21870,42351,17778,34162,34163,21875,25973,25974,34166,25976,30074,25979,21884,9597,21886,"
                + "9598,9599,62848,9600,13697,54657,17798,54662,13703,13707,13709,42384,58771,46492,58781,"
                + "62878,42401,46501,26034,54709,13749,9655,50616,46521,46524,50623,50624,26048,50625,46531,"
                + "34244,1478,1481,1482,46540,21965,13774,30169,34267,58845,46560,50658,5604,17894,58854,"
                + "9709,42477,9710,1522,1523,17908,13812,1525,13817,26105,50689,58885,5639,17928,42508,17936,"
                + "50705,5650,46615,34332,42531,13867,42549,50743,22072,26169,58939,13887,13895,17993,9805,"
                + "54867,58965,34391,1628,1638,5735,34408,9841,13941,38521,9855,30336,30337,9857,34445,42640,"
                + "59027,59029,50837,34457,30366,13982,1698,54947,46758,26279,46759,46760,9898,5802,1710,"
                + "22190,13999,9903,22192,22193,22194,46772,54965,5814,22201,26303,9919,42693,1734,1737,"
                + "14027,9932,38607,59093,9943,1752,38618,5850,14053,14055,22251,14059,46834,14077,14082,"
                + "1795,14084,30469,14085,42757,26374,42761,34575,1809,5908,5909,26392,1817,5914,1821,42784,"
                + "42786,30506,50992,14133,14134,59191,42807,30526,26431,55109,26437,26439,46920,14153,55114,"
                + "30539,51021,51022,42837,38742,22359,42841,18267,55133,42850,14179,51046,51049,1898,51058,"
                + "14202,14205,1918,14213,18311,18315,22413,51088,42897,51091,30612,22421,22422,22424,38812,"
                + "1949,51104,14253,55217,1969,26546,1972,1974,34745,26557,6080,34759,30664,30671,2001,26582,"
                + "30678,30679,30680,14297,30681,2010,2012,47072,14304,22497,22498,26595,55268,22501,55272,"
                + "51176,34792,22505,38891,47088,6130,55287,51191,47095,26617,55289,26618,26619,2053,43015,"
                + "18443,6156,59404,59406,30738,10260,26644,18453,10261,10262,10263,10264,43032,10265,2073,"
                + "10266,10267,10268,10269,10270,10271,59423,10272,10273,22561,10274,6178,10275,22563,10276,"
                + "2084,55332,10277,55333,10278,10279,10280,10281,10282,10283,10284,10285,10286,10287,51247,"
                + "10288,10289,10290,51250,10291,14387,10292,6196,59444,10293,59445,10294,10295,10296,10297,"
                + "55353,10298,10299,10300,14396,10301,10302,10303,10304,10305,10306,2114,10307,22595,10308,"
                + "10309,10310,10311,10312,10314,10315,10316,59468,43084,10317,10318,10319,10320,43088,10321,"
                + "10322,10323,2134,10327,43095,47191,6231,34903,10328,22617,10329,10330,10331,10332,22620,"
                + "10333,43101,10334,43102,10335,10336,10337,10338,6242,10339,39011,10340,51300,10341,10342,"
                + "6246,10343,2151,22632,10344,10345,51306,10346,10347,10348,10349,22637,10350,10351,10352,"
                + "10353,51313,10354,10355,43123,10356,47220,10357,10358,10359,10360,10361,43129,30841,10362,"
                + "55418,10363,10364,10365,10366,51326,10367,10368,10369,10370,55426,10371,10372,10373,51333,"
                + "10374,51334,10375,14471,10376,10377,2185,51337,26762,10378,18571,10379,10380,10381,14477,"
                + "10382,10383,51343,55440,2197,55447,51352,30874,55450,18587,14493,6301,14494,22686,55458,"
                + "55461,22700,10416,10417,10418,10419,10420,51380,10421,34998,39095,26810,51393,39109,18632,"
                + "26826,39116,14541,22734,59600,51408,35025,43229,26852,55525,18661,6381,6382,39151,18676,"
                + "14582,30968,39162,6395,30973,39166,30974,18692,59655,39180,2320,51472,47381,10518,26903,"
                + "55579,14622,55582,6430,2335,14626,35114,35115,59693,6445,55599,18737,47409,26930,22837,"
                + "43318,2358,2364,39230,14655,43328,55620,14664,14669,14673,51538,18772,14676,6488,14682,"
                + "18781,26978,22882,51557,26981,26982,55658,35182,22900,10620,6524,2428,47486,27006,31106,"
                + "47491,6534,22919,6535,35207,6537,35209,51597,6544,51606,51608,10648,59804,59806,59811,"
                + "18852,39338,10668,59822,59825,27059,22966,39351,31160,39353,14779,59835,18882,35267,39367,"
                + "35271,14795,51663,55763,27092,6612,6614,6615,6616,6617,6618,6619,23004,6621,6622,6623,"
                + "31200,6624,6625,14818,6626,39395,6627,10724,55787,18924,14831,23024,47600,35314,47603,"
                + "23030,6648,43517,55808,47623,6665,55825,31252,55831,55834,43548,47644,51748,14886,23079,"
                + "6702,23088,2608,55859,35379,35383,31287,14903,2616,10817,14914,27203,39493,35400,31310,"
                + "31311,31312,14928,39505,10834,2655,39521,55912,27241,10857,14954,6767,55923,14965,14966,"
                + "39542,47738,43642,35455,35463,35464,27275,23181,27283,10900,19097,23193,31385,15002,19100,"
                + "39580,51869,31390,51877,55974,23212,35501,55983,35504,51893,51895,10938,43708,31421,55999,"
                + "43711,15039,19139,6855,56008,39625,43722,35530,39630,51918,47822,19151,39634,6868,6871,"
                + "51929,51931,43739,6878,23271,15080,60137,19180,39660,27372,51953,10993,51960,35577,2810,"
                + "11005,11008,19206,27401,35597,2832,51988,15124,47902,31518,31520,11040,56099,11044,23333,"
                + "47910,19241,31533,15154,23347,27449,11067,6971,11068,19261,2883,43847,11086,52048,52056,"
                + "31578,19290,11101,52065,43877,15208,27497,15210,47980,60268,56173,60269,23406,31599,47984,"
                + "35700,39798,7032,23421,2943,15238,60294,7047,60298,11154,52116,35741,19357,35743,27552,"
                + "19361,52131,52132,56235,23470,11183,19377,23479,7098,15291,3015,11209,7118,7119,56274,"
                + "31698,19414,19416,19417,48095,60386,11237,27628,44014,52209,44019,35834,27644,39933,3070,"
                + "31743,11263,35840,44037,44038,15366,60427,11275,31756,27661,15379,11284,52246,27676,39972,"
                + "60454,39975,44071,3115,3118,35886,15411,39990,60470,52280,60475,3131,23612,48189,56384,"
                + "3137,27714,3143,11338,3146,7242,52298,44108,31823,11351,40024,35930,35937,7269,23653,"
                + "11366,60518,7277,11376,31859,52340,19572,60533,52343,11387,48251,11388,56444,44158,19583,"
                + "56451,11395,44174,31886,7311,7312,48281,11421,31901,7325,40095,56480,40098,23721,60586,"
                + "3244,56492,3246,48303,56495,31922,27829,19637,11450,56517,7366,48331,60621,23761,36050,"
                + "27861,56536,23768,27865,60634,56539,23771,60635,11483,56543,23776,44258,52452,11492,52456,"
                + "48364,44271,19695,60656,44274,11507,44276,44277,44279,3320,44281,56571,19709,19710,44287,"
                + "60671,44288,44289,23809,19713,27908,15623,32008,15626,44299,44304,23824,52503,44317,15649,"
                + "60706,32036,27944,32046,23857,36149,15669,23874,11589,40262,3399,27978,7501,48463,15701,"
                + "44374,19800,48473,40289,15713,7523,7524,44391,36201,60782,48494,15732,19829,56699,19835,"
                + "56700,56701,56702,56703,56704,56706,23943,56712,48522,3467,11665,11668,15765,60821,11672,"
                + "44442,11674,40347,15772,52638,44448,23970,48550,60839,7596,44461,19886,7598,44463,44467,"
                + "7613,44481,11713,24004,40401,7634,52690,52692,28119,28121,32222,44513,44518,19944,19945,"
                + "36335,11766,28151,44536,11769,52729,44540,15868,3583,7680,56840,32265,28170,56842,60943,"
                + "19992,7705,44572,44577,60967,11817,48688,32304,48694,28215,56888,56889,36409,7737,3642,"
                + "60987,7743,3647,3649,7750,61000,36425,44618,24138,36433,7769,24154,36442,36443,3676,36444,"
                + "24163,3684,48750,61044,11893,61045,52854,32375,28281,28291,48772,16006,44680,28299,52876,"
                + "7825,3731,16021,16022,44695,7840,36514,7843,52902,24235,24236,44716,32434,40629,28344,"
                + "24263,32457,32459,28367,28368,3792,28369,57048,57049,57050,57051,16092,48868,16107,24307,"
                + "40701,52991,52992,7940,52997,52998,48906,40721,7961,12061,40737,32545,57121,48937,24365,"
                + "7983,12080,7987,53044,57145,44857,53049,53051,7996,32573,28477,53054,3903,53059,40773,"
                + "53065,53068,20304,53073,16217,57178,12123,53083,61275,3934,36703,16224,36704,28519,40809,"
                + "28522,16234,12139,28523,28525,57198,61294,3951,8048,57202,3956,12156,8060,44925,44930,"
                + "53122,49027,49028,53124,16267,24461,32660,24470,12189,57246,61342,4001,16292,44967,20400,"
                + "44982,40886,4023,44986,20412,61374,53183,53184,53185,4034,53191,32711,24521,24524,24527,"
                + "24529,36827,28636,36835,61415,20464,45041,20467,53237,45053,40959";

        String[] split = s.split(",");
        List<Integer> collect = Arrays.stream(split).map(Integer::valueOf).collect(Collectors.toList());

        ScanGroupInfo scanGroupInfo = new ScanGroupInfo();
        scanGroupInfo.setLogicGroupName("Y-tech");
        scanGroupInfo.setGroupId(0);
        scanGroupInfo.setProjectIds(collect);
        scanGroupInfoList.add(scanGroupInfo);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanProjectInfo.setContainsUserProjects(true);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> typeList = Lists.newArrayList();
        typeList.add(ScanCaseType.PTP_TRANSFER_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(typeList));

        scanPlanService.save(scanPlan);

        List<ScanFilter> scanFilterList = Lists.newArrayList();

        ScanFilter scanFilter = new ScanFilter();
        scanFilter.setFilterType(ScanFilterType.FILE_NAME.getType());
        List<String> dirList = Lists.newArrayList("**.log**", "**.out", "**.swo", "**.swp", "**.so");
        scanFilter.setFilterValue(JSONUtils.serialize(dirList));
        scanFilter.setGmtCreate(LocalDateTime.now());
        scanFilter.setGmtModified(LocalDateTime.now());
        scanFilter.setScanPlanId(scanPlan.getId()); // 需要修改

        scanFilterList.add(scanFilter);

        ScanFilter scanFilter1 = new ScanFilter();
        scanFilter1.setFilterType(ScanFilterType.DIRECT.getType());
        List<String> dirList1 = Lists.newArrayList("**.bak");
        scanFilter1.setFilterValue(JSONUtils.serialize(dirList1));
        scanFilter1.setGmtCreate(LocalDateTime.now());
        scanFilter1.setGmtModified(LocalDateTime.now());
        scanFilter1.setScanPlanId(scanPlan.getId()); // 需要修改

        scanFilterList.add(scanFilter1);

        scanFilterService.saveBatch(scanFilterList);

    }

    @Test
    public void testPath() {
        File file = new File("/Users/<USER>/develop/idea-project");
        System.out.println(file.getPath());
    }

    @Test
    public void initDsGroup() {

    }

    @Test
    public void testAddMoreInfo() {
        for (long i = 241; i < 256; i++) {
            if (i == 242) {
                continue;
            }
            ScanPlan scanPlan = scanPlanService.getById(i);
            String projectInfo = scanPlan.getProjectInfo();
            ScanProjectInfo deserialize = JSONUtils.deserialize(projectInfo, ScanProjectInfo.class);
            List<ScanGroupInfo> scanGroupInfos = deserialize.getScanGroupInfos();
            for (ScanGroupInfo scanGroupInfo : scanGroupInfos) {
                scanGroupInfo.setExcludeProjectIds(Lists.newArrayList(207));
            }
            scanPlan.setProjectInfo(JSONUtils.serialize(deserialize));
            scanPlanService.updateById(scanPlan);
        }
    }

    @Test
    public void testYTechGroup() {
        ScanPlan scanPlan = scanPlanService.getById(245);
        String projectInfo = scanPlan.getProjectInfo();
        ScanProjectInfo scanProjectInfo = JSONUtils.deserialize(projectInfo, ScanProjectInfo.class);
        List<ScanGroupInfo> scanGroupInfos = scanProjectInfo.getScanGroupInfos();
        Set<Integer> needScanProjectIds = Sets.newHashSet();
        Set<Integer> noNeedScanProjectIds = Sets.newHashSet();
        for (ScanGroupInfo scanGroupInfo : scanGroupInfos) {
            Integer groupId = scanGroupInfo.getGroupId();
            List<GitlabProject> gitlabProjects = selfGitApi.listAllProjectsByGroupIdWithSubGroups(groupId);
            for (GitlabProject gitlabProject : gitlabProjects) {
                SelfGitDetailRequest selfGitDetailRequest = new SelfGitDetailRequest();
                selfGitDetailRequest.setGitProjectId(gitlabProject.getId());
                selfGitDetailRequest.setStatistics(true);
                SelfGitProject projectDetail = selfGitApi.getProjectDetail(selfGitDetailRequest);
                Long repositorySize = projectDetail.getStatistics().getRepositorySize();
                if (10737418240L >= repositorySize) {
                    needScanProjectIds.add(gitlabProject.getId());
                } else {
                    noNeedScanProjectIds.add(gitlabProject.getId());
                }
            }
        }
        System.out.println("needScanProjectIds is : " + JSONUtils.serialize(needScanProjectIds));
        System.out.println("needScanProjectIds size is : " + needScanProjectIds.size());
        System.out.println("noNeedScanProjectIds is : " + JSONUtils.serialize(noNeedScanProjectIds));
        System.out.println("noNeedScanProjectIds size is : " + noNeedScanProjectIds.size());
    }

    @Test
    public void testScanCase() {
        // curatorFramework、GenericZkBasedNodeBuilder、ZkBasedNodeResource、KsCuratorFrameworkFactory、makeAzZkPath
        List<String> scanCases = Lists.newArrayList("SingleAz", "Singleaz", "singleAz", "singleaz");
        List<ScanCase> list = new ArrayList<>();
        for (String scanCase : scanCases) {
            list.add(new ScanCase(scanCase, "", ScanCaseType.SINGLE_AZ.getType()));
        }
        // 匹配字段 拓展字段 匹配类型
        scanCaseService.saveOrUpdateBatch(list);
    }

    @Test
    public void testDsScanPlan() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("az2特征");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setGroupId(13681);
        scanGroupInfo14.setLogicGroupName("live-server");
        scanGroupInfoList.add(scanGroupInfo14);

        ScanGroupInfo scanGroupInfo15 = new ScanGroupInfo();
        scanGroupInfo15.setGroupId(489);
        scanGroupInfo15.setLogicGroupName("server");
        scanGroupInfoList.add(scanGroupInfo15);

        ScanGroupInfo scanGroupInfo16 = new ScanGroupInfo();
        scanGroupInfo16.setGroupId(57);
        scanGroupInfo16.setLogicGroupName("ks");
        scanGroupInfoList.add(scanGroupInfo16);

        ScanGroupInfo scanGroupInfo17 = new ScanGroupInfo();
        scanGroupInfo17.setGroupId(262);
        scanGroupInfo17.setLogicGroupName("ks-ad");
        scanGroupInfoList.add(scanGroupInfo17);

        ScanGroupInfo scanGroupInfo18 = new ScanGroupInfo();
        scanGroupInfo18.setGroupId(4494);
        scanGroupInfo18.setLogicGroupName("zt-basic");
        scanGroupInfoList.add(scanGroupInfo18);

        ScanGroupInfo scanGroupInfo19 = new ScanGroupInfo();
        scanGroupInfo19.setGroupId(23824);
        scanGroupInfo19.setLogicGroupName("kuaishou-eco");
        scanGroupInfoList.add(scanGroupInfo19);

        ScanGroupInfo scanGroupInfo20 = new ScanGroupInfo();
        scanGroupInfo20.setGroupId(8025);
        scanGroupInfo20.setLogicGroupName("edu");
        scanGroupInfoList.add(scanGroupInfo20);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.AZ2_STRUCT.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);
    }

    @Test
    public void testAdGrpcOld1() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("主站");

        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo = new ScanGroupInfo();
        scanGroupInfo.setGroupId(57);
        scanGroupInfo.setLogicGroupName("主站项目1");
        scanGroupInfoList.add(scanGroupInfo);

        ScanGroupInfo scanGroupInfo1 = new ScanGroupInfo();
        scanGroupInfo1.setGroupId(489);
        scanGroupInfo1.setLogicGroupName("主站项目2");
        scanGroupInfoList.add(scanGroupInfo1);

        ScanGroupInfo scanGroupInfo2 = new ScanGroupInfo();
        scanGroupInfo2.setGroupId(13681);
        scanGroupInfo2.setLogicGroupName("主站项目3");
        scanGroupInfoList.add(scanGroupInfo2);

        ScanGroupInfo scanGroupInfo3 = new ScanGroupInfo();
        scanGroupInfo3.setGroupId(304);
        scanGroupInfo3.setLogicGroupName("主站项目4");
        scanGroupInfoList.add(scanGroupInfo3);

        ScanGroupInfo scanGroupInfo4 = new ScanGroupInfo();
        scanGroupInfo4.setGroupId(262);
        scanGroupInfo4.setLogicGroupName("主站项目5");
        scanGroupInfoList.add(scanGroupInfo4);

        ScanGroupInfo scanGroupInfo6 = new ScanGroupInfo();
        scanGroupInfo6.setGroupId(4494);
        scanGroupInfo6.setLogicGroupName("主站项目7");
        scanGroupInfoList.add(scanGroupInfo6);

        ScanGroupInfo scanGroupInfo7 = new ScanGroupInfo();
        scanGroupInfo7.setGroupId(23824);
        scanGroupInfo7.setLogicGroupName("主站项目8");
        scanGroupInfoList.add(scanGroupInfo7);

        ScanGroupInfo scanGroupInfo8 = new ScanGroupInfo();
        scanGroupInfo8.setGroupId(8025);
        scanGroupInfo8.setLogicGroupName("主站项目9");
        scanGroupInfoList.add(scanGroupInfo8);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanProjectInfo.setNeedCommitter(true);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.GRPC_CLIENT_OLD_ALL_CASE.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlan.setApplicant("sunyapeng");
        scanPlanService.save(scanPlan);

    }

    @Test
    public void testLogBackPackingData() {

        ScanCase scanCase = new ScanCase();
        scanCase.setCaseType(ScanCaseType.INFRA_LOGBACK_PACKING_DATA.getType());
        scanCase.setCaseStr("packagingData=\"true\"");

        scanCaseService.save(scanCase);
    }

    @Test
    public void testInitDs() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("电商");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setGroupId(2428);
        scanGroupInfo14.setLogicGroupName("plateco-dev");
        scanGroupInfoList.add(scanGroupInfo14);

        ScanGroupInfo scanGroupInfo16 = new ScanGroupInfo();
        scanGroupInfo16.setGroupId(809);
        scanGroupInfo16.setLogicGroupName("kspay");
        scanGroupInfoList.add(scanGroupInfo16);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.INFRA_LOGBACK_PACKING_DATA.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);
    }

    @Test
    public void testInitOneProject() {

        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("自己测试");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setProjectIds(Lists.newArrayList(24049));
        scanGroupInfo14.setLogicGroupName("自己测试");
        scanGroupInfoList.add(scanGroupInfo14);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.INFRA_LOGBACK_PACKING_DATA.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlanService.save(scanPlan);


        ScanFilter scanFilter = new ScanFilter();
        scanFilter.setFilterType(ScanFilterType.FILE_EXTEND_INCLUDE.getType());
        List<String> dirList = Lists.newArrayList(".xml");
        scanFilter.setFilterValue(JSONUtils.serialize(dirList));
        scanFilter.setGmtCreate(LocalDateTime.now());
        scanFilter.setGmtModified(LocalDateTime.now());
        scanFilter.setScanPlanId(scanPlan.getId()); // 需要修改

        scanFilterService.save(scanFilter);
    }

    @Test
    public void testInitCase() {
        List<ScanCase> scanCases = Lists.newArrayList();
        ScanCase scanCase = new ScanCase();
        scanCase.setCaseType(ScanCaseType.DYNAMIC_USER_INPUT.getType());
        scanCase.setCaseStr("CacheSetterUtils.load");
        scanCases.add(scanCase);

        ScanCase scanCase1 = new ScanCase();
        scanCase1.setCaseType(ScanCaseType.DYNAMIC_USER_INPUT.getType());
        scanCase1.setCaseStr("CacheSetterUtils.reload");
        scanCases.add(scanCase1);

        ScanCase scanCase2 = new ScanCase();
        scanCase2.setCaseType(ScanCaseType.DYNAMIC_USER_INPUT.getType());
        scanCase2.setCaseStr("load");
        scanCases.add(scanCase2);

        ScanCase scanCase3 = new ScanCase();
        scanCase3.setCaseType(ScanCaseType.DYNAMIC_USER_INPUT.getType());
        scanCase3.setCaseStr("reload");
        scanCases.add(scanCase3);


        scanCaseService.saveBatch(scanCases);
    }

    @Test
    public void testIssueScan() {
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(LocalDateTime.now());
        scanPlan.setGmtModified(LocalDateTime.now());
        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("主站");
        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo14 = new ScanGroupInfo();
        scanGroupInfo14.setLogicGroupName("主站1");
        scanGroupInfo14.setGroupId(57);
        scanGroupInfoList.add(scanGroupInfo14);

        ScanGroupInfo scanGroupInfo15 = new ScanGroupInfo();
        scanGroupInfo15.setLogicGroupName("主站2");
        scanGroupInfo15.setGroupId(4909);
        scanGroupInfoList.add(scanGroupInfo15);

        ScanGroupInfo scanGroupInfo16 = new ScanGroupInfo();
        scanGroupInfo16.setLogicGroupName("主站3");
        scanGroupInfo16.setGroupId(3620);
        scanGroupInfoList.add(scanGroupInfo16);


        ScanGroupInfo scanGroupInfo17 = new ScanGroupInfo();
        scanGroupInfo17.setLogicGroupName("主站4");
        scanGroupInfo17.setGroupId(4494);
        scanGroupInfoList.add(scanGroupInfo17);

        ScanGroupInfo scanGroupInfo18 = new ScanGroupInfo();
        scanGroupInfo18.setLogicGroupName("主站5");
        scanGroupInfo18.setGroupId(304);
        scanGroupInfoList.add(scanGroupInfo18);

        ScanGroupInfo scanGroupInfo19 = new ScanGroupInfo();
        scanGroupInfo19.setLogicGroupName("主站6");
        scanGroupInfo19.setGroupId(489);
        scanGroupInfoList.add(scanGroupInfo19);

        ScanGroupInfo scanGroupInfo20 = new ScanGroupInfo();
        scanGroupInfo20.setLogicGroupName("主站7");
        scanGroupInfo20.setGroupId(262);
        scanGroupInfoList.add(scanGroupInfo20);

        ScanGroupInfo scanGroupInfo21 = new ScanGroupInfo();
        scanGroupInfo21.setLogicGroupName("主站8");
        scanGroupInfo21.setGroupId(13681);
        scanGroupInfoList.add(scanGroupInfo21);

        ScanGroupInfo scanGroupInfo22 = new ScanGroupInfo();
        scanGroupInfo22.setLogicGroupName("主站9");
        scanGroupInfo22.setGroupId(23824);
        scanGroupInfoList.add(scanGroupInfo22);

        ScanGroupInfo scanGroupInfo23 = new ScanGroupInfo();
        scanGroupInfo23.setLogicGroupName("主站10");
        scanGroupInfo23.setGroupId(8025);
        scanGroupInfoList.add(scanGroupInfo23);


        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.SINGLE_AZ.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlan.setApplicant("tianliangliang");
        scanPlanService.save(scanPlan);
    }

    @Test
    public void init(){
        // 初始化扫描过滤规则
        List<ScanFilter> scanFilterList = Lists.newArrayList();
        ScanFilter scanFilter = new ScanFilter();
        scanFilter.setFilterType(ScanFilterType.FILE_EXTEND_INCLUDE.getType());
        List<String> dirList = Lists.newArrayList();
        dirList.add(".java");
        dirList.add(".kt");
        scanFilter.setGmtCreate(LocalDateTime.now());
        scanFilter.setGmtModified(LocalDateTime.now());
        scanFilter.setScanPlanId(0L);
        scanFilter.setFilterValue(JSONUtils.serialize(dirList));
        scanFilterList.add(scanFilter);

        ScanFilter scanFilter1 = new ScanFilter();
        scanFilter1.setFilterType(ScanFilterType.IMPORT_ITEM_SCAN.getType());
        scanFilter1.setGmtCreate(LocalDateTime.now());
        scanFilter1.setGmtModified(LocalDateTime.now());
        scanFilter1.setScanPlanId(0L);
        scanFilter1.setFilterValue(JSONUtils.serialize(Lists.newArrayList()));
        scanFilterList.add(scanFilter1);

        scanFilterService.saveBatch(scanFilterList);
    }

    @Test
    public void testAcfunIpScan() {
        LocalDateTime now = LocalDateTime.now();
        ScanPlan scanPlan = new ScanPlan();
        scanPlan.setGmtCreate(now);
        scanPlan.setGmtModified(now);

        ScanProjectInfo scanProjectInfo = new ScanProjectInfo();
        scanProjectInfo.setLogicGroupName("acfun");

        List<ScanGroupInfo> scanGroupInfoList = Lists.newArrayList();
        ScanGroupInfo scanGroupInfo = new ScanGroupInfo();
        scanGroupInfo.setLogicGroupName("acfun1");
        scanGroupInfo.setGroupId(45655);
        scanGroupInfoList.add(scanGroupInfo);

        scanProjectInfo.setScanGroupInfos(scanGroupInfoList);
        scanPlan.setProjectInfo(JSONUtils.serialize(scanProjectInfo));

        List<Integer> jobList = Lists.newArrayList();
        jobList.add(ScanCaseType.IPV4_TO_IPV6_ALL.getType());
        scanPlan.setScanCaseTypes(JSONUtils.serialize(jobList));
        scanPlan.setApplicant("lilong");
        scanPlan.setScanCases("");
        scanPlanService.save(scanPlan);

        Long planId = scanPlan.getId();

        List<ScanFilter> scanFilterList = Lists.newArrayList();
        ScanFilter scanFilter = new ScanFilter();
        scanFilter.setFilterType(ScanFilterType.FILE_EXTEND_INCLUDE.getType());
        List<String> dirList = Lists.newArrayList();
        dirList.add(".java");
        scanFilter.setGmtCreate(now);
        scanFilter.setGmtModified(now);
        scanFilter.setScanPlanId(planId);
        scanFilter.setFilterValue(JSONUtils.serialize(dirList));
        scanFilterList.add(scanFilter);

        ScanFilter scanFilter1 = new ScanFilter();
        scanFilter1.setFilterType(ScanFilterType.IMPORT_ITEM_SCAN.getType());
        scanFilter1.setGmtCreate(now);
        scanFilter1.setGmtModified(now);
        scanFilter1.setScanPlanId(planId);
        scanFilter1.setFilterValue(JSONUtils.serialize(Lists.newArrayList()));
        scanFilterList.add(scanFilter1);

        scanFilterService.saveBatch(scanFilterList);
    }


}
