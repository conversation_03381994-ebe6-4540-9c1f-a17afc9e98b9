package com.kuaishou.serveree.themis;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.component.client.kdev.KdevApi;
import com.kuaishou.serveree.themis.component.client.kdev.SelfKdevApi;
import com.kuaishou.serveree.themis.component.entity.kdev.IdAndName;
import com.kuaishou.serveree.themis.component.entity.kdev.LocalBuildInfo;
import com.kuaishou.serveree.themis.component.entity.kdev.PipelineBuildParamVo;
import com.kuaishou.serveree.themis.component.entity.kdev.SearchRequest;
import com.kuaishou.serveree.themis.component.entity.kdev.SearchResponse;
import com.kuaishou.serveree.themis.component.entity.kdev.UserProjectIdsResponse;
import com.kuaishou.serveree.themis.component.service.KdevInteractiveService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.response.CustomBuildParamVo;

import cn.hutool.core.date.StopWatch;

/**
 * <AUTHOR>
 * @since 2021/12/15 3:07 下午
 */
public class KdevApiTest extends SpringBaseTest {

    @Autowired
    private KdevApi kdevApi;

    @Autowired
    private SelfKdevApi selfKdevApi;

    @Autowired
    private KdevInteractiveService kdevInteractiveService;

    @Test
    public void testGetBuildParams() {
        PipelineBuildParamVo pipelineBuildParam = kdevApi.getPipelineBuildParam(13223999);
        System.out.println(JSONUtils.serialize(pipelineBuildParam));
    }

    @Test
    public void testInteractiveService() {
        CustomBuildParamVo customBuildParam = kdevInteractiveService.getCustomBuildParam(21081785);
        System.out.println(JSONUtils.serialize(customBuildParam));
    }

    @Test
    public void testGetUserProjectIds() {
        long l = System.currentTimeMillis();
        UserProjectIdsResponse userProjectIdsResponse = kdevApi.getUserGitProjectIds("wangchangjin");
        long l1 = System.currentTimeMillis();
        System.out.println("cost time is " + (l1 - l) + " ms");
        System.out.println(JSONUtils.serialize(userProjectIdsResponse));
    }

    @Test
    public void testGetId() {
        List<IdAndName> idAndNameList = selfKdevApi.listGitRolesByProjectId(69954, "liuliming05");
        System.out.println(JSONUtils.serialize(idAndNameList));
    }

    @Test
    public void testHasGitProjectConfigPerm() {
        List<IdAndName> idAndNameList = selfKdevApi.listGitRolesByProjectId(1019, "wangwei45");
        System.out.println(JSONUtils.serialize(idAndNameList));
        boolean hasPerm = selfKdevApi.hasGitProjectConfigAdminPerm(1019, "wangwei45");
        assert !hasPerm;
        idAndNameList = selfKdevApi.listGitRolesByProjectId(1852, "wangwei45");
        System.out.println(JSONUtils.serialize(idAndNameList));
        hasPerm = selfKdevApi.hasGitProjectConfigAdminPerm(1852, "wangwei45");
        assert hasPerm;
    }

    @Test
    public void testRepo() {
        UserProjectIdsResponse userProjectIdsResponse = selfKdevApi.getUserProjectIds("liuxiaoyue");
        System.out.println(JSONUtils.serialize(userProjectIdsResponse));
    }

    @Test
    public void codeSearch() {
        StopWatch stopWatch = StopWatch.create("codeSearch");
        stopWatch.start();
        SearchRequest searchRequest = SearchRequest.newBuilder()
                .search("getIpv4String")
                .disableHighlight(true)
                .pageNo(1)
                .pageSize(100)
                .build();
        SearchResponse searchResponse = kdevApi.codeSearch(searchRequest);
        stopWatch.stop();
        System.out.println(stopWatch.getTotalTimeMillis());
        System.out.println(JSONUtils.serialize(searchResponse));
    }

    @Test
    public void testQueryTeamIdByKspBuildId() {
        long kspBuildId = 21375563;
        String taskId = kdevApi.queryTeamIdByKspBuildId(kspBuildId);
        System.out.println(taskId);
    }

    @Test
    public void testGetLocalBuildInfo() {
        LocalBuildInfo localBuildInfo = kdevApi.getLocalBuildInfo(1446453L);
        System.out.println(JSONUtils.serialize(localBuildInfo));
    }

}
