package com.kuaishou.serveree.themis;

import java.util.List;

import org.apache.commons.compress.utils.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.api.controller.AnalyzeReportController;
import com.kuaishou.serveree.themis.component.common.entity.TaskRequestToken;
import com.kuaishou.serveree.themis.component.entity.report.IllegalMeta;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.ThemisTaskTokenUtil;
import com.kuaishou.serveree.themis.component.vo.request.AnalyzeReportRequest;

/**
 * <AUTHOR>
 * @since 2020/12/17 4:47 下午
 */
public class ReportTest extends SpringBaseTest {

    @Autowired
    private AnalyzeReportController analyzeReportController;

    @Test
    public void test01() {
        AnalyzeReportRequest reportRequest = new AnalyzeReportRequest();
        reportRequest.setRepoUrl("*************************:serveree/ks-serveree-themis.git");
        reportRequest.setBranch("master");
        reportRequest.setPipelineId(8047L);
        reportRequest.setBuildId(9084L);
        reportRequest.setCommitId("11ce394");
        reportRequest.setRuleType(1);
        reportRequest.setRuleTypePrefix("S");
        reportRequest.setAnalyzePlanExecuteId(1L);

        TaskRequestToken taskRequestToken = new TaskRequestToken();
        taskRequestToken.setSource("ksp");
        ThemisTaskTokenUtil.set(taskRequestToken);

        List<IllegalMeta> illegalMetaList = Lists.newArrayList();
        IllegalMeta illegalMeta = new IllegalMeta();
        illegalMeta.setRuleId("S001");
        illegalMeta.setIllegalFile("aaaa");
        illegalMetaList.add(illegalMeta);

        IllegalMeta illegalMeta1 = new IllegalMeta();
        illegalMeta1.setRuleId("S002");
        illegalMeta1.setIllegalFile("dafafaf");
        illegalMetaList.add(illegalMeta1);

        IllegalMeta illegalMeta2 = new IllegalMeta();
        illegalMeta2.setRuleId("S003");
        illegalMeta2.setIllegalDependency("afagag");
        illegalMetaList.add(illegalMeta2);

        reportRequest.setIllegalMetaList(illegalMetaList);
        analyzeReportController.report(reportRequest);
    }

    @Test
    public void test02(){
        String s = "{\"projectId\":null,\"mrId\":null,\"repoUrl\":\"lxx@lxx\",\"branch\":\"master\","
                        + "\"commitId\":\"12313sdad\","
                        + "\"pipelineId\":10082,\"buildId\":5,\"buildModules\":null,\"compileReport\":true,"
                        + "\"analyzePlanExecuteId\":null,\"processExecutionId\":null,\"ruleTypePrefix\":\"C\","
                        + "\"illegalMetaList\":null}\n";
        AnalyzeReportRequest deserialize = JSONUtils.deserialize(s, AnalyzeReportRequest.class);
        analyzeReportController.report(deserialize);
    }

}
