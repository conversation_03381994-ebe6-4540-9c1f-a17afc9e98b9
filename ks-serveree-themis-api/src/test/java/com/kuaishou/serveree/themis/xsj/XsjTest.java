package com.kuaishou.serveree.themis.xsj;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.regex.Pattern;

import org.junit.Test;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.utils.FutureUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-09 17:20
 **/
@Slf4j
public class XsjTest {
    private Executor executor = Executors.newFixedThreadPool(8);

    @Test
    public void testRegular() {
        List<String> ss = Lists.newArrayList(
                "线下活动有很多缺陷",
                "缺陷管理需要线下沟通",
                "ab线cd下缺陷ef",
                "1缺陷2线下3",
                "下线缺陷"
        );
        ss.forEach(s -> {
            System.out.println(ReUtil.contains(Pattern.compile("^(?=.*线下)(?=.*缺陷).+$"), s));
        });
        ss.forEach(s -> {
            System.out.println(ReUtil.contains(Pattern.compile(".*线下.*缺陷.*|.*缺陷.*线下.*"), s));
        });
    }

    @Test
    public void testTestKey() throws IOException {
        String jsonString = "{\"status\":200,\"message\":\"success\",\"data\":{\"branchId\":2732534,\"name\":\"bugfix_B1344751_liantiao_kws_deploy\",\"repoId\":11,\"groupId\":154,\"repoName\":\"kdev-parent\",\"path\":\"serveree/kdev-parent\",\"branchType\":12,\"branchTag\":\"研发分支-Bug修复\",\"lastCommitSha\":\"26bac285dff1625bdb27bfdc46cdc395aae55da5\",\"latestCommitTime\":1676257856000,\"deleted\":0,\"gitProjectId\":18621,\"teamIds\":[\"B1344751\"]},\"traceId\":\"0a9b0001641ad179b7f65f6b09561886\",\"timestamp\":1679479161300}";
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode jsonNode = objectMapper.readTree(jsonString);
        JsonNode nameNode = jsonNode.findValue("teamIds");
        String teamId = "";
        if (nameNode.isArray()) {
            teamId = nameNode.get(0).asText();
        }
        System.out.println(teamId);
    }

    @Test
    public void testArraysList() {
        List<String> triggers = Arrays.asList(
                "0 0 01 ? * MON,WED,FRI,TUE,THU",
                "0 0 02 ? * MON,WED,FRI,TUE,THU",
                "0 0 03 ? * MON,WED,FRI,TUE,THU",
                "0 0 04 ? * MON,WED,FRI,TUE,THU");
        List<String> list = new ArrayList<>(triggers);
        CollUtil.removeAny(list, "0 0 01 ? * MON,WED,FRI,TUE,THU");
        System.out.println(list);
    }

    @Test
    public void testCompletable() {
        long start = System.currentTimeMillis();

        CompletableFuture<String> task1 = FutureUtil.supplyAsyncWithException(() -> {
            try {
                Thread.sleep(1000L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("task1 thread name == {}", Thread.currentThread().getName());
            return "task1";
        }, executor, "task1");

        CompletableFuture<String> task2 = FutureUtil.supplyAsyncWithException(() -> {
            try {
                Thread.sleep(2000L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("task2 thread name == {}", Thread.currentThread().getName());
            return "task2";
        }, executor, "task2");

        CompletableFuture<String> task3 = FutureUtil.supplyAsyncWithException(() -> {
            try {
                Thread.sleep(3000L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("task3 thread name == {}", Thread.currentThread().getName());
            return "task3";
        }, executor, "task3");

        FutureUtil.getSuppleResult(task1);
        FutureUtil.getSuppleResult(task2);
        FutureUtil.getSuppleResult(task3);

        log.info("cost time:{}", System.currentTimeMillis() - start);
    }

    @Test
    public void testPeek() {
        List<CheckRepo> repos = new ArrayList<>();
        for (int i = 1; i < 5; i++) {
            CheckRepo repo = new CheckRepo();
            repo.setVersion(1);
            repos.add(repo);
        }
        repos.stream().peek(repo -> repo.setVersion(100));
        System.out.println();
    }

    @Test
    public void test() {
        String projectPath = System.getProperty("user.dir");
        System.out.println(projectPath);
    }
}
