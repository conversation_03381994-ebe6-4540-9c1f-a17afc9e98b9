package com.kuaishou.serveree.themis.api.controller;

import java.time.LocalDateTime;

import javax.annotation.Resource;

import org.junit.Test;

import com.kuaishou.serveree.themis.SpringBaseTest;
import com.kuaishou.serveree.themis.component.common.entity.MrCheckPointResult;
import com.kuaishou.serveree.themis.component.service.MrCheckPointResultService;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckGetOrCreateProjectRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckIssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckPointSponsorRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckResultRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-12-20
 */
public class MrStuckPointControllerTest extends SpringBaseTest {

    @Resource
    private MrStuckPointController mrStuckPointController;

    @Test
    public void testSponsorMrStuckCodeCheck() {
        MrStuckPointSponsorRequest sponsorRequest = new MrStuckPointSponsorRequest();
        sponsorRequest.setGitProjectId(1400);
        sponsorRequest.setRepoUrl("****************************************:kdev-test/ks-serveree-themis.git");
        sponsorRequest.setRepoLanguage("java");
        sponsorRequest.setMrId(4);
        sponsorRequest.setSourceBranch("main_v1");
        sponsorRequest.setTargetBranch("main");
        // sponsorRequest.setStuckSeverity("主要");
        sponsorRequest.setCommitId("1d50486e23648e1e87e2b6bf69c7bd818fa897b0");
        // sponsorRequest.setOnlyDiff(false);
        System.out.println(mrStuckPointController.sponsor(sponsorRequest));
    }

    @Test
    public void testGetScanResult() {
        MrStuckResultRequest resultRequest = new MrStuckResultRequest();
        resultRequest.setGitProjectId(1400L);
        resultRequest.setMrId(4L);
        resultRequest.setCommitId("1d50486e23648e1e87e2b6bf69c7bd818fa897b0");
        System.out.println(mrStuckPointController.getStuckResult(resultRequest));
    }

    @Test
    public void testIssueTransition() {
        MrStuckIssueTransitionRequest transitionRequest = new MrStuckIssueTransitionRequest();
        transitionRequest.setGitProjectId(1400L);
        transitionRequest.setMrId(4L);
        transitionRequest.setCommitId("1d50486e23648e1e87e2b6bf69c7bd818fa897b0");
        transitionRequest.setIssueId(575648L);
        transitionRequest.setTransition("falsepositive");
        System.out.println(mrStuckPointController.issueTransition(transitionRequest));
    }

    @Test
    public void testGetOrCreateProject() {
        MrStuckGetOrCreateProjectRequest request = new MrStuckGetOrCreateProjectRequest();
        request.setGitProjectId(1400);
        request.setRepoUrl("****************************************:kdev-test/ks-serveree-themis.git");
        System.out.println(mrStuckPointController.getOrCreateProject(request));
    }
    
    @Resource
    private MrCheckPointResultService mrCheckPointResultService;
    
    @Test
    public void testMrCheckPointResultService() {
        MrCheckPointResult result = new MrCheckPointResult();
        result.setGitProjectId(1400L);
        result.setMrId(69L);
        result.setCommitId("6b48725df8dae2123a345952d8cd37448cbc4877");
        result.setCheckpointName("CodeScan");
        result.setStatus("FAILEd");
        result.setPipelineId(0L);
        result.setBuildId(0L);
        result.setBuildUrl("");
        result.setGmtCreate(LocalDateTime.now());
        result.setGmtModified(LocalDateTime.now());

        mrCheckPointResultService.saveOnDuplicateKeyUpdate(result);
    }
}
