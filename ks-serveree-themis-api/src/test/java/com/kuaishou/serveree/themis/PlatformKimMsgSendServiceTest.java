package com.kuaishou.serveree.themis;

import java.util.Collections;
import java.util.Set;

import javax.annotation.Resource;

import org.junit.Test;

import com.kuaishou.serveree.themis.component.service.platform.notice.PlatformKimMsgSendService;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-01-12
 */
public class PlatformKimMsgSendServiceTest extends SpringBaseTest {

    @Resource
    private PlatformKimMsgSendService kimMsgSendService;

    @Test
    public void testSendTxtMsg() {
        kimMsgSendService.sendTextMsg(Set.of("wangwei45"), "### test");
    }

    @Test
    public void testSendToUser() {
        kimMsgSendService.sendMarkdownMsgToKimRobotAndUsers("", "#### test", Set.of("wangwei45"));
    }

    @Test
    public void testSendToKimRobot() {
        kimMsgSendService.sendMarkdownMsgToKimRobotAndUsers("https://kim-robot.kwaitalk.com/api/robot/send?key=58f95250-9a40-4a9c-b208-07c2783291d2",
                "### test", Collections.emptySet());
    }

    @Test
    public void testSendToUsersAndKimRobot() {
        kimMsgSendService.sendMarkdownMsgToKimRobotAndUsers("https://kim-robot.kwaitalk.com/api/robot/send?key=65c4aaa1-a421-42b0-8b62-9fcfab09d690",
                "### test", Collections.emptySet());
    }

}
