package com.kuaishou.serveree.themis.ww;

import java.util.Objects;

import javax.annotation.Resource;

import org.junit.Assert;
import org.junit.Test;

import com.kuaishou.serveree.themis.SpringBaseTest;
import com.kuaishou.serveree.themis.component.common.entity.ComplexityExecRecord;
import com.kuaishou.serveree.themis.component.service.ComplexityExecRecordService;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-02-28
 */
public class ProjectMetricStatisticTest extends SpringBaseTest {

    @Resource
    private ComplexityExecRecordService complexityExecRecordService;

    @Test
    public void testComplexityExecRecordService() {
        ComplexityExecRecord execRecord = complexityExecRecordService.getByKdevTaskId(111L);
        Assert.assertTrue(Objects.isNull(execRecord));
    }
}
