package com.kuaishou.serveree.themis;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.alibaba.excel.EasyExcel;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.component.common.entity.Az2DepartmentProjectInfo;
import com.kuaishou.serveree.themis.component.common.entity.Az2ScanPlan;
import com.kuaishou.serveree.themis.component.common.entity.Az2ScanWhitelist;
import com.kuaishou.serveree.themis.component.constant.statics.AZ2WhitelistType;
import com.kuaishou.serveree.themis.component.service.db.Az2DepartmentProjectInfoService;
import com.kuaishou.serveree.themis.component.service.db.Az2ScanIssueService;
import com.kuaishou.serveree.themis.component.service.db.Az2ScanPlanService;
import com.kuaishou.serveree.themis.component.service.db.Az2ScanWhitelistService;
import com.kuaishou.serveree.themis.component.service.statics.az2.AZ2HardCodeStringMatchScanHelper;
import com.kuaishou.serveree.themis.component.service.statics.az2.AZ2HardCodeStringMatchScanService;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.json.JSONUtil;

/**
 * <AUTHOR>
 * @since 2022/11/29 4:33 PM
 */
public class AZ2ScanTest extends SpringBaseTest {

    @Autowired
    private AZ2HardCodeStringMatchScanService az2HardCodeStringMatchScanService;
    @Autowired
    private AZ2HardCodeStringMatchScanHelper az2HardCodeStringMatchScanHelper;
    @Autowired
    private Az2ScanPlanService az2ScanPlanService;
    @Autowired
    private Az2ScanIssueService az2ScanIssueService;
    @Autowired
    private Az2DepartmentProjectInfoService az2DepartmentProjectInfoService;
    @Autowired
    private Az2ScanWhitelistService az2ScanWhitelistService;

    @Test
    public void testDomainInit() {
        StopWatch stopWatch = StopWatch.create("aaa");
        stopWatch.start();
        LocalDateTime now = LocalDateTime.now();
        az2HardCodeStringMatchScanService.initAZ2DomainList(now);
        stopWatch.stop();
        System.out.println(stopWatch.getTotalTimeMillis());
    }

    @Test
    public void testOneProject() {
        //        az2HardCodeStringMatchScanService.initAZ2DomainList(LocalDateTime.now());

        StopWatch stopWatch = StopWatch.create("bbb");
        stopWatch.start();
        az2HardCodeStringMatchScanHelper.scanFileCodeLine(
                "/Users/<USER>/develop/idea-project/kuaishou-search-component", 1, 1L);
        stopWatch.stop();
        System.out.println(stopWatch.getTotalTimeMillis());
    }

    @Test
    public void testClean3Days() {
        az2HardCodeStringMatchScanService.cleanBefore3DaysDomain();
    }

    @Test
    public void refresh() {
        az2HardCodeStringMatchScanService.refreshDepartmentGitProjectIds();
    }

    @Test
    public void testLength() {
        Az2ScanPlan az2ScanPlan = az2ScanPlanService.getById(155);
        String scanGitProjectIds = az2ScanPlan.getScanGitProjectIds();
        ArrayList<Integer> integers = Arrays.stream(scanGitProjectIds.split(",")).map(Integer::parseInt)
                .collect(Collectors.toCollection(Lists::newArrayList));
        System.out.println(integers.size());
    }

    @Test
    public void testDepartment() {
        int count = 0;
        List<String> ids = Lists.newArrayList();
        List<Az2DepartmentProjectInfo> list = az2DepartmentProjectInfoService.list();
        for (Az2DepartmentProjectInfo az2DepartmentProjectInfo : list) {
            if (az2DepartmentProjectInfo.getId() >= 82 && az2DepartmentProjectInfo.getId() <= 147) {
                String[] split = az2DepartmentProjectInfo.getConfirmGitProjectIds().split(",");
                ArrayList<String> strings = Lists.newArrayList(split);
                ids.addAll(strings);
                count += split.length;
                System.out.println(strings.toString());
            }
        }
        System.out.println(count);
        Az2ScanPlan az2ScanPlan = new Az2ScanPlan();
        az2ScanPlan.setComplete(false);
        az2ScanPlan.setRounds(0);
        az2ScanPlan.setAz2DepartmentProjectInfoId(0L);
        az2ScanPlan.setGmtModified(LocalDateTime.now());
        az2ScanPlan.setGmtCreate(LocalDateTime.now());
        az2ScanPlan.setScanGitProjectIds(Joiner.on(",").join(ids));
        az2ScanPlanService.save(az2ScanPlan);
    }

    @Test
    public void initPlan() {
        List<Az2DepartmentProjectInfo> list = az2DepartmentProjectInfoService.list();
        List<Az2ScanPlan> az2ScanPlans = Lists.newArrayList();
        for (Az2DepartmentProjectInfo az2DepartmentProjectInfo : list) {
            if (az2DepartmentProjectInfo.getId() > 1) {
                Az2ScanPlan az2ScanPlan = new Az2ScanPlan();
                az2ScanPlan.setComplete(false);
                az2ScanPlan.setGmtModified(LocalDateTime.now());
                az2ScanPlan.setGmtCreate(LocalDateTime.now());
                az2ScanPlan.setAz2DepartmentProjectInfoId(az2DepartmentProjectInfo.getId());
                az2ScanPlan.setRounds(1);
                az2ScanPlan.setScanGitProjectIds(az2DepartmentProjectInfo.getConfirmGitProjectIds());
                az2ScanPlans.add(az2ScanPlan);
            }
        }
        az2ScanPlanService.saveBatch(az2ScanPlans);
    }

    @Test
    public void testIn() throws FileNotFoundException {
        Az2ScanPlan az2ScanPlan = az2ScanPlanService.getById(83);
        String scanGitProjectIds = az2ScanPlan.getScanGitProjectIds();
        String[] split = scanGitProjectIds.split(",");
        List<Integer> collect = Arrays.stream(split).map(Integer::parseInt).collect(Collectors.toList());
        //        ArrayList<Integer> integers =
        //                Lists.newArrayList(16297, 37684, 25114, 23894, 38492, 31133, 44084, 52838, 62112, 17534,
        //                8163, 20956,
        //                        44505,
        //                        56454, 30460, 65354, 39536, 55608, 33228, 30373, 15744, 25045, 56620, 25158, 10826,
        //                        7828, 34719,
        //                        43386,
        //                        45185, 52250, 19256, 33640, 2383, 2845, 17276, 35681, 25748, 5408, 1910, 2219,
        //                        7849, 52719,
        //                        22137,
        //                        14762, 6302, 15332, 56165, 38493, 9558, 17258, 31532, 52636, 41098, 61098, 5230,
        //                        55108, 49045,
        //                        30124,
        //                        29761, 37685, 45184, 36605, 43992, 45511, 45676, 58058, 51117, 49772, 23125, 32575,
        //                        53131,
        //                        46401, 53129,
        //                        57138, 1354, 65869, 38772, 9921, 7744, 3008, 18962, 31363, 6863, 3135, 30181,
        //                        14759, 29806,
        //                        7553, 46632,
        //                        28644, 5407, 18997, 41514, 46726, 66254, 5075, 50475, 39564, 32669, 46944, 37683,
        //                        43601, 5472,
        //                        35754,
        //                        35110, 11292, 52068, 11741, 3003, 36169, 27032, 31701, 48485, 17269, 6631, 43629,
        //                        8574, 36349,
        //                        4659,
        //                        19006, 35986, 21806, 17271, 64981, 45932, 3736, 11441, 32047, 8352, 40319, 47514,
        //                        902, 34141,
        //                        20207,
        //                        5066, 35676, 45502, 35552, 9286, 17286, 22180, 46015, 43209, 8964, 45102, 21060,
        //                        57824, 65557,
        //                        66064,
        //                        44170, 24051, 51433, 67197, 43934, 10325, 38399, 18494, 65974, 63697, 35682, 30534,
        //                        46682,
        //                        59227, 53692,
        //                        7944, 65046, 5833, 15353, 35683, 22730, 52888, 2674, 20688, 59201, 52721, 6128,
        //                        34140, 640,
        //                        55777,
        //                        21148, 1819, 50429, 42244, 45080, 34603, 41660, 52398, 67365, 46406, 41663, 29969,
        //                        32868, 123,
        //                        109, 83,
        //                        4243, 31917, 21196, 22348, 8933, 25959, 28727, 49309, 20642, 166, 22446, 62854,
        //                        58761, 35345,
        //                        28847,
        //                        57511, 6064, 170, 33008, 24838, 26062, 27832, 46999, 207, 27853, 28967, 30690,
        //                        38309, 20776,
        //                        22459,
        //                        66279, 24861, 17139, 17142, 37183, 55274, 37185, 41714, 16709, 26625, 47087, 54762,
        //                        6673, 4782,
        //                        34279,
        //                        41726, 65909, 62234, 12948, 49957, 14340, 29501, 41291, 2112, 43598, 49552, 49554,
        //                        22599, 27151,
        //                        9722,
        //                        33192, 33194, 34889, 6249, 65933, 55934, 58902, 30239, 65968, 50680, 4539, 30232,
        //                        66401, 26692,
        //                        4918,
        //                        57805, 25029, 67173, 884, 26223, 34939, 46721, 883, 41425, 34952, 50841, 487,
        //                        34964, 30375,
        //                        26294,
        //                        33642, 63656, 9915, 9912, 33641, 33651, 8674, 34460, 1740, 34494, 27869, 25152,
        //                        51362, 51832,
        //                        33648,
        //                        55779, 1742, 57966, 51839, 48338, 33653, 29261, 10427, 41595, 35022, 51838, 27265,
        //                        33662, 41916,
        //                        67326,
        //                        51836, 51840, 30950, 67343, 39569, 13972, 26356, 37835, 35042, 9165, 41923, 55957,
        //                        27877, 51841,
        //                        14545,
        //                        25583, 23247, 37874, 51842, 23739, 51843, 64245, 35513, 66573, 23329, 39662, 56103,
        //                        63721,
        //                        35620, 30947,
        //                        10946, 30992, 14546, 27456, 42033, 42067, 66644, 33872, 13386, 21616, 42076, 33956,
        //                        43808,
        //                        42177, 2918,
        //                        37833, 889, 46292, 46291, 11604, 36178, 48430, 6995, 2325, 26951, 23355, 2919,
        //                        11113, 23323,
        //                        35740,
        //                        19345, 23485, 19388, 19847, 36268, 40332, 11717, 28155, 48632, 28208, 52822, 24155,
        //                        36450,
        //                        61007, 55665,
        //                        61031, 20162, 48814, 40703, 24157, 32521, 12071, 7938, 7998, 44899, 18749, 12028,
        //                        36766, 8071,
        //                        3988,
        //                        32719, 3987, 40929, 55709, 55656, 35274, 48088, 35779, 39866, 11236, 35817, 4079,
        //                        60407, 4012,
        //                        44064,
        //                        39374, 39897, 48170, 64568, 64569, 48144, 52305, 39873, 48224, 44107, 31137, 56310,
        //                        27560,
        //                        23509, 22877,
        //                        59361, 3456, 42541, 2678, 61646, 4469, 21801, 66513, 37916, 65249, 1391, 45039,
        //                        63253, 28870,
        //                        85, 51824,
        //                        359, 12859, 11021, 31835, 27068, 16504, 17334, 63820, 57024, 16300, 5976, 65355,
        //                        20249, 7824,
        //                        63259,
        //                        5576, 859, 11166, 38444, 55431, 32820, 16518, 2724, 7819, 8516, 1322, 32662, 27120,
        //                        63299,
        //                        19726, 47503,
        //                        54572, 32663, 24587, 1369, 37180, 45939, 13932, 24273, 3408, 22727, 26719, 64642,
        //                        1332, 7779,
        //                        33134,
        //                        42198, 55466, 6725, 36352, 14378, 9648, 49449, 23189, 33520, 19118, 32905, 67278,
        //                        27488, 47147,
        //                        1767,
        //                        21769, 6553, 35003, 16590, 19478, 7909, 4189, 53492, 753, 1274, 39799, 45747,
        //                        19166, 45045,
        //                        18825,
        //                        65503, 41602, 25341, 44345, 8383, 45562, 30611, 5441, 62046, 62799, 12913, 19780,
        //                        59784, 17257,
        //                        1950,
        //                        23219, 58158, 42008, 6546, 52434, 14793, 24310, 63964, 7586, 31989, 5967, 824,
        //                        46354, 10914,
        //                        19298,
        //                        6856, 20687, 795, 35769, 4407, 8910, 30156, 46634, 28199, 46636, 63134, 53866,
        //                        23962, 8557,
        //                        1186, 21699,
        //                        15786, 43872, 62305, 360, 43298, 7147, 12409, 47838, 52482, 66030, 33649, 50274,
        //                        47143, 52714,
        //                        17001,
        //                        1000, 17324, 25282, 2882, 1062, 3391, 23361, 27407, 3225, 6890, 20591, 9178, 34790,
        //                        61708,
        //                        15505, 15266,
        //                        5526, 13078, 39551, 8213, 23560, 4605, 61739, 50508, 24630, 406, 57072, 48348,
        //                        13575, 40358,
        //                        26313,
        //                        3099, 3338, 33711, 25806, 33496, 15033, 17835, 40822, 27460, 17567, 46686, 37287,
        //                        41277, 43172,
        //                        10587,
        //                        27698, 22929, 13403, 28743, 8530, 20246, 12826, 811, 1271, 66999, 16888, 36607,
        //                        67494, 19555,
        //                        49542,
        //                        23549, 16452, 25369, 27307, 43304, 61926, 28623, 40119, 1967, 15388, 65723, 4188,
        //                        9434, 47426,
        //                        14010,
        //                        19250, 35069, 26238, 53020, 47581, 9749, 21212, 15952, 65048, 19501, 48195, 27136,
        //                        11991, 41825,
        //                        463,
        //                        15219, 6994, 26876, 32165, 26601, 23989, 15573, 21307, 4457, 18418, 55889, 58174,
        //                        22706, 5280,
        //                        4382,
        //                        33478, 32965, 1032, 46232, 33042, 38466, 1552, 24299, 23516, 63130, 888, 30090,
        //                        27656, 7243,
        //                        25609,
        //                        66420, 61624, 50459, 11063, 10666, 45044, 65874, 19980, 54085, 1050, 8714, 17314,
        //                        38155, 67348,
        //                        17284,
        //                        349, 22487, 4496, 3403, 65518, 6901, 43030, 785, 17180, 37497, 6800, 6562, 17652,
        //                        5389, 47868,
        //                        38775,
        //                        24283, 61628, 45591, 19526, 19106, 17270, 21755, 2726, 23334, 3428, 23520, 2306,
        //                        44698, 42646,
        //                        2880,
        //                        57724, 62695, 4185, 21968, 7490, 24184, 16397, 44634, 59598, 22707, 14067, 66028,
        //                        28225, 172,
        //                        55820,
        //                        22722, 995, 7096, 46752, 8610, 51505, 20212, 62008, 20240, 27003, 10406, 199,
        //                        25420, 18039,
        //                        21735,
        //                        25556, 49625, 64585, 55682, 5156, 40280, 9542, 8713, 8803, 7286, 49940, 5294,
        //                        12803, 12392,
        //                        29462,
        //                        42586, 15282, 21400, 400, 33272, 19812, 52254, 64552, 51331, 65077, 23160, 24984,
        //                        8202, 21211,
        //                        23455,
        //                        17566, 13325, 13794, 43821, 24179, 37709, 22979, 60608, 40220, 11959, 29014, 40210,
        //                        2126, 41818,
        //                        38068,
        //                        2124, 35612, 56500, 50418, 42400, 9651, 6639, 18023, 40875, 17330, 15578, 1196,
        //                        63654, 57520,
        //                        20460,
        //                        57268, 45294, 40240, 13525, 13997, 20107, 1568, 5196, 2804, 12182, 1364, 55804,
        //                        31128, 31327,
        //                        63498,
        //                        53207, 52217, 1701, 30315, 27779, 25772, 21469, 45980, 26525, 39627, 32387, 3550,
        //                        13868, 4070,
        //                        17935,
        //                        31790, 19049, 24162, 66753, 4584, 23382, 3849, 21581, 44296, 19728, 21779, 23191,
        //                        47330, 45885,
        //                        34469,
        //                        65329, 2808, 14975, 5053, 53611, 58423, 4401, 9243, 27357, 2153, 14069, 25763,
        //                        12820, 10712,
        //                        15460,
        //                        57586, 16416, 15523, 33782, 39138, 40106, 66210, 6517, 21994, 17748, 40082, 16376,
        //                        17283, 10926,
        //                        24134,
        //                        31793, 219, 43654, 6339, 21549, 59896, 30340, 7668, 18422, 5766, 674, 11289, 60213,
        //                        12231, 5502,
        //                        28650,
        //                        9318, 4552, 19267);
        //        System.out.println(collect);
        //        System.out.println("=======");
        //        collect.removeIf(o -> integers.contains(o));
        //        System.out.println(collect);
        //        az2ScanPlan.setScanGitProjectIds(Joiner.on(",").join(collect));
        //        az2ScanPlanService.updateById(az2ScanPlan);
        List<Integer> integers = ListUtil.splitAvg(collect, 6).get(4);
        System.out.println(JSONUtil.toJsonStr(integers));
    }

    @Test
    public void updateInfo() throws FileNotFoundException {
        List<Az2DepartmentProjectInfo> list = az2DepartmentProjectInfoService.list();
        FileInputStream fileInputStream =
                new FileInputStream("/Users/<USER>/Downloads/idp_export_idp_10160952_newfile_20221208112140.xlsx");
        List<Map<Integer, String>> domainMapList = EasyExcel.read(fileInputStream, null, null).sheet().doReadSync();
        List<String> nameString = Lists.newArrayList();
        for (Map<Integer, String> integerStringMap : domainMapList) {
            nameString.add(integerStringMap.get(0));
        }
        ArrayList<Long> objects = Lists.newArrayList();
        for (Az2DepartmentProjectInfo az2DepartmentProjectInfo : list) {
            if (!nameString.contains(az2DepartmentProjectInfo.getDepartmentName())) {
                objects.add(az2DepartmentProjectInfo.getId());
            }
        }
        az2DepartmentProjectInfoService.removeByIds(objects);
    }

    @Test
    public void test1() {
        Kconf<Boolean> parallelSwitch =
                Kconfs.ofBoolean("qa.themis.az2UseParallelStream", false).build();
        Boolean aBoolean = parallelSwitch.get();
        System.out.println(aBoolean);
    }

    @Test
    public void initWhitelist() {
        List<String> whiteList =
                Lists.newArrayList("https://git.corp.kuaishou.com/ks-game-qa-for-agency/molibaobei.git",
                        "https://git.corp.kuaishou.com/ks-game-qa-for-agency/zhengzhanjiyuan.git",
                        "https://git.corp.kuaishou.com/ks-game-qa-for-agency/wudongxsy.git",
                        "https://git.corp.kuaishou.com/ks-game-qa-for-agency/island_match.git",
                        "https://git.corp.kuaishou.com/ks-game-qa-for-agency/game_cloud_ad_mock_server.git",
                        "https://git.corp.kuaishou.com/ks-game-qa-for-agency/shenyinzhizi.git",
                        "https://git.corp.kuaishou.com/ks-game-qa-for-agency/game_cloud_ad_stress_test.git",
                        "https://git.corp.kuaishou.com/ks-game-qa-for-agency/game_cloud_ad_auto_test.git",
                        "https://git.corp.kuaishou.com/ks-game-qa-for-agency/kdip.git",
                        "https://git.corp.kuaishou.com/ks-game-qa-for-agency/xingzhishengge.git",
                        "https://git.corp.kuaishou.com/game-cloud/game-cloud-datax-shushu.git",
                        "https://git.corp.kuaishou.com/game-web/kuaishou-frontend-gamegm-next.git",
                        "https://git.corp.kuaishou.com/game-demo/other/gdc-crawler.git",
                        "https://git.corp.kuaishou.com/game-cloud/easydata-frontend.git",
                        "https://git.corp.kuaishou.com/game-cloud/easydata-server.git",
                        "https://git.corp.kuaishou.com/game-cloud/game-cloud-kgprofile.git",
                        "https://git.corp.kuaishou.com/game-web/product_tag_fe.git",
                        "https://git.corp.kuaishou.com/game-cloud/game-cloud-coredump.git",
                        "https://git.corp.kuaishou.com/lib-iOS/GameLiveProtoModel.git",
                        "https://git.corp.kuaishou.com/game-web/kuaishou-frontend-custormer-service-admin.git",
                        "https://git.corp.kuaishou.com/tianyan-gamead/mlbb_content_rec.git",
                        "https://git.corp.kuaishou.com/kuaishou-game/script/release-doraemon-server.git",
                        "https://git.corp.kuaishou.com/sre/ksgame-nginx-conf-global",
                        "https://git.corp.kuaishou.com/sre/ksgame-nginx-conf",
                        "https://git.corp.kuaishou.com/kwaiops-sre/kwaiops-task.git",
                        "https://git.corp.kuaishou.com/kwaiops-sre/kwai-report.git",
                        "https://git.corp.kuaishou.com/kwaiops-sre/kwaiops-executestatus.git",
                        "https://git.corp.kuaishou.com/kwaiops-sre/vue-element-admin.git",
                        "https://git.corp.kuaishou.com/kwaiops-sre/kwaiops-flow-message.git",
                        "https://git.corp.kuaishou.com/kwaiops-sre/kwaiops-crontabtree.git",
                        "https://git.corp.kuaishou.com/kwaiops-sre/keysystem.git",
                        "https://git.corp.kuaishou.com/kwaiops-sre/kwaiops-filestore.git",
                        "https://git.corp.kuaishou.com/kwaiops-sre/kwaiops-flowapprove.git",
                        "https://git.corp.kuaishou.com/ksgame/yhjjx/conf.git"
                );

        List<Az2ScanWhitelist> az2ScanWhitelists = Lists.newArrayList();
        for (String s : whiteList) {
            Az2ScanWhitelist az2ScanWhitelist = new Az2ScanWhitelist();
            az2ScanWhitelist.setGmtCreate(LocalDateTime.now());
            az2ScanWhitelist.setGmtModified(LocalDateTime.now());
            az2ScanWhitelist.setWhiteType(AZ2WhitelistType.PROJECT_WHITELIST.getType());
            az2ScanWhitelist.setWhiteLink(s);
            az2ScanWhitelists.add(az2ScanWhitelist);
        }

        az2ScanWhitelistService.saveBatch(az2ScanWhitelists);
    }

    @Test
    public void testScanId(){
        List<Integer> integers = az2ScanIssueService.listProjectIdByScanPlanId(83L);
        System.out.println(integers);
    }

}
