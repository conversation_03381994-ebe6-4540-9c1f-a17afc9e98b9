package com.kuaishou.serveree.themis;

import static com.google.common.base.CaseFormat.LOWER_CAMEL;
import static com.google.common.base.CaseFormat.LOWER_UNDERSCORE;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;

import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Sets;
import org.gitlab.api.models.GitlabProject;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.google.common.collect.Lists;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.serveree.themis.api.controller.MallProjectController;
import com.kuaishou.serveree.themis.api.controller.SonarController;
import com.kuaishou.serveree.themis.component.client.git.GitOperations;
import com.kuaishou.serveree.themis.component.client.git.SelfGitApi;
import com.kuaishou.serveree.themis.component.client.kim.KimApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.CorpSonarApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.KFormatSonarApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.MetricSonarApi;
import com.kuaishou.serveree.themis.component.client.sonar.api.SonarCommonApi;
import com.kuaishou.serveree.themis.component.client.sonar.operations.ClusterNode1Operations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.ClusterNode4Operations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.ClusterNode5Operations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.CorpSonarOperations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.KFormatSonarOperations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterOperations;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarClusterSimpleFactory;
import com.kuaishou.serveree.themis.component.client.sonar.operations.SonarOperations;
import com.kuaishou.serveree.themis.component.common.entity.CheckLabel;
import com.kuaishou.serveree.themis.component.common.entity.CheckRule;
import com.kuaishou.serveree.themis.component.common.entity.CheckRuleLabelRelation;
import com.kuaishou.serveree.themis.component.common.entity.PCheckBase;
import com.kuaishou.serveree.themis.component.common.entity.PCheckIssue;
import com.kuaishou.serveree.themis.component.common.exception.ThemisException;
import com.kuaishou.serveree.themis.component.constant.ResultCodeConstant;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformLanguageEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.sonar.SonarMetricsEnum;
import com.kuaishou.serveree.themis.component.entity.platform.CheckRuleListCondition;
import com.kuaishou.serveree.themis.component.entity.plugin.changedFiles.ChangedFilesResponse.ChangedFile;
import com.kuaishou.serveree.themis.component.entity.plugin.changedFiles.ChangedFilesResponse.ChangedFile.Fragment;
import com.kuaishou.serveree.themis.component.entity.process.ScanModeContext;
import com.kuaishou.serveree.themis.component.entity.sonar.HookPayload;
import com.kuaishou.serveree.themis.component.entity.sonar.Issue;
import com.kuaishou.serveree.themis.component.entity.sonar.Measure;
import com.kuaishou.serveree.themis.component.entity.sonar.Profiles;
import com.kuaishou.serveree.themis.component.entity.sonar.Project;
import com.kuaishou.serveree.themis.component.entity.sonar.RuleDetail;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarComponent;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarProject;
import com.kuaishou.serveree.themis.component.entity.sonar.User;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasuresComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.QualityProfileReq;
import com.kuaishou.serveree.themis.component.entity.sonar.req.SonarSearchRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.BranchListResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.HistoryMeasureResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.IssuesResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentTreeResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasuresComponentTreeResponse;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProfileInheritanceResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProfileProjectsResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProjectAnalyseResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProjectCreateResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.ProjectsResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.QualityProfileResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.RulesDetailResp;
import com.kuaishou.serveree.themis.component.service.CheckLabelService;
import com.kuaishou.serveree.themis.component.service.CheckRuleLabelRelationService;
import com.kuaishou.serveree.themis.component.service.CheckRuleService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.PCheckIssueService;
import com.kuaishou.serveree.themis.component.service.SonarIssueService;
import com.kuaishou.serveree.themis.component.service.sonar.CommonSonarReportService;
import com.kuaishou.serveree.themis.component.service.sonar.JavaMavenSonarReportService;
import com.kuaishou.serveree.themis.component.service.sonar.SonarProjectService;
import com.kuaishou.serveree.themis.component.service.sonar.SonarService;
import com.kuaishou.serveree.themis.component.service.sonar.mode.CompleteModeServiceImpl;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.ProjectTeamRequest;
import com.kuaishou.serveree.themis.component.vo.request.SearchIssueRequest;
import com.kuaishou.serveree.themis.component.vo.request.SearchRuleRequest;
import com.kuaishou.serveree.themis.component.vo.request.SonarProjectCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.SonarStuckPointRequest;
import com.kuaishou.serveree.themis.component.vo.request.sonar.PluginPipelineReportRequest;
import com.kuaishou.serveree.themis.component.vo.request.sonar.PluginPipelineReportRequest.PipelineIssue;
import com.kuaishou.serveree.themis.component.vo.response.Facet;
import com.kuaishou.serveree.themis.component.vo.response.Facet.FacetValue;
import com.kuaishou.serveree.themis.component.vo.response.ProjectTeamResponse;
import com.kuaishou.serveree.themis.component.vo.response.SearchIssuesResponse;
import com.kuaishou.serveree.themis.component.vo.response.SearchRulesResponse;
import com.kuaishou.serveree.themis.component.vo.response.SearchRulesResponse.ActiveRuleInfo;
import com.kuaishou.serveree.themis.component.vo.response.SonarStuckPointResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.NeedCompileResponse;
import com.kuaishou.serveree.themis.component.vo.response.sonar.ParallelExecuteResponse;

/**
 * <AUTHOR>
 * @since 2020/10/21 11:35 上午
 */
public class SonarTest extends SpringBaseTest {

    @Autowired
    private CorpSonarApi corpSonarApi;

    @Autowired
    private KFormatSonarApi kFormatSonarApi;

    @Autowired
    private JavaMavenSonarReportService javaMavenSonarReportService;

    @Autowired
    private CorpSonarOperations corpSonarOperations;

    @Autowired
    private CommonSonarReportService commonSonarReportService;

    @Autowired
    private MallProjectController mallProjectController;

    @Autowired
    private SonarProjectService sonarProjectService;

    @Autowired
    private SonarController sonarController;

    @Autowired
    private SonarService sonarService;

    @Autowired
    private KimApi kimApi;

    @Autowired
    private MetricSonarApi metricSonarApi;

    @Autowired
    private KFormatSonarOperations kFormatSonarOperations;

    @Autowired
    private SonarClusterSimpleFactory sonarClusterSimpleFactory;

    @Autowired
    private SelfGitApi selfGitApi;

    @Autowired
    private ClusterNode1Operations node1Operations;

    @Autowired
    private CheckRuleService checkRuleService;

    @Autowired
    private CheckLabelService checkLabelService;

    @Autowired
    private CheckRuleLabelRelationService checkRuleLabelRelationService;

    @Autowired
    private CompleteModeServiceImpl scanModeService;

    @Autowired
    private PCheckBaseService pCheckBaseService;

    @Autowired
    private PCheckIssueService pCheckIssueService;

    @Autowired
    private SonarIssueService sonarIssueService;

    @Autowired
    private GitOperations gitOperations;

    @Autowired
    private ClusterNode1Operations clusterNode1Operations;

    @Autowired
    private ClusterNode1Operations clusterNode3Operations;

    @Value("${maven-scanner-new.issue-list-url}")
    private String issueUrl;

    @Autowired
    private List<SonarClusterOperations> sonarClusterOperations;
    @Autowired
    private ClusterNode4Operations clusterNode4Operations;
    @Autowired
    private ClusterNode5Operations clusterNode5Operations;

    @Test
    public void getProjects() {
        ProjectsResp projects = corpSonarApi.getProjects(1, 10);
        System.out.println(JSONUtils.serialize(projects));
    }

    @Test
    public void test02() {
        ProjectAnalyseResp projectAnalyseResp =
                corpSonarApi.searchAnalyses("kuaishou:zt-encourage-portrait", null, "2022-06-22");
        System.out.println(JSONUtils.serialize(projectAnalyseResp));
    }

    @Test
    public void test03() {
        HistoryMeasureResp bugs =
                corpSonarApi.getHistoryMeasure("kuaishou:kwaishop-sellergrowth-engine-center-parent", "bugs", "", "");
        System.out.println(JSONUtils.serialize(bugs));
    }

    @Test
    public void test04() {
        //        VULNERABILITY
        int allV = corpSonarOperations
                .getIssuesCount("kuaishou:kuaishou-recommend", "2020-11-07T10:40:19+0800", "2020-11-07T10:40:19+0800",
                        "VULNERABILITY", null);

        int falseV = corpSonarOperations
                .getIssuesCount("kuaishou:kuaishou-recommend", "2020-11-07T10:40:19+0800", "2020-11-07T10:40:19+0800",
                        "", false);

        System.out.println("allV is " + allV + "    falseV is " + falseV);

    }

    @Test
    public void test05() {

        String metrics = Arrays.stream(Measure.class.getDeclaredFields()).collect(toList()).stream() //
                .map(Field::getName) //
                .map(LOWER_CAMEL.converterTo(LOWER_UNDERSCORE)::convert) //
                .collect(joining(","));
        Measure historyMeasure = corpSonarOperations
                .getHistoryMeasure("kuaishou:kuaishou-recommend", metrics, "2020-11-07T10:40:19+0800",
                        "2020-11-07T10:40:19+0800");
        System.out.println(JSONUtils.serialize(historyMeasure));

    }

    @Test
    public void test06() {
        javaMavenSonarReportService.generateSonarReport();
    }

    @Test
    public void test07() {
        commonSonarReportService.generateSonarReport();
    }

    @Test
    public void test08() {
        Kconf<Boolean> CHECK_TAG = Kconfs.ofBoolean("qa.themis.checkTag", true).build();
        System.out.println(CHECK_TAG.get());
        Kconf<Boolean> COMPENSATE_TAG = Kconfs.ofBoolean("qa.themis.compensateTag", true).build();
        System.out.println(COMPENSATE_TAG.get());
    }

    @Test
    public void test09() {
        ProjectTeamRequest projectTeamRequest = new ProjectTeamRequest();
        List<String> repoUrls = Lists.newArrayList();
        repoUrls.add("https://git.corp.kuaishou.com/plateco-dev/kuaishou-merchant-education");
        repoUrls.add("https://git.corp.kuaishou.com/plateco-dev/kuaishou-merchant-ecology");
        repoUrls.add("https://git.corp.kuaishou.com/plateco-dev/kuaishou-merchant-settlement");
        projectTeamRequest.setRepoUrls(repoUrls);
        ThemisResponse<ProjectTeamResponse> projectTeamResponseThemisResponse =
                mallProjectController.projectTeamBatchInfo(projectTeamRequest);
        System.out.println(JSONUtils.serialize(projectTeamResponseThemisResponse));
    }

    @Test
    public void test10() {

        String repoUrl, projectName, projectKey, groupName, language, qualifyProfile, visibility, permission;

        repoUrl = "*************************:plateco-dev/kwaishop-m/kwaishop-ecologic-locus.git";
        projectName = "ramdom_name" + UUID.randomUUID();
        projectKey = "random_key" + UUID.randomUUID();
        groupName = "random_group" + UUID.randomUUID();
        language = "c++";
        qualifyProfile = "empty-rules";
        visibility = "private";
        permission = "admin";

        SonarProjectCreateRequest request = SonarProjectCreateRequest.builder()
                .repoUrl(repoUrl)
                // .projectName(projectName)
                // .projectKey(projectKey)
                // .groupName(groupName)
                // .language(language)
                // .qualifyProfile(qualifyProfile)
                // .visibility(visibility)
                // .permission(permission)
                .kformatSonar(true)
                .language("c++")
                .qualifyProfile("KFormat-for-pipeline")
                .build();
        System.out.println(sonarProjectService.createSonarProject(request));
    }

    @Test
    public void test11_createProject() {
        String projectName, projectKey, visibility;

        projectName = "test_project102";
        projectKey = "testGroup:" + projectName + ":false";
        visibility = "private";
        ProjectCreateResp resp = corpSonarApi.createProject(projectName, projectKey, visibility);
        System.out.println("ProjectCreateResp: " + resp);
    }

    @Test
    public void test12_addProjectQualityprofile() {
        String language, projectKey, qualityProfile;

        language = "c++";
        projectKey = "testGroup:" + "test_project101";
        qualityProfile = "KFormat-for-pipeline";
        corpSonarApi.addProjectQualityprofile(language, projectKey, qualityProfile);
    }

    @Test
    public void test13_createUserGroup() {
        String groupName;

        groupName = "testGroup";

        corpSonarApi.createUserGroup(groupName);
    }

    @Test
    public void test14_addGroupPermission() {
        String groupName, permission, projectKey;

        groupName = "testGroup";
        permission = "securityhotspotadmin01";
        projectKey = "testGroup:test_project101";

        corpSonarApi.addGroupPermission(groupName, permission, projectKey);
    }

    @Test
    public void test16_getAllUsers() {
        List<User> allUsers = corpSonarOperations.getAllUsers();
        System.out.println("测试获取 sonar users");
        System.out.println(
                "获取allUsers.size(): " + allUsers.size());
        System.out.println(
                "allUsers login: " + allUsers.stream().map(User::getLogin).collect(toList()));
        System.out.println(
                "allUsers name: " + allUsers.stream().map(User::getName).collect(toList()));
        System.out.println(
                "allUsers email: " + allUsers.stream().map(User::getEmail).collect(toList()));
    }

    @Test
    public void test16_createUser() {
        metricSonarApi.createUser("wangyufei", "wangyufei", "<EMAIL>");
    }

    @Test
    public void testHook() {
        String request = "{\"serverUrl\":\"http://sonar.corp.kuaishou.com\",\"taskId\":\"AXsusKjZBMpVeg-gk3gh\","
                + "\"status\":\"SUCCESS\",\"analysedAt\":\"2021-08-10T14:11:24+0800\","
                + "\"revision\":\"cdce73828d13abdd274ebd4e71d0ada181f1e0b6\","
                + "\"changedAt\":\"2021-08-10T14:11:24+0800\",\"project\":{\"key\":\"kuaishou:ks-serveree-themis\","
                + "\"name\":\"ks-serveree-themis\",\"url\":\"http://sonar.corp.kuaishou"
                + ".com/dashboard?id=kuaishou%3Aks-serveree-themis\"},\"branch\":{\"name\":\"master\","
                + "\"type\":\"LONG\",\"isMain\":true,\"url\":\"http://sonar.corp.kuaishou"
                + ".com/dashboard?id=kuaishou%3Aks-serveree-themis\"},\"qualityGate\":{\"name\":\"kuaishou\","
                + "\"status\":\"OK\",\"conditions\":[{\"metric\":\"new_reliability_rating\","
                + "\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\",\"errorThreshold\":\"1\"},"
                + "{\"metric\":\"new_security_rating\",\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\","
                + "\"errorThreshold\":\"1\"},{\"metric\":\"new_maintainability_rating\","
                + "\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\",\"errorThreshold\":\"1\"},"
                + "{\"metric\":\"new_duplicated_lines_density\",\"operator\":\"GREATER_THAN\","
                + "\"status\":\"NO_VALUE\",\"errorThreshold\":\"3\"},{\"metric\":\"new_blocker_violations\","
                + "\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\",\"errorThreshold\":\"2\"}]},"
                + "\"properties\":{}}";
        HookPayload deserialize = JSONUtils.deserialize(request, HookPayload.class);
        sonarController.globalHook(deserialize);
    }

    @Test
    public void testCreateUsers() {
        sonarService.createUsers();
    }

    @Test
    public void testComponent() {
        MeasureComponentRequest request = new MeasureComponentRequest();
        request.setComponent("kuaishou:kt-production");
        request.setServerUrl("https://sonar.corp.kuaishou.com");
        request.setMetricKeys("bugs,coverage");
        ThemisResponse<MeasureComponentResp> measureComponentRespThemisResponse =
                sonarController.measureComponent(request);
        System.out.println(JSONUtils.serialize(measureComponentRespThemisResponse));
    }

    @Test
    public void testKimApi() throws Exception {
        StringBuilder sb = new StringBuilder("## ").append(String.format("新增%s个issue", 12)).append("\n");
        sb.append("时间: ").append("2012-00-00").append("\n");
        sb.append("项目: ").append("ks-serveree-themis").append("\n");
        sb.append("作者: ").append("李小鑫").append("\n");
        List<String> files = Lists.newArrayList("122", "222");
        String delimiter = ", ";
        int toIndex = Math.min(files.size(), 10);
        String join = String.join(delimiter, files.subList(0, toIndex));
        if (files.size() > toIndex) {
            join += "...";
        }
        sb.append("文件: ").append(join).append("\n");
        sb.append("[详情](").append("https://www.baidu.com").append(")");
        kimApi.sendText("lixiaoxin", sb.toString());
    }

    @Test
    public void testSonarParser() {
        String request = "{\n"
                + "  \"serverUrl\": \"https://metric-sonar.corp.kuaishou.com\",\n"
                + "  \"taskId\": \"AXxz0leQc_SI2U7SBid_\",\n"
                + "  \"status\": \"SUCCESS\",\n"
                + "  \"analysedAt\": \"2021-10-12T17:24:44+0800\",\n"
                + "  \"revision\": \"****************************************\",\n"
                + "  \"changedAt\": \"2021-10-12T17:24:44+0800\",\n"
                + "  \"project\": {\n"
                + "    \"key\": \"kuaishou:kwaishop-trade-toolkit\",\n"
                + "    \"name\": \"kwaishop-trade-toolkit\",\n"
                + "    \"url\": \"https://metric-sonar.corp.kuaishou"
                + ".com/dashboard?id=kuaishou%3Akwaishop-trade-toolkit\"\n"
                + "  },\n"
                + "  \"branch\": {\n"
                + "    \"name\": \"master\",\n"
                + "    \"type\": \"LONG\",\n"
                + "    \"isMain\": true,\n"
                + "    \"url\": \"https://metric-sonar.corp.kuaishou"
                + ".com/dashboard?id=kuaishou%3Akwaishop-trade-toolkit\"\n"
                + "  },\n"
                + "  \"qualityGate\": {\n"
                + "    \"name\": \"Sonar way\",\n"
                + "    \"status\": \"ERROR\",\n"
                + "    \"conditions\": [\n"
                + "      {\n"
                + "        \"metric\": \"new_reliability_rating\",\n"
                + "        \"operator\": \"GREATER_THAN\",\n"
                + "        \"value\": \"1\",\n"
                + "        \"status\": \"OK\",\n"
                + "        \"errorThreshold\": \"1\"\n"
                + "      },\n"
                + "      {\n"
                + "        \"metric\": \"new_security_rating\",\n"
                + "        \"operator\": \"GREATER_THAN\",\n"
                + "        \"value\": \"1\",\n"
                + "        \"status\": \"OK\",\n"
                + "        \"errorThreshold\": \"1\"\n"
                + "      },\n"
                + "      {\n"
                + "        \"metric\": \"new_maintainability_rating\",\n"
                + "        \"operator\": \"GREATER_THAN\",\n"
                + "        \"value\": \"1\",\n"
                + "        \"status\": \"OK\",\n"
                + "        \"errorThreshold\": \"1\"\n"
                + "      },\n"
                + "      {\n"
                + "        \"metric\": \"new_coverage\",\n"
                + "        \"operator\": \"LESS_THAN\",\n"
                + "        \"value\": \"0.0\",\n"
                + "        \"status\": \"ERROR\",\n"
                + "        \"errorThreshold\": \"80\"\n"
                + "      },\n"
                + "      {\n"
                + "        \"metric\": \"new_duplicated_lines_density\",\n"
                + "        \"operator\": \"GREATER_THAN\",\n"
                + "        \"value\": \"0.0\",\n"
                + "        \"status\": \"OK\",\n"
                + "        \"errorThreshold\": \"3\"\n"
                + "      }\n"
                + "    ]\n"
                + "  },\n"
                + "  \"properties\": {\n"
                + "    \"sonar.analysis.checkBaseId\": \"448\",\n"
                + "    \"sonar.analysis.executionId\": \"448\",\n"
                + "    \"sonar.analysis.checkTaskId\": \"3689\",\n"
                + "    \"sonar.analysis.repoUrl\": \"*********************"
                + ".com:plateco-dev/kwaishop-platform/kwaishop-trade-toolkit.git\",\n"
                + "    \"sonar.analysis.commitId\": \"****************************************\",\n"
                + "    \"sonar.analysis.branch\": \"master\"\n"
                + "  }\n"
                + "}";
        HookPayload deserialize = JSONUtils.deserialize(request, HookPayload.class);
        sonarService.checkParser(deserialize);
    }

    @Test
    public void testVisibility() {
        int page = 1;
        int pageSize = 500;
        while (true) {
            ProjectsResp projectsResp =
                    corpSonarApi.searchProjects(null, page, pageSize, "kuaishou:zt-encourage-portrait");
            List<Project> components = projectsResp.getComponents();
            if (CollectionUtils.isEmpty(components)) {
                break;
            }
            for (Project project : components) {
                String key = project.getKey();
                String visibility = project.getVisibility();
                if ("private".equals(visibility)) {
                    logger.info("key is {},visibility is private,skip", key);
                    continue;
                }
                kFormatSonarApi.updateVisibility(key, "private");
                logger.info("key is {}, update visibility success ", key);
            }
            page++;
        }
    }

    @Test
    public void testKim() {
        String request = "{\"serverUrl\":\"http://sonar.corp.kuaishou.com\",\"taskId\":\"AXx-BZ_SJZA_2N7RvQJq\","
                + "\"status\":\"SUCCESS\",\"analysedAt\":\"2021-10-14T16:24:30+0800\","
                + "\"revision\":\"9bf1a6e6ea9728fda84ae67a14b0d3ecc31eff40\","
                + "\"changedAt\":\"2021-10-14T16:24:30+0800\",\"project\":{\"key\":\"yangyuanxi:gifshow-android\","
                + "\"name\":\"yangyuanxi:gifshow-android\",\"url\":\"http://sonar.corp.kuaishou"
                + ".com/dashboard?id=yangyuanxi%3Agifshow-android\"},\"branch\":{\"name\":\"master\","
                + "\"type\":\"LONG\",\"isMain\":true,\"url\":\"http://sonar.corp.kuaishou"
                + ".com/dashboard?id=yangyuanxi%3Agifshow-android\"},\"qualityGate\":{\"name\":\"kuaishou\","
                + "\"status\":\"ERROR\",\"conditions\":[{\"metric\":\"new_reliability_rating\","
                + "\"operator\":\"GREATER_THAN\",\"value\":\"4\",\"status\":\"ERROR\",\"errorThreshold\":\"1\"},"
                + "{\"metric\":\"new_security_rating\",\"operator\":\"GREATER_THAN\",\"value\":\"5\","
                + "\"status\":\"ERROR\",\"errorThreshold\":\"1\"},{\"metric\":\"new_maintainability_rating\","
                + "\"operator\":\"GREATER_THAN\",\"value\":\"1\",\"status\":\"OK\",\"errorThreshold\":\"1\"},"
                + "{\"metric\":\"new_duplicated_lines_density\",\"operator\":\"GREATER_THAN\",\"value\":\"5"
                + ".592961972617631\",\"status\":\"ERROR\",\"errorThreshold\":\"3\"},"
                + "{\"metric\":\"new_blocker_violations\",\"operator\":\"GREATER_THAN\",\"value\":\"44\","
                + "\"status\":\"ERROR\",\"errorThreshold\":\"2\"}]},\"properties\":{}}";
        HookPayload deserialize = JSONUtils.deserialize(request, HookPayload.class);
        sonarController.globalHook(deserialize);
    }

    @Test
    public void testCustomHook() {
        String request = "{\"serverUrl\":\"http://sonar.corp.kuaishou.com\",\"taskId\":\"AX2i9i8AJZA_2N7Rvp-O\","
                + "\"status\":\"SUCCESS\",\"analysedAt\":\"2021-12-10T14:09:23+0800\","
                + "\"revision\":\"b0ed834f3d2b3f0ae3a83034806684bb2a191100\","
                + "\"changedAt\":\"2021-12-10T14:09:23+0800\",\"project\":{\"key\":\"kuaishou:kwai-component-util\","
                + "\"name\":\"kwai-component-util\",\"url\":\"http://sonar.corp.kuaishou"
                + ".com/dashboard?id=kuaishou%3Akwai-component-util\"},\"branch\":{\"name\":\"master\","
                + "\"type\":\"LONG\",\"isMain\":true,\"url\":\"http://sonar.corp.kuaishou"
                + ".com/dashboard?id=kuaishou%3Akwai-component-util\"},\"qualityGate\":{\"name\":\"kuaishou\","
                + "\"status\":\"ERROR\",\"conditions\":[{\"metric\":\"new_reliability_rating\","
                + "\"operator\":\"GREATER_THAN\",\"value\":\"3\",\"status\":\"ERROR\",\"errorThreshold\":\"1\"},"
                + "{\"metric\":\"new_security_rating\",\"operator\":\"GREATER_THAN\",\"value\":\"1\","
                + "\"status\":\"OK\",\"errorThreshold\":\"1\"},{\"metric\":\"new_maintainability_rating\","
                + "\"operator\":\"GREATER_THAN\",\"value\":\"1\",\"status\":\"OK\",\"errorThreshold\":\"1\"},"
                + "{\"metric\":\"new_duplicated_lines_density\",\"operator\":\"GREATER_THAN\",\"value\":\"38"
                + ".35294117647059\",\"status\":\"ERROR\",\"errorThreshold\":\"3\"},"
                + "{\"metric\":\"new_blocker_violations\",\"operator\":\"GREATER_THAN\",\"value\":\"0\","
                + "\"status\":\"OK\",\"errorThreshold\":\"2\"}]},\"properties\":{\"sonar.analysis"
                + ".projectId\":\"4145\",\"sonar.analysis.buildUserName\":\"wangyuhui\"}}\t";
        HookPayload deserialize = JSONUtils.deserialize(request, HookPayload.class);
        sonarController.customHook(deserialize);
    }

    @Test
    public void testProcessHook() {
        String req = "{\"serverUrl\":\"https://sonar-cluster-5.corp.kuaishou.com\","
                + "\"taskId\":\"AYjA6qHZRu6x-7guz_nX\",\"status\":\"SUCCESS\","
                + "\"analysedAt\":\"2023-06-16T05:12:41+0800\","
                + "\"revision\":\"9c9b9fb0c2e19493de55bb06366406b9cbf812a5\","
                + "\"changedAt\":\"2023-06-16T05:12:41+0800\",\"project\":{\"key\":\"measure:46677\","
                + "\"name\":\"measure:kuaishou-encourage-game\",\"url\":\"https://sonar-cluster-5.corp.kuaishou"
                + ".com/dashboard?id\\u003dmeasure%3A46677\"},\"branch\":{\"name\":\"master\",\"type\":\"BRANCH\","
                + "\"isMain\":true,\"url\":\"https://sonar-cluster-5.corp.kuaishou"
                + ".com/dashboard?id\\u003dmeasure%3A46677\"},\"qualityGate\":{\"name\":\"Sonar way\","
                + "\"status\":\"OK\",\"conditions\":[{\"metric\":\"new_reliability_rating\","
                + "\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\",\"errorThreshold\":\"1\"},"
                + "{\"metric\":\"new_security_rating\",\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\","
                + "\"errorThreshold\":\"1\"},{\"metric\":\"new_maintainability_rating\","
                + "\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\",\"errorThreshold\":\"1\"},"
                + "{\"metric\":\"new_coverage\",\"operator\":\"LESS_THAN\",\"status\":\"NO_VALUE\","
                + "\"errorThreshold\":\"80\"},{\"metric\":\"new_duplicated_lines_density\","
                + "\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\",\"errorThreshold\":\"3\"},"
                + "{\"metric\":\"new_security_hotspots_reviewed\",\"operator\":\"LESS_THAN\",\"status\":\"NO_VALUE\","
                + "\"errorThreshold\":\"100\"}]},\"properties\":{\"sonar.analysis.detectedscm\":\"git\",\"sonar"
                + ".analysis.detectedci\":\"undetected\",\"sonar.analysis.checkBaseId\":\"662\",\"sonar.analysis"
                + ".sendKimNotice\":\"false\",\"sonar.analysis.projectId\":\"46677\",\"sonar.analysis.mrId\":\"\","
                + "\"sonar.analysis.executionId\":\"660\",\"sonar.analysis.buildLanguageVersion\":\"17\",\"sonar"
                + ".analysis.kspBuildId\":\"20692220\",\"sonar.analysis.modules\":\"allModules\",\"sonar.analysis"
                + ".testEnv\":\"false\",\"sonar.analysis.buildUserName\":\"lixiaoxin\",\"sonar.analysis"
                + ".repoUrl\":\"*************************:ks-encourage/kuaishou-encourage-game.git\",\"sonar.analysis"
                + ".platformOfflineCheck\":\"true\",\"sonar.analysis.commitId\":\"\",\"sonar.analysis"
                + ".localBuildId\":\"0\",\"sonar.analysis.branch\":\"master\",\"sonar.analysis"
                + ".kspPipelineId\":\"404837\",\"sonar.analysis.buildLanguage\":\"java\",\"sonar.analysis"
                + ".scannerType\":\"1\",\"sonar.analysis.stuckPoint\":\"true\",\"sonar.analysis"
                + ".incrementMode\":\"false\"}}";

        HookPayload deserialize = JSONUtils.deserialize(req, HookPayload.class);
        sonarController.processCheckHook(deserialize);
    }

    /**
     * componentKeys=social-reactnative%3Asocialreactnative&s=FILE_LINE
     * &resolved=false&types=CODE_SMELL&ps=100&organization=default-organization&facets=severities%2Ctypes
     * &additionalFields=_all
     */
    @Test
    public void testSearchIssueByTypes() {
        SonarSearchRequest sonarSearchRequest = new SonarSearchRequest();
        sonarSearchRequest.setComponentKeys("kuaishou:zt-encourage-portrait");
        sonarSearchRequest.setResolved(false);
        sonarSearchRequest.setTypes("BUG");
        sonarSearchRequest.setFacets("severities");
        sonarSearchRequest.setPageSize(1);
        IssuesResp issuesResp = corpSonarApi.searchIssues(sonarSearchRequest);
        System.out.println(JSONUtils.serialize(issuesResp));
    }

    @Test
    public void testStuckPoint() {
        SonarStuckPointRequest request = new SonarStuckPointRequest();
        request.setKspBuildId(18096008L);
        request.setBugSettings("{\"stuckSwitch\":\"true\",\"severity\":\"major\",\"count\":\"0\"}");
        //        request.setCodeSmellSettings("{\"stuckSwitch\":\"true\",\"severity\":\"major\",\"count\":\"0\"}");
        request.setVulnerabilitySettings("{\"stuckSwitch\":\"true\",\"severity\":\"major\",\"count\":\"0\"}");
        ThemisResponse<SonarStuckPointResponse> response = sonarService.pluginStuckPoint(request);
        System.out.println(JSONUtils.serialize(response));
    }

    @Test
    public void testStuckPointHook() {
        String req = "{\"serverUrl\":\"https://sonar-cluster-2.corp.kuaishou.com\",\"taskId\":\"AX3Xxw6zp6f1ag_FB7Ge\","
                + "\"status\":\"SUCCESS\",\"analysedAt\":\"2021-12-20T20:17:45+0800\","
                + "\"revision\":\"4776f224aeabdb144a65bc6112c4f22aa1701d11\","
                + "\"changedAt\":\"2021-12-20T20:17:45+0800\",\"project\":{\"key\":\"24049\","
                + "\"name\":\"ks-serveree-themis\",\"url\":\"http://10.44.101.47:9012/dashboard?id=24049\"},"
                + "\"branch\":{\"name\":\"master||6217443||allModules\",\"type\":\"LONG\",\"isMain\":false,"
                + "\"url\":\"http://10.44.101.47:9012/dashboard?id=24049&branch=master%7C%7C6217443%7C%7CallModules"
                + "\"},\"qualityGate\":{\"name\":\"Sonar way\",\"status\":\"OK\","
                + "\"conditions\":[{\"metric\":\"new_reliability_rating\",\"operator\":\"GREATER_THAN\","
                + "\"status\":\"NO_VALUE\",\"errorThreshold\":\"1\"},{\"metric\":\"new_security_rating\","
                + "\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\",\"errorThreshold\":\"1\"},"
                + "{\"metric\":\"new_maintainability_rating\",\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\","
                + "\"errorThreshold\":\"1\"},{\"metric\":\"new_coverage\",\"operator\":\"LESS_THAN\","
                + "\"status\":\"NO_VALUE\",\"errorThreshold\":\"80\"},{\"metric\":\"new_duplicated_lines_density\","
                + "\"operator\":\"GREATER_THAN\",\"status\":\"NO_VALUE\",\"errorThreshold\":\"3\"}]},"
                + "\"properties\":{\"sonar.analysis.projectId\":\"24049\",\"sonar.analysis.kspBuildId\":\"6217443\","
                + "\"sonar.analysis.kspPipelineId\":\"313650\",\"sonar.analysis.stuckPoint\":\"true\"}}\t";
        HookPayload deserialize = JSONUtils.deserialize(req, HookPayload.class);
        sonarService.stuckPointHook(deserialize);
    }

    @Test
    public void testSearchIssueByDiff() {
        List<Issue> allIssuesDiffType =
                corpSonarOperations.getAllIssuesDiffType("lxx-lxx:lxx-lxx", "master", "RESOLVED");
        System.out.println(JSONUtils.serialize(allIssuesDiffType));
        System.out.println("=====" + allIssuesDiffType.size());
    }

    @Test
    public void testBusinessNewSonarScan() {
        final Integer groupId = 10974;
        List<GitlabProject> gitlabProjects = selfGitApi.listAllProjectsByGroupIdWithSubGroups(10974);
        for (GitlabProject gitlabProject : gitlabProjects) {
            Integer projectId = gitlabProject.getId();
            SonarOperations clusterOperations =
                    sonarClusterSimpleFactory.getClusterOperations(projectId.longValue());
            clusterOperations.sonarApi()
                    .addProjectQualityprofile("java", projectId.toString(), "plateco-dev-merchant-custom");
        }
    }

    @Test
    public void testRuleInit() {
        String profileName = "AX4E-ePQ9Qp97tCRv691";
        SearchRuleRequest searchRuleRequest = new SearchRuleRequest();
        searchRuleRequest.setQprofile(profileName);
        searchRuleRequest.setP(1);
        searchRuleRequest.setPs(500);
        SonarCommonApi sonarCommonApi = node1Operations.sonarApi();
        SearchRulesResponse searchRulesResponse = sonarCommonApi.searchRules(searchRuleRequest);
        System.out.println(JSONUtils.serialize(searchRulesResponse));
        List<CheckRule> checkRules = Lists.newArrayList();
        List<RuleDetail> rules = searchRulesResponse.getRules();
        LocalDateTime now = LocalDateTime.now();
        for (RuleDetail ruleDetail : rules) {
            String key = ruleDetail.getKey();
            RulesDetailResp detailResp = sonarCommonApi.getRuleDetail(key, null, null);
            CheckRule checkRule = new CheckRule();
            checkRule.setRuleKey(ruleDetail.getKey());
            checkRule.setScanner(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner());
            checkRule.setLanguage(PlatformLanguageEnum.JAVA.getName());
            checkRule.setName(ruleDetail.getName());
            checkRule.setRuleType(ruleDetail.getType());
            checkRule.setSeverity(CheckIssueSeverity.getKeyBySonarStatus(ruleDetail.getSeverity()));
            checkRule.setDescription(detailResp.getRule().getMdDesc());
            checkRule.setHtmlDesc(detailResp.getRule().getHtmlDesc());
            checkRule.setGmtCreate(now);
            checkRule.setGmtModified(now);
            checkRules.add(checkRule);
        }
        checkRuleService.saveBatch(checkRules);
    }

    @Test
    public void testLabelInit() {
        SearchRuleRequest searchRuleRequest = new SearchRuleRequest();
        searchRuleRequest.setQprofile("AX4E-ePQ9Qp97tCRv691");
        searchRuleRequest.setF("isTemplate,name,lang,langName,severity,status,sysTags,tags,templateKey,actives,params");
        searchRuleRequest.setFacets("tags");
        searchRuleRequest.setPs(500);
        searchRuleRequest.setS("name");
        searchRuleRequest.setActivation("true");
        SonarCommonApi sonarCommonApi = node1Operations.sonarApi();
        SearchRulesResponse searchRulesResponse = sonarCommonApi.searchRules(searchRuleRequest);
        System.out.println(JSONUtils.serialize(searchRulesResponse));
        List<Facet> facets = searchRulesResponse.getFacets();
        List<CheckLabel> checkLabels = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        for (Facet facet : facets) {
            List<FacetValue> values = facet.getValues();
            for (FacetValue value : values) {
                CheckLabel checkLabel = new CheckLabel();
                checkLabel.setGmtCreate(now);
                checkLabel.setGmtModified(now);
                checkLabel.setName(value.getVal());
                checkLabels.add(checkLabel);
            }
        }
        List<RuleDetail> ruleDetails = searchRulesResponse.getRules();
        List<CheckRuleLabelRelation> checkRuleLabelRelations = Lists.newArrayList();
        for (RuleDetail ruleDetail : ruleDetails) {
            List<String> sysTags = ruleDetail.getSysTags();
            if (CollectionUtils.isEmpty(sysTags)) {
                continue;
            }
            for (String sysTag : sysTags) {
                CheckRuleLabelRelation checkRuleLabelRelation = new CheckRuleLabelRelation();
                checkRuleLabelRelation.setRuleKey(ruleDetail.getKey());
                checkRuleLabelRelation.setLabelName(sysTag);
                checkRuleLabelRelation.setGmtModified(now);
                checkRuleLabelRelation.setGmtCreate(now);
                checkRuleLabelRelations.add(checkRuleLabelRelation);
            }
        }
        checkRuleLabelRelationService.saveBatch(checkRuleLabelRelations);
        //        checkLabelService.saveBatch(checkLabels);
    }

    @Test
    public void testUnion() {
        String profileName = "AX4E-ePQ9Qp97tCRv691";
        SearchRuleRequest searchRuleRequest = new SearchRuleRequest();
        searchRuleRequest.setQprofile(profileName);
        searchRuleRequest.setP(1);
        searchRuleRequest.setPs(500);
        SonarCommonApi sonarCommonApi = node1Operations.sonarApi();
        SearchRulesResponse searchRulesResponse = sonarCommonApi.searchRules(searchRuleRequest);
        System.out.println(JSONUtils.serialize(searchRulesResponse));
        List<RuleDetail> rules = searchRulesResponse.getRules();
        List<CheckRuleLabelRelation> list = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        for (RuleDetail rule : rules) {
            String key = rule.getKey();
            List<String> sysTags = rule.getSysTags();
            for (String sysTag : sysTags) {
                CheckRuleLabelRelation checkRuleLabelRelation = new CheckRuleLabelRelation();
                checkRuleLabelRelation.setRuleKey(key);
                checkRuleLabelRelation.setLabelName(sysTag);
                checkRuleLabelRelation.setGmtCreate(now);
                checkRuleLabelRelation.setGmtModified(now);
                list.add(checkRuleLabelRelation);
            }
        }
        Map<String, List<CheckRuleLabelRelation>> collect =
                list.stream().collect(Collectors.groupingBy(o -> o.getRuleKey() + o.getLabelName()));
        List<CheckRuleLabelRelation> real = Lists.newArrayList();
        for (Entry<String, List<CheckRuleLabelRelation>> stringListEntry : collect.entrySet()) {
            CheckRuleLabelRelation checkRuleLabelRelation = stringListEntry.getValue().get(0);
            real.add(checkRuleLabelRelation);
        }
        checkRuleLabelRelationService.saveBatch(real);
    }

    @Test
    public void testCreateProfile() {
        // ProfileCreateResp resp = node1Operations.sonarApi().createProfile("java", "ww-test91111");
        clusterNode5Operations.sonarApi().createProfile("java", "test:Sonar way [kuaishou]");
        // System.out.println(resp.getProfile());
    }

    @Test
    public void testChangeProfileParent() {
        node1Operations.sonarApi().changeProfileParent("java", "test:eb2212c0a42e4097a3b467308aa0f8b5", null);
    }

    @Test
    public void testDeleteProfile() {
        SonarCommonApi sonarCommonApi = node1Operations.sonarApi();
        sonarCommonApi.deleteProfile("java", "afafaf");
    }

    @Test
    public void testAddKey() {
        SonarCommonApi sonarCommonApi = node1Operations.sonarApi();
        QualityProfileReq profileReq = QualityProfileReq.builder()
                .project("65748")
                .language("java")
                .build();
        QualityProfileResp qualityProfile = sonarCommonApi.getQualityProfile(profileReq);
        List<Profiles> profilesList = qualityProfile.getProfiles();
        Profiles profiles = profilesList.get(0);
        String key = profiles.getKey();
        sonarCommonApi.activeRule(key, "findbugs:RANGE_ARRAY_OFFSET", "SERIOUS");
    }

    @Test
    public void testMeasureComponentApi() {
        MeasuresComponentTreeRequest treeRequest = MeasuresComponentTreeRequest.builder()
                .component("kuaishou:ks-serveree-cr")
                .metricKeys("complexity")
                .build();
        MeasuresComponentTreeResponse treeResponse = corpSonarApi.measuresComponentTree(treeRequest);
        System.out.println(JSONUtils.serialize(treeResponse));
    }

    @Test
    public void testMeasureComponentBlockApi() {
        MeasuresComponentTreeRequest treeRequest = MeasuresComponentTreeRequest.builder()
                .component("kuaishou:ks-serveree-cr")
                .metricKeys("duplicated_lines_density")
                .build();
        MeasuresComponentTreeResponse treeResponse = corpSonarApi.measuresComponentTree(treeRequest);
        System.out.println(JSONUtils.serialize(treeResponse));
    }

    @Test
    public void testMeasureComponentBlockAll() {
        List<SonarComponent> allMeasuresTree = corpSonarOperations.getAllMeasuresTree("kuaishou:ks-serveree-cr",
                "master", "cognitive_complexity");
        System.out.println(JSONUtils.serialize(allMeasuresTree));
    }

    @Test
    public void testTree() {
        MeasureComponentTreeRequest measureComponentTreeRequest = new MeasureComponentTreeRequest();
        measureComponentTreeRequest.setComponent("kuaishou:kuaishou-promotion-demeter");
        measureComponentTreeRequest.setMetricKeys("complexity");
        measureComponentTreeRequest.setTarget("MetricSonarApi");
        measureComponentTreeRequest.setPage(1);
        measureComponentTreeRequest.setPageSize(20);
        MeasureComponentTreeResp measureComponentTreeResp = sonarService.sonarMeasureTree(measureComponentTreeRequest);
        System.out.println(JSONUtils.serialize(measureComponentTreeResp));
    }

    @Test
    public void testDelete() {
        corpSonarApi.deleteProject("kuaishou:zt-encourage-portrait");
    }

    @Test
    public void testSearchRules() {
        //        SearchRuleRequest searchRuleRequest = new SearchRuleRequest();
        //        searchRuleRequest.setQprofile("AXW7RaoP7KsbmUziR768");
        //        searchRuleRequest.setTypes("BUG,VULNERABILITY");
        //        searchRuleRequest.setPs(500);
        //        SearchRulesResponse searchRulesResponse = corpSonarApi.searchRules(searchRuleRequest);
        //
        //        List<String> sonarCorpApiRules = Lists.newArrayList();
        //
        //        List<RuleDetail> rules = searchRulesResponse.getRules();
        //        for (RuleDetail rule : rules) {
        //            String name = rule.getName();
        //            sonarCorpApiRules.add(name);
        //        }

        CheckRuleListCondition java = CheckRuleListCondition.builder()
                .language("java")
                .build();
        List<CheckRule> checkRules = checkRuleService.listByCondition(java);
        System.out.println(checkRules.size());
        //
        //        SearchRuleRequest searchRuleRequest1 = new SearchRuleRequest();
        //        searchRuleRequest1.setQprofile("AXuVDVuwc_SI2U7SA_r4");
        //        searchRuleRequest1.setTypes("BUG,VULNERABILITY");
        //        searchRuleRequest1.setPs(500);
        //
        //        SearchRulesResponse searchRulesResponse1 = metricSonarApi.searchRules(searchRuleRequest1);
        //
        //        List<String> sonarCorpApiRules1 = Lists.newArrayList();
        //
        //        List<RuleDetail> rules1 = searchRulesResponse1.getRules();
        //        for (RuleDetail rule : rules1) {
        //            String name = rule.getName();
        //            sonarCorpApiRules1.add(name);
        //        }
        //
        //        Collection<String> disjunction = CollectionUtil.disjunction(sonarCorpApiRules1, sonarCorpApiRules);
        //        System.out.println("======");
        //        for (String s : disjunction) {
        //            System.out.println(s);
        //        }
        //        System.out.println("======");
    }

    @Test
    public void testSendKimNotice() {
        PCheckBase pCheckBase = pCheckBaseService.getByBuildId(10516177L);
        pCheckBase.setSponsor("lixiaoxin");
        List<PCheckIssue> pCheckIssues = pCheckIssueService.listByPBaseIdAndType(pCheckBase.getId(),
                ProcessExecutionReferType.MAVEN_SONAR.getType());
        ScanModeContext scanModeContext = new ScanModeContext();
        scanModeContext.setPCheckBase(pCheckBase);
        scanModeContext.setPCheckIssueList(pCheckIssues);
        scanModeContext.setKimNoticeUrl(issueUrl);
        scanModeService.sendKimNotice(scanModeContext);
    }

    @Test
    public void testIssueSearch() {
        SearchIssueRequest searchIssueRequest = new SearchIssueRequest();
        searchIssueRequest.setKspBuildId(13182469L);
        searchIssueRequest.setP(1L);
        searchIssueRequest.setPs(50L);
        searchIssueRequest.setExecutionReferType(1);
        SearchIssuesResponse searchIssuesResponse = sonarIssueService.searchIssues(searchIssueRequest);
        System.out.println(JSONUtils.serialize(searchIssuesResponse));
    }

    @Test
    public void testUserChange() {
        SonarSearchRequest searchRequest = SonarSearchRequest.builder()
                .statuses("RESOLVED")
                .build();
        IssuesResp issuesResp = metricSonarApi.searchIssues(searchRequest);
        System.out.println(JSONUtils.serialize(issuesResp));
    }

    @Test
    public void testDoTransition() {
        Issue issue = kFormatSonarApi.doTransition("AX2y_ZvXDeumaGJH3WQ3", "falsepositive");
        System.out.println(JSONUtils.serialize(issue));
    }

    @Test
    public void testProjectCreateAndProfileBind() {

        List<Integer> projectIds =
                Lists.newArrayList(31672, 31458, 41420, 11521, 25558, 3690, 29712, 5093, 6053, 51124, 33675, 40489,
                        3882, 36451, 38858, 550, 26065, 19866, 9108, 40551, 7773, 43932, 8047, 4374, 40255, 40591, 306,
                        5202, 10632, 17037, 32305, 31672, 6162, 43439, 35570, 34798, 40662, 38420, 48298, 14497, 32169,
                        40209, 40091, 41933, 48117, 34705, 31816, 55454, 43038, 40194);
        System.out.println(projectIds.size());

        for (Integer projectId : projectIds) {
            projectCreateAndProfileBind0(projectId);
        }

    }

    private void projectCreateAndProfileBind0(int projectId) {
        // 创建项目 并且绑定profile
        int gitProjectId = projectId;
        SonarOperations clusterOperations = sonarClusterSimpleFactory.getClusterOperations((long) gitProjectId);
        SonarCommonApi sonarCommonApi = clusterOperations.sonarApi();
        String projectKey = String.valueOf(gitProjectId);
        String finalProfileName = "0d07725e3e884cd6a8d1a7099d7d54dc";
        GitlabProject project = gitOperations.getProject(gitProjectId);
        String finalProjectName = GitUtils.getRepoName(project.getSshUrl());
        // 创建项目 并且绑定profile
        try {
            sonarCommonApi.createProject(finalProjectName, projectKey, "private");
        } catch (ThemisException e) {
            if (ResultCodeConstant.SONAR_PROJECTKEY_ALREADY_EXISTS.getCode() != e.getCode()) {
                throw new RuntimeException(e);
            }
        }
        sonarCommonApi.addProjectQualityprofile("java", projectKey, finalProfileName);
    }

    @Test
    public void testRuleDetail() {
        // RulesDetailResp ruleDetail = node1Operations.sonarApi().getRuleDetail("@skyeye/no-internal-url", null, null);
        RulesDetailResp ruleDetail = clusterNode5Operations.sonarApi().getRuleDetail("squid:custom:wwTestKconfChecker", null, null);
        System.out.println(ruleDetail);
    }

    @Test
    public void testSearchMeasures() {
        MeasureComponentResp componentResp =
                clusterNode1Operations.getMeasureData("measure:24536:test", "master", "ncloc_language_distribution");
        System.out.println(JSONUtils.serialize(componentResp));
    }

    @Test
    public void testSonarProject() {

        Set<Integer> projectSet = Sets.newHashSet();

        for (SonarClusterOperations sonarClusterOperation : sonarClusterOperations) {
            String profileKey = sonarClusterOperation.getProfileKeyByName("kspay-custom");
            SonarCommonApi sonarCommonApi = sonarClusterOperation.sonarApi();
            ProfileProjectsResp profileProjects = sonarCommonApi.getProfileProjects(profileKey, 1, 500);
            for (SonarProject sonarProject : profileProjects.getResults()) {
                String key = sonarProject.getKey();
                if (key.contains(":")) {
                    String[] split = key.split(":");
                    key = split[1];
                }
                projectSet.add(Integer.parseInt(key));
            }
        }

        System.out.println(projectSet.size());

        System.out.println(JSONUtils.serialize(projectSet));
    }

    @Test
    public void batchUpdateProfiles() {
        ArrayList<Integer> integers =
                Lists.newArrayList(24155, 1743, 23323, 4079, 20642, 1824, 20162, 9380, 3900, 35520, 8042, 1738, 170,
                        24157,
                        37874, 8674, 6109, 35199, 37246, 20644, 33872, 4243, 44899, 44583, 1741, 45598, 45588, 45310,
                        10392,
                        40332, 41126, 44846, 45401, 46291, 46292, 48054, 48055, 33948, 51447, 51448, 51450, 51590,
                        51417, 36282,
                        52424, 21487, 55665, 60553, 61334, 61698, 61489, 63301, 64072, 64450, 64210, 64211, 64879,
                        65050);
        for (Integer integer : integers) {
            SonarOperations clusterOperations = sonarClusterSimpleFactory.getClusterOperations(Long.valueOf(integer));
            clusterOperations.sonarApi().addProjectQualityprofile("java", "measure:" + integer, "kspay-custom\t");
            System.out.println("--");
        }
    }


    @Test
    public void testSearchRule() {
        SonarCommonApi sonarCommonApi = node1Operations.sonarApi();
        QualityProfileReq profileReq = QualityProfileReq.builder()
                .qualityProfile("test:a967a005e79d42948e7aa46caddb2dc1")
                .language("java")
                .build();
        QualityProfileResp qualityProfile = sonarCommonApi.getQualityProfile(profileReq);
        List<Profiles> profilesList = qualityProfile.getProfiles();
        Profiles profiles = profilesList.get(0);
        String profileName = profiles.getKey();

        // String profileName = "AX4E-ePQ9Qp97tCRv691";
        SearchRuleRequest searchRuleRequest = new SearchRuleRequest();
        searchRuleRequest.setQprofile(profileName);
        searchRuleRequest.setP(1);
        searchRuleRequest.setPs(500);
        searchRuleRequest.setActivation("true");
        SearchRulesResponse searchRulesResponse = sonarCommonApi.searchRules(searchRuleRequest);
        System.out.println(searchRulesResponse);
    }

    @Test
    public void testSearchCommonRule() {
        long s1 = System.currentTimeMillis();
        logger.info("" + s1);
        Map<String, Pair<Integer, List<RuleDetail>>> resultMap = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (SonarClusterOperations sonarClusterOperation : sonarClusterOperations) {
            CompletableFuture<Void> f = CompletableFuture.runAsync(
                    () -> queryRulesBySonarOperation(sonarClusterOperation.sonarApi(), resultMap));
            futures.add(f);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        logger.info("" + (System.currentTimeMillis() - s1));
        System.out.println(resultMap);

        long s2 = System.currentTimeMillis();
        logger.info("" + s2);
        Map<String, Pair<Integer, List<RuleDetail>>> map = new HashMap<>();
        for (SonarClusterOperations sonarClusterOperation : sonarClusterOperations) {
            queryRulesBySonarOperation(sonarClusterOperation.sonarApi(), map);
        }
        logger.info("" + (System.currentTimeMillis() - s2));
        System.out.println(map);
    }

    private void queryRulesBySonarOperation(SonarCommonApi sonarCommonApi,
            Map<String, Pair<Integer, List<RuleDetail>>> resultMap) {
        SearchRuleRequest searchRuleRequest = new SearchRuleRequest();
        int p = 1;
        searchRuleRequest.setP(p);
        searchRuleRequest.setPs(500);
        searchRuleRequest.setActivation("true");
        searchRuleRequest.setLanguages("java");
        SearchRulesResponse searchRulesResponse = sonarCommonApi.searchRules(searchRuleRequest);
        List<RuleDetail> allJavaRules = Lists.newArrayList();
        int total = searchRulesResponse.getTotal();
        while (CollectionUtils.isNotEmpty(searchRulesResponse.getRules())) {
            List<RuleDetail> collect = searchRulesResponse.getRules()
                    .stream().filter(rule -> rule.getKey().startsWith("squid:custom:"))
                    .collect(toList());
            allJavaRules.addAll(collect);

            p++;
            searchRuleRequest.setP(p);
            searchRulesResponse = sonarCommonApi.searchRules(searchRuleRequest);
        }

        String sonarOperationName = sonarCommonApi.getClass().getSimpleName();
        Pair<Integer, List<RuleDetail>> totalAndCustomRules = Pair.of(total, allJavaRules);
        resultMap.put(sonarOperationName, totalAndCustomRules);
        logger.info("[{}] total size == [{}], custom size == [{}]", sonarOperationName, total, allJavaRules.size());
    }

    @Test
    public void testAllComponentMeasures() {
        String projectKey = "measure:3456";
        String branch = "master";
        List<String> metricKeys = SonarMetricsEnum.getAllMetricKeys();
        Map<String, SonarComponent> map =
                node1Operations.getAllComponentsMeasures(projectKey, branch, metricKeys);
        System.out.println(JSONUtils.serialize(map));

        System.out.println(JSONUtils.serialize(map).getBytes(StandardCharsets.UTF_8).length);

    }

    public void testSearchProfileRules() {
        SearchRuleRequest searchRuleRequest = new SearchRuleRequest();
        // searchRuleRequest.setQprofile("AYet12kSSw4IR5icafE7");
        searchRuleRequest.setQprofile("AYYRQf9fR1HZdrX-PCyI");
        // searchRuleRequest.setRuleKey("findbugs:RANGE_ARRAY_INDEX");
        searchRuleRequest.setPs(10);
        SearchRulesResponse searchRulesResponse = clusterNode1Operations.sonarApi().searchRules(searchRuleRequest);
        Map<String, List<ActiveRuleInfo>> actives = searchRulesResponse.getActives();
        System.out.println(actives);
    }

    @Test
    public void testActiveRule() {
        String ruleKey = "findbugs:RANGE_ARRAY_INDEX";
        String profileKey = "AYVb64_JR1HZdrX-Oig1";
        String severity = "COMMON";
        clusterNode1Operations.sonarApi().activeRule(profileKey, ruleKey, severity);
    }

    @Test
    public void testDeActiveRule() {
        String ruleKey = "squid:S3923";
        String profileKey = "AYet12kSSw4IR5icafE7";
        clusterNode1Operations.sonarApi().deActiveRule(profileKey, ruleKey);
    }

    @Test
    public void testGetProfileInheritance() {
        String language = "java";
        String qualityProfile = "test:c49ba25d5a0f49ab867dfe3525abc1d1";
        ProfileInheritanceResp resp =
                clusterNode1Operations.sonarApi().getProfileInheritance(language, qualityProfile);
        System.out.println(resp);
    }

    @Test
    public void testSearchProject() {
        String projectKey = "52668";
        Project project = clusterNode1Operations.sonarApi().searchProjectByKey(projectKey);
        System.out.println(project);
        ProjectsResp resp = clusterNode1Operations.sonarApi().searchProjects(projectKey, 1, 10, null);
        System.out.println(resp.getComponents());
    }

    @Test
    public void testGetProjectProfile() {
        String projectKey = "measure:20554:test";
        QualityProfileReq profileReq = QualityProfileReq.builder()
                .project(projectKey)
                .language(PlatformLanguageEnum.JAVA.getName())
                .build();
        QualityProfileResp resp = clusterNode3Operations.sonarApi().getQualityProfile(profileReq);
        System.out.println(resp);
    }

    @Test
    public void testRoute() {
        SonarOperations clusterOperations = sonarClusterSimpleFactory.getClusterOperations(24049L);
    }

    @Test
    public void testGetProjectBranchesList() {
        String projectKey = "46676";
        BranchListResp branchListResp = clusterNode1Operations.sonarApi().getProjectBranchesList(projectKey);
        System.out.println(branchListResp.getBranches());
    }

    @Test
    public void testJudgeCompile() {
        ThemisResponse<NeedCompileResponse> needCompileResponseThemisResponse = sonarService.needCompile(28883695L);
    }

    @Test
    public void testParallelCount() {
        ParallelExecuteResponse parallelExecuteResponse = sonarService.canProcessParallelExecute(24049);
    }

    @Test
    public void testPipelineReport() {
        String s =
                "{\n"
                        + "    \"kspBuildId\":29524481,\n"
                        + "    \"sponsor\":\"wangwei45\",\n"
                        + "    \"gitBranch\":\"feature_ww_test_rule\",\n"
                        + "    \"gitProjectId\":24049,\n"
                        + "    \"buildModules\":\"ks-serveree-themis-component;ks-serveree-themis-api\",\n"
                        + "    \"mrId\":\"355\",\n"
                        + "    \"localBuildId\":0,\n"
                        + "    \"kspPipelineId\":726590,\n"
                        + "    \"commitId\":\"9a25b02a56460161aa03b65c2a17434d744c08c7\",\n"
                        + "    \"incrementMode\":true,\n"
                        + "    \"sendKimNotice\":false,\n"
                        + "    \"realCompile\":true,\n"
                        + "    \"sourceCommitId\":\"\",\n"
                        + "    \"mrStuck\":true,\n"
                        + "    \"onlyDiffIssue\":true,\n"
                        + "    \"issues\":[\n"
                        + "        {\n"
                        + "            \"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\n"
                        + "            \"message\":\"Null pointer dereference of msg in com.kuaishou.serveree.themis.api.Application.main(String[])\",\n"
                        + "            \"severity\":\"COMMON\",\n"
                        + "            \"type\":\"BUG\",\n"
                        + "            \"startLine\":34,\n"
                        + "            \"endLine\":34,\n"
                        + "            \"startOffset\":0,\n"
                        + "            \"endOffset\":43,\n"
                        + "            \"author\":\"wangwei45\",\n"
                        + "            \"ruleKey\":\"findbugs:NP_ALWAYS_NULL\"\n"
                        + "        },\n"
                        + "        {\n"
                        + "            \"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\n"
                        + "            \"message\":\"Null pointer dereference of str in com.kuaishou.serveree.themis.api.Application.test1()\",\n"
                        + "            \"severity\":\"COMMON\",\n"
                        + "            \"type\":\"BUG\",\n"
                        + "            \"startLine\":40,\n"
                        + "            \"endLine\":40,\n"
                        + "            \"startOffset\":0,\n"
                        + "            \"endOffset\":41,\n"
                        + "            \"author\":\"wangwei45\",\n"
                        + "            \"ruleKey\":\"findbugs:NP_ALWAYS_NULL\"\n"
                        + "        },\n"
                        + "        {\n"
                        + "            \"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/RefreshableLocalCache.java\",\n"
                        + "            \"message\":\"Null pointer dereference of name in com.kuaishou.serveree.themis.api.ww.RefreshableLocalCache.main(String[])\",\n"
                        + "            \"severity\":\"COMMON\",\n"
                        + "            \"type\":\"BUG\",\n"
                        + "            \"startLine\":126,\n"
                        + "            \"endLine\":126,\n"
                        + "            \"startOffset\":0,\n"
                        + "            \"endOffset\":44,\n"
                        + "            \"author\":\"wangwei45\",\n"
                        + "            \"ruleKey\":\"findbugs:NP_ALWAYS_NULL\"\n"
                        + "        },\n"
                        + "        {\n"
                        + "            \"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/WwTest.java\",\n"
                        + "            \"message\":\"Null passed for non-null parameter of new org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate(DataSource) in new com.kuaishou.serveree.themis.api.WwTest()\",\n"
                        + "            \"severity\":\"COMMON\",\n"
                        + "            \"type\":\"BUG\",\n"
                        + "            \"startLine\":29,\n"
                        + "            \"endLine\":29,\n"
                        + "            \"startOffset\":0,\n"
                        + "            \"endOffset\":104,\n"
                        + "            \"author\":\"wangwei45\",\n"
                        + "            \"ruleKey\":\"findbugs:NP_NONNULL_PARAM_VIOLATION\"\n"
                        + "        },\n"
                        + "        {\n"
                        + "            \"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/WwTest.java\",\n"
                        + "            \"message\":\"Null passed for non-null parameter of new org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate(DataSource) in com.kuaishou.serveree.themis.api.WwTest.namedParameterJdbcTemplate()\",\n"
                        + "            \"severity\":\"COMMON\",\n"
                        + "            \"type\":\"BUG\",\n"
                        + "            \"startLine\":32,\n"
                        + "            \"endLine\":32,\n"
                        + "            \"startOffset\":0,\n"
                        + "            \"endOffset\":65,\n"
                        + "            \"author\":\"wangwei45\",\n"
                        + "            \"ruleKey\":\"findbugs:NP_NONNULL_PARAM_VIOLATION\"\n"
                        + "        },\n"
                        + "        {\n"
                        + "            \"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/WwTest.java\",\n"
                        + "            \"message\":\"com.kuaishou.serveree.themis.api.WwTest.name() may return null, but is declared @Nonnull\",\n"
                        + "            \"severity\":\"COMMON\",\n"
                        + "            \"type\":\"BUG\",\n"
                        + "            \"startLine\":49,\n"
                        + "            \"endLine\":49,\n"
                        + "            \"startOffset\":0,\n"
                        + "            \"endOffset\":20,\n"
                        + "            \"author\":\"wangwei45\",\n"
                        + "            \"ruleKey\":\"findbugs:NP_NONNULL_RETURN_VIOLATION\"\n"
                        + "        }\n"
                        + "    ]\n"
                        + "}";
        PluginPipelineReportRequest request = JSONUtils.deserialize(s, PluginPipelineReportRequest.class);
        sonarService.pipelineReport(request);
    }

    @Test
    public void testRemoveProjectBranch() {
        String projectKey = "46676";
        String branch = "master||8287571||allModules";
        clusterNode1Operations.sonarApi().deleteProjectBranch(projectKey, branch);
    }

    @Test
    public void testPipelineReportInitIssuesInDiff() {
        String json = "{\"kspBuildId\":39609046,\"sponsor\":\"wangwei45\",\"gitBranch\":\"feature_ww_test_rule2\",\"gitProjectId\":24049,\"buildModules\":\"allModules\",\"mrId\":\"0\",\"localBuildId\":0,\"kspPipelineId\":701468,\"commitId\":\"1080e2a9\",\"incrementMode\":true,\"incrementType\":2,\"sendKimNotice\":false,\"realCompile\":true,\"sourceCommitId\":\"\",\"mrStuck\":false,\"onlyDiffIssue\":false,\"issues\":[{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.atag()\\\",\\\"source\\\":\\\"b74acb288c9d64d3e9c3be360d335440\\\",\\\"message\\\":\\\"Remove this throw statement from this finally block.\\\"}\",\"severity\":\"MAJOR\",\"type\":\"BUG\",\"startLine\":93,\"endLine\":93,\"startOffset\":16,\"endOffset\":21,\"author\":\"wangwei45\",\"ruleKey\":\"squid:S1143\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.test()\\\",\\\"source\\\":\\\"fb986cc150d72a0d6cbeba6d75b97de2\\\",\\\"message\\\":\\\"可能发生Key冲突的toMap方法\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":61,\"endLine\":61,\"startOffset\":38,\"endOffset\":105,\"author\":\"wangwei45\",\"ruleKey\":\"squid:S20002\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.toPercent(long)\\\",\\\"source\\\":\\\"717398aa3021f2d22a3cff56effaccbb\\\",\\\"message\\\":\\\"浮点数运算可能会有精度丢失问题, 建议改造为BigDecimal\\\"}\",\"severity\":\"MAJOR\",\"type\":\"BUG\",\"startLine\":67,\"endLine\":67,\"startOffset\":29,\"endOffset\":59,\"author\":\"wangwei45\",\"ruleKey\":\"squid:custom:FloatingPointCalculationChecker\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.toPercent(long)\\\",\\\"source\\\":\\\"36e2b4aaf006848fc1639a8f17a68857\\\",\\\"message\\\":\\\"Use \\\\\\\"BigDecimal.valueOf\\\\\\\" instead.\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":68,\"endLine\":68,\"startOffset\":23,\"endOffset\":50,\"author\":\"wangwei45\",\"ruleKey\":\"squid:S2111\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"\\\",\\\"source\\\":\\\"1fcff6c7e5ee66178a4bf09183c5e3b4\\\",\\\"message\\\":\\\"Complete the task associated to this TODO comment.\\\"}\",\"severity\":\"COMMON\",\"type\":\"CODE_SMELL\",\"startLine\":72,\"endLine\":72,\"startOffset\":0,\"endOffset\":83,\"author\":\"wangwei45\",\"ruleKey\":\"squid:S1135\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"\\\",\\\"source\\\":\\\"403f489049aa77f85bddcbe938311c11\\\",\\\"message\\\":\\\"Complete the task associated to this TODO comment.\\\"}\",\"severity\":\"COMMON\",\"type\":\"CODE_SMELL\",\"startLine\":74,\"endLine\":74,\"startOffset\":0,\"endOffset\":25,\"author\":\"wangwei45\",\"ruleKey\":\"squid:S1135\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.batchExecuteCommandWithNoCheck(java.util.List, String)\\\",\\\"source\\\":\\\"176f1cf06cbcfec84cea2c7a0a0689ad\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":116,\"endLine\":118,\"startOffset\":14,\"endOffset\":13,\"author\":\"wangwei45\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.batchExecuteCommandWithNoCheck(java.util.List, String)\\\",\\\"source\\\":\\\"20353fe87a6d15388181338f88060ac7\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":124,\"endLine\":126,\"startOffset\":14,\"endOffset\":13,\"author\":\"wangwei45\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.batchExecuteCommandWithNoCheck(java.util.List, String)\\\",\\\"source\\\":\\\"176f1cf06cbcfec84cea2c7a0a0689ad\\\",\\\"message\\\":\\\"catch到非具体子类型的异常要求打印异常栈\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":116,\"endLine\":118,\"startOffset\":14,\"endOffset\":13,\"author\":\"wangwei45\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.batchExecuteCommandWithNoCheck(java.util.List, String)\\\",\\\"source\\\":\\\"20353fe87a6d15388181338f88060ac7\\\",\\\"message\\\":\\\"catch到非具体子类型的异常要求打印异常栈\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":124,\"endLine\":126,\"startOffset\":14,\"endOffset\":13,\"author\":\"wangwei45\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.main(String[])\\\",\\\"source\\\":\\\"3a32a664d91830c1b09805c3f7e2ba93\\\",\\\"message\\\":\\\"Null pointer dereference of msg1 in com.kuaishou.serveree.themis.api.Application.main(String[])\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":153,\"endLine\":153,\"startOffset\":0,\"endOffset\":68,\"author\":\"wangwei45\",\"ruleKey\":\"findbugs:NP_ALWAYS_NULL\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.batchExecuteCommandWithNoCheck(java.util.List, String)\\\",\\\"source\\\":\\\"2c7c3e5a1e6b6154aa651c4cc756479f\\\",\\\"message\\\":\\\"conn is null guaranteed to be dereferenced in com.kuaishou.serveree.themis.api.Application.batchExecuteCommandWithNoCheck(List, String) on exception path\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":123,\"endLine\":123,\"startOffset\":0,\"endOffset\":41,\"author\":\"wangwei45\",\"ruleKey\":\"findbugs:NP_GUARANTEED_DEREF_ON_EXCEPTION_PATH\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.Application.bbb(long)\\\",\\\"source\\\":\\\"5aef118326aad5f89783fce39ed29e09\\\",\\\"message\\\":\\\"com.kuaishou.serveree.themis.api.Application.bbb(long) makes inefficient use of keySet iterator instead of entrySet iterator\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":139,\"endLine\":139,\"startOffset\":0,\"endOffset\":56,\"author\":\"wangwei45\",\"ruleKey\":\"findbugs:WMI_WRONG_MAP_ITERATOR\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/WwTest.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.ww.WwTest.test(java.util.Map, java.util.List)\\\",\\\"source\\\":\\\"0c4ded230712eb5e757ddb89104b2b43\\\",\\\"message\\\":\\\"可能发生Key冲突的toMap方法\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":18,\"endLine\":18,\"startOffset\":39,\"endOffset\":108,\"author\":\"wangwei45\",\"ruleKey\":\"squid:S20002\"},{\"location\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/RefreshableLocalCache.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.api.ww.RefreshableLocalCache.main(String[])\\\",\\\"source\\\":\\\"f4547fc110328545c67806351e3efcb9\\\",\\\"message\\\":\\\"Null pointer dereference of name in com.kuaishou.serveree.themis.api.ww.RefreshableLocalCache.main(String[])\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":101,\"endLine\":101,\"startOffset\":0,\"endOffset\":44,\"author\":\"wangwei45\",\"ruleKey\":\"findbugs:NP_ALWAYS_NULL\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.utils.IssueUtils.genIssueUniqIdV2(com.kuaishou.serveree.themis.component.common.entity.PCheckIssue, Integer, int)\\\",\\\"source\\\":\\\"9fb962332d6bd7d687194ee5862ae504\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":103,\"endLine\":106,\"startOffset\":10,\"endOffset\":9,\"author\":\"wangwei45\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.utils.IssueUtils.genIssueUniqIdV2(com.kuaishou.serveree.themis.component.common.entity.PCheckIssue, Integer, int)\\\",\\\"source\\\":\\\"9fb962332d6bd7d687194ee5862ae504\\\",\\\"message\\\":\\\"catch到非具体子类型的异常要求打印异常栈\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":103,\"endLine\":106,\"startOffset\":10,\"endOffset\":9,\"author\":\"wangwei45\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.utils.IssueUtils.genIssueUniqIdV2(com.kuaishou.serveree.themis.component.common.entity.CheckIssue, Integer, int)\\\",\\\"source\\\":\\\"9a87ab02b9b5cd44c176e6d55667e90a\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":142,\"endLine\":145,\"startOffset\":10,\"endOffset\":9,\"author\":\"wangwei45\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.utils.IssueUtils.genIssueUniqIdV2(com.kuaishou.serveree.themis.component.common.entity.CheckIssue, Integer, int)\\\",\\\"source\\\":\\\"9a87ab02b9b5cd44c176e6d55667e90a\\\",\\\"message\\\":\\\"catch到非具体子类型的异常要求打印异常栈\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":142,\"endLine\":145,\"startOffset\":10,\"endOffset\":9,\"author\":\"wangwei45\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/PlatformRuleServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.platform.impl.PlatformRuleServiceImpl.convert2RuleRelations(String, java.util.Map, java.util.List)\\\",\\\"source\\\":\\\"4ddaf1698c98308ef4f58f443f2621b7\\\",\\\"message\\\":\\\"可能发生Key冲突的toMap方法\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":945,\"endLine\":945,\"startOffset\":44,\"endOffset\":104,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:S20002\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/PlatformRuleServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.platform.impl.PlatformRuleServiceImpl.convert2RuleRelations(String, java.util.Map, java.util.List)\\\",\\\"source\\\":\\\"58e6102288b521bc25580438863b309a\\\",\\\"message\\\":\\\"com.kuaishou.serveree.themis.component.service.platform.impl.PlatformRuleServiceImpl.convert2RuleRelations(String, Map, List) makes inefficient use of keySet iterator instead of entrySet iterator\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":925,\"endLine\":925,\"startOffset\":0,\"endOffset\":75,\"author\":\"xieshijie\",\"ruleKey\":\"findbugs:WMI_WRONG_MAP_ITERATOR\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.updateProjectId()\\\",\\\"source\\\":\\\"5480acd6815f4605395d3cabc7243ab3\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":315,\"endLine\":317,\"startOffset\":14,\"endOffset\":13,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.updateProjectId()\\\",\\\"source\\\":\\\"5480acd6815f4605395d3cabc7243ab3\\\",\\\"message\\\":\\\"catch到Exception需要打印日志\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":315,\"endLine\":317,\"startOffset\":14,\"endOffset\":13,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.updateLinkUrl()\\\",\\\"source\\\":\\\"8e6529d687e8228d082310f5e4cdd467\\\",\\\"message\\\":\\\"可能发生Key冲突的toMap方法\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":329,\"endLine\":329,\"startOffset\":25,\"endOffset\":85,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:S20002\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.platformBatchInitProject(com.kuaishou.serveree.themis.component.vo.request.ProjectBatchInitRequest)\\\",\\\"source\\\":\\\"028e3dcfc3b27d5d9cf2dd32d71e4534\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":700,\"endLine\":702,\"startOffset\":14,\"endOffset\":13,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.platformBatchInitProject(com.kuaishou.serveree.themis.component.vo.request.ProjectBatchInitRequest)\\\",\\\"source\\\":\\\"028e3dcfc3b27d5d9cf2dd32d71e4534\\\",\\\"message\\\":\\\"catch到非具体子类型的异常要求打印异常栈\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":700,\"endLine\":702,\"startOffset\":14,\"endOffset\":13,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.transferKemJava(String)\\\",\\\"source\\\":\\\"05c04fb2b086cbbe547ac1d9ecf23424\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":739,\"endLine\":741,\"startOffset\":14,\"endOffset\":13,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.transferKemJava(String)\\\",\\\"source\\\":\\\"05c04fb2b086cbbe547ac1d9ecf23424\\\",\\\"message\\\":\\\"catch到非具体子类型的异常要求打印异常栈\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":739,\"endLine\":741,\"startOffset\":14,\"endOffset\":13,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.sonarJavaSettingSync()\\\",\\\"source\\\":\\\"a6ab537f7533e9290ff8768f8d1ee09b\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":1084,\"endLine\":1086,\"startOffset\":14,\"endOffset\":13,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.sonarJavaSettingSync()\\\",\\\"source\\\":\\\"a6ab537f7533e9290ff8768f8d1ee09b\\\",\\\"message\\\":\\\"catch到非具体子类型的异常要求打印异常栈\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":1084,\"endLine\":1086,\"startOffset\":14,\"endOffset\":13,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.platformFixRepoId()\\\",\\\"source\\\":\\\"548ad18cfe050cfb94702cd78a715d2d\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":1100,\"endLine\":1102,\"startOffset\":14,\"endOffset\":13,\"author\":\"xieshijie\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.platformFixRepoId()\\\",\\\"source\\\":\\\"548ad18cfe050cfb94702cd78a715d2d\\\",\\\"message\\\":\\\"catch到非具体子类型的异常要求打印异常栈\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":1100,\"endLine\":1102,\"startOffset\":14,\"endOffset\":13,\"author\":\"xieshijie\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"\\\",\\\"source\\\":\\\"1fc4eff1545d856893d24a3bdd3fa178\\\",\\\"message\\\":\\\"Complete the task associated to this TODO comment.\\\"}\",\"severity\":\"COMMON\",\"type\":\"CODE_SMELL\",\"startLine\":1146,\"endLine\":1146,\"startOffset\":0,\"endOffset\":33,\"author\":\"xieshijie\",\"ruleKey\":\"squid:S1135\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.changeReferType1Issue(com.kuaishou.serveree.themis.component.common.entity.IssueSummary)\\\",\\\"source\\\":\\\"5c9bcb6468990b2383dfd7995b16d7fe\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":1420,\"endLine\":1422,\"startOffset\":10,\"endOffset\":9,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.changeReferType1Issue(com.kuaishou.serveree.themis.component.common.entity.IssueSummary)\\\",\\\"source\\\":\\\"5c9bcb6468990b2383dfd7995b16d7fe\\\",\\\"message\\\":\\\"catch到非具体子类型的异常要求打印异常栈\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":1420,\"endLine\":1422,\"startOffset\":10,\"endOffset\":9,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.getSonarIssueLink(Integer, String, String)\\\",\\\"source\\\":\\\"af44085bc05a678685a57cc300d354ae\\\",\\\"message\\\":\\\"代码内不应该出现fail-safe逻辑, 请上抛异常触发流程阻塞\\\"}\",\"severity\":\"SERIOUS\",\"type\":\"BUG\",\"startLine\":1444,\"endLine\":1447,\"startOffset\":10,\"endOffset\":9,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:InvalidTryCatchInChecker\"},{\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"message\":\"{\\\"owner\\\":\\\"com.kuaishou.serveree.themis.component.service.impl.LocalServiceImpl.getSonarIssueLink(Integer, String, String)\\\",\\\"source\\\":\\\"af44085bc05a678685a57cc300d354ae\\\",\\\"message\\\":\\\"catch到Exception需要打印日志\\\"}\",\"severity\":\"COMMON\",\"type\":\"BUG\",\"startLine\":1444,\"endLine\":1447,\"startOffset\":10,\"endOffset\":9,\"author\":\"lixiaoxin\",\"ruleKey\":\"squid:custom:KspayCatchExceptionNoLogChecker\"}]}";
        PluginPipelineReportRequest request = JSONUtils.deserialize(json, PluginPipelineReportRequest.class);
        List<PCheckIssue> pCheckIssueList = org.apache.commons.compress.utils.Lists.newArrayList();
        for (PipelineIssue pipelineIssue : request.getIssues()) {
            PCheckIssue pCheckIssue = PCheckIssue.builder()
                    .startLine(pipelineIssue.getStartLine())
                    .endLine(pipelineIssue.getEndLine())
                    .location(pipelineIssue.getLocation())
                    .inDiff(true) // 默认值
                    .validStuck(true)
                    .build();
            pCheckIssueList.add(pCheckIssue);
        }
        List<ChangedFile> changedFiles = getDiff();
        // 聚合 <文件 --> 新增块列表>
        Map<String, List<Fragment>> fileFramentsMap = changedFiles.stream()
                .collect(Collectors.toMap(
                                ChangedFile::getFilePath,
                                f -> CollectionUtils.isEmpty(f.getFragments()) ? Collections.emptyList() : f.getFragments()
                        )
                );
        for (int i = 0; i < 5; i++) {
            shuffle(pCheckIssueList);
            long s = System.nanoTime();
            filterAndResetIssuesInDiff1(pCheckIssueList, fileFramentsMap);
            long e = System.nanoTime();
            System.out.println("m1: cost: " + (e - s));
            shuffle(pCheckIssueList);
            s = System.nanoTime();
            filterAndResetIssuesInDiff2(pCheckIssueList, fileFramentsMap);
            e = System.nanoTime();
            System.out.println("m2: cost: " + (e - s));
            shuffle(pCheckIssueList);
            s = System.nanoTime();
            filterAndResetIssuesInDiff3(pCheckIssueList, fileFramentsMap);
            e = System.nanoTime();
            System.out.println("m3: cost: " + (e - s));
        }
    }

    private void shuffle(List<PCheckIssue> pCheckIssueList) {
        pCheckIssueList.forEach(a -> {a.setInDiff(true); a.setValidStuck(true);});
        int n = pCheckIssueList.size() - 1;
        for (int i = 0; i < n / 2; i++) {
            PCheckIssue temp = pCheckIssueList.get(i);
            pCheckIssueList.set(i, pCheckIssueList.get(n - 1 - i));
            pCheckIssueList.set(n - 1 - i, temp);
        }
    }

    /**
     * 过滤diff行内的issue，并重置相关字段，方法一
     * issue无序，fragments 有序，顺序查找
     */
    private void filterAndResetIssuesInDiff1(List<PCheckIssue> pCheckIssueList,
            Map<String, List<Fragment>> fileFramentsMap) {
        // 遍历issue
        for (PCheckIssue pCheckIssue : pCheckIssueList) {
            // issue所在文件的diff片段
            List<Fragment> fragmentList = fileFramentsMap.get(pCheckIssue.getLocation());
            Integer endLine = pCheckIssue.getEndLine();
            boolean inDiff = false;
            if (CollectionUtils.isNotEmpty(fragmentList)) {
                for (Fragment fragment : fragmentList) {
                    // 当前issue在某个diff片段内
                    if (endLine >= fragment.getStartLine() && endLine <= fragment.getEndLine()) {
                        inDiff = true;
                        break;
                    }
                }
            }
            // 不在diff内，卡点不生效
            if (!inDiff) {
                pCheckIssue.setInDiff(false);
                pCheckIssue.setValidStuck(false);
            }
        }
        System.out.println(pCheckIssueList.stream().filter(PCheckIssue::getValidStuck).count());
    }

    /**
     * 过滤diff行内的issue，并重置相关字段，方法二
     *
     * issue排序，fragments有序
     */
    private void filterAndResetIssuesInDiff2(List<PCheckIssue> pCheckIssueList,
            Map<String, List<Fragment>> fileFramentsMap) {
        Map<String, List<PCheckIssue>> issueMap = pCheckIssueList.stream().collect(
                Collectors.groupingBy(PCheckIssue::getLocation,
                        Collectors.collectingAndThen(toList(), list -> list.stream().sorted(
                                Comparator.comparingInt(PCheckIssue::getEndLine)).collect(toList()))));
        for (Entry<String, List<PCheckIssue>> entry : issueMap.entrySet()) {
            String location = entry.getKey();
            List<PCheckIssue> issueList = entry.getValue();
            // issue所在文件的diff片段
            List<Fragment> fragmentList = fileFramentsMap.get(location);
            if (CollectionUtils.isEmpty(fragmentList)) {
                issueList.forEach(a -> {a.setInDiff(false); a.setValidStuck(false);});
                continue;
            }
            for (int i = 0, j = 0; i < issueList.size(); i++) {
                PCheckIssue issue = issueList.get(i);
                while (j < fragmentList.size() && issue.getEndLine() > fragmentList.get(j).getEndLine()) {
                    j++;
                }
                if (j >= fragmentList.size() || issue.getEndLine() < fragmentList.get(j).getStartLine()) {
                    issue.setInDiff(false);
                    issue.setValidStuck(false);
                }
            }
        }
        // 遍历issue
        System.out.println(pCheckIssueList.stream().filter(PCheckIssue::getValidStuck).count());
    }

    /**
     * 过滤diff行内的issue，并重置相关字段，方法三
     *
     * issue不排序，fragments有序， 二分搜索
     */
    private void filterAndResetIssuesInDiff3(List<PCheckIssue> pCheckIssueList,
            Map<String, List<Fragment>> fileFramentsMap) {
        // 遍历issue
        for (PCheckIssue pCheckIssue : pCheckIssueList) {
            // issue所在文件的diff片段
            List<Fragment> fragmentList = fileFramentsMap.get(pCheckIssue.getLocation());
            Integer endLine = pCheckIssue.getEndLine();
            boolean inDiff = false;
            if (CollectionUtils.isNotEmpty(fragmentList)) {
                int l = 0, r = fragmentList.size();
                while (l < r) {
                    int m = l + r >> 1;
                    if (fragmentList.get(m).getEndLine() < endLine) {
                        l++;
                    } else if (fragmentList.get(m).getStartLine() > endLine) {
                        r = m;
                    } else {
                        inDiff = true;
                        break;
                    }
                }
            }
            // 不在diff内，卡点不生效
            if (!inDiff) {
                pCheckIssue.setInDiff(false);
                pCheckIssue.setValidStuck(false);
            }
        }
        System.out.println(pCheckIssueList.stream().filter(PCheckIssue::getValidStuck).count());
    }

    private List<ChangedFile> getDiff() {
        String json = "[{\"filePath\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":3,\"endLine\":14},{\"startLine\":17,\"endLine\":17},{\"startLine\":23,\"endLine\":28},{\"startLine\":48,\"endLine\":48},{\"startLine\":51,\"endLine\":78},{\"startLine\":80,\"endLine\":147},{\"startLine\":150,\"endLine\":153}]},{\"filePath\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/controller/LocalController.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":237,\"endLine\":241},{\"startLine\":247,\"endLine\":251}]},{\"filePath\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/Department.java\",\"changeType\":\"A\",\"fragments\":[{\"startLine\":1,\"endLine\":13}]},{\"filePath\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/KconfTest.java\",\"changeType\":\"A\",\"fragments\":[{\"startLine\":1,\"endLine\":139}]},{\"filePath\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/KconfTestService.java\",\"changeType\":\"A\",\"fragments\":[{\"startLine\":1,\"endLine\":18}]},{\"filePath\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/RefreshableLocalCache.java\",\"changeType\":\"A\",\"fragments\":[{\"startLine\":1,\"endLine\":120}]},{\"filePath\":\"ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/ww/WwTest.java\",\"changeType\":\"A\",\"fragments\":[{\"startLine\":1,\"endLine\":27}]},{\"filePath\":\"ks-serveree-themis-api/src/main/resources/mappers/PipelineLogMapper.xml\",\"changeType\":\"A\",\"fragments\":[{\"startLine\":1,\"endLine\":438}]},{\"filePath\":\"ks-serveree-themis-api/src/test/java/com/kuaishou/serveree/themis/GitTest.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":1090,\"endLine\":1090},{\"startLine\":1100,\"endLine\":1108}]},{\"filePath\":\"ks-serveree-themis-api/src/test/java/com/kuaishou/serveree/themis/SonarTest.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":1007,\"endLine\":1008}]},{\"filePath\":\"ks-serveree-themis-api/src/test/java/com/kuaishou/serveree/themis/ww/WwTest.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":8,\"endLine\":8},{\"startLine\":11,\"endLine\":11},{\"startLine\":213,\"endLine\":230}]},{\"filePath\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/LocalService.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":90,\"endLine\":93}]},{\"filePath\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/impl/LocalServiceImpl.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":7,\"endLine\":7},{\"startLine\":18,\"endLine\":18},{\"startLine\":45,\"endLine\":45},{\"startLine\":71,\"endLine\":71},{\"startLine\":126,\"endLine\":126},{\"startLine\":136,\"endLine\":137},{\"startLine\":157,\"endLine\":157},{\"startLine\":238,\"endLine\":239},{\"startLine\":569,\"endLine\":569},{\"startLine\":595,\"endLine\":595},{\"startLine\":1684,\"endLine\":1735},{\"startLine\":1744,\"endLine\":1769}]},{\"filePath\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/service/platform/impl/PlatformRuleServiceImpl.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":926,\"endLine\":929}]},{\"filePath\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/utils/IssueUtils.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":161,\"endLine\":168}]},{\"filePath\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component/vo/request/ProfileAddRuleRequestVo.java\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":31,\"endLine\":33}]},{\"filePath\":\"script/java_module/sonar/maven_scanner_new.sh\",\"changeType\":\"M\",\"fragments\":[]},{\"filePath\":\"script/java_module/sonar/pmd_check.sh\",\"changeType\":\"M\",\"fragments\":[{\"startLine\":3,\"endLine\":34},{\"startLine\":112,\"endLine\":112},{\"startLine\":117,\"endLine\":133},{\"startLine\":169,\"endLine\":169}]}]";
        return JSONUtils.deserializeList(json, ChangedFile.class);
    }

    @Test
    public void testGetAllResolved() {
        List<Issue> featureWwTestRule = node1Operations.getAllIssuesMarkedAsClosedOrToReview("measure:24049", "feature_ww_test_rule");
        Assert.assertNotNull(featureWwTestRule);
        Assert.assertTrue(featureWwTestRule.isEmpty());
    }
}
