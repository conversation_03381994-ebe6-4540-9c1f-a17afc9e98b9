package com.kuaishou.serveree.themis;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.kuaishou.kconf.client.Kconf;
import com.kuaishou.kconf.client.Kconfs;
import com.kuaishou.qa.serveree.common.sso.data.SsoUserInfo;
import com.kuaishou.serveree.themis.component.client.sonar.operations.ClusterNode3Operations;
import com.kuaishou.serveree.themis.component.common.entity.CheckBase;
import com.kuaishou.serveree.themis.component.common.entity.CheckDisplay;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.config.redis.KsRedisClient;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformSonarInteractiveType;
import com.kuaishou.serveree.themis.component.constant.platform.RepoStarActionType;
import com.kuaishou.serveree.themis.component.entity.platform.ExecuteScanContext;
import com.kuaishou.serveree.themis.component.entity.platform.LanguageSetting;
import com.kuaishou.serveree.themis.component.entity.platform.PlatformSonarInteractiveContext;
import com.kuaishou.serveree.themis.component.entity.sonar.HookPayload;
import com.kuaishou.serveree.themis.component.service.CheckBaseService;
import com.kuaishou.serveree.themis.component.service.CheckDisplayService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryGroupService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCommonService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformFileMeasureService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformFileService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformIssueService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformProfileService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRuleService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformScanTriggerService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformSonarInteractiveService;
import com.kuaishou.serveree.themis.component.service.platform.notice.CheckNoticeActionService;
import com.kuaishou.serveree.themis.component.service.platform.scan.PlatformScanHelper;
import com.kuaishou.serveree.themis.component.service.sonar.process.hooks.MavenScannerNewOfflineCheckHook;
import com.kuaishou.serveree.themis.component.service.sonar.process.hooks.MavenScannerNewPluginProcessHook;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.FileDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.FileSegmentsRequest;
import com.kuaishou.serveree.themis.component.vo.request.GitBranchListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.GitBranchRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.GitRepoSearchRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.IssueCycleComplexityRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueDuplicationRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueListRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.request.LabelListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.LanguageListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ListRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileAddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCopyRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileCreateRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileDeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileGetByNameRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ProfileSearchRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoMeasuresHistoryRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoMeasuresOverviewRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoMeasuresOverviewRequest.MetricKeys;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoScanHistoryRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSearchRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSettingsRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoSkyeyeDetailRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoStarRequest;
import com.kuaishou.serveree.themis.component.vo.request.RuleDetailRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.ScannerListRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.IssueTransitionBpmRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckIssueTransitionRequest;
import com.kuaishou.serveree.themis.component.vo.response.FileDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.FileSegmentsResponse;
import com.kuaishou.serveree.themis.component.vo.response.GitBranchListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.GitRepoSearchResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.IssueCycleComplexityResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueDuplicationResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.LabelListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.LanguageListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ListRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileCopyResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileGetByNameResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ProfileSearchResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.RepoCreateResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoInfoResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoMeasuresHistoryResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoMeasuresOverviewResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoScanHistoryResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSearchResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSettingsResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoSkyeyeDetailResponse;
import com.kuaishou.serveree.themis.component.vo.response.RuleDetailResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.ScannerListResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.sonar.SonarIssueVo;

/**
 * <AUTHOR>
 * @since 2022/5/20 6:42 PM
 */
public class PlatformTest extends SpringBaseTest {

    @Autowired
    private PlatformRepoService platformRepoService;

    @Autowired
    private PlatformProfileService platformProfileService;

    @Autowired
    private PlatformSonarInteractiveService platformSonarInteractiveService;

    @Autowired
    private CheckDisplayService checkDisplayService;

    @Autowired
    private MavenScannerNewOfflineCheckHook mavenScannerNewOfflineCheckHook;

    @Autowired
    private MavenScannerNewPluginProcessHook mavenScannerNewPluginCheckHook;

    @Autowired
    private PlatformScanHelper platformScanHelper;

    @Autowired
    private PlatformCommonService platformCommonService;

    @Autowired
    private KsRedisClient ksRedisClient;

    @Autowired
    private PlatformRuleService platformRuleService;

    @Autowired
    private PlatformIssueService platformIssueService;

    @Autowired
    private PlatformScanTriggerService platformScanTriggerService;

    @Autowired
    private PlatformFileService platformFileService;

    private final String profileName = "test:lxx-test-01";

    @Autowired
    private IssueSummaryService issueSummaryService;

    @Autowired
    private IssueSummaryGroupService issueSummaryGroupService;

    @Autowired
    private ClusterNode3Operations clusterNode3Operations;

    @Autowired
    PlatformFileMeasureService fileMeasureService;

    private static final Kconf<List<Integer>> NEED_UT_GIT_PROJECT_IDS =
            Kconfs.ofIntegerList("qa.themis.platformNeedUtGitProjectIds",
                    org.apache.commons.compress.utils.Lists.newArrayList()).build();

    private Kconf<Map<String, String>> az2ProjectIdBranchMap =
            Kconfs.ofStringMap("qa.themis.az2ProjectIdBranchSetting", Maps.newHashMap()).build();

    @Autowired
    private CheckNoticeActionService checkNoticeActionService;

    @Autowired
    private CheckBaseService checkBaseService;

    @Test
    public void testCreate() {
        LanguageSetting languageSetting = new LanguageSetting();
        languageSetting.setLanguage("go");
        languageSetting.setVersion("1.16.7");
        RepoCreateRequest createRequest = RepoCreateRequest.builder()
                .gitProjectId(24536)
                .branch("master")
                .repoUrl("*************************:kbase-monitor/grafana.git")
                .profileName("Sonar way")
                .scanner(PlatformScannerEnum.SONAR_SCANNER_NEW.getScanner())
                .triggerCron("0 0 00 ? * MON,WED,FRI,SUN,SAT,THU,TUE")
                .languageSetting(languageSetting)
                .build();
        System.out.println(JSONUtils.serialize(createRequest));
        RepoCreateResponse createResponse = platformRepoService.create(createRequest, "lixiaoxin");
        System.out.println(JSONUtils.serialize(createResponse));
    }

    @Test
    public void testCreateProfile() {
        ProfileCreateRequestVo profileCreateRequestVo = new ProfileCreateRequestVo();
        profileCreateRequestVo.setProfileName("lxx-test01");
        profileCreateRequestVo.setLanguage("java");
        profileCreateRequestVo.setScanner(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner());
        platformProfileService.create(profileCreateRequestVo, "lixiaoxin");
    }

    @Test
    public void testProfileAddRules() {
        ProfileAddRuleRequestVo requestVo = new ProfileAddRuleRequestVo();
        requestVo.setProfileName(profileName);
        requestVo.setRuleKeyList(Lists.newArrayList("squid:S3923"));
        platformProfileService.addRule(requestVo, "lixiaoxin");
    }

    @Test
    public void testProfileDeleteRules() {
        ProfileDeleteRuleRequestVo requestVo = new ProfileDeleteRuleRequestVo();
        requestVo.setProfileName(profileName);
        //        requestVo.setScanner(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner());
        requestVo.setRuleKeyList(Lists.newArrayList("findbugs:RE_POSSIBLE_UNINTENDED_PATTERN"));
        platformProfileService.deleteRule(requestVo, "lixiaoxin");
    }

    @Test
    public void testSearchProfiles() {
        ProfileSearchRequestVo requestVo = ProfileSearchRequestVo.builder()
                .language("java")
                .search("test")
                .build();
        ProfileSearchResponseVo search = platformProfileService.search(requestVo, "lixiaoxin");
        System.out.println(JSONUtils.serialize(search));
    }

    @Test
    public void testProfileCopy() {
        ProfileCopyRequestVo copyRequestVo = ProfileCopyRequestVo.builder()
                .displayName("李小鑫的copy规则集911")
                .profileName("lxx-test-01")
                .build();
        ProfileCopyResponseVo responseVo = platformProfileService.copyProfile(copyRequestVo, "zhangsan111");
        System.out.println(JSONUtils.serialize(responseVo));
    }

    @Test
    public void testAddRule() {
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.PROFILE_ADD_RULE.getType())
                .profileName(profileName)
                .ruleKeyList(Lists.newArrayList("findbugs:TLW_TWO_LOCK_WAIT", "findbugs:UW_UNCOND_WAIT",
                        "findbugs:ML_SYNC_ON_FIELD_TO_GUARD_CHANGING_THAT_FIELD"))
                .language("java")
                .build();
        platformSonarInteractiveService.interactive(context);
    }

    @Test
    public void testAddRuleWithSeverity() {
        ProfileAddRuleRequestVo.RuleInfo ruleInfo = new ProfileAddRuleRequestVo.RuleInfo();
        ruleInfo.setRuleKey("findbugs:RE_POSSIBLE_UNINTENDED_PATTERN");
        ruleInfo.setSeverity("SERIOUS");
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.PROFILE_ADD_RULE.getType())
                .profileName("test:a967a005e79d42948e7aa46caddb2dc1")
                .ruleInfos(Arrays.asList(ruleInfo))
                .language("java")
                .build();
        platformSonarInteractiveService.interactive(context);
    }


    @Test
    public void testDeleteRule() {
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.PROFILE_DELETE_RULE.getType())
                .profileName(profileName)
                .ruleKeyList(Lists.newArrayList("findbugs:TLW_TWO_LOCK_WAIT"))
                .language("java")
                .build();
        platformSonarInteractiveService.interactive(context);
    }

    @Test
    public void testKafkaProjectUpdateProfile() {
        PlatformSonarInteractiveContext context = PlatformSonarInteractiveContext.builder()
                .actionType(PlatformSonarInteractiveType.CREATE_PROJECT.getType())
                .gitProjectId(23221)
                .build();
        platformSonarInteractiveService.interactive(context);
    }

    @Test
    public void testCopy() {
        String s = "{\"actionType\":6,\"language\":null,\"profileName\":\"test:jmm-test-08\",\"profileType\":null,"
                + "\"projectKey\":null,\"gitProjectId\":null,\"ruleKeyList\":null,\"ruleInfos\":null,"
                + "\"toProfileName\":\"test:1f586007bc6946229ddb43dd61aa4a91\"}";
        PlatformSonarInteractiveContext deserialize = JSONUtils.deserialize(s, PlatformSonarInteractiveContext.class);
        platformSonarInteractiveService.interactive(deserialize);
    }

    @Test
    public void testInitDisplay() {
        CheckDisplay checkDisplay = new CheckDisplay();
        checkDisplay.setDetailDisplayJson("{\"templateList\":[{\"metricKey\":\"bugs_count\",\"name\":\"Bugs\","
                + "\"iconUrl\":\"https://cdnfile.corp.kuaishou"
                + ".com/kc/files/a/kdev-workbench/codeScan-prod/assets/img/bug.svg\",\"iconType\":2,\"value\":null,"
                + "\"desc\":\"一个会破坏代码的编码错误，需要立即修复。\"}]}");
        checkDisplay.setLanguage("javascript");
        checkDisplay.setListDisplayJson("{\"mappingList\":[{\"mappingName\":\"可靠性\","
                + "\"mappingVos\":[{\"title\":\"Bugs\",\"metricKey\":\"bugs_count\",\"value\":null,"
                + "\"iconUrl\":\"https://cdnfile.corp.kuaishou"
                + ".com/kc/files/a/kdev-workbench/codeScan-prod/assets/img/bug.svg\",\"iconType\":2,"
                + "\"desc\":\"一个会破坏代码的编码错误，需要立即修复。\",\"jumpUrl\":\"${kdevUrl}/web/codescan/project/issueList?types"
                + "=bugs&gitProjectId=${gitProjectId}&branch=${branch}\"}]}]}");
        checkDisplay.setScanner(PlatformScannerEnum.SKY_EYE.getScanner());
        checkDisplay.setGmtModified(LocalDateTime.now());
        checkDisplay.setGmtCreate(LocalDateTime.now());

        checkDisplayService.save(checkDisplay);
    }

    @Test
    public void testHook() {
        String hook = "{\n" +
                "    \"serverUrl\":\"https://sonar-cluster-2.corp.kuaishou.com\",\n" +
                "    \"taskId\":\"AYgyGP5n2CPuqaFGcHY4\",\n" +
                "    \"status\":\"SUCCESS\",\n" +
                "    \"analysedAt\":\"2023-05-19T11:39:32+0800\",\n" +
                "    \"revision\":\"86ac7ebf4660608e374f9f6623e8d9c2954893ed\",\n" +
                "    \"changedAt\":\"2023-05-19T11:39:32+0800\",\n" +
                "    \"project\":{\n" +
                "        \"key\":\"24049\",\n" +
                "        \"name\":\"ks-serveree-themis\",\n" +
                "        \"url\":\"https://sonar-cluster-2.corp.kuaishou.com/dashboard?id=24049\"\n" +
                "    },\n" +
                "    \"branch\":{\n" +
                "        \"name\":\"master||19435071||allModules\",\n" +
                "        \"type\":\"LONG\",\n" +
                "        \"isMain\":false,\n" +
                "        \"url\":\"https://sonar-cluster-2.corp.kuaishou.com/dashboard?id=24049&amp;branch=master%7C%7C19435071%7C%7CallModules\"\n" +
                "    },\n" +
                "    \"qualityGate\":{\n" +
                "        \"name\":\"Sonar way\",\n" +
                "        \"status\":\"OK\",\n" +
                "        \"conditions\":[\n" +
                "            {\n" +
                "                \"metric\":\"new_reliability_rating\",\n" +
                "                \"operator\":\"GREATER_THAN\",\n" +
                "                \"status\":\"NO_VALUE\",\n" +
                "                \"errorThreshold\":\"1\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"metric\":\"new_security_rating\",\n" +
                "                \"operator\":\"GREATER_THAN\",\n" +
                "                \"status\":\"NO_VALUE\",\n" +
                "                \"errorThreshold\":\"1\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"metric\":\"new_maintainability_rating\",\n" +
                "                \"operator\":\"GREATER_THAN\",\n" +
                "                \"status\":\"NO_VALUE\",\n" +
                "                \"errorThreshold\":\"1\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"metric\":\"new_coverage\",\n" +
                "                \"operator\":\"LESS_THAN\",\n" +
                "                \"status\":\"NO_VALUE\",\n" +
                "                \"errorThreshold\":\"80\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"metric\":\"new_duplicated_lines_density\",\n" +
                "                \"operator\":\"GREATER_THAN\",\n" +
                "                \"status\":\"NO_VALUE\",\n" +
                "                \"errorThreshold\":\"3\"\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"properties\":{\n" +
                "        \"sonar.analysis.sendKimNotice\":\"false\",\n" +
                "        \"sonar.analysis.projectId\":\"24049\",\n" +
                "        \"sonar.analysis.mrId\":\"\",\n" +
                "        \"sonar.analysis.buildLanguageVersion\":\"11\",\n" +
                "        \"sonar.analysis.kspBuildId\":\"19435074\",\n" +
                "        \"sonar.analysis.modules\":\"allModules\",\n" +
                "        \"sonar.analysis.buildUserName\":\"lixiaoxin\",\n" +
                "        \"sonar.analysis.repoUrl\":\"*************************:serveree/ks-serveree-themis.git\",\n" +
                "        \"sonar.analysis.commitId\":\"86ac7ebf\",\n" +
                "        \"sonar.analysis.branch\":\"master\",\n" +
                "        \"sonar.analysis.kspPipelineId\":\"313650\",\n" +
                "        \"sonar.analysis.buildLanguage\":\"java\",\n" +
                "        \"sonar.analysis.scannerType\":\"1\",\n" +
                "        \"sonar.analysis.stuckPoint\":\"true\",\n" +
                "        \"sonar.analysis.incrementMode\":\"false\"\n" +
                "    }\n" +
                "}";
        HookPayload deserialize = JSONUtils.deserialize(hook, HookPayload.class);
        mavenScannerNewPluginCheckHook.handle(deserialize);
    }

    @Test
    public void testScan() {
        ExecuteScanContext scanContext = ExecuteScanContext.builder()
                .checkRepoBranchId(4427)
                .triggerRecordId(1371L)
                .scanner(PlatformScannerEnum.MAVEN_SCANNER_NEW.getScanner())
                .build();
        platformScanHelper.executeScan(scanContext);
    }

    @Test
    public void testRepoSearch() {
        ksRedisClient.sync().get("aaa");
        RepoSearchRequest searchRequest = RepoSearchRequest.builder()
                .selectedType(0)
                .language("java")
                .build();
        RepoSearchResponse repoSearchResponse = platformRepoService.search(searchRequest, "lixiaoxin");
        System.out.println(JSONUtils.serialize(repoSearchResponse));
    }

    @Test
    public void testRepoDetail() {
        ksRedisClient.sync().get("aaa");
        RepoDetailRequest repoDetailRequest = new RepoDetailRequest();
        repoDetailRequest.setGitProjectId(32412);
        repoDetailRequest.setBranch("master");
        RepoDetailResponse response = platformRepoService.detail(repoDetailRequest, "lixiaoxin");
        System.out.println(JSONUtils.serialize(response));
    }

    @Test
    public void testRepoSkyDetail() {
        ksRedisClient.sync().get("aaa");
        RepoSkyeyeDetailRequest repoSkyeyeDetailRequest = new RepoSkyeyeDetailRequest();
        repoSkyeyeDetailRequest.setGitProjectId(42847);
        repoSkyeyeDetailRequest.setBranch("master");
        RepoSkyeyeDetailResponse response = platformRepoService.skyeyeDetail(repoSkyeyeDetailRequest, "lixiaoxin");
        System.out.println(JSONUtils.serialize(response));
    }

    @Test
    public void testMeasuresHistory() {
        ksRedisClient.sync().get("aaa");
        RepoMeasuresHistoryRequest request = new RepoMeasuresHistoryRequest();
        //        request.setDateType(0);
        //        request.setMetricKeys(Lists.newArrayList("bugs_score", "code_smell_score", "vulnerability_score",
        //                "duplication_score", "complexity_score", "best_practice_score"));
        //        request.setProjects(Lists.newArrayList(new GitBranchRequestVo(42847, "master"),
        //                new GitBranchRequestVo(625, "feature_test_skyeye_mr")));
        //        request.setStartTime(
        //                DateUtils.getMillTimestampFromLocalDateTime(DateUtils.getLocalDateTimeFromString
        //                ("2001-01-01")));
        //        request.setEndTime(
        //                DateUtils.getMillTimestampFromLocalDateTime(DateUtils.getLocalDateTimeFromString
        //                ("2022-12-02")));


        String s = "{\"projects\":[{\"gitProjectId\":19985,\"branch\":\"jim_feature_taskBrand\"}],\"dateType\":0,"
                + "\"metricKeys\":[\"bugs_score\",\"code_smell_score\",\"duplication_score\",\"complexity_score\","
                + "\"vulnerability_score\",\"best_practice_score\"],\"startTime\":1656922050203,"
                + "\"endTime\":1659514050204}";
        RepoMeasuresHistoryRequest deserialize = JSONUtils.deserialize(s, RepoMeasuresHistoryRequest.class);

        RepoMeasuresHistoryResponse response = platformRepoService.measuresHistory(deserialize, "lixiaoxin");
        System.out.println(JSONUtils.serialize(response));
    }

    @Test
    public void testMeasuresOverview() {
        ksRedisClient.sync().get("aaa");
        RepoMeasuresOverviewRequest request = new RepoMeasuresOverviewRequest();
        MetricKeys metricKeys = new MetricKeys(Lists.newArrayList("bugs_count", "code_smell_count"),
                Lists.newArrayList("framework", "ui_library"));
        request.setMetricKeys(metricKeys);
        request.setProjects(Lists.newArrayList(new GitBranchRequestVo(42847, "master"),
                new GitBranchRequestVo(44766, "master")));
        RepoMeasuresOverviewResponse response = platformRepoService.measuresOverview(request, "liwanlu05");
        System.out.println(JSONUtils.serialize(response));

    }

    @Test
    public void testRepoSettings() {
        ksRedisClient.sync().get("aaa");
        RepoSettingsRequest settingsRequest = new RepoSettingsRequest();
        settingsRequest.setGitProjectId(625);
        settingsRequest.setBranch("feature_test_skyeye_mr");
        RepoSettingsResponse settings = platformRepoService.settings(settingsRequest);
        System.out.println(JSONUtils.serialize(settings));
    }

    @Test
    public void testRuleList() {
        long l = System.currentTimeMillis();
        ListRuleRequestVo ruleRequestVo = ListRuleRequestVo.builder()
                .language("java")
                .profileName("lxx-test-01")
                .page(1)
                .pageSize(50)
                .selected(false)
                .profileName("64cd76814a644b6590f49f04a40dee2f")
                .build();
        ListRuleResponseVo list = platformRuleService.list(ruleRequestVo, "lixiaoxin");
        long l1 = System.currentTimeMillis();
        System.out.println("cost time is {}" + (l1 - l));
        System.out.println(JSONUtils.serialize(list));
    }

    @Test
    public void testGitRepoSearch() {
        GitRepoSearchRequestVo requestVo = GitRepoSearchRequestVo.builder()
                .search("kuaishou")
                .build();
        GitRepoSearchResponseVo gitRepoSearchResponseVo = platformCommonService.gitRepoSearch(requestVo,
                SsoUserInfo.getUserName());
        System.out.println(JSONUtils.serialize(gitRepoSearchResponseVo));
    }

    @Test
    public void testGitBranchSearch() {
        GitBranchListRequestVo listRequestVo = GitBranchListRequestVo.builder()
                .gitProjectId(24049)
                .search("feature")
                .build();
        GitBranchListResponseVo gitBranchListResponseVo = platformCommonService.gitBranchList(listRequestVo);
        System.out.println(JSONUtils.serialize(gitBranchListResponseVo));
    }

    @Test
    public void testDuplications() {
        IssueDuplicationRequest issueDuplicationRequest = new IssueDuplicationRequest();
        issueDuplicationRequest.setGitProjectId(5809);
        issueDuplicationRequest.setBranch("master");
        issueDuplicationRequest.setType(1);
        IssueDuplicationResponse duplication = platformIssueService.duplication(issueDuplicationRequest, "lixiaoxin");
        System.out.println(JSONUtils.serialize(duplication));
    }

    @Test
    public void testComplexity() {
        IssueCycleComplexityRequest issueCycleComplexityRequest = new IssueCycleComplexityRequest();
        issueCycleComplexityRequest.setGitProjectId(5809);
        issueCycleComplexityRequest.setBranch("master");
        IssueCycleComplexityResponse response =
                platformIssueService.cycleComplexity(issueCycleComplexityRequest, "lixiaoxin");
        System.out.println(JSONUtils.serialize(response));
    }

    @Test
    public void testIssueSearch() {
        long l = System.currentTimeMillis();
        IssueListRequest issueListRequest = new IssueListRequest();
        issueListRequest.setGitProjectId(14699);
        issueListRequest.setBranch("master");
        issueListRequest.setTypes("BUG");
        issueListRequest.setIssueKeys("14699l03412c3el921539897l2432430102l258434555l362041558");
        IssueListResponse list = platformIssueService.list(issueListRequest);
        long l1 = System.currentTimeMillis();
        System.out.println(JSONUtils.serialize(list));
        System.out.println("cost time is " + (l1 - l) + "ms");
    }

    @Test
    public void testTrans() {
        IssueTransitionRequest issueTransitionRequest = new IssueTransitionRequest();
        issueTransitionRequest.setIssueUniqId("24049l12a30fafl1455163347l410410047l1482734189l1692352049");
        issueTransitionRequest.setTransition("wontfix");
        issueTransitionRequest.setGitProjectId(24049);
        issueTransitionRequest.setGitBranch("master");
        SonarIssueVo sonarIssueVo = platformIssueService.transition(issueTransitionRequest, "lixiaoxin");
        System.out.println(JSONUtils.serialize(sonarIssueVo));
    }

    @Test
    public void testScannerList() {
        ScannerListRequestVo requestVo = ScannerListRequestVo.builder().language("java").build();
        ScannerListResponseVo scannerListResponseVo = platformCommonService.scannerList(requestVo);
        System.out.println(JSONUtils.serialize(scannerListResponseVo));
    }

    @Test
    public void testRuleDetail() {
        RuleDetailRequestVo requestVo = RuleDetailRequestVo.builder()
                .ruleKey("accessor-pairs")
                .build();
        RuleDetailResponseVo detail = platformRuleService.detail(requestVo);
        System.out.println(JSONUtils.serialize(detail));
    }

    @Test
    public void taskInit() {
        platformScanTriggerService.triggerInit();
    }

    @Test
    public void taskScan() {
        platformScanTriggerService.triggerScan(List.of(PlatformScannerEnum.MAVEN_SCANNER_NEW));
    }


    @Test
    public void testLanguageSearch() {
        LanguageListRequestVo build = LanguageListRequestVo.builder().build();
        LanguageListResponseVo languageListResponseVo = platformCommonService.languageList(build);
        System.out.println(JSONUtils.serialize(languageListResponseVo));
    }

    @Test
    public void testLabelSearch() {
        LabelListRequestVo requestVo = LabelListRequestVo.builder().build();
        LabelListResponseVo labelListResponseVo = platformCommonService.labelList(requestVo);
        System.out.println(JSONUtils.serialize(labelListResponseVo));
    }

    @Test
    public void testRepoStar() {
        RepoStarRequest repoStarRequest = new RepoStarRequest();
        repoStarRequest.setGitProjectId(42847);
        repoStarRequest.setBranch("master");
        repoStarRequest.setActionType(RepoStarActionType.STAR.getType());
        platformRepoService.star(repoStarRequest, "lixiaoxin");
    }

    @Test
    public void testProjectUpdateProfile() {
        RepoProfileUpdateRequest repoProfileUpdateRequest = new RepoProfileUpdateRequest();
        repoProfileUpdateRequest.setProfileName("lxx-test-02");
        repoProfileUpdateRequest.setGitProjectId(14699);
        repoProfileUpdateRequest.setBranch("master");
        platformRepoService.updateProfile(repoProfileUpdateRequest, "lixiaoxin");
    }

    @Test
    public void testRepoInfo() {
        RepoInfoResponse info = platformRepoService.info(23221, "master");
        System.out.println(JSONUtils.serialize(info));
    }

    @Test
    public void testGetByProfileName() {
        ProfileGetByNameRequestVo requestVo = ProfileGetByNameRequestVo.builder().profileName("lxx-test-01").build();
        ProfileGetByNameResponseVo getByNameResponseVo = platformProfileService.getByProfileName(requestVo);
        System.out.println(JSONUtils.serialize(getByNameResponseVo));
    }

    @Test
    public void testDeleteProfile() {
        ProfileDeleteRequestVo deleteRequestVo = ProfileDeleteRequestVo.builder()
                .profileName("jmm-test-02")
                .build();
        platformProfileService.delete(deleteRequestVo, "lixiaoxin");
    }

    @Test
    public void testGetFile() {
        FileDetailRequest detailRequest = FileDetailRequest.builder()
                .gitProjectId(69525)
                .filePath("src/Util/api.ts")
                .commitId("1d8e950d047dfe610ccc076b4a2b10f0d5bcabaa")
                .kspBuildId(6485779L)
                .build();
        FileDetailResponse fileDetailResponse =
                platformFileService.fileDetail(detailRequest, "lixiaoxin");
        System.out.println(JSONUtils.serialize(fileDetailResponse));
    }

    @Test
    public void testGetFileSegments() {
        FileSegmentsRequest request = new FileSegmentsRequest();
        request.setGitProjectId(1400);
        request.setCommitId("47978bbb6e65455f781f87ee25ff5152ba881d47");
        request.setFilePath("ks-serveree-themis-api/src/main/java/com/kuaishou/serveree/themis/api/Application.java");
        request.setStartLine(1);
        request.setEndLine(5);
        FileSegmentsResponse response = platformFileService.fileSegments(request);
        System.out.println(JSONUtils.serialize(response));
    }

    @Test
    public void testScanHistory() {
        RepoScanHistoryRequest request = new RepoScanHistoryRequest();
        request.setGitProjectId(44267);
        request.setBranch("master");
        RepoScanHistoryResponse scanHistory = platformRepoService.scanHistory(request, "lixiaoxin");
        System.out.println(scanHistory);
    }

    @Test
    public void updateBatch() {
        IssueSummary byId = issueSummaryService.getById(172958L);
        byId.setSonarIssueKey("lxxxx");
        issueSummaryService.updateBatchByProjectBranchUniqId(Lists.newArrayList(byId));
    }

    @Test
    public void testGroup() {
        issueSummaryGroupService.weeklyGroup();
    }

    @Test
    public void testUtId() {
        List<Integer> integers = NEED_UT_GIT_PROJECT_IDS.get();
        System.out.println(integers);
    }

    @Test
    public void testSendNotice() {
        CheckBase checkBase = checkBaseService.getById(470);
        checkNoticeActionService.sendNotice(checkBase);
    }

    @Test
    public void testNoticeBuchang() {
        checkNoticeActionService.compensatePushNotice();
    }

    @Test
    public void testPlatformSys() {
        platformRuleService.sysSonarSeverity();
    }

    @Test
    public void testGetValue() {
        String s = az2ProjectIdBranchMap.get().get("1");
        System.out.println(s);
        System.out.println(StringUtils.isEmpty(s));
    }

    @Test
    public void testMavenScannerNewOfflineHook() {
        String hook = "{\n"
                + "    \"serverUrl\": \"http://themis-sonar.test.gifshow.com\",\n"
                + "    \"taskId\": \"AY50qvu_QhQx1sBvizGn\",\n"
                + "    \"status\": \"SUCCESS\",\n"
                + "    \"analysedAt\": \"2024-03-25T16:06:08+0800\",\n"
                + "    \"revision\": \"845d487741e65e60bc965035adf2fe119cdc15fd\",\n"
                + "    \"changedAt\": \"2024-03-25T16:06:08+0800\",\n"
                + "    \"project\": {\n"
                + "        \"key\": \"measure:11935\",\n"
                + "        \"name\": \"measure:is-bpm-backend\",\n"
                + "        \"url\": \"http://themis-sonar.test.gifshow.com/dashboard?id=measure%3A11935\"\n"
                + "    },\n"
                + "    \"branch\": {\n"
                + "        \"name\": \"master\",\n"
                + "        \"type\": \"LONG\",\n"
                + "        \"isMain\": true,\n"
                + "        \"url\": \"http://themis-sonar.test.gifshow.com/dashboard?id=measure%3A11935\"\n"
                + "    },\n"
                + "    \"qualityGate\": {\n"
                + "        \"name\": \"Sonar way\",\n"
                + "        \"status\": \"ERROR\",\n"
                + "        \"conditions\": [\n"
                + "            {\n"
                + "                \"metric\": \"new_reliability_rating\",\n"
                + "                \"operator\": \"GREATER_THAN\",\n"
                + "                \"value\": \"3\",\n"
                + "                \"status\": \"ERROR\",\n"
                + "                \"errorThreshold\": \"1\"\n"
                + "            },\n"
                + "            {\n"
                + "                \"metric\": \"new_security_rating\",\n"
                + "                \"operator\": \"GREATER_THAN\",\n"
                + "                \"value\": \"1\",\n"
                + "                \"status\": \"OK\",\n"
                + "                \"errorThreshold\": \"1\"\n"
                + "            },\n"
                + "            {\n"
                + "                \"metric\": \"new_maintainability_rating\",\n"
                + "                \"operator\": \"GREATER_THAN\",\n"
                + "                \"value\": \"1\",\n"
                + "                \"status\": \"OK\",\n"
                + "                \"errorThreshold\": \"1\"\n"
                + "            },\n"
                + "            {\n"
                + "                \"metric\": \"new_coverage\",\n"
                + "                \"operator\": \"LESS_THAN\",\n"
                + "                \"value\": \"0.0\",\n"
                + "                \"status\": \"ERROR\",\n"
                + "                \"errorThreshold\": \"80\"\n"
                + "            },\n"
                + "            {\n"
                + "                \"metric\": \"new_duplicated_lines_density\",\n"
                + "                \"operator\": \"GREATER_THAN\",\n"
                + "                \"value\": \"0.0\",\n"
                + "                \"status\": \"OK\",\n"
                + "                \"errorThreshold\": \"3\"\n"
                + "            }\n"
                + "        ]\n"
                + "    },\n"
                + "    \"properties\": {\n"
                + "        \"sonar.analysis.checkBaseId\": \"110\",\n"
                + "        \"sonar.analysis.sendKimNotice\": \"false\",\n"
                + "        \"sonar.analysis.projectId\": \"11935\",\n"
                + "        \"sonar.analysis.repoUrl\": \"*************************:ks-ep/pa-be/is-bpm-backend.git\",\n"
                + "        \"sonar.analysis.testEnv\": \"true\",\n"
                + "        \"sonar.analysis.mrId\": \"\",\n"
                + "        \"sonar.analysis.kspPipelineId\": \"404837\",\n"
                + "        \"sonar.analysis.executionId\": \"110\",\n"
                + "        \"sonar.analysis.buildLanguage\": \"java\",\n"
                + "        \"sonar.analysis.baseUrl\": \"https://kdev-prt.corp.kuaishou.com\",\n"
                + "        \"sonar.analysis.scannerType\": \"1\",\n"
                + "        \"sonar.analysis.platformOfflineCheck\": \"true\",\n"
                + "        \"sonar.analysis.commitId\": \"\",\n"
                + "        \"sonar.analysis.stuckPoint\": \"true\",\n"
                + "        \"sonar.analysis.incrementMode\": \"false\",\n"
                + "        \"sonar.analysis.localBuildId\": \"0\",\n"
                + "        \"sonar.analysis.branch\": \"master\",\n"
                + "        \"sonar.analysis.referType\": \"1\",\n"
                + "        \"sonar.analysis.buildLanguageVersion\": \"8\",\n"
                + "        \"sonar.analysis.kspBuildId\": \"35233499\",\n"
                + "        \"sonar.analysis.buildUserName\": \"kdev\",\n"
                + "        \"sonar.analysis.modules\": \"allModules\"\n"
                + "    }\n"
                + "}";
        HookPayload deserialize = JSONUtils.deserialize(hook, HookPayload.class);
        // fileMeasureService.deleteByCheckRepoIdAndBranchIdAndCheckBaseId(4266L, 4271, 631);
        mavenScannerNewOfflineCheckHook.handle(deserialize);
    }

    @Test
    public void testIssueTransitionByBpm() {
        MrStuckIssueTransitionRequest transitionRequest = new MrStuckIssueTransitionRequest();
        transitionRequest.setGitProjectId(1400L);
        transitionRequest.setMrId(4L);
        transitionRequest.setCommitId("1d50486e23648e1e87e2b6bf69c7bd818fa897b0");
        transitionRequest.setIssueId(575648L);
        transitionRequest.setTransition("falsepositive");

        IssueTransitionBpmRequest request = new IssueTransitionBpmRequest();
        request.setReason("测试一下");
        request.setOperation("误判");
        request.setIssueUrl("https://kdev.corp.kuaishou.com");

        platformIssueService.issueTransitionByBpm(request, "wangwei45");
    }
}
