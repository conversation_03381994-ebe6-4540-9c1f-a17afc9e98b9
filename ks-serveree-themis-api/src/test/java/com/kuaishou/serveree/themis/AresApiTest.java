package com.kuaishou.serveree.themis;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.client.ares.AresApi;
import com.kuaishou.serveree.themis.component.common.entity.KsUserInfo;
import com.kuaishou.serveree.themis.component.entity.ares.AresMessageEntity;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

/**
 * <AUTHOR>
 * @since 2022/6/7 10:57 AM
 */
public class AresApiTest extends SpringBaseTest {

    @Autowired
    private AresApi aresApi;

    @Test
    public void testBatchGetUsers() {
        List<String> userNames = Lists.newArrayList("lixiaoxin", "wangchangjin");
        List<KsUserInfo> ksUserInfos = aresApi.batchGetUserInfo(userNames);
        System.out.println(JSONUtils.serialize(ksUserInfos));
    }

    @Test
    public void testSendMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("## 代码扫描平台通知\n")
                .append("wangwei45您好，您创建的规则集【aaa】所继承的规则集【bbb】中，发生了如下变更：\n")
                .append("- 新增了规则【abc】，严重等级为【严重】\n")
                .append("- 新增了规则【def】，严重等级为【阻断】\n")
                .append("- 删除了规则【ggg】，严重等级为【阻断】\n")
                .append("- 修改了规则【ddd】，严重等级由【主要】变更为【阻断】\n")
                .append("操作人，aaa");
        AresMessageEntity messageEntity = AresMessageEntity.builder()
                .text(sb.toString())
                .templateId(0)
                .msgTypes(Lists.newArrayList(7))
                .userNames(Lists.newArrayList("wangwei45"))
                .build();
        String s = aresApi.sendKimNotice(messageEntity);
        System.out.println(s);
    }

}

