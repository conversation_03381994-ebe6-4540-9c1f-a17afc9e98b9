package com.kuaishou.serveree.themis;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.component.common.entity.CheckFile;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileDuplication;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileDuplicationIndex;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileFunction;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileMeasures;
import com.kuaishou.serveree.themis.component.service.id.tables.TablesIdGeneratorManager;

/**
 * <AUTHOR>
 * @since 2022/7/14 10:42 AM
 */
public class SeqIdTest extends SpringBaseTest {

    @Autowired
    private TablesIdGeneratorManager tablesIdGeneratorManager;

    @Test
    public void testCheckFileIdSequence() {
        for (int i = 0; i < 100; i++) {
            Long tableId = tablesIdGeneratorManager.generateDistributionId(CheckFile.class);
            System.out.println("==" + tableId);
        }
    }

    @Test
    public void testCheckFileMeasuresIdSequence() {
        for (int i = 0; i < 100; i++) {
            Long tableId = tablesIdGeneratorManager.generateDistributionId(CheckFileMeasures.class);
            System.out.println("==" + tableId);
        }
    }

    @Test
    public void testCheckFileDuplicationIdSequence() {
        for (int i = 0; i < 100; i++) {
            Long tableId = tablesIdGeneratorManager.generateDistributionId(CheckFileDuplication.class);
            System.out.println("==" + tableId);
        }
    }

    @Test
    public void testCheckFileDuplicationIndexIdSequence() {
        for (int i = 0; i < 100; i++) {
            Long tableId = tablesIdGeneratorManager.generateDistributionId(CheckFileDuplicationIndex.class);
            System.out.println("==" + tableId);
        }
    }

    @Test
    public void testCheckFileFunctionIdSequence() {
        for (int i = 0; i < 100; i++) {
            Long tableId = tablesIdGeneratorManager.generateDistributionId(CheckFileFunction.class);
            System.out.println("==" + tableId);
        }
    }
}
