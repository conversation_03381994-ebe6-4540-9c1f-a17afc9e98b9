package com.kuaishou.serveree.themis.api.controller;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.SpringBaseTest;
import com.kuaishou.serveree.themis.component.client.sonar.api.CorpSonarApi;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepo;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.MeasureComponentTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.gitMetricRequest.IssuesRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.gitMetricRequest.MeasureRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.req.gitMetricRequest.MeasureTreeRequest;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.IssuesResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentResp;
import com.kuaishou.serveree.themis.component.entity.sonar.resp.MeasureComponentTreeResp;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.sonar.SonarService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-05-09
 */
public class SonarControllerTest extends SpringBaseTest {

    @Autowired
    SonarController sonarController;

    @Autowired
    SonarService sonarService;

    @Autowired
    CorpSonarApi corpSonarApi;

    @Autowired
    CheckRepoService checkRepoService;


    @Test
    public void sonarMeasures() {
        MeasureComponentRequest request = new MeasureComponentRequest();
        request.setComponent("kuaishou:kuaishou-live-parent");
        request.setMetricKeys("functions,complexity");
        request.setTarget("MetricSonarApi");
        MeasureComponentResp measureComponentResp=sonarController.sonarMeasures(request);
        System.out.print(measureComponentResp.toString());
    }

    @Test
    public void cyclomaticComplexity() {
        MeasureComponentTreeRequest request = new MeasureComponentTreeRequest();
        String target = "CorpSonarApi";
        request.setMetricKeys("functions,complexity");
        request.setPageSize(500);
        request.setComponent("serveree:ks-serveree-cr");
//        request.setComponent("kuaishou:ks-serveree-themis");
        request.setPage(1);
        request.setTarget(target);

        MeasureComponentTreeResp resp = sonarController.sonarMeasureTree(request);
        System.out.print(resp.toString());
    }

    @Test
    public void metricSonarMeasureTree() throws Exception {
        MeasureTreeRequest req = new MeasureTreeRequest();
        req.setGitProjectId(3988);
        req.setMetricKeys("functions");
        req.setPage(1);
        req.setPageSize(500);
        MeasureComponentTreeResp  resp = sonarController.metricSonarMeasureTree(req);
        System.out.println(resp.toString());
    }

    @Test
    public void metricSonarMeasures(){
        MeasureRequest req = new MeasureRequest();
        req.setMetricKeys("sqale_index");
        req.setGitProjectId(3988);

        MeasureComponentResp resp = sonarController.metricSonarMeasures(req);
        System.out.println(resp.toString());
    }

    @Test
    public void measureComponent() {
        MeasureComponentRequest request=new MeasureComponentRequest();
        request.setComponent("kuaishou:oversea-id-seq");
        request.setMetricKeys("tests,ncloc,bugs,coverage,test_errors,test_failures,test_success_density,lines_to_cover,uncovered_lines,new_lines_to_cover,new_uncovered_lines,new_coverage,new_line_coverage");
        request.setServerUrl("https://sonar.corp.kuaishou.com");
        MeasureComponentResp resp = sonarService.measureComponent(request);
        ThemisResponse<MeasureComponentResp> themisResponse = ThemisResponse.success(resp);
        System.out.println(themisResponse.toString());
    }

    @Test
    public void issueSearch(){
        IssuesRequest request = new IssuesRequest();
        request.setGitProjectId(3988);
        request.setPage(1);
        request.setPageSize(500);
        request.setTypes("BUG");
        request.setSeverities("MAJOR");
        IssuesResp resp = sonarController.metricIssueSearch(request);
        System.out.println(JSONUtils.serialize(resp));
    }
    @Test
    public void test1(){
        CheckRepo repo = checkRepoService.getCheckRepoByProjectId(3988);
        if (repo==null) {
            throw new RuntimeException("gitProjectId not found");
        }
        if (repo.getSonarKey().length()==0) {
            throw new RuntimeException("sonarProjectKey cannot be found by gitProjectId");
        }
        System.out.println(repo.toString());
    }

}