package com.kuaishou.serveree.themis.ww;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.Filter;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.ApplicationContext;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.SpringBaseTest;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfileRuleRelation;
import com.kuaishou.serveree.themis.component.common.entity.CheckRule;
import com.kuaishou.serveree.themis.component.common.entity.IssueSummary;
import com.kuaishou.serveree.themis.component.common.entity.MrCheckPointResult;
import com.kuaishou.serveree.themis.component.common.mappers.CheckProfileMapper;
import com.kuaishou.serveree.themis.component.constant.kdev.MrCheckpointEnum;
import com.kuaishou.serveree.themis.component.constant.platform.ScanModeEnum;
import com.kuaishou.serveree.themis.component.constant.process.ProcessScannerType;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueStatus;
import com.kuaishou.serveree.themis.component.entity.platform.CheckProfileSearchCondition;
import com.kuaishou.serveree.themis.component.entity.platform.IssueSummaryListCondition;
import com.kuaishou.serveree.themis.component.service.CheckProfileRuleRelationService;
import com.kuaishou.serveree.themis.component.service.CheckRuleService;
import com.kuaishou.serveree.themis.component.service.IssueSummaryService;
import com.kuaishou.serveree.themis.component.service.LocalService;
import com.kuaishou.serveree.themis.component.service.MrCheckPointResultService;
import com.kuaishou.serveree.themis.component.service.PCheckBaseService;
import com.kuaishou.serveree.themis.component.service.kdev.MrCheckpointExecutor;
import com.kuaishou.serveree.themis.component.service.kdev.MrStuckPointService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRuleService;
import com.kuaishou.serveree.themis.component.service.plugin.SonarPluginService;
import com.kuaishou.serveree.themis.component.utils.CommonUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.RuleDetailRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.kdev.KdevPipelineCallbackRequest.KdevPipelineBuildInfo;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckPointSponsorRequest;
import com.kuaishou.serveree.themis.component.vo.request.kdev.MrStuckPointSponsorRequest.MrCheckpoint;

import cn.hutool.core.lang.Assert;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-07-14
 */
public class WwTest extends SpringBaseTest {

    @Autowired
    CheckRuleService checkRuleService;

    @Autowired
    PlatformRuleService platformRuleService;

    @Autowired
    CheckProfileRuleRelationService checkProfileRuleRelationService;

    @Resource
    private PCheckBaseService pCheckBaseService;

    @Test
    public void testAddCheckRuleZhDesc() throws IOException {
        File[] files = new File("/Users/<USER>/Workspace/rule_trans").listFiles();
        for (File file : files) {
            String fileName = file.getName();
            byte[] bytes = Files.readAllBytes(Path.of(file.getPath()));
            String content = new String(bytes);
            // BufferedReader reader = new BufferedReader(new FileReader(file));
            // List<String> stringList = reader.lines().collect(Collectors.toList());
            // String content = stringList.get(0);
            // byte[] bytes = Files.readAllBytes(Paths.get(fileName));
            // String content = new String(bytes, StandardCharsets.UTF_8);
            String[] split = content.split("@@");
            String nameZh = split[0], descZh = split[1];
            // System.out.println(nameZh + "-->" + descZh);
            checkRuleService.update(null,
                    Wrappers.<CheckRule> lambdaUpdate()
                            .set(CheckRule::getHtmlDescZh, descZh)
                            .set(CheckRule::getNameZh, nameZh)
                            .eq(CheckRule::getLanguage, "java")
                            .eq(CheckRule::getRuleKey, fileName));
        }
    }

    @Test
    public void addTranslatedRuleToProfile() {
        String userName = "wangwei45";
        String profile = "4ffcbec6e96f40b08ecce2dd7066d0d3"; // 王伟测试翻译规则集
        // 找出有中文翻译内容的java规则
        List<CheckRule> checkRuleList =
                checkRuleService.list(Wrappers.<CheckRule> lambdaQuery().eq(CheckRule::getLanguage, "java"));
        List<String> ruleKeyList = checkRuleList.stream().filter(cr -> StringUtils.isNotBlank(cr.getHtmlDescZh()))
                .map(CheckRule::getRuleKey).collect(Collectors.toList());
        // todo 下次往这个表里插入数据时，先取出来这里面有哪些 ruleKey，已经翻译过的
        // todo 然后清除数据
        // todo 从待插入的规则列表中排除掉这些已经翻译过的，只保留本次新加的需要翻译的
        // todo 保存，这个规则集里面始终只需要 本次翻译过的 进行 review
        // 插入规则集
        LocalDateTime now = LocalDateTime.now();
        List<CheckProfileRuleRelation> list = Lists.newArrayList();
        for (String ruleKey : ruleKeyList) {
            CheckProfileRuleRelation ruleRelation = new CheckProfileRuleRelation();
            ruleRelation.setRuleKey(ruleKey);
            ruleRelation.setDeleted(false);
            ruleRelation.setRuleSeverity("COMMON");
            ruleRelation.setGmtModified(now);
            ruleRelation.setGmtCreate(now);
            ruleRelation.setProfileName(profile);
            ruleRelation.setCreator(userName);
            ruleRelation.setUpdater(userName);
            list.add(ruleRelation);
        }
        checkProfileRuleRelationService.saveBatch(list);
    }

    @Autowired
    private LocalService localService;

    @Test
    public void testSyncRuleLabel() {
        localService.sonarLabelInit("AYYRTjlBR1HZdrX-PCyU");
    }

    @Test
    public void testSyncRuleInit() {
        localService.sonarRuleInit("AYYRTjlBR1HZdrX-PCyU", "maven-scanner-new", "java");
    }

    @Test
    public void testGetRuleInfo() {
        RuleDetailRequestVo requestVo = RuleDetailRequestVo.builder().ruleKey("findbugs:RANGE_ARRAY_LENGTH").build();
        System.out.println(platformRuleService.detail(requestVo));
    }

    @Test
    public void testPCheckBaseService() {
        pCheckBaseService.initBaseByKspBuildIdAndScannerType(666666L, ProcessScannerType.JAVA_MAVEN_SCANNER);
    }

    @Resource
    private ApplicationContext context;

    @Test
    public void testFilter() {
        Map<String, Filter> beansOfType = context.getBeansOfType(Filter.class);
        // 获取所有 FilterRegistrationBean 实例
        Map<String, FilterRegistrationBean> filterRegistrations = context.getBeansOfType(FilterRegistrationBean.class);

        // 遍历过滤器注册信息并输出
        for (FilterRegistrationBean registration : filterRegistrations.values()) {
            System.out.println(registration.getClass());
            System.out.println("URL Patterns: " + registration.getUrlPatterns());
            // 其他过滤器相关信息
        }
    }

    @Resource
    private MrCheckpointExecutor mrCheckpointExecutor;

    @Resource
    private MrStuckPointService mrStuckPointService;

    @Test
    public void testMrStuckCodeCheckSponsor() {
        MrStuckPointSponsorRequest request = new MrStuckPointSponsorRequest();
        request.setGitProjectId(1400);
        request.setRepoUrl("****************************************:kdev-test/ks-serveree-themis.git");
        request.setRepoLanguage("Java");
        request.setMrId(347);
        request.setSourceBranch("feature_ww_test_rule");
        request.setTargetBranch("master");

        MrCheckpoint checkpoint = new MrCheckpoint();
        checkpoint.setCheckpointName(MrCheckpointEnum.CodeScan.name());
        checkpoint.setStuckSeverity("主要");

        request.setCheckpointList(List.of(checkpoint));

        mrCheckpointExecutor.executeCheck(request);
    }

    @Resource
    private SonarPluginService sonarPluginService;

    @Test
    public void testGetMrChangedFiles() {
        System.out.println(sonarPluginService.getCachedMrDiffList(24049, 347, "sss"));
    }


    @Test
    public void testUpdateExecuteRecord() {
        String json =
                "{\"id\":6994543,\"pipelineId\":10298865,\"triggerType\":8,\"triggerTypeDesc\":\"接口触发\",\"kspBuildId\":29385824,\"status\":4,\"statusDesc\":\"失败\",\"failReason\":\"\"}";
        KdevPipelineBuildInfo buildInfo = JSONUtils.deserialize(json, KdevPipelineBuildInfo.class);
        mrStuckPointService.finishKdevPipelineExecute(buildInfo);
    }

    @Resource
    private CheckProfileMapper checkProfileMapper;

    @Test
    public void test() {
        CheckProfileSearchCondition searchCondition = CheckProfileSearchCondition.builder()
                .language("java")
                .scanner("maven-scanner-new")
                .maxLayer(1)
                .build();
        IPage<String> page = checkProfileMapper.pageMostUsedProfileNames(new Page<>(2, 5), searchCondition);
        Assert.isTrue(page.getTotal() == 33);
        Assert.isTrue(page.getSize() == 5);
        Assert.isTrue(page.getCurrent() == 2);
        Assert.isTrue(page.getRecords().size() == 5);
        Assert.isTrue(page.getRecords().get(0).equals("537ac261497c4359af1e3757a2565ae6"));
        Assert.isTrue(page.getRecords().get(4).equals("jmm-test-04"));
    }

    @Resource
    private MrCheckPointResultService mrCheckPointResultService;

    @Test
    public void tttt() {
        System.out.println(mrCheckPointResultService.list(Wrappers.<MrCheckPointResult> lambdaQuery()
                .eq(MrCheckPointResult::getGitProjectId, 0L)
                .eq(MrCheckPointResult::getMrId, 0L)
                .eq(MrCheckPointResult::getCommitId, "commitId")
                .eq(MrCheckPointResult::getCheckpointName, "checkpointName")
                .orderByDesc(MrCheckPointResult::getId)
                .last("limit 1")
        ));
    }

    @Test
    public void testFindPom() {
        List<String> list1 = List.of("moduleA/pom.xml", "moduleB/pom.xml", "moduleB/moduleC/pom.xml", "pom.xml");
        List<String> list2 = List.of("moduleA/pom.xml", "moduleB/pom.xml", "moduleB/moduleC/pom.xml");
        List<String> list3 = List.of("moduleA/pom.xml", "moduleB/moduleC/pom.xml");
        System.out.println(CommonUtils.findAllParentPoms(list1));
        System.out.println(CommonUtils.findAllParentPoms(list2));
        System.out.println(CommonUtils.findAllParentPoms(list3));
    }

    @Autowired
    private IssueSummaryService issueSummaryService;

    @Test
    public void testPageLoad() {
        List<IssueSummary> dbCheckIssues = issueSummaryService.pageByCondition(
                IssueSummaryListCondition.builder()
                        .gitProjectId(625)
                        .branch("feature_test_skyeye_mr")
                        .scanMode(ScanModeEnum.OFFLINE.getCode())
                        .statuses(List.of(CheckIssueStatus.OPEN.getStatus(), CheckIssueStatus.RE_OPEN.getStatus(),
                                CheckIssueStatus.CLOSED.getStatus(), CheckIssueStatus.TO_REVIEW.getStatus()))
                        // .minGmtModified(checkBase.getGmtCreate().minusMonths(3))
                        .page(1)
                        .pageSize(Integer.MAX_VALUE >> 1)
                        .build()
        ).getRecords();
        assert dbCheckIssues.size() == 5344;
    }
}
