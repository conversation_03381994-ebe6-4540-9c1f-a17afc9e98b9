package com.kuaishou.serveree.themis;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.component.common.mappers.ThemisAnalyzeIssueMapper;

/**
 * <AUTHOR>
 * @since 2021/2/2 2:12 下午
 */
public class MapperTest extends SpringBaseTest {

    @Autowired
    ThemisAnalyzeIssueMapper analyzeIssueMapper;

    @Test
    public void testSql1() {
        Integer integer = analyzeIssueMapper.issueGroupByCount(1L, 2);
        System.out.println(integer);
    }

    @Test
    public void testSql2() {
        List<String> strings = analyzeIssueMapper.issueGroupByFilePathList(1L, 2, 0L, 100L);
        System.out.println(strings);
    }

}
