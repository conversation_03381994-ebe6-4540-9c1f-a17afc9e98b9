package com.kuaishou.serveree.themis;

import java.nio.charset.Charset;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.service.check.custom.CustomRuleCheckChain;
import com.kuaishou.serveree.themis.component.service.check.custom.CustomRuleContext;
import com.kuaishou.serveree.themis.component.service.check.custom.entity.CustomRulePair;
import com.kuaishou.serveree.themis.component.service.check.custom.entity.DiffInfo;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.DiffContent;

import cn.hutool.http.HttpUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2021/8/2 10:51 上午
 */
public class TestCustomCheck extends SpringBaseTest {

    @Autowired
    private CustomRuleCheckChain customRuleCheckChain;

    @Test
    public void testDiffSqlBuilder() {
        CustomRulePair customRulePair = new CustomRulePair();
        customRulePair.setRuleId("Z001");
        List<CustomRulePair> customRulePairs = Lists.newArrayList();
        customRulePairs.add(customRulePair);
        List<DiffContent> diffContentList = Lists.newArrayList();
        DiffContent diffContent = DiffContent.builder()
                .lineContent("SQLBuilder sql = new SQLBuilder")
                .build();
        DiffContent diffContent1 = DiffContent.builder()
                .lineContent("// SQLBuilder sql = new SQLBuilder")
                .build();
        diffContentList.add(diffContent);
        diffContentList.add(diffContent1);
        DiffInfo diffInfo = DiffInfo.builder()
                .filePath("a.java")
                .diffContentList(diffContentList)
                .build();
        List<DiffInfo> diffInfos = Lists.newArrayList();
        diffInfos.add(diffInfo);
        CustomRuleContext customRuleContext = CustomRuleContext.builder()
                .customRulePairs(customRulePairs)
                .diffInfos(diffInfos)
                .illegalInfos(Lists.newArrayList())
                .build();
        customRuleCheckChain.execute(customRuleContext);
        System.out.println(JSONUtils.serialize(customRuleContext));
    }

    @Test
    public void testDownload() {
        String s = HttpUtil.downloadString(
                "https://halo.corp.kuaishou.com/api/cloud-storage/v1/public-objects/maven-bucket/a.json",
                Charset.defaultCharset());
        List<A> as = JSONUtils.deserializeList(s, A.class);
        System.out.println(s);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class A {
        private String a;
        private String b;
    }

}
