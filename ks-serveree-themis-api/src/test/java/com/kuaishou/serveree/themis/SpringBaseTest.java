package com.kuaishou.serveree.themis;

import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.SpringBootTest.WebEnvironment;
import org.springframework.test.context.junit4.SpringRunner;

import com.kuaishou.serveree.themis.api.Application;


/**
 * SpringBaseTest
 * <p>
 * Write the code. Change the world.
 *
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class, webEnvironment = WebEnvironment.RANDOM_PORT)
public abstract class SpringBaseTest {

    protected static final Logger logger = LoggerFactory.getLogger(SpringBaseTest.class);

//    @Before
//    public void beforeAddToken() {
//        TaskRequestToken taskRequestToken = new TaskRequestToken();
//        taskRequestToken.setSource("admin-check");
//        ThemisTaskTokenUtil.set(taskRequestToken);
//    }

}