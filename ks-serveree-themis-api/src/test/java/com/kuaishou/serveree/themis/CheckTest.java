package com.kuaishou.serveree.themis;

import static com.kuaishou.serveree.themis.component.service.impl.QualityAnalyzeServiceImpl.DEFAULT_GIT_GROUP_CONFIG;
import static com.kuaishou.serveree.themis.component.service.impl.QualityAnalyzeServiceImpl.DEFAULT_NEW_AND_OLD_TIMESTAMP_DIVIDING_LINE;
import static com.kuaishou.serveree.themis.component.service.impl.QualityAnalyzeServiceImpl.DEFAULT_NEW_CHECK_TYPE_LEVEL_MAP_CONFIG;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.api.controller.QualityAnalyzeController;
import com.kuaishou.serveree.themis.component.client.ksp.KspApi;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.common.entity.TaskRequestToken;
import com.kuaishou.serveree.themis.component.common.entity.ThemisAnalyze;
import com.kuaishou.serveree.themis.component.constant.quality.CheckPluginInputConstants;
import com.kuaishou.serveree.themis.component.constant.quality.JobTypeEnum;
import com.kuaishou.serveree.themis.component.constant.quality.TaskStatusEnum;
import com.kuaishou.serveree.themis.component.entity.ksp.KsPipelineJobLogRequest;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineJobLogResponse;
import com.kuaishou.serveree.themis.component.entity.quality.QualityTaskBaseDto.UserParams;
import com.kuaishou.serveree.themis.component.entity.quality.QualityTaskExecuteBaseDto;
import com.kuaishou.serveree.themis.component.service.CheckRepoLanguageService;
import com.kuaishou.serveree.themis.component.service.ProjectInfoService;
import com.kuaishou.serveree.themis.component.service.QualityAnalyzeService;
import com.kuaishou.serveree.themis.component.service.QualityCheckService;
import com.kuaishou.serveree.themis.component.service.QualityTaskDistributeService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.service.ThemisAnalyzeService;
import com.kuaishou.serveree.themis.component.service.ThemisRuleService;
import com.kuaishou.serveree.themis.component.service.check.QualityTaskKspSyncService;
import com.kuaishou.serveree.themis.component.service.check.custom.entity.CustomRulePair;
import com.kuaishou.serveree.themis.component.service.check.custom.entity.DiffInfo;
import com.kuaishou.serveree.themis.component.service.sonar.JavaMavenSonarReportService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.LocalStringUtils;
import com.kuaishou.serveree.themis.component.utils.ThemisTaskTokenUtil;
import com.kuaishou.serveree.themis.component.vo.request.CheckSponsorRequest;
import com.kuaishou.serveree.themis.component.vo.request.DiffContent;
import com.kuaishou.serveree.themis.component.vo.request.QualityAnalyzeDetailInfoRequest;
import com.kuaishou.serveree.themis.component.vo.request.QualityAnalyzeIssueListRequest;
import com.kuaishou.serveree.themis.component.vo.response.CheckInfoResponse;
import com.kuaishou.serveree.themis.component.vo.response.DefaultSettingResponse;
import com.kuaishou.serveree.themis.component.vo.response.DependencyConfigResponse;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeDetailInfoResponse;
import com.kuaishou.serveree.themis.component.vo.response.QualityAnalyzeIssueListResponse;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;

/**
 * <AUTHOR>
 * @since 2020/11/3 7:52 下午
 */
public class CheckTest extends SpringBaseTest {

    @Autowired
    private QualityCheckService qualityCheckService;

    @Autowired
    private QualityTaskDistributeService qualityTaskDistributeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskConfigService taskConfigService;

    @Autowired
    private KspApi kspApi;

    @Autowired
    private QualityTaskKspSyncService qualityTaskKspSyncService;

    @Autowired
    private ThemisRuleService themisRuleService;

    @Autowired
    private QualityAnalyzeController qualityAnalyzeController;

    @Autowired
    private QualityAnalyzeService qualityAnalyzeService;

    @Autowired
    private ProjectInfoService projectInfoService;

    @Autowired
    private JavaMavenSonarReportService javaMavenSonarReportService;

    @Autowired
    private ThemisAnalyzeService themisAnalyzeService;

    @Autowired
    private CheckRepoLanguageService checkRepoLanguageService;

    @Test
    public void test02() {
        TaskRequestToken taskRequestToken = new TaskRequestToken();
        taskRequestToken.setIsDeleted(false);
        taskRequestToken.setToken("88888888");
        taskRequestToken.setSource("serveree");
        taskRequestToken.setSourceDesc("self test");
        ThemisTaskTokenUtil.set(taskRequestToken);
        CheckSponsorRequest checkSponsorRequest = new CheckSponsorRequest();
        checkSponsorRequest.setRepoUrl("*************************:ks/infra-id-card-verify.git");
        checkSponsorRequest.setBranch("bayeasy_tax");
        checkSponsorRequest.setPriority("HIGH");
        checkSponsorRequest.setSource("test");
        checkSponsorRequest.setSourceId("10086");
        checkSponsorRequest.setCheckType("JAVA_KS_PLUGIN_CHECK");
        checkSponsorRequest.setOriginKspBuildId(1234L);
        checkSponsorRequest.setJavaVersion(11);
        checkSponsorRequest.setGitProjectId(2);
        checkSponsorRequest.setParams("{\"MVN_BUILD_ARGUMENTS\":\" -Dcicheck.skip=false -Dcicheck"
                + ".skipSpringInitCheck=true -Ddeprecated"
                + ".check.skip=false\"}");
        System.out.println(JSONUtils.serialize(checkSponsorRequest));
        //        checkSponsorRequest.setKdevJavaPreCompileCommand("KdevJavaPreCompile.sh");
        //        checkSponsorRequest.setKdevJavaPreCompileInput("{\"repoBranchs\":[{\"repo\":\"************.kuaishou"
        //                + ".com:serveree/ks-serveree-artemis.git\",\"branch\":\"feature_lxx_test\",
        //                \"ENABLE\":\"编译\"}],"
        //                + "\"useCache\":\"true\",\"KDEV_API_SERVER\":\"https://kdev.corp.kuaishou.com\","
        //                + "\"KDEV_JOB_LOG_ID\":\"779262\",
        //                \"KDEV_JOB_LOG_TOKEN\":\"9d681a6d21289b99f05d3a394c53d582\"}");
        String a = "{ \"repoUrl\": \"*************************:reco/kuaishou-reco-index-builder.git\", \"branch\": "
                + "\"feature_lqm\", \"commitId\": \"2db71190\", \"checkType\": \"JAVA_KS_PLUGIN_CHECK\", "
                + "\"originKspPipelineId\": \"536851\", \"originKspBuildId\": \"12886884\", \"originKspName\": "
                + "\"kdev-pipeline-6694658\", \"originKspStepId\": \"89114640\", \"checker\": \"liuqingmin\", "
                + "\"kdevBranchType\": \"11\", \"javaVersion\": \"8\", \"mrId\":\"\", \"localBuildId\": \"\", "
                + "\"params\": \"{ \\\"MVN_BUILD_ARGUMENTS\\\": \\\"-Dmaven.test.skip=true -DrepoUrl=************"
                + ".kuaishou.com:reco/kuaishou-reco-index-builder.git -Dbranch=feature_lqm -DcommitId=2db71190 "
                + "-DpipelineId=536851 -DbuildId=12886884 -DprojectId=17456 "
                + "-DbuildModules=kuaishou-reco-index-builder-app -DbuildUserName=liuqingmin -T 1C\\\", "
                + "\\\"BETA_PACKAGE_VERSIONS\\\": \\\"kwaishop-short-video-service-client:1.0"
                + ".32-feature_plc_show_opt-BETA-SNAPSHOT\\\", \\\"BUILD_MODULES\\\": "
                + "\\\"kuaishou-reco-index-builder-app\\\" }\", \"gitProjectId\": \"17456\" }";
        //        CheckSponsorRequest deserialize = JSONUtils.deserialize(a, CheckSponsorRequest.class);
        Long taskId = qualityCheckService.sponsorCheck(checkSponsorRequest);
        System.out.println("===taskId is " + taskId);
    }

    @Test
    public void test03() {
        Collection<QualityTaskExecuteBaseDto> qualityTaskList =
                qualityTaskDistributeService.getQualityTaskList(JobTypeEnum.LOCAL, TaskStatusEnum.WAITING);
        System.out.println(JSONUtils.serialize(qualityTaskList));
    }

    @Test
    public void test05() {
        UserParams userParams = new UserParams();
        userParams.setMvnOpts("-Dcheckstyle.skip=true");
        System.out.println(JSONUtils.serialize(userParams));
    }

    @Test
    public void test06() {
        taskService.updateTaskStatusByTaskId(2L, TaskStatusEnum.WAITING);
    }

    @Test
    public void test07() {
        List<LocalDateTime> localDateTimes = new ArrayList<>();
        List<Task> list = taskService.list();
        list.forEach(l -> {
            localDateTimes.add(l.getExecuteStart());
            localDateTimes.add(l.getExecuteEnd());
        });
        localDateTimes.sort(LocalDateTime::compareTo);
        System.out.println(JSONUtils.serialize(localDateTimes));
    }

    @Test
    public void test09() {
        Task task = Task.builder().remark("1008611").id(8L).build();
        taskService.updateById(task);
    }

    @Test
    public void test10() {
        Task task = taskService.getById(45);
        TaskConfig taskConfig = taskConfigService.getTaskConfigByTaskId(45L);
        qualityCheckService.sponsorKspPipeline(task, taskConfig);
    }

    @Test
    public void test11() {
        Task task = taskService.getById(4620L);
        TaskConfig taskConfig = taskConfigService.getTaskConfigByTaskId(4620L);
        qualityCheckService.syncKspPipelineResult(task, taskConfig);
    }

    @Test
    public void test12() {
        TaskConfig taskConfig = taskConfigService.getTaskConfigByTaskId(1L);
        KspPipelineJobLogResponse stepLog = kspApi.getStepLog(
                KsPipelineJobLogRequest.builder()
                        .kspPipelineId(taskConfig.getSponsorKspPipelineId())
                        .full(true)
                        .buildId(taskConfig.getSponsorKspBuildId())
                        .stepId(taskConfig.getSponsorKspStepId())
                        .build()
        );
        System.out.println(stepLog.getLog());
    }

    @Test
    public void testGetV4StepLog() {
        KspPipelineJobLogResponse stepLog = kspApi.getV4StepLog(
                KsPipelineJobLogRequest.builder()
                        .kspPipelineId(15009L)
                        .full(true)
                        .buildId(16606841L)
                        .stepId(104610971L)
                        .build()
        );
        System.out.println(stepLog.getLog());
    }

    @Test
    public void testGetPartStepLog() {
        String stepLog = kspApi.getPartLog(
                KsPipelineJobLogRequest.builder()
                        .kspPipelineId(15009L)
                        .full(true)
                        .buildId(16582224L)
                        .stepId(104505278L)
                        .build()
        );
        System.out.println(stepLog);
    }


    @Test
    public void test13() throws InterruptedException {
        qualityTaskKspSyncService.kspPipelineSync();
        Thread.sleep(10000);
    }

    @Test
    public void test15() {
        List<Task> unLabelTasks = taskService.listUnLabelTasks();
        System.out.println("===== ====== =====" + JSONUtils.serialize(unLabelTasks));
    }

    @Test
    public void test16() {
        List<Task> list = taskService.list();
        System.out.println(JSONUtils.serialize(list));
    }

    @Test
    public void test17() {
        Task task = taskService.getById(27);
        TaskConfig taskConfig = taskConfigService.getTaskConfigByTaskId(27L);
        qualityCheckService.syncKspPipelineResult(task, taskConfig);
    }

    @Test
    public void test18() {
        List<Task> taskList = taskService.listUnLabelTasks();
        System.out.println(JSONUtils.serialize(taskList));
    }

    @Test
    public void test19() {
        String params = "{ \"MVN_BUILD_ARGUMENTS\": \"-Dprotocheck=true\", \"BETA_PACKAGE_VERSIONS\": "
                + "\"\", \"BUILD_MODULES\": \"\" , \"MR_ID\": \"\","
                + " \"CI_CHECK_SWITCH\": \"是\", \"CI_CHECK_LEVEL\": \"I\", "
                + "\"CHECKSTYLE_SWITCH\": \"否\", \"CHECKSTYLE_LEVEL\": "
                + "\"I\", \"KCHECK_LEVEL\": \"I\"}";
        String ruleIds = themisRuleService.getRuleIdListByInputParams(params);
        String selectedMvnArgs = themisRuleService.getMvnArgsByInputParams(params);
        Map<String, String> paramsMap = JSONUtils.deserializeMap(params, String.class, String.class);
        String mvnBuildArguments = paramsMap.get(CheckPluginInputConstants.MVN_BUILD_ARGUMENTS);
        String finalMvnArgs = mvnBuildArguments + " -DanalyzeRuleIds=" + ruleIds + selectedMvnArgs;
        paramsMap.put(CheckPluginInputConstants.MVN_BUILD_ARGUMENTS, finalMvnArgs);
        System.out.println(JSONUtils.serialize(paramsMap));
    }

    @Test
    public void test20() {
        ThemisResponse<QualityAnalyzeIssueListResponse> qualityAnalyzeIssueListResponseThemisRespone =
                qualityAnalyzeController.analyzeIssueList(null);
        System.out.println(JSONUtils.serialize(qualityAnalyzeIssueListResponseThemisRespone));
    }

    @Test
    public void test21() {
        QualityAnalyzeDetailInfoRequest request = new QualityAnalyzeDetailInfoRequest();
        request.setBuildId(9039L);
        request.setMrId("zzz10086");
        request.setCommitId("199280184184dafa");
        request.setProjectId(11111);
        ThemisResponse<QualityAnalyzeDetailInfoResponse> responseThemisResponse =
                qualityAnalyzeService.analyzeDetail(request);
        System.out.println(JSONUtils.serialize(responseThemisResponse));
    }

    @Test
    public void test22() {
        long l = System.currentTimeMillis();
        QualityAnalyzeIssueListRequest request = new QualityAnalyzeIssueListRequest();
        //        request.setBuildId(51408L);
        request.setMrId("15");
        request.setCommitId("fcc61b03a42fb3cebc384518659c75f5f64159c3");
        request.setProjectId(56);
        request.setPage(1L);
        request.setPageSize(10L);
        request.setRuleType(4);
        ThemisResponse<QualityAnalyzeIssueListResponse> responseThemisResponse =
                qualityAnalyzeService.analyzeIssueList(request);
        long l1 = System.currentTimeMillis();
        System.out.println("---------------" + (l1 - l));
        System.out.println(JSONUtils.serialize(responseThemisResponse));
    }

    @Test
    public void testKconf() {
        List<String> strings = DEFAULT_GIT_GROUP_CONFIG.get();
        System.out.println(strings);
        HashMap<Integer, String> integerStringHashMap = DEFAULT_NEW_CHECK_TYPE_LEVEL_MAP_CONFIG.get();
        System.out.println(integerStringHashMap);
    }

    @Test
    public void testTimeLine() {
        LocalDateTime oldNewDefaultDividingLineTime = LocalDateTime
                .ofEpochSecond(DEFAULT_NEW_AND_OLD_TIMESTAMP_DIVIDING_LINE.get(), 0, ZoneOffset.ofHours(8));
        System.out.println(oldNewDefaultDividingLineTime);
    }

    @Test
    public void testAnalyzeController() {
        ThemisResponse<DefaultSettingResponse> a = qualityAnalyzeController
                .getDefaultSettings("http://serveree-gitlab-ha.corp.kuaishou.com/kdev-test/java-example-test.git");
        System.out.println(JSONUtils.serialize(a));
    }

    @Test
    public void testRepoCheck() {
        CheckInfoResponse checkInfoResponse =
                qualityAnalyzeService.checkRepoInfo("*************************:ks/kuaishou-ares.git");
        System.out.println(JSONUtils.serialize(checkInfoResponse));
    }

    @Test
    public void test00() {
        Assert.assertFalse(projectInfoService.isPlatecoDevProject(""));
        Assert.assertFalse(projectInfoService.isPlatecoDevProject("*************************:serveree/ks-serveree.git"));
        Assert.assertFalse(projectInfoService.isPlatecoDevProject("*************************:plateco/ssss/ss.git"));
        Assert.assertTrue(projectInfoService.isPlatecoDevProject("*************************:plateco-dev/ssss/ss.git"));
    }

    @Test
    public void testCheck() {
        ThemisResponse<DependencyConfigResponse> dependencyConfigResponseThemisResponse =
                qualityAnalyzeService.dependencyConfig("*************************:serveree/ks-serveree-themis.git");
        System.out.println(JSONUtils.serialize(dependencyConfigResponseThemisResponse));
    }

    @Test
    public void testCheck1() {
        Task byId = taskService.getById(1);
        String kdevJavaPreCompileCommand = byId.getKdevJavaPreCompileInput();
        System.out.println(kdevJavaPreCompileCommand);
        //{repoBranchs:[{repo:*************************:serveree/ks-serveree-artemis.git,branch:feature_lxx_test}],
        //useCache:true,KDEV_API_SERVER:https://kdev.corp.kuaishou.com,KDEV_JOB_LOG_ID:819637,
        //KDEV_JOB_LOG_TOKEN:f71c4f0aefe98d015daf96e69ed60055}
        String noStr = "{repoBranchs:[{repo:*************************:serveree/ks-serveree-artemis.git,"
                + "branch:feature_lxx_test}],useCache:true,KDEV_API_SERVER:https://kdev.corp.kuaishou.com,"
                + "KDEV_JOB_LOG_ID:819637,KDEV_JOB_LOG_TOKEN:f71c4f0aefe98d015daf96e69ed60055}";

        byId.setKdevJavaPreCompileInput(JSONUtils.serialize(LocalStringUtils.parseNoQuotationJson(noStr)));
        taskService.updateById(byId);
    }

    @Test
    public void testKconf1() {
        Map<String, String> stringStringMap = JavaMavenSonarReportService.GIT_ID_REPORT_EMAILS_MAP.get();
        System.out.println("======" + stringStringMap);
        String s = stringStringMap.get("111");
        System.out.println("======" + s);
    }

    @Test
    public void testMrCheck() throws InterruptedException {
        CustomRulePair customRulePair = new CustomRulePair();
        customRulePair.setRuleId("Z001");
        List<CustomRulePair> customRulePairs = Lists.newArrayList();
        customRulePairs.add(customRulePair);
        List<DiffContent> diffContentList = Lists.newArrayList();
        DiffContent diffContent = DiffContent.builder()
                .lineContent("SQLBuilder sql = new SQLBuilder")
                .build();
        DiffContent diffContent1 = DiffContent.builder()
                .lineContent("// SQLBuilder sql = new SQLBuilder")
                .build();
        diffContentList.add(diffContent);
        diffContentList.add(diffContent1);
        DiffInfo diffInfo = DiffInfo.builder()
                .filePath("a.java")
                .diffContentList(diffContentList)
                .build();
        List<DiffInfo> diffInfos = Lists.newArrayList();
        diffInfos.add(diffInfo);
        TaskRequestToken taskRequestToken = new TaskRequestToken();
        taskRequestToken.setIsDeleted(false);
        taskRequestToken.setToken("88888888");
        taskRequestToken.setSource("serveree");
        taskRequestToken.setSourceDesc("self test");
        ThemisTaskTokenUtil.set(taskRequestToken);
        CheckSponsorRequest checkSponsorRequest = new CheckSponsorRequest();
        checkSponsorRequest.setRepoUrl("*************************:plateco-dev/kuaishou-merchant-commodity.git");
        checkSponsorRequest.setBranch("bugfix_7");
        checkSponsorRequest.setCommitId("4a300d4a2e181b8d0195494848f87420165552ff");
        checkSponsorRequest.setMrId("570");
        checkSponsorRequest.setProjectId(10946);
        checkSponsorRequest.setPriority("HIGH");
        checkSponsorRequest.setSource("test");
        checkSponsorRequest.setSourceId("10086");
        checkSponsorRequest.setPluginCheckRunType(3);
        checkSponsorRequest.setCustomRulePairs(customRulePairs);
        checkSponsorRequest.setDiffInfos(diffInfos);
        Long taskId = qualityCheckService.sponsorCheck(checkSponsorRequest);
        System.out.println("===taskId is " + taskId);
        TimeUnit.SECONDS.sleep(600);
    }

    @Test
    public void testMrCheckV() {
        ThemisAnalyze byId = themisAnalyzeService.getById(77);
        qualityCheckService.runMrCheck(byId);
    }

    @Test
    public void testComponent() {
        qualityCheckService.compensateAnalyze();
    }

    @Test
    public void testLanguages() {
        List<String> strings = checkRepoLanguageService.selectLanguage();
        System.out.println(strings);
    }

}
