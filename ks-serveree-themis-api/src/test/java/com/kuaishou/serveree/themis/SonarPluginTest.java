package com.kuaishou.serveree.themis;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.api.controller.SonarPluginController;
import com.kuaishou.serveree.themis.component.service.plugin.SonarPluginService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.IncrementChangedFilesRequest;
import com.kuaishou.serveree.themis.component.vo.response.IncrementChangedFilesResponse;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2022-08-30
 */
public class SonarPluginTest extends SpringBaseTest {

    @Autowired
    SonarPluginService sonarPluginService;

    @Autowired
    SonarPluginController sonarPluginController;

    @Test
    public void getChangedFile() {
        String reqJson = "{\n"
                + "    \"currentCommitId\": \"af41f2b0\",\n"
                + "    \"gitProjectId\": 4079,\n"
                + "    \"increment\": true,\n"
                + "    \"branch\": \"feature_20230108_version0display\",\n"
                + "    \"kspBuildId\": \"********\",\n"
                + "    \"moreProcessModulePath\": \"kspay-account-runner\",\n"
                + "    \"mrId\": 0,\n"
                + "    \"localBuildId\": 0,\n"
                + "    \"sourceCommitId\": \"\"\n"
                + "}";

        IncrementChangedFilesRequest request = JSONUtils.deserialize(reqJson, IncrementChangedFilesRequest.class);
        // IncrementChangedFilesRequest request = new IncrementChangedFilesRequest();
        // request.setGitProjectId(69525);
        // request.setLocalBuildId(1446453L);
        // request.setMrId(0);
        // request.setIncrement(true);
        IncrementChangedFilesResponse changedFiles = sonarPluginService.getChangedFiles(request);
        System.out.println(JSONUtils.serialize(changedFiles));

    }
}
