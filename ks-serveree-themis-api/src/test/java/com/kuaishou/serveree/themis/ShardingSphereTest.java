package com.kuaishou.serveree.themis;

import java.time.LocalDateTime;
import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.CheckFileMeasures;
import com.kuaishou.serveree.themis.component.service.CheckFileMeasuresService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.core.date.StopWatch;

/**
 * <AUTHOR>
 * @since 2022/7/7 7:46 PM
 */
public class ShardingSphereTest extends SpringBaseTest {

    @Autowired
    private CheckFileMeasuresService checkFileMeasuresService;

    @Test
    public void testInsert() {
        for (int i = 0; i < 10; i++) {
            CheckFileMeasures checkFileMeasures = new CheckFileMeasures();
            checkFileMeasures.setId((long) i + 1);
            checkFileMeasures.setFileId((long) i);
            checkFileMeasures.setCheckRepoId((long) i);
            checkFileMeasures.setBaseId((long) i);
            checkFileMeasures.setCheckRepoBranchId((long) i);
            checkFileMeasures.setMetricKey("aaa");
            checkFileMeasures.setMetricValue("123");
            checkFileMeasures.setGmtCreate(LocalDateTime.now());
            checkFileMeasures.setGmtModified(LocalDateTime.now());
            checkFileMeasuresService.save(checkFileMeasures);
        }
    }

    @Test
    public void testBatchInsert() {
        List<CheckFileMeasures> checkFileMeasuresList = Lists.newArrayList();
        for (int i = 0; i < 10; i++) {
            CheckFileMeasures checkFileMeasures = new CheckFileMeasures();
            checkFileMeasures.setId((long) i + 11);
            checkFileMeasures.setFileId((long) i);
            checkFileMeasures.setCheckRepoId((long) i);
            checkFileMeasures.setBaseId((long) i);
            checkFileMeasures.setCheckRepoBranchId((long) i);
            checkFileMeasures.setMetricKey("aaa");
            checkFileMeasures.setMetricValue("123");
            checkFileMeasures.setGmtCreate(LocalDateTime.now());
            checkFileMeasures.setGmtModified(LocalDateTime.now());
            checkFileMeasuresList.add(checkFileMeasures);
        }
        checkFileMeasuresService.saveBatch(checkFileMeasuresList);
    }

    @Test
    public void get() {
        StopWatch stopWatch = StopWatch.create("a");
        stopWatch.start();
        List<CheckFileMeasures> checkFileMeasures = checkFileMeasuresService.list(
                Wrappers.<CheckFileMeasures> lambdaQuery().eq(CheckFileMeasures::getCheckRepoId, 474L));
        stopWatch.stop();
        System.out.println("===" + stopWatch.getTotalTimeMillis());
        System.out.println(JSONUtils.serialize(checkFileMeasures));
    }

}
