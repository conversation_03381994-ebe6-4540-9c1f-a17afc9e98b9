package com.kuaishou.serveree.themis;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.api.controller.MonitorController;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.MonitorReportRequest;

/**
 * <AUTHOR>
 * @since 2021/7/6 4:27 下午
 */
public class MonitorTest extends SpringBaseTest {

    @Autowired
    private MonitorController monitorController;

    @Test
    public void testAdd() {
        MonitorReportRequest monitorReportRequest = new MonitorReportRequest();
        monitorReportRequest.setCostTime(437L);
        monitorReportRequest.setSource("mavenScannerNew");
        monitorReportRequest.setKspBuildId(195522L);
        monitorController.report(monitorReportRequest);
    }

    @Test
    public void testRecordInsert() {
        String s = "{\"gitProjectId\":24049,\"source\":\"sonarIdeaPlugin\",\"issues\":[{\"severity\":\"MAJOR\","
                + "\"endLine\":701,\"endOffset\":75,\"startLine\":701,\"type\":\"BUG\",\"message\":\"com.kuaishou"
                + ".serveree.themis.component.service.platform.impl.PlatformRuleServiceImpl.convert2RuleRelations"
                + "(String, Map, List) makes inefficient use of keySet iterator instead of entrySet iterator\","
                + "\"ruleKey\":\"findbugs:WMI_WRONG_MAP_ITERATOR\",\"startOffset\":0,"
                + "\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component"
                + "/service/platform/impl/PlatformRuleServiceImpl.java\"},{\"severity\":\"MAJOR\",\"endLine\":68,"
                + "\"endOffset\":96,\"startLine\":68,\"type\":\"BUG\",\"message\":\"com.kuaishou.serveree.themis"
                + ".component.service.platform.impl.PlatformSonarInteractiveServiceImpl.interactive"
                + "(PlatformSonarInteractiveContext) makes inefficient use of keySet iterator instead of entrySet "
                + "iterator\",\"ruleKey\":\"findbugs:WMI_WRONG_MAP_ITERATOR\",\"startOffset\":0,"
                + "\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component"
                + "/service/platform/impl/PlatformSonarInteractiveServiceImpl.java\"},{\"severity\":\"MAJOR\","
                + "\"endLine\":591,\"endOffset\":108,\"startLine\":591,\"type\":\"BUG\",\"message\":\"Private method "
                + "com.kuaishou.serveree.themis.component.service.platform.impl.PlatformIssueServiceImpl"
                + ".listGroupCount(Integer, String, String, String, String) is never called\","
                + "\"ruleKey\":\"findbugs:UPM_UNCALLED_PRIVATE_METHOD\",\"startOffset\":0,"
                + "\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component"
                + "/service/platform/impl/PlatformIssueServiceImpl.java\"},{\"severity\":\"MAJOR\",\"endLine\":495,"
                + "\"endOffset\":22,\"startLine\":495,\"type\":\"BUG\",\"message\":\"Remove this conditional "
                + "structure or edit its code blocks so that they're not all the same.\",\"ruleKey\":\"squid:S3923\","
                + "\"startOffset\":20,\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree"
                + "/themis/component/service/impl/IssueSummaryServiceImpl.java\"},{\"severity\":\"MAJOR\","
                + "\"endLine\":373,\"endOffset\":81,\"startLine\":373,\"type\":\"BUG\",\"message\":\"com.kuaishou"
                + ".serveree.themis.component.service.platform.impl.PlatformProfileServiceImpl.fillUpRelationList"
                + "(List, List, Map, Map, String, String, LocalDateTime) makes inefficient use of keySet iterator "
                + "instead of entrySet iterator\",\"ruleKey\":\"findbugs:WMI_WRONG_MAP_ITERATOR\",\"startOffset\":0,"
                + "\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component"
                + "/service/platform/impl/PlatformProfileServiceImpl.java\"},{\"severity\":\"MAJOR\",\"endLine\":18,"
                + "\"endOffset\":37,\"startLine\":18,\"type\":\"BUG\",\"message\":\"com.kuaishou.serveree.themis"
                + ".component.common.entity.IssueGroupPanel overrides equals in IssueSummary and may not be "
                + "symmetric\",\"ruleKey\":\"findbugs:EQ_OVERRIDING_EQUALS_NOT_SYMMETRIC\",\"startOffset\":0,"
                + "\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component"
                + "/common/entity/IssueGroupPanel.java\"},{\"severity\":\"MAJOR\",\"endLine\":10,\"endOffset\":8,"
                + "\"startLine\":10,\"type\":\"BUG\",\"message\":\"Unread field: com.kuaishou.serveree.themis"
                + ".component.vo.response.ProfileUpdateRuleResponse.updateResult\","
                + "\"ruleKey\":\"findbugs:URF_UNREAD_FIELD\",\"startOffset\":0,"
                + "\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component"
                + "/vo/response/ProfileUpdateRuleResponse.java\"},{\"severity\":\"MAJOR\",\"endLine\":432,"
                + "\"endOffset\":104,\"startLine\":432,\"type\":\"BUG\",\"message\":\"Null passed for non-null "
                + "parameter of getStuckPointSettingsJson(SonarStuckPointRequest$StuckPointSettings, "
                + "SonarStuckPointRequest$StuckPointSettings) in com.kuaishou.serveree.themis.component.service.sonar"
                + ".impl.SonarServiceImpl.getCheckResultAndSavePluginSettings(SonarStuckPointRequest, "
                + "SonarPipelineMeasure)\",\"ruleKey\":\"findbugs:NP_NULL_PARAM_DEREF\",\"startOffset\":0,"
                + "\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component"
                + "/service/sonar/impl/SonarServiceImpl.java\"},{\"severity\":\"MAJOR\",\"endLine\":432,"
                + "\"endOffset\":104,\"startLine\":432,\"type\":\"BUG\",\"message\":\"Null passed for non-null "
                + "parameter of getStuckPointSettingsJson(SonarStuckPointRequest$StuckPointSettings, "
                + "SonarStuckPointRequest$StuckPointSettings) in com.kuaishou.serveree.themis.component.service.sonar"
                + ".impl.SonarServiceImpl.getCheckResultAndSavePluginSettings(SonarStuckPointRequest, "
                + "SonarPipelineMeasure)\",\"ruleKey\":\"findbugs:NP_NULL_PARAM_DEREF\",\"startOffset\":0,"
                + "\"location\":\"ks-serveree-themis-component/src/main/java/com/kuaishou/serveree/themis/component"
                + "/service/sonar/impl/SonarServiceImpl.java\"},{\"severity\":\"MAJOR\",\"endLine\":13,"
                + "\"endOffset\":80,\"startLine\":13,\"type\":\"CODE_SMELL\",\"message\":\"Remove this commented out "
                + "code.\",\"ruleKey\":\"xml:S125\",\"startOffset\":12,"
                + "\"location\":\"ks-serveree-themis-api/src/main/resources/logback.xml\"},{\"severity\":\"MAJOR\","
                + "\"endLine\":23,\"endOffset\":84,\"startLine\":23,\"type\":\"CODE_SMELL\",\"message\":\"Remove this"
                + " commented out code.\",\"ruleKey\":\"xml:S125\",\"startOffset\":12,"
                + "\"location\":\"ks-serveree-themis-api/src/main/resources/logback.xml\"},{\"severity\":\"MAJOR\","
                + "\"endLine\":46,\"endOffset\":84,\"startLine\":46,\"type\":\"CODE_SMELL\",\"message\":\"Remove this"
                + " commented out code.\",\"ruleKey\":\"xml:S125\",\"startOffset\":12,"
                + "\"location\":\"ks-serveree-themis-api/src/main/resources/logback.xml\"},{\"severity\":\"MAJOR\","
                + "\"endLine\":70,\"endOffset\":78,\"startLine\":70,\"type\":\"CODE_SMELL\",\"message\":\"Remove this"
                + " commented out code.\",\"ruleKey\":\"xml:S125\",\"startOffset\":12,"
                + "\"location\":\"ks-serveree-themis-api/src/main/resources/logback.xml\"},{\"severity\":\"MAJOR\","
                + "\"endLine\":92,\"endOffset\":78,\"startLine\":92,\"type\":\"CODE_SMELL\",\"message\":\"Remove this"
                + " commented out code.\",\"ruleKey\":\"xml:S125\",\"startOffset\":12,"
                + "\"location\":\"ks-serveree-themis-api/src/main/resources/logback.xml\"}],\"ideaTriggerType\":4,"
                + "\"analyzeCostTime\":105,\"compileCostTime\":41}\n";
        MonitorReportRequest monitorReportRequest = JSONUtils.deserialize(s, MonitorReportRequest.class);
        monitorController.report(monitorReportRequest);
    }

}
