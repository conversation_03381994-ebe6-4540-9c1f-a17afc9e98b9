package com.kuaishou.serveree.themis;

import java.util.List;
import java.util.Map;

import javax.annotation.Nullable;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.infra.scheduler.ShardParam;
import com.kuaishou.infra.scheduler.SimpleTaskInfo;
import com.kuaishou.infra.scheduler.SimpleTaskInfo.Builder;
import com.kuaishou.infra.scheduler.client.TaskContext;
import com.kuaishou.serveree.themis.component.constant.analyze.LocalAnalyzeTaskEnum;
import com.kuaishou.serveree.themis.component.service.LocalAnalysisService;

/**
 * <AUTHOR>
 * @since 2021/3/29 11:09 上午
 */
public class DependencyAnalysisTest extends SpringBaseTest {

    @Autowired
    private LocalAnalysisService localAnalysisService;

    @Test
    public void testAnalysisDistribute() {

        TaskContext taskContext = new TaskContext() {

            @Override
            public String args() {
                return "oneMonthActiveProjects:true";
            }

            @Override
            public int totalShard() {
                return 0;
            }

            @Override
            public List<Integer> shards() {
                return null;
            }

            @Override
            public long expectedStartTime() {
                return 0;
            }

            @Nullable
            @Override
            public SimpleTaskInfo taskInfo() {
                Builder builder = SimpleTaskInfo.newBuilder();
                builder.setTaskName(LocalAnalyzeTaskEnum.LOCAL_ANALYZE.getName());
                return builder.build();
            }

            @Nullable
            @Override
            public String parentResultAsString() {
                return null;
            }

            @Nullable
            @Override
            public Map<ShardParam, String> parentResults() {
                return null;
            }

            @Nullable
            @Override
            public String swimLaneId() {
                return null;
            }
        };
        localAnalysisService.sponsorDispatch(taskContext);
    }

    @Test
    public void testPerf() {
        localAnalysisService.perfLocalDependencyData();
    }

    @Test
    public void testCollect() {
        localAnalysisService.collectDependencySize();
    }

    @Test
    public void offlineSonarCheckTest() {

        TaskContext taskContext = new TaskContext() {

            @Nullable
            @Override
            public String args() {
                return "projectIds:[24049,36686]";
            }

            @Override
            public int totalShard() {
                return 0;
            }

            @Override
            public List<Integer> shards() {
                return null;
            }

            @Override
            public long expectedStartTime() {
                return 0;
            }

            @Nullable
            @Override
            public SimpleTaskInfo taskInfo() {
                Builder builder = SimpleTaskInfo.newBuilder();
                builder.setTaskName(LocalAnalyzeTaskEnum.LOCAL_OFFLINE_CHECK.getName());
                return builder.build();
            }

            @Nullable
            @Override
            public String parentResultAsString() {
                return null;
            }

            @Nullable
            @Override
            public Map<ShardParam, String> parentResults() {
                return null;
            }

            @Nullable
            @Override
            public String swimLaneId() {
                return null;
            }
        };
        localAnalysisService.sponsorDispatch(taskContext);
    }

    @Test
    public void offlineFrontCheckTest() {
        TaskContext taskContext = new TaskContext() {

            @Nullable
            @Override
            public String args() {
                return "projectIds:[24049,36686]";
            }

            @Override
            public int totalShard() {
                return 0;
            }

            @Override
            public List<Integer> shards() {
                return null;
            }

            @Override
            public long expectedStartTime() {
                return 0;
            }

            @Nullable
            @Override
            public SimpleTaskInfo taskInfo() {
                Builder builder = SimpleTaskInfo.newBuilder();
                builder.setTaskName(LocalAnalyzeTaskEnum.LOCAL_FRONT_METRIC_CHECK.getName());
                return builder.build();
            }

            @Nullable
            @Override
            public String parentResultAsString() {
                return null;
            }

            @Nullable
            @Override
            public Map<ShardParam, String> parentResults() {
                return null;
            }

            @Nullable
            @Override
            public String swimLaneId() {
                return null;
            }
        };
        localAnalysisService.sponsorDispatch(taskContext);
    }

}
