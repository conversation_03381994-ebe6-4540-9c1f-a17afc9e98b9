package com.kuaishou.serveree.themis.ww;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.junit.Test;
import org.testng.collections.Lists;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.kuaishou.serveree.themis.SpringBaseTest;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;
import com.kuaishou.serveree.themis.component.common.entity.CheckProfileRuleRelation;
import com.kuaishou.serveree.themis.component.common.entity.CheckRule;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformLanguageEnum;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformScannerEnum;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueSeverity;
import com.kuaishou.serveree.themis.component.constant.quality.CheckIssueType;
import com.kuaishou.serveree.themis.component.entity.platform.CheckRuleListCondition;
import com.kuaishou.serveree.themis.component.service.CheckProfileRuleRelationService;
import com.kuaishou.serveree.themis.component.service.CheckProfileService;
import com.kuaishou.serveree.themis.component.service.CheckRuleService;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-08-13
 */
public class CoverityRuleGenerateTest extends SpringBaseTest {

    /**
     *   {
     *     "category" : "API usage errors",
     *     "checkerName" : "ALLOC_FREE_MISMATCH",
     *     "type" : "wrong_deallocator",
     *     "subtype" : "alloc_free_mismatch",
     *     "cweCategory" : 762,
     *     "weaknessId" : 1170,
     *     "language" : "C/C++",
     *     "code-language" : "c/c++",
     *     "impact" : "Medium",
     *     "origin" : "Coverity",
     *     "qualityKind" : true,
     *     "securityKind" : false,
     *     "testKind" : false,
     *     "subcategory" : "none",
     *     "subcategoryLocalEffect" : "Depending on the difference between the correct and incorrect allocator, there may be a resource leak or memory corruption.",
     *     "subcategoryLongDescription" : "A resource is freed using the wrong deallocator",
     *     "subcategoryShortDescription" : "Incorrect deallocator used",
     *     "isDocumented" : true
     *   }
     */

    @Resource
    private CheckRuleService checkRuleService;


    private String genRuleKey(JSONObject jsonObject) {
        String codeLanguage = jsonObject.getStr("code-language");
        String checkerName = jsonObject.getStr("checkerName");
        String type = jsonObject.getStr("type", "null");
        String subtype = jsonObject.getStr("subtype", "null");
        assert codeLanguage != null;
        assert checkerName != null;
        return codeLanguage + ":" + checkerName + ":" + type + ":" + subtype;
    }

    private String gentRuleName(JSONObject jsonObject) {
        String category = jsonObject.getStr("category");
        String subcategory = jsonObject.getStr("subcategory");
        String subcategoryShortDescription = jsonObject.getStr("subcategoryShortDescription");
        String name = category;
        if (subcategory != null && !"none".equals(subcategory)) {
            name = name + " : " + subcategory;
        }
        return name +  " : " + subcategoryShortDescription;
    }

    private CheckIssueType getCoverityRuleType(JSONObject jsonObject) {
        Boolean qualityKind = jsonObject.getBool("qualityKind", false);
        if (qualityKind) {
            return CheckIssueType.BUG;
        }
        Boolean securityKind = jsonObject.getBool("securityKind", false);
        if (securityKind) {
            return CheckIssueType.VULNERABILITY;
        }
        return CheckIssueType.CODE_SMELL;
    }

    private CheckIssueSeverity getCoverityRuleSeverity(JSONObject jsonObject) {
        String impact = jsonObject.getStr("impact");
        if ("High".equals(impact)) {
            return CheckIssueSeverity.SERIOUS;
        }
        if ("Low".equals(impact)) {
            return CheckIssueSeverity.COMMON;
        }
        return CheckIssueSeverity.MAJOR;
    }

    @Test
    public void loadCoverityRules() {
        JSONArray jsonArray =
                JSONUtil.readJSONArray(
                        new File("../script/java_module/coverity/rule/builtin-c_c++_checker-properties.json"),
                        StandardCharsets.UTF_8);

        List<CheckRule> checkRuleList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            CheckRule checkRule = new CheckRule();
            checkRule.setRuleKey(genRuleKey(jsonObject));
            checkRule.setName(gentRuleName(jsonObject));
            checkRule.setRuleType(getCoverityRuleType(jsonObject).getType());
            checkRule.setSeverity(getCoverityRuleSeverity(jsonObject).getKey());
            checkRule.setLanguage(PlatformLanguageEnum.CPP.getName());
            checkRule.setScanner(PlatformScannerEnum.COVERITY_SCANNER.getScanner());
            checkRule.setDescription(jsonObject.getStr("subcategoryLongDescription"));
            checkRule.setHtmlDesc(String.format("<p>%s</p>", jsonObject.getStr("subcategoryLongDescription")));
            checkRule.setHtmlDescZh("");
            checkRule.setNameZh("");
            checkRule.setRuleLink("");
            checkRule.setGmtCreate(now);
            checkRule.setGmtModified(now);
            checkRule.setDeleted(false);
            checkRuleList.add(checkRule);
        }
        // 保存
        System.out.println(checkRuleList.size());
        Map<String, List<CheckRule>> map =
                checkRuleList.stream().collect(Collectors.groupingBy(CheckRule::getName));
        for (Entry<String, List<CheckRule>> entry : map.entrySet()) {
            if (entry.getValue().size() > 1) {
                // 名称有重复，加上 type_subtype
                // key的命名格式是 language : checkerName : type : subtype
                List<CheckRule> list = entry.getValue();
                for (CheckRule checkRule : list) {
                    String ruleKey = checkRule.getRuleKey();
                    checkRule.setName(checkRule.getName() + ":" + ruleKey.substring(findNthChar(ruleKey, 2, ':') + 1));
                }
            }
        }
        assert checkRuleList.stream().map(CheckRule::getRuleKey).distinct().count() == checkRuleList.size();
        assert checkRuleList.stream().map(CheckRule::getName).distinct().count() == checkRuleList.size();
        // System.out.println();
        // 先清空之前的
        checkRuleService.deleteByScanner(PlatformScannerEnum.COVERITY_SCANNER.getScanner());
        checkRuleService.saveBatch(checkRuleList);
    }

    @Resource
    private CheckProfileService checkProfileService;
    @Resource
    private CheckProfileRuleRelationService checkProfileRuleRelationService;

    /**
     * 把所有都加入标准规则集中
     */
    @Test
    public void addRuleProfileRelation() {
        CheckProfile checkProfile = checkProfileService.getOne(
                Wrappers.<CheckProfile> lambdaQuery().eq(CheckProfile::getDisplayName, "Coverty-Standard"));
        assert checkProfile != null;
        // 所有coverity的规则
        List<CheckRule> ruleList = checkRuleService.listByCondition(
                CheckRuleListCondition.builder().scanner(PlatformScannerEnum.COVERITY_SCANNER.getScanner()).build());
        List<CheckProfileRuleRelation> needSaveList = Lists.newArrayList();
        LocalDateTime now = LocalDateTime.now();
        for (CheckRule checkRule : ruleList) {
            CheckProfileRuleRelation relation = new CheckProfileRuleRelation();
            relation.setProfileName(checkProfile.getProfileName());
            relation.setRuleKey(checkRule.getRuleKey());
            relation.setCreator("system");
            relation.setUpdater("system");
            relation.setDeleted(false);
            relation.setGmtCreate(now);
            relation.setGmtModified(now);
            relation.setRuleSeverity(checkRule.getSeverity());
            needSaveList.add(relation);
        }
        // 先清空之前的
        checkProfileRuleRelationService.deleteByProfileName(checkProfile.getProfileName());
        checkProfileRuleRelationService.saveBatch(needSaveList);
    }

    private static int findNthChar(String str, int n, char c) {
        for (int i = 0; i < str.length(); ++i) {
            if (str.charAt(i) == c) {
                if (--n == 0) {
                    return i;
                }
            }
        }
        return -1;
    }
}
