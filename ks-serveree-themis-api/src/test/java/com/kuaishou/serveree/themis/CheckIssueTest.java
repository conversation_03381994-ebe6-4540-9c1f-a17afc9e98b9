package com.kuaishou.serveree.themis;

import java.util.List;
import java.util.Map;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.component.service.CheckIssueService;

/**
 * <AUTHOR>
 * @since 2022/4/29 4:11 下午
 */
public class CheckIssueTest extends SpringBaseTest {

    @Autowired
    private CheckIssueService checkIssueService;

    @Test
    public void testGroup(){
        List<Map<String, Object>> statusGroupList =
                checkIssueService.groupCountByCondition(3L, "severity");
        System.out.println(statusGroupList);
    }

}

