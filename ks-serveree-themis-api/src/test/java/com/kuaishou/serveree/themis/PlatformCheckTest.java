package com.kuaishou.serveree.themis;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.CheckMetric;
import com.kuaishou.serveree.themis.component.common.entity.TaskRequestToken;
import com.kuaishou.serveree.themis.component.service.CheckMetricService;
import com.kuaishou.serveree.themis.component.service.CheckRepoService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformCheckActionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformIssueService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformPermissionService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.utils.ThemisTaskTokenUtil;
import com.kuaishou.serveree.themis.component.vo.request.AppendCheckMeasureVo;
import com.kuaishou.serveree.themis.component.vo.request.CheckActionRequest;
import com.kuaishou.serveree.themis.component.vo.request.CheckIssueVo;
import com.kuaishou.serveree.themis.component.vo.request.CheckIssueVo.CheckDuplicationVo;
import com.kuaishou.serveree.themis.component.vo.request.CheckIssueVo.Complexity;
import com.kuaishou.serveree.themis.component.vo.request.DataAppendRequest;
import com.kuaishou.serveree.themis.component.vo.request.EditIssueVo;
import com.kuaishou.serveree.themis.component.vo.request.IssueSearchRequest;
import com.kuaishou.serveree.themis.component.vo.request.IssueVo;
import com.kuaishou.serveree.themis.component.vo.request.PipelineReportRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoLatestCommitRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoMeasuresSearchRequest;
import com.kuaishou.serveree.themis.component.vo.response.CheckActionResponse;
import com.kuaishou.serveree.themis.component.vo.response.DataAppendResponse;
import com.kuaishou.serveree.themis.component.vo.response.IssueSearchResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoLatestCommitResponse;
import com.kuaishou.serveree.themis.component.vo.response.RepoMeasuresSearchResponse;

import cn.hutool.core.io.FileUtil;

/**
 * <AUTHOR>
 * @since 2021/9/1 4:59 下午
 */
public class PlatformCheckTest extends SpringBaseTest {

    @Resource(name = "PlatformCheckActionServiceImpl")
    private PlatformCheckActionService platformCheckActionService;

    @Autowired
    private PlatformIssueService platformIssueService;

    @Autowired
    private PlatformRepoService platformRepoService;

    @Autowired
    private CheckMetricService checkMetricService;

    @Autowired
    private PlatformPermissionService platformPermissionService;

    @Autowired
    private CheckRepoService checkRepoService;

    @Test
    public void testLatestCommit() {
        RepoLatestCommitRequest repoLatestCommitRequest = new RepoLatestCommitRequest();
        repoLatestCommitRequest.setRepoUrl("*************************:serveree/ks-serveree-cr.git");
        RepoLatestCommitResponse repoLatestCommitResponse = platformRepoService.latestCommit(repoLatestCommitRequest);
        System.out.println(JSONUtils.serialize(repoLatestCommitResponse));
    }

    @Test
    public void testSearchMeasures() {
        RepoMeasuresSearchRequest repoMeasuresSearchRequest = new RepoMeasuresSearchRequest();
        repoMeasuresSearchRequest.setRepoUrl("*************************:ks-ad/ad-fe/eco/skyeye-scanner.git");
        repoMeasuresSearchRequest.setBranch("feature_scanner");
        RepoMeasuresSearchResponse repoMeasuresSearchResponse =
                platformRepoService.measuresSearch(repoMeasuresSearchRequest);
        System.out.println(JSONUtils.serialize(repoMeasuresSearchResponse));
    }

    @Test
    public void testSearchIssue() {
        IssueSearchRequest issueSearchRequest = new IssueSearchRequest();
        issueSearchRequest.setRepoUrl("*************************:ks-ad/ad-fe/eco/skyeye-scanner.git");
        issueSearchRequest.setBranch("feature_scanner");
        long l = System.currentTimeMillis();
        IssueSearchResponse search = platformIssueService.search(issueSearchRequest);
        long l1 = System.currentTimeMillis();
        System.out.println("=======" + (l1 - l));
        System.out.println(JSONUtils.serialize(search));
    }

    @Test
    public void testAction() {

        CheckActionRequest checkActionRequest = new CheckActionRequest();
        checkActionRequest.setRepoUrl("*************************:serveree/ks-serveree-cr.git");
        checkActionRequest.setCommitId("aafafafaf11241aefsefss22332");
        checkActionRequest.setBranch("master");

        List<CheckIssueVo> checkIssueVoList = Lists.newArrayList();
        CheckIssueVo checkIssueVo = new CheckIssueVo();
        checkIssueVo.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/client/DevTeamClient.java");
        checkIssueVo.setAuthor("<EMAIL>");
        checkIssueVo.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo.setCreateTime(1630489267878L);
        checkIssueVo.setEffect(0);
        checkIssueVo.setStatus("OPEN");
        checkIssueVo.setRule("afafafafaf");
        checkIssueVo.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo.setType("BUG");
        checkIssueVo.setSeverity("COMMON");
        checkIssueVo.setStartLine(121);
        checkIssueVo.setEndLine(222);
        checkIssueVoList.add(checkIssueVo);

        CheckIssueVo checkIssueVo1 = new CheckIssueVo();
        checkIssueVo1.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/client/MasterDataClient"
                        + ".java");
        checkIssueVo1.setAuthor("<EMAIL>");
        checkIssueVo1.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo1.setCreateTime(1630489267878L);
        checkIssueVo1.setEffect(0);
        checkIssueVo1.setStatus("OPEN");
        checkIssueVo1.setRule("afafafafaf");
        checkIssueVo1.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo1.setType("BUG");
        checkIssueVo1.setSeverity("COMMON");
        checkIssueVo1.setStartLine(121);
        checkIssueVo1.setEndLine(222);
        checkIssueVoList.add(checkIssueVo1);

        CheckIssueVo checkIssueVo2 = new CheckIssueVo();
        checkIssueVo2.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/client/MasterDataClient"
                        + ".java");
        checkIssueVo2.setAuthor("<EMAIL>");
        checkIssueVo2.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo2.setCreateTime(1630489267878L);
        checkIssueVo2.setEffect(0);
        checkIssueVo2.setStatus("OPEN");
        checkIssueVo2.setRule("afafafafaf");
        checkIssueVo2.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo2.setType("COMPLEXITY");
        checkIssueVo2.setSeverity("COMMON");
        checkIssueVo2.setStartLine(121);
        checkIssueVo2.setEndLine(222);
        checkIssueVo2.setComplexity(new Complexity(123));
        checkIssueVoList.add(checkIssueVo2);

        CheckIssueVo checkIssueVo3 = new CheckIssueVo();
        checkIssueVo3.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/client/MasterDataClient"
                        + ".java");
        checkIssueVo3.setAuthor("<EMAIL>");
        checkIssueVo3.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo3.setCreateTime(1630489267878L);
        checkIssueVo3.setEffect(0);
        checkIssueVo3.setStatus("OPEN");
        checkIssueVo3.setRule("afafafafaf");
        checkIssueVo3.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo3.setType("COMPLEXITY");
        checkIssueVo3.setSeverity("COMMON");
        checkIssueVo3.setStartLine(155);
        checkIssueVo3.setEndLine(222);
        checkIssueVo3.setComplexity(new Complexity(12223));
        checkIssueVoList.add(checkIssueVo3);

        CheckIssueVo checkIssueVo4 = new CheckIssueVo();
        checkIssueVo4.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/common/conf"
                        + "/TaskExecutorConfig.java");
        checkIssueVo4.setAuthor("<EMAIL>");
        checkIssueVo4.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo4.setCreateTime(1630489267878L);
        checkIssueVo4.setEffect(0);
        checkIssueVo4.setStatus("OPEN");
        checkIssueVo4.setRule("afafafafaf");
        checkIssueVo4.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo4.setType("DUPLICATION");
        checkIssueVo4.setSeverity("COMMON");
        checkIssueVo4.setStartLine(155);
        checkIssueVo4.setEndLine(222);
        List<CheckDuplicationVo> checkDuplicationVos = Lists.newArrayList();
        CheckDuplicationVo checkDuplicationVo = new CheckDuplicationVo();
        checkDuplicationVo.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/common/conf"
                        + "/TaskExecutorConfig.java");
        checkDuplicationVo.setStartLine(12);
        checkDuplicationVo.setEndLine(99);
        checkDuplicationVos.add(checkDuplicationVo);
        checkIssueVo4.setDuplications(checkDuplicationVos);
        checkIssueVoList.add(checkIssueVo4);

        checkActionRequest.setIssues(checkIssueVoList);
        TaskRequestToken taskRequestToken = new TaskRequestToken();
        taskRequestToken.setSource("admin-check");
        ThemisTaskTokenUtil.set(taskRequestToken);
        platformCheckActionService.action(checkActionRequest);
    }

    @Test
    public void testEditAction() {

        CheckActionRequest checkActionRequest = new CheckActionRequest();
        checkActionRequest.setRepoUrl("*************************:serveree/ks-serveree-cr.git");
        checkActionRequest.setCommitId("fffaf23q4348989sdafaafa");
        checkActionRequest.setBranch("master");

        List<CheckIssueVo> checkIssueVoList = Lists.newArrayList();
        CheckIssueVo checkIssueVo = new CheckIssueVo();
        checkIssueVo.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/client/DevTeamqqqClient"
                        + ".java");
        checkIssueVo.setAuthor("<EMAIL>");
        checkIssueVo.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo.setCreateTime(1630489267878L);
        checkIssueVo.setEffect(0);
        checkIssueVo.setStatus("OPEN");
        checkIssueVo.setRule("afafafafaf");
        checkIssueVo.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo.setType("BUG");
        checkIssueVo.setSeverity("COMMON");
        checkIssueVo.setStartLine(13);
        checkIssueVo.setEndLine(22);
        checkIssueVoList.add(checkIssueVo);

        checkActionRequest.setIssues(checkIssueVoList);

        List<EditIssueVo> editVos = Lists.newArrayList();

        EditIssueVo checkIssueVo1 = new EditIssueVo();
        checkIssueVo1.setIssueId(1L);
        checkIssueVo1.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/client/MaAAAterDataClient"
                        + ".java");
        checkIssueVo1.setAuthor("<EMAIL>");
        checkIssueVo1.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo1.setCreateTime(1630489267878L);
        checkIssueVo1.setEffect(0);
        checkIssueVo1.setStatus("OPEN");
        checkIssueVo1.setRule("afafafafaf");
        checkIssueVo1.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo1.setType("BUG");
        checkIssueVo1.setSeverity("COMMON");
        checkIssueVo1.setStartLine(121);
        checkIssueVo1.setEndLine(222);
        editVos.add(checkIssueVo1);

        EditIssueVo checkIssueVo2 = new EditIssueVo();
        checkIssueVo2.setIssueId(2L);
        checkIssueVo2.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/client/MasterDataClient"
                        + ".java");
        checkIssueVo2.setAuthor("<EMAIL>");
        checkIssueVo2.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo2.setCreateTime(1630489267878L);
        checkIssueVo2.setEffect(0);
        checkIssueVo2.setStatus("OPEN");
        checkIssueVo2.setRule("afafafafaf");
        checkIssueVo2.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo2.setType("BUG");
        checkIssueVo2.setSeverity("COMMON");
        checkIssueVo2.setStartLine(121);
        checkIssueVo2.setEndLine(222);
        checkIssueVo2.setComplexity(new Complexity(123));
        editVos.add(checkIssueVo2);

        EditIssueVo checkIssueVo3 = new EditIssueVo();
        checkIssueVo3.setIssueId(3L);
        checkIssueVo3.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/client/AAAAMasterDataClient"
                        + ".java");
        checkIssueVo3.setAuthor("<EMAIL>");
        checkIssueVo3.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo3.setCreateTime(1630489267878L);
        checkIssueVo3.setEffect(0);
        checkIssueVo3.setStatus("OPEN");
        checkIssueVo3.setRule("afafafafaf");
        checkIssueVo3.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo3.setType("COMPLEXITY");
        checkIssueVo3.setSeverity("COMMON");
        checkIssueVo3.setStartLine(15);
        checkIssueVo3.setEndLine(22);
        checkIssueVo3.setComplexity(new Complexity(12244423));
        editVos.add(checkIssueVo3);

        EditIssueVo checkIssueVo4 = new EditIssueVo();
        checkIssueVo4.setIssueId(5L);
        checkIssueVo4.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/common/conf"
                        + "/TaskExecutorConfigChange.java");
        checkIssueVo4.setAuthor("<EMAIL>");
        checkIssueVo4.setCreateCommitId("afafafafeafwefrg");
        checkIssueVo4.setCreateTime(1630489267878L);
        checkIssueVo4.setEffect(0);
        checkIssueVo4.setStatus("OPEN");
        checkIssueVo4.setRule("afafafafaf");
        checkIssueVo4.setMessage("adaffsdfsdgfbdgfb");
        checkIssueVo4.setType("DUPLICATION");
        checkIssueVo4.setSeverity("COMMON");
        checkIssueVo4.setStartLine(155);
        checkIssueVo4.setEndLine(222);
        List<CheckDuplicationVo> checkDuplicationVos = Lists.newArrayList();
        CheckDuplicationVo checkDuplicationVo = new CheckDuplicationVo();
        checkDuplicationVo.setLocation(
                "ks-serveree-cr-component/src/main/java/com/kuaishou/serveree/cr/component/common/conf"
                        + "/TaskExecutorConfig.java");
        checkDuplicationVo.setStartLine(14);
        checkDuplicationVo.setEndLine(1000);
        checkDuplicationVos.add(checkDuplicationVo);
        checkIssueVo4.setDuplications(checkDuplicationVos);
        editVos.add(checkIssueVo4);


        TaskRequestToken taskRequestToken = new TaskRequestToken();
        taskRequestToken.setSource("admin-check");
        ThemisTaskTokenUtil.set(taskRequestToken);

        checkActionRequest.setEditIssues(editVos);


        platformCheckActionService.action(checkActionRequest);
    }

    @Test
    public void testCheckAction() {
        TaskRequestToken taskRequestToken = new TaskRequestToken();
        taskRequestToken.setSource("business-front");
        ThemisTaskTokenUtil.set(taskRequestToken);
        String requestJson = "{\"repoUrl\":\"*************************:ks-ad/ad-fe/eco/kuaishou-frontend-social"
                + ".git\",\"branch\":\"feature_test_skyeye_mr\",\"commitId\":\"eea1a5dab\","
                + "\"issues\":[{\"rule\":\"no-unused-vars\",\"message\":\"'obj' is defined but never used.\","
                + "\"location\":\"src/common/test/util.ts\",\"startLine\":1,\"endLine\":1,\"type\":\"CODE_SMELL\","
                + "\"severity\":\"COMMON\",\"status\":\"OPEN\",\"effect\":1,\"gitLink\":\"https://git.corp.kuaishou"
                + ".com/ks-ad/ad-fe/eco/kuaishou-frontend-social/-/blob/master/src/common/test/util.ts#L1\","
                + "\"issueLink\":\"https://eslint.org/docs/rules/no-unused-vars\",\"author\":\"unknown\","
                + "\"createCommitId\":\"unknown\",\"createTime\":0},{\"rule\":\"no-undef\",\"message\":\"'ObjectAny' "
                + "is not defined.\",\"location\":\"src/common/test/util.ts\",\"startLine\":1,\"endLine\":1,"
                + "\"type\":\"CODE_SMELL\",\"severity\":\"COMMON\",\"status\":\"OPEN\",\"effect\":1,"
                + "\"gitLink\":\"https://git.corp.kuaishou"
                + ".com/ks-ad/ad-fe/eco/kuaishou-frontend-social/-/blob/master/src/common/test/util.ts#L1\","
                + "\"issueLink\":\"https://eslint.org/docs/rules/no-undef\",\"author\":\"unknown\","
                + "\"createCommitId\":\"unknown\",\"createTime\":0},{\"rule\":\"no-unused-vars\","
                + "\"message\":\"'option' is defined but never used.\",\"location\":\"src/common/test/util.ts\","
                + "\"startLine\":1,\"endLine\":1,\"type\":\"CODE_SMELL\",\"severity\":\"COMMON\",\"status\":\"OPEN\","
                + "\"effect\":1,\"gitLink\":\"https://git.corp.kuaishou"
                + ".com/ks-ad/ad-fe/eco/kuaishou-frontend-social/-/blob/master/src/common/test/util.ts#L1\","
                + "\"issueLink\":\"https://eslint.org/docs/rules/no-unused-vars\",\"author\":\"unknown\","
                + "\"createCommitId\":\"unknown\",\"createTime\":0},{\"rule\":\"no-undef\",\"message\":\"'IOptions' "
                + "is not defined.\",\"location\":\"src/common/test/util.ts\",\"startLine\":1,\"endLine\":1,"
                + "\"type\":\"CODE_SMELL\",\"severity\":\"COMMON\",\"status\":\"OPEN\",\"effect\":1,"
                + "\"gitLink\":\"https://git.corp.kuaishou"
                + ".com/ks-ad/ad-fe/eco/kuaishou-frontend-social/-/blob/master/src/common/test/util.ts#L1\","
                + "\"issueLink\":\"https://eslint.org/docs/rules/no-undef\",\"author\":\"unknown\","
                + "\"createCommitId\":\"unknown\",\"createTime\":0},{\"rule\":\"no-undef\",\"message\":\"'ObjectAny' "
                + "is not defined.\",\"location\":\"src/common/test/util.ts\",\"startLine\":1,\"endLine\":1,"
                + "\"type\":\"CODE_SMELL\",\"severity\":\"COMMON\",\"status\":\"OPEN\",\"effect\":1,"
                + "\"gitLink\":\"https://git.corp.kuaishou"
                + ".com/ks-ad/ad-fe/eco/kuaishou-frontend-social/-/blob/master/src/common/test/util.ts#L1\","
                + "\"issueLink\":\"https://eslint.org/docs/rules/no-undef\",\"author\":\"unknown\","
                + "\"createCommitId\":\"unknown\",\"createTime\":0},{\"rule\":\"no-undef\",\"message\":\"'cloneDeep' "
                + "is not defined.\",\"location\":\"src/common/test/util.ts\",\"startLine\":2,\"endLine\":2,"
                + "\"type\":\"CODE_SMELL\",\"severity\":\"COMMON\",\"status\":\"OPEN\",\"effect\":1,"
                + "\"gitLink\":\"https://git.corp.kuaishou"
                + ".com/ks-ad/ad-fe/eco/kuaishou-frontend-social/-/blob/master/src/common/test/util.ts#L2\","
                + "\"issueLink\":\"https://eslint.org/docs/rules/no-undef\",\"author\":\"unknown\","
                + "\"createCommitId\":\"unknown\",\"createTime\":0},{\"rule\":\"one-var\",\"message\":\"Combine this "
                + "with the previous 'const' statement.\",\"location\":\"src/common/test/util.ts\",\"startLine\":3,"
                + "\"endLine\":3,\"type\":\"CODE_SMELL\",\"severity\":\"COMMON\",\"status\":\"OPEN\",\"effect\":1,"
                + "\"gitLink\":\"https://git.corp.kuaishou"
                + ".com/ks-ad/ad-fe/eco/kuaishou-frontend-social/-/blob/master/src/common/test/util.ts#L3\","
                + "\"issueLink\":\"https://eslint.org/docs/rules/one-var\",\"author\":\"unknown\","
                + "\"createCommitId\":\"unknown\",\"createTime\":0},{\"rule\":\"one-var\",\"message\":\"Combine this "
                + "with the previous 'const' statement.\",\"location\":\"src/common/test/util.ts\",\"startLine\":4,"
                + "\"endLine\":4,\"type\":\"CODE_SMELL\",\"severity\":\"COMMON\",\"status\":\"OPEN\",\"effect\":1,"
                + "\"gitLink\":\"https://git.corp.kuaishou"
                + ".com/ks-ad/ad-fe/eco/kuaishou-frontend-social/-/blob/master/src/common/test/util.ts#L4\","
                + "\"issueLink\":\"https://eslint.org/docs/rules/one-var\",\"author\":\"unknown\","
                + "\"createCommitId\":\"unknown\",\"createTime\":0}],\"editIssues\":[],"
                + "\"measures\":[{\"key\":\"duplicated_lines_density\",\"value\":[\"0\"]},"
                + "{\"key\":\"duplicated_lines\",\"value\":[\"13\"]},{\"key\":\"duplicated_blocks\","
                + "\"value\":[\"0\"]},{\"key\":\"cognitive_complexity\",\"value\":[\"17.455\"]},"
                + "{\"key\":\"maintainability_index\",\"value\":[\"114.35\"]},{\"key\":\"complexity\","
                + "\"value\":[\"8\"]},{\"key\":\"comment_lines\",\"value\":[\"0\"]},"
                + "{\"key\":\"comment_lines_density\",\"value\":[\"0\"]},{\"key\":\"ncloc_language_distribution\","
                + "\"value\":[{\"languageLineMap\":{\"typescript\":13},\"languagePercentMap\":{\"typescript\":1}}]},"
                + "{\"key\":\"ncloc\",\"value\":[\"13\"]},{\"key\":\"score\",\"value\":[\"88.88455006504029\"]},"
                + "{\"key\":\"bugs_score\",\"value\":[\"100\"]},{\"key\":\"code_smell_score\",\"value\":[\"77"
                + ".4227503252014\"]},{\"key\":\"vulnerability_score\",\"value\":[\"100\"]},"
                + "{\"key\":\"duplication_score\",\"value\":[\"100\"]},{\"key\":\"complexity_score\","
                + "\"value\":[\"67\"]},{\"key\":\"best_practice_score\",\"value\":[\"100\"]},{\"key\":\"bugs_count\","
                + "\"value\":[\"0\"]},{\"key\":\"bugs\",\"value\":[\"0\"]},{\"key\":\"code_smell_count\","
                + "\"value\":[\"8\"]},{\"key\":\"code_smells\",\"value\":[\"8\"]},{\"key\":\"vulnerability_count\","
                + "\"value\":[\"0\"]},{\"key\":\"vulnerabilities\",\"value\":[\"0\"]},{\"key\":\"framework\","
                + "\"value\":[\"react_^16.13.1\"]},{\"key\":\"ui_library\",\"value\":[\"@ad/kcfe-asset-dsp_^1.33"
                + ".0-beta.fixSelectArea.0,antd_^4.9.1,m-ui_^1.10.9\"]},{\"key\":\"state_library\","
                + "\"value\":[\"@dobux/store_^1.0.1,react-redux_^6.0.1,redux_^3.6.0\"]}]}";

        CheckActionRequest deserialize = JSONUtils.deserialize(requestJson, CheckActionRequest.class);
        CheckActionResponse action = platformCheckActionService.action(deserialize);
        System.out.println(JSONUtils.serialize(action));
    }

    @Test
    public void testMetricCheck() {
        List<CheckMetric> all = checkMetricService.all();
        checkMetricService.saveBatch(all);
    }

    @Test
    public void testDataAppend() {
        List<AppendCheckMeasureVo> appendCheckMeasureVos = Lists.newArrayList();
        AppendCheckMeasureVo appendCheckMeasureVo = new AppendCheckMeasureVo();
        appendCheckMeasureVo.setMetricKey("lxx-test1");
        appendCheckMeasureVo.setMetricValue(100);
        appendCheckMeasureVos.add(appendCheckMeasureVo);

        AppendCheckMeasureVo appendCheckMeasureVo1 = new AppendCheckMeasureVo();
        appendCheckMeasureVo1.setMetricKey("lxx-test2");
        appendCheckMeasureVo1.setMetricValue(100);
        appendCheckMeasureVos.add(appendCheckMeasureVo1);

        DataAppendRequest request = DataAppendRequest.builder()
                .baseId(603L)
                .measures(appendCheckMeasureVos)
                .build();
        DataAppendResponse dataAppendResponse = platformCheckActionService.dataAppend(request);
        System.out.println(JSONUtils.serialize(dataAppendResponse));
    }

    @Test
    public void testPipelineReport() {
        TaskRequestToken taskRequestToken = new TaskRequestToken();
        taskRequestToken.setSource("sonar-cplus");
        ThemisTaskTokenUtil.set(taskRequestToken);

        PipelineReportRequest reportRequest = new PipelineReportRequest();
        reportRequest.setGitProjectId(24049);
        reportRequest.setCommitId("1239hdiahjih39793hkankcjjna");
        reportRequest.setBranch("feature_lxx");
        reportRequest.setSponsor("lxx");
        reportRequest.setKspBuildId(127L);
        reportRequest.setRepoUrl("************************");
        reportRequest.setIssueList(genIssueVoList());
        reportRequest.setIncrementMode(false);
        System.out.println(JSONUtils.serialize(reportRequest));

        String s = FileUtil.readUtf8String("/Users/<USER>/Desktop/sonar9.9 插件/json.txt");
        reportRequest = JSONUtils.deserialize(s, PipelineReportRequest.class);
        platformCheckActionService.pipelineReport(reportRequest);
    }

    private List<IssueVo> genIssueVoList() {
        List<IssueVo> issueVos = Lists.newArrayList();
        IssueVo issueVo = IssueVo.builder()
                .author("lixiaoxin")
                .createTime(0L)
                .location("ks-serveree-themis-api/src/test/java/com/kuaishou/serveree/themis/PlatformCheckATest.java")
                .type("BUG")
                .severity("MAJOR")
                .message(
                        "Impossible cast from Integer to Long in com.kuaishou.serveree.cr.component.service.impl"
                                + ".MrSettingsServiceImpl.getMrSettingsResponse(MrSettingsUpdateRequest)")
                .rule("findbugs:BC_IMPOSSIBLE_CAST")
                .status("OPEN")
                .startLine(1)
                .endLine(2)
                .startOffset(23)
                .endOffset(24)
                .build();
        issueVos.add(issueVo);

        IssueVo issueVo1 = IssueVo.builder()
                .author("lixiaoxin1")
                .createTime(0L)
                .location("ks-serveree-themis-api/src/test/java/com/kuaishou/serveree/themis1/PlatformCheckATest.java")
                .type("BUG")
                .severity("MAJOR")
                .message(
                        "1Impossible cast from Integer to Long in com.kuaishou.serveree.cr.component.service.impl"
                                + ".MrSettingsServiceImpl.getMrSettingsResponse(MrSettingsUpdateRequest)")
                .rule("findbugs:BC_IMPOSSIBLE_CAST")
                .status("OPEN")
                .startLine(1)
                .endLine(2)
                .startOffset(23)
                .endOffset(24)
                .build();
        issueVos.add(issueVo1);
        return issueVos;
    }

    @Test
    public void testPermission() {
        platformPermissionService.checkKdevGitPermission(checkRepoService.getCheckRepoByProjectId(69954), "liuliming05");
    }

}
