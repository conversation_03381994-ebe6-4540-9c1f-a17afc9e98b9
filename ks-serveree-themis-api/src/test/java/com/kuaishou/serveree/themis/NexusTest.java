package com.kuaishou.serveree.themis;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.component.client.nexus.NexusApi;
import com.kuaishou.serveree.themis.component.entity.report.DependencyDTO;

/**
 * <AUTHOR>
 * @since 2021/5/24 3:17 下午
 */
public class NexusTest extends SpringBaseTest {

    @Autowired
    private NexusApi nexusApi;

    @Test
    public void testDependencySize() {
        DependencyDTO build = DependencyDTO.builder()
                .groupId("org.springframework.boot")
                .artifactId("spring-boot-starter-mail")
                .version("2.3.7.RELEASE")
                .type("jar")
                .build();
        long artifactLength = nexusApi.getArtifactLength(build);
        System.out.println("========" + artifactLength);
    }

}
