package com.kuaishou.serveree.themis;

import static java.time.temporal.ChronoField.DAY_OF_WEEK;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.eclipse.jgit.api.BlameCommand;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.blame.BlameResult;
import org.eclipse.jgit.lib.PersonIdent;
import org.junit.Test;
import org.springframework.util.ResourceUtils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.github.phantomthief.util.MoreFunctions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.Uninterruptibles;
import com.kuaishou.serveree.themis.component.common.entity.AstIssue;
import com.kuaishou.serveree.themis.component.common.entity.PCheckExecution;
import com.kuaishou.serveree.themis.component.common.entity.ScanWhitelist;
import com.kuaishou.serveree.themis.component.constant.process.ProcessExecutionReferType;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineDataRun;
import com.kuaishou.serveree.themis.component.entity.ksp.KspPipelineSponsorRequest;
import com.kuaishou.serveree.themis.component.entity.platform.DuplicationDetail;
import com.kuaishou.serveree.themis.component.entity.platform.DuplicationFileDetail;
import com.kuaishou.serveree.themis.component.entity.platform.FileDetail;
import com.kuaishou.serveree.themis.component.entity.platform.FileDuplication;
import com.kuaishou.serveree.themis.component.entity.platform.FileInfo;
import com.kuaishou.serveree.themis.component.entity.platform.RepoDetailTemplate;
import com.kuaishou.serveree.themis.component.entity.platform.RepoDetailTemplate.TemplateMapping;
import com.kuaishou.serveree.themis.component.entity.platform.RepoDetailTemplate.TemplateMapping.TemplateMappingVo;
import com.kuaishou.serveree.themis.component.entity.platform.RepoListTemplate;
import com.kuaishou.serveree.themis.component.entity.platform.RepoListTemplate.ListTemplate;
import com.kuaishou.serveree.themis.component.entity.quality.QualityTaskExecuteBaseDto;
import com.kuaishou.serveree.themis.component.entity.sonar.SonarConfigContent;
import com.kuaishou.serveree.themis.component.proxy.SonarConfigEnum;
import com.kuaishou.serveree.themis.component.service.check.ksp.pipeline.template.JavaKsPluginCheckTemplate;
import com.kuaishou.serveree.themis.component.service.check.ksp.pipeline.template.JavaKsPluginCheckTemplate.KsPluginCheckMeta;
import com.kuaishou.serveree.themis.component.utils.GitUtils;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.ProjectProfileBatchUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.RepoProfileUpdateRequest;
import com.kuaishou.serveree.themis.component.vo.request.SkyeyeLocalReportRequest;

import cn.hutool.core.codec.Base62;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.cron.pattern.CronPatternUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2020/11/6 2:34 下午
 */
public class SimpleTest {

    @Test
    public void test01() {
        LocalDateTime minus1WeekDateTime = LocalDateTime.of(LocalDate.of(2023, 03, 06), LocalTime.MIN).minusWeeks(1);
        System.out.println(minus1WeekDateTime.toString());
        int dayOfWeek = minus1WeekDateTime.getDayOfWeek().get(DAY_OF_WEEK);
        LocalDateTime endDateTime = minus1WeekDateTime.plusDays(8 - dayOfWeek);
        LocalDateTime startDateTime = minus1WeekDateTime.minusDays(dayOfWeek - 1).withNano(1);
        System.out.println(endDateTime.toString());
        System.out.println(startDateTime.toString());
    }

    @Test
    public void test02() {
        List<QualityTaskExecuteBaseDto> qualityTaskExecuteBaseDtoList = new ArrayList<>();
        QualityTaskExecuteBaseDto qualityTaskExecuteBaseDto1 = new QualityTaskExecuteBaseDto();
        qualityTaskExecuteBaseDto1.setTaskId(1L);
        qualityTaskExecuteBaseDto1.setBranch("master");
        qualityTaskExecuteBaseDto1.setRepoUrl("xxxxxxxx");
        qualityTaskExecuteBaseDto1.setCommitId("sssssss");
        qualityTaskExecuteBaseDto1.setCheckType("JAVA_CHECK");
        qualityTaskExecuteBaseDto1.setParams("");
        qualityTaskExecuteBaseDto1.setPriority(3);
        qualityTaskExecuteBaseDtoList.add(qualityTaskExecuteBaseDto1);

        QualityTaskExecuteBaseDto qualityTaskExecuteBaseDto2 = new QualityTaskExecuteBaseDto();
        qualityTaskExecuteBaseDto2.setTaskId(1L);
        qualityTaskExecuteBaseDto2.setBranch("master");
        qualityTaskExecuteBaseDto2.setRepoUrl("xxxxxxxx");
        qualityTaskExecuteBaseDto2.setCommitId("sssssss");
        qualityTaskExecuteBaseDto2.setCheckType("JAVA_CHECK");
        qualityTaskExecuteBaseDto2.setParams("");
        qualityTaskExecuteBaseDto2.setPriority(1);
        qualityTaskExecuteBaseDtoList.add(qualityTaskExecuteBaseDto2);

        QualityTaskExecuteBaseDto qualityTaskExecuteBaseDto3 = new QualityTaskExecuteBaseDto();
        qualityTaskExecuteBaseDto3.setTaskId(1L);
        qualityTaskExecuteBaseDto3.setBranch("master");
        qualityTaskExecuteBaseDto3.setRepoUrl("xxxxxxxx");
        qualityTaskExecuteBaseDto3.setCommitId("sssssss");
        qualityTaskExecuteBaseDto3.setCheckType("JAVA_CHECK");
        qualityTaskExecuteBaseDto3.setParams("dadad");
        qualityTaskExecuteBaseDto3.setPriority(1);
        qualityTaskExecuteBaseDtoList.add(qualityTaskExecuteBaseDto3);

        QualityTaskExecuteBaseDto qualityTaskExecuteBaseDto4 = new QualityTaskExecuteBaseDto();
        qualityTaskExecuteBaseDto4.setTaskId(1L);
        qualityTaskExecuteBaseDto4.setBranch("master");
        qualityTaskExecuteBaseDto4.setRepoUrl("xxxxxxxx");
        qualityTaskExecuteBaseDto4.setCommitId("sssssss");
        qualityTaskExecuteBaseDto4.setCheckType("JAVA_CHECK");
        qualityTaskExecuteBaseDto4.setParams("");
        qualityTaskExecuteBaseDto4.setPriority(3);
        qualityTaskExecuteBaseDtoList.add(qualityTaskExecuteBaseDto4);

        // 相同条件的待发起的任务只会发起一次 检查完毕整体进行相同数据的更新
        Map<String, List<QualityTaskExecuteBaseDto>> sameConditionMap = qualityTaskExecuteBaseDtoList.stream()
                .collect(Collectors.groupingBy(
                        o -> o.getRepoUrl()
                                + ":" + o.getBranch()
                                + ":" + o.getCommitId()
                                + ":" + o.getCheckType()
                                + ":" + o.getParams())
                );

        List<QualityTaskExecuteBaseDto> qualityTaskExecuteBaseDtos = new ArrayList<>();
        sameConditionMap.forEach((key, val) -> {
            Optional<QualityTaskExecuteBaseDto> maxPriorityDto =
                    val.stream().max(Comparator.comparingInt(QualityTaskExecuteBaseDto::getPriority));
            if (maxPriorityDto.isPresent()) {
                QualityTaskExecuteBaseDto qualityTaskExecuteBaseDto = maxPriorityDto.get();
                qualityTaskExecuteBaseDtos.add(qualityTaskExecuteBaseDto);
            }
        });

        System.out.println(JSONUtils.serialize(qualityTaskExecuteBaseDtos));
    }

    @Test
    public void test03() {
        List<String> logList = Collections.singletonList("aaaaaaaaaa");
        String logStorageUrl = "/opt/check-log/" + 20201109 + "/" + "check-log.lo";
        try {
            FileUtils.writeLines(new File(logStorageUrl), logList);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void test04() throws Exception {
        File jsonFile = ResourceUtils.getFile("classpath:template/java_plugin_check_template.json");
        String templateJson = FileUtils.readFileToString(jsonFile);
        KspPipelineDataRun kspPipelineDataRun = JSONUtils.deserialize(templateJson, KspPipelineDataRun.class);
        // 以下做个断言 但是不可能出现
        assert kspPipelineDataRun != null;
        KspPipelineSponsorRequest sponsorRequest = KspPipelineSponsorRequest.builder()
                .params(kspPipelineDataRun.getCovers())
                .build();
        System.out.println(JSONUtils.serialize(sponsorRequest));

    }

    @Test
    public void test05() {
        KspPipelineSponsorRequest sponsorRequest = JavaKsPluginCheckTemplate.convertSponsorRequest(
                KsPluginCheckMeta.builder()
                        .repoUrl("xxxxxx")
                        .branch("balabala")
                        .betaPackageVersions("xxxxxx:2.1.3")
                        .mvnArgs("mvn clean package")
                        .buildModules("api")
                        .build()
        );
        System.out.println(JSONUtils.serialize(sponsorRequest));
    }

    @Test
    public void test06() {
        LocalDateTime updateTime = LocalDateTime.now();
        LocalDateTime now = updateTime.plusSeconds(10);
        Duration dur = Duration.between(updateTime, now);
        long seconds = dur.getSeconds();
        System.out.println(seconds);
    }

    @Test
    public void test08() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime today0h0m0s = now.withHour(0).withMinute(0).withSecond(0);
        // 明天的0点0分0秒
        LocalDateTime localDateTime = today0h0m0s.plusDays(1);
        // 现在到明天的0点0分0秒的时间设置为redis失效时间
        Duration between = Duration.between(now, localDateTime);
        long seconds = between.getSeconds();
        System.out.println(seconds);
    }

    @Test
    public void text09() {

        LocalDate minus3Date = LocalDate.now().minusDays(3);
        String minus1dateFormat = DateTimeFormatter.ofPattern("yyyyMMdd").format(minus3Date);
        System.out.println(minus1dateFormat);
    }

    @Test
    public void getGroupName() {
        String groupName = GitUtils.getGroupName(
                "*************************:plateco-dev-fe/kwaishop-seller/kwaishop-seller-themis-pc.git");
        System.out.println(groupName);
    }

    @Test
    public void testJSON() {
        String serialize = JSONUtils.serialize(null);
        System.out.println(serialize);
    }

    @Test
    public void testJsonBody() {
        SonarConfigContent sonarConfigContent = new SonarConfigContent();
        Map<String, Map<String, String>> settingsMap = Maps.newHashMap();
        Map<String, String> map = Maps.newHashMap();
        map.put("bugs", "gt");
        map.put("test_errors,test_failures", "gt");
        map.put("line_coverage", "lt");
        settingsMap.put("threshold", map);
        sonarConfigContent.setSettingsMap(settingsMap);
        sonarConfigContent.setRobotIds(Lists.newArrayList("265441d7-3edc-4a5c-94e6-3050a061ad6a"));
        String serialize = JSONUtils.serialize(sonarConfigContent);
        System.out.println("======" + serialize);
    }

    @Test
    public void testHuToolId() {
        Snowflake snowflake = IdUtil.getSnowflake();
        for (int i = 0; i < 10; i++) {
            long l = snowflake.nextId();
            System.out.println(l);
        }
    }

    @Test
    public void testLocalDateTime() {
        LocalDateTime now0h0m0s = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        LocalDateTime _1WeeksNow0h0m0s = now0h0m0s.minusWeeks(1);

    }

    @Test
    public void testGitUrl() {
        String url = "*************************:serveree/ks-serveree-themis.git";
        String httpsUrl = GitUtils.getHttpsUrl(url);
        System.out.println(httpsUrl);
    }

    @Test
    public void testCast() {
        Boolean aTrue = new Boolean("false");
        System.out.println(aTrue);

        int i = Integer.parseInt("111");
        System.out.println(i);
    }

    @Test
    public void testFilter() {
        List<PCheckExecution> pCheckExecutions = Lists.newArrayList();
        PCheckExecution latestPCheckExecution = pCheckExecutions.stream()
                .filter(o -> ProcessExecutionReferType.MAVEN_SONAR.getType() == o.getReferType())
                .max(Comparator.comparing(PCheckExecution::getGmtCreate))
                .orElse(null);
        System.out.println(latestPCheckExecution);
    }

    @Test
    public void testMd5() {
        String aaaaa = SecureUtil.md5("aaaaa");
        System.out.println(aaaaa);
    }

    @Test
    public void testLocalDateTime1() {
        LocalDateTime now = LocalDateTime.now();
        int value = now.getDayOfWeek().getValue();
        System.out.println(value);
    }

    @Test
    public void testCron() {
        Date date = new Date();
        List<Date> dates =
                CronPatternUtil.matchedDates("0 0 20 ? * MON", date, DateUtils.addDays(date, 40),
                        10, false);
        System.out.println(dates.toString());
    }

    @Test
    public void testTime() {
        long time = new Date().getTime();
        System.out.println(time);
    }

    @Test
    public void testSplit() {
        String a = "";
        ArrayList<String> strings = Lists.newArrayList(a.split(","));
        System.out.println(strings);
    }

    @Test
    public void initJavaListJson() {
        List<ListTemplate> templateList = Lists.newArrayList();
        ListTemplate listTemplate = ListTemplate.builder()
                .metricKey("bugs")
                .desc("一个会破坏代码的编码错误，需要立即修复。")
                .iconUrl("https://cdnfile.corp.kuaishou.com/kc/files/a/kdev-workbench/codeScan-prod/assets/img/bug.svg")
                .name("Bugs")
                .iconType(2)
                .build();
        templateList.add(listTemplate);
        ListTemplate listTemplate1 = ListTemplate.builder()
                .metricKey("ksCodeSmell")
                .desc("易混淆且难以维护的代码。")
                .iconUrl(
                        "https://cdnfile.corp.kuaishou.com/kc/files/a/kdev-workbench/codeScan-prod/assets/img"
                                + "/codeSmell.svg")
                .name("异味")
                .iconType(2)
                .build();
        templateList.add(listTemplate1);
        ListTemplate listTemplate2 = ListTemplate.builder()
                .metricKey("complexity")
                .desc("代码复杂度的衡量标准，数值越高，代表越复杂。")
                .iconUrl(
                        "lxx")
                .name("圈复杂度")
                .iconType(3)
                .build();
        templateList.add(listTemplate2);
        ListTemplate listTemplate3 = ListTemplate.builder()
                .metricKey("duplicated_lines_density")
                .desc("重复行的密度，数值越高，代码重复度越高。")
                .iconUrl(
                        "lxx")
                .name("重复")
                .iconType(3)
                .build();
        templateList.add(listTemplate3);
        RepoListTemplate template = RepoListTemplate.builder()
                .templateList(templateList)
                .build();
        System.out.println(JSONUtils.serialize(template));
    }

    @Test
    public void initJavaDetailJson() {
        List<TemplateMapping> mappingList = Lists.newArrayList();
        List<TemplateMappingVo> mappingVos = Lists.newArrayList();
        TemplateMappingVo mappingVo = TemplateMappingVo.builder()
                .iconUrl("https://cdnfile.corp.kuaishou.com/kc/files/a/kdev-workbench/codeScan-prod/assets/img/bug.svg")
                .desc("一个会破坏代码的编码错误，需要立即修复。")
                .jumpUrl(
                        "${kdevUrl}/web/codescan/project/issueList?types=bugs&gitProjectId=${gitProjectId}&branch=$"
                                + "{branch}")
                .metricKey("bugs")
                .title("Bugs")
                .iconType(2)
                .build();
        mappingVos.add(mappingVo);
        TemplateMapping mapping = TemplateMapping.builder()
                .mappingName("可靠性")
                .mappingVos(mappingVos)
                .build();
        mappingList.add(mapping);


        List<TemplateMappingVo> mappingVos1 = Lists.newArrayList();
        TemplateMappingVo mappingVo1 = TemplateMappingVo.builder()
                .iconUrl(
                        "https://cdnfile.corp.kuaishou.com/kc/files/a/kdev-workbench/codeScan-prod/assets/img"
                                + "/codeSmell.svg")
                .desc("易混淆且难以维护的代码。")
                .jumpUrl(
                        "${kdevUrl}/web/codescan/project/issueList?types=codeSmell&gitProjectId=${gitProjectId"
                                + "}&branch=${branch}")
                .metricKey("ksCodeSmell")
                .title("异味")
                .iconType(2)
                .build();
        mappingVos1.add(mappingVo1);
        TemplateMapping mapping1 = TemplateMapping.builder()
                .mappingName("可维护性")
                .mappingVos(mappingVos1)
                .build();
        mappingList.add(mapping1);

        List<TemplateMappingVo> mappingVos2 = Lists.newArrayList();
        TemplateMappingVo mappingVo2 = TemplateMappingVo.builder()
                .iconUrl("lxx")
                .desc("重复行的密度，数值越高，代码重复度越高。")
                .jumpUrl(
                        "${kdevUrl}/web/codescan/project/duplicateDetail?type=1&gitProjectId=${gitProjectId}&branch=$"
                                + "{branch}")
                .metricKey("duplicated_lines_density")
                .title("重复行密度")
                .iconType(3)
                .build();
        mappingVos2.add(mappingVo2);
        TemplateMappingVo mappingVo3 = TemplateMappingVo.builder()
                .iconUrl("")
                .desc("重复的代码块数量，数值越高，代码重复度越高。")
                .jumpUrl(
                        "${kdevUrl}/web/codescan/project/duplicateDetail?type=2&gitProjectId=${gitProjectId}&branch=$"
                                + "{branch}")
                .metricKey("duplicated_blocks")
                .title("重复代码块")
                .iconType(3)
                .build();
        mappingVos2.add(mappingVo3);
        TemplateMapping mapping2 = TemplateMapping.builder()
                .mappingName("重复")
                .mappingVos(mappingVos2)
                .build();
        mappingList.add(mapping2);

        List<TemplateMappingVo> mappingVos4 = Lists.newArrayList();
        TemplateMappingVo mappingVo4 = TemplateMappingVo.builder()
                .iconUrl("")
                .desc("代码复杂度的衡量标准，数值越高，代表越复杂。")
                .jumpUrl("${kdevUrl}/web/codescan/project/complexDetail?gitProjectId=${gitProjectId}&branch=${branch}")
                .metricKey("complexity")
                .title("圈复杂度")
                .iconType(3)
                .build();
        mappingVos4.add(mappingVo4);
        TemplateMapping mapping4 = TemplateMapping.builder()
                .mappingName("复杂度")
                .mappingVos(mappingVos4)
                .build();
        mappingList.add(mapping4);

        RepoDetailTemplate detailTemplate = RepoDetailTemplate.builder()
                .mappingList(mappingList)
                .build();
        System.out.println(JSONUtils.serialize(detailTemplate));
    }

    @Test
    public void initJavaScriptListJson() {
        List<ListTemplate> templateList = Lists.newArrayList();
        ListTemplate listTemplate = ListTemplate.builder()
                .metricKey("bugs_count")
                .desc("一个会破坏代码的编码错误，需要立即修复。")
                .iconUrl("https://cdnfile.corp.kuaishou.com/kc/files/a/kdev-workbench/codeScan-prod/assets/img/bug.svg")
                .name("Bugs")
                .iconType(2)
                .build();
        templateList.add(listTemplate);
        RepoListTemplate template = RepoListTemplate.builder()
                .templateList(templateList)
                .build();
        System.out.println(JSONUtils.serialize(template));
    }

    @Test
    public void initJavascriptDetailJson() {
        List<TemplateMapping> mappingList = Lists.newArrayList();
        List<TemplateMappingVo> mappingVos = Lists.newArrayList();
        TemplateMappingVo mappingVo = TemplateMappingVo.builder()
                .iconUrl("https://cdnfile.corp.kuaishou.com/kc/files/a/kdev-workbench/codeScan-prod/assets/img/bug.svg")
                .desc("一个会破坏代码的编码错误，需要立即修复。")
                .jumpUrl(
                        "${kdevUrl}/web/codescan/project/issueList?types=bugs&gitProjectId=${gitProjectId}&branch=$"
                                + "{branch}")
                .metricKey("bugs_count")
                .title("Bugs")
                .iconType(2)
                .build();
        mappingVos.add(mappingVo);
        TemplateMapping mapping = TemplateMapping.builder()
                .mappingName("可靠性")
                .mappingVos(mappingVos)
                .build();
        mappingList.add(mapping);

        RepoDetailTemplate detailTemplate = RepoDetailTemplate.builder()
                .mappingList(mappingList)
                .build();
        System.out.println(JSONUtils.serialize(detailTemplate));
    }

    @Test
    public void testTrim() {
        String s = "Sonar {} sss ";
        String trim = s.trim();
        System.out.println("====" + trim + "====");
    }

    @Test
    public void testSubString() {
        StringBuilder reasonSb = new StringBuilder();
        reasonSb.append("bug超过阈值;sc超过阈值;");
        String reasonStr = reasonSb.toString();
        if (StringUtils.isNotEmpty(reasonStr)) {
            reasonStr = reasonStr.substring(0, reasonStr.length() - 1);
        }
        System.out.println("==" + reasonStr);
    }

    @Test
    public void testUUID() {
        String s = UUID.fastUUID().toString().replace("-", "");
        System.out.println(s);
    }

    @Test
    public void testDotSplit() {
        String a = "sss.ts";
        String[] split = a.split("\\.");
        for (String s : split) {
            System.out.println(s);
        }
    }

    @Test
    public void testJson() {
        int i = 1;
        System.out.println(JSONUtils.serialize(i));
    }

    @Test
    public void testHash() {
        int i = 2151 % 64;
        System.out.println(i);
        int i1 = 2151 % 128;
        System.out.println(i1);
    }

    @Test
    public void testValidate() {
        String s = FileUtil.readUtf8String("/Users/<USER>/Desktop/param.json");
        SkyeyeLocalReportRequest reportRequest = JSONUtils.deserialize(s, SkyeyeLocalReportRequest.class);
        FileDuplication duplicationInfo = reportRequest.getDuplicationInfo();
        List<DuplicationDetail> duplicationList = duplicationInfo.getDuplicationList();
        FileInfo fileInfo = reportRequest.getFileInfo();
        List<FileDetail> fileList = fileInfo.getFileList();
        List<String> collect = fileList.stream().map(FileDetail::getPath).collect(Collectors.toList());

        for (DuplicationDetail duplicationDetail : duplicationList) {
            List<DuplicationFileDetail> fileList1 = duplicationDetail.getFileList();
            for (DuplicationFileDetail duplicationFileDetail : fileList1) {
                if (!collect.contains(duplicationFileDetail.getPath())) {
                    System.out.println(duplicationFileDetail.getPath() + "不存在file");
                }
            }

        }
    }

    @Test
    public void getPath() {
        File file = new File("Users/lixiaoxin/Desktop/发票");
        String path = file.getPath();
        System.out.println(path);
    }

    @Test
    public void testCal() {
        ArrayList<Integer> integers = Lists.newArrayList(5318
                , 3517
                , 30
                , 2972
                , 13437
                , 3174
                , 1095
                , 1568
                , 1432
                , 127
                , 2450
                , 795
                , 412
                , 10
                , 49
                , 1186
                , 69
                , 2652
                , 4456
                , 1451
                , 1263);
        int sum = 0;
        for (Integer integer : integers) {
            sum += integer;
        }
        System.out.println(sum);
    }

    @Test
    public void testA() {
        String a = "";
        System.out.println(a.split(",").length);
    }

    @Test
    public void testDe() {
        ArrayList<Integer> integers =
                Lists.newArrayList(11044, 1789, 8203, 10782, 5285, 10894, 10566, 5366, 10159, 2878, 4029, 10131, 10134,
                        10151, 10135, 10144, 10165, 7608, 2873, 4357, 10781, 3412, 5913, 11731, 11994, 12002, 12337,
                        12385, 12682, 12910, 13798, 13800, 13987, 15194, 15442, 15486, 15566, 15576,
                        15912, 16325, 16385, 16625, 16693, 16725, 16730, 16867, 18112, 18384, 18592, 18607, 18987,
                        19806, 20060, 20066, 20253, 20484, 20517, 20539, 20581, 20642, 20643, 21041, 21227, 21387,
                        21924, 21995, 23238, 23613, 24173, 24213, 24350, 24361, 24916);
        ArrayList<Integer> integers1 =
                Lists.newArrayList(10144, 18592, 18112, 12002, 11044, 20581, 10566, 10151, 13800, 15566, 10159, 18384,
                        21041,
                        16625, 10131, 11731, 10165, 10134, 5366, 10135, 16730, 4029, 19806);
        boolean b = integers.removeAll(integers1);
        System.out.println(integers);
    }

    @Test
    public void generateLink() {
        URIBuilder uriBuilder;
        try {
            uriBuilder = new URIBuilder("https://kdev.corp.kuaishou.com");
        } catch (URISyntaxException e) {
            return;
        }
        uriBuilder.setPath("/web/codescan/project/issueList");
        uriBuilder.setParameter("gitProjectId", "23221");
        uriBuilder.setParameter("branch", "master");
        uriBuilder.setParameter("issueKeys", "23221l04b1fb70l1968591369l29290146l624252081l612189278");
        System.out.println(uriBuilder.toString());
    }

    @Test
    public void testWeek() {
        LocalDateTime minus1WeekDateTime = LocalDateTime.of(LocalDate.now(), LocalTime.MIN).minusWeeks(1);
        // 获取今天是周几
        int dayOfWeek = minus1WeekDateTime.getDayOfWeek().get(DAY_OF_WEEK);
        // 上周日
        LocalDateTime minus1WeekEndDateTime = minus1WeekDateTime.plusDays(8 - dayOfWeek);
        LocalDateTime minus1WeekStartDateTime = minus1WeekDateTime.minusDays(dayOfWeek).withSecond(1);

        System.out.println(minus1WeekStartDateTime);
        System.out.println(minus1WeekEndDateTime);
    }

    @Test
    public void testBase62() {
        String encode = Base62.encode("100000000");
        System.out.println(encode);
    }

    @Test
    public void testJson1() {
        String s = "{ \"MVN_BUILD_ARGUMENTS\": \"-Dmaven.test.skip=true  -DrepoUrl=*********************"
                + ".com:user-growth/ug-user-manage/kuaishou-wechat-app.git -Dbranch=master -DcommitId=99fdaace "
                + "-DpipelineId=500810 -DbuildId=12645918 -DprojectId=31939 -DbuildModules=kuaishou-wechat-app-api "
                + "-DbuildUserName=cuizhijun -Denforcer.checkSnapShotsSkip=false -Ddeprecated.check.skip=false "
                + "kuaishou:deprecated-methods-check-plugin:check -T 1C\", \"BETA_PACKAGE_VERSIONS\": "
                + "\"kuaishou-webservice:1.0.3349\n"
                + "\", \"BUILD_MODULES\": \"kuaishou-wechat-app-api\" }";
        Map<String, String> stringStringMap = JSONUtils.deserializeMap(s, String.class, String.class);
        String s1 = stringStringMap.get("BETA_PACKAGE_VERSIONS");
        System.out.println(s1);
        String[] split = s1.split(":");
        System.out.println(split[0]);
        System.out.println("====");
        System.out.print(split[1]);
        System.out.print("====");
    }

    @Test
    public void testRequestJson() {
        List<Integer> projectIds =
                Lists.newArrayList(30947,30949,31090,25959,31093,30950,30951);
        ProjectProfileBatchUpdateRequest updateRequest = new ProjectProfileBatchUpdateRequest();
        List<RepoProfileUpdateRequest> profileUpdateRequestList = Lists.newArrayList();
        for (Integer projectId : projectIds) {
            RepoProfileUpdateRequest repoProfileUpdateRequest = new RepoProfileUpdateRequest();
            repoProfileUpdateRequest.setGitProjectId(projectId);
            repoProfileUpdateRequest.setBranch("master");
            repoProfileUpdateRequest.setProfileName("ed02b21f925c43dbb3ab66f83281d968");
            repoProfileUpdateRequest.setProfileType(1);
            profileUpdateRequestList.add(repoProfileUpdateRequest);
        }
        updateRequest.setProfileUpdateRequestList(profileUpdateRequestList);
        System.out.println(JSONUtils.serialize(updateRequest));
    }

    @Test
    public void testStr2Decimal() {
        BigDecimal multiply100Decimal = new BigDecimal("0")
                .multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
        System.out.println(multiply100Decimal.toString());
        int i = Integer.parseInt(multiply100Decimal.toString());
        System.out.println(i);
    }

    @Test
    public void testDecode() {
        Map<String, List<String>> stringListMap =
                HttpUtil.decodeParams("asda&a=saafafafsfafafaf", StandardCharsets.UTF_8);
        System.out.println(JSONUtils.serialize(stringListMap));
    }

    @Test
    public void testCron1() {
        Date date = new Date();
        DateTime beginOfDay = DateUtil.beginOfDay(date);
        DateTime endOfDay = DateUtil.endOfDay(date);
        List<Date> dates = CronPatternUtil.matchedDates("0 0 21 ? * MON-FRI ", beginOfDay, endOfDay, 1, true);
        for (Date date1 : dates) {
            System.out.println(date1);
        }
    }

    @Test
    public void testWhitelist() {
        // 然后查询当前流水线有没有上传记录
        List<AstIssue> astIssues = generateAstIssues();
        // 去除api模块的数据
        astIssues.removeIf(o -> o.getGavLocation().endsWith("-api"));
        System.out.println("===1" + JSONUtils.serialize(astIssues));
        if (CollectionUtils.isEmpty(astIssues)) {
            return;
        }
        // 增加白名单
        List<ScanWhitelist> scanWhitelists = generateScanWhitelist();
        if (CollectionUtils.isNotEmpty(scanWhitelists)) {
            List<String> whitelistGaLocationList = scanWhitelists.stream()
                    .map(scanWhitelist -> scanWhitelist.getGroupId() + ":" + scanWhitelist.getArtifactId())
                    .collect(Collectors.toList());
            // 去除白名单中的模块
            astIssues.removeIf(o -> whitelistGaLocationList.contains(o.getGavLocation()));
            if (CollectionUtils.isEmpty(astIssues)) {
                return;
            }
        }
        System.out.println("===2" + JSONUtils.serialize(astIssues));
    }

    private List<ScanWhitelist> generateScanWhitelist() {
        List<ScanWhitelist> scanWhitelists = Lists.newArrayList();
        ScanWhitelist scanWhitelist = new ScanWhitelist();
        scanWhitelist.setArtifactId("ks-abc-sdk");
        scanWhitelist.setGroupId("kuaishou");
        scanWhitelists.add(scanWhitelist);
        return scanWhitelists;
    }

    private List<AstIssue> generateAstIssues() {
        List<AstIssue> astIssues = Lists.newArrayList();
        AstIssue astIssue = new AstIssue();
        astIssue.setGavLocation("kuaishou:ks-abc-api");
        astIssues.add(astIssue);

        AstIssue astIssue1 = new AstIssue();
        astIssue1.setGavLocation("kuaishou:ks-abc-sdk");
        astIssues.add(astIssue1);

        AstIssue astIssue2 = new AstIssue();
        astIssue2.setGavLocation("kuaishou:ks-abc-compile");
        astIssues.add(astIssue2);

        AstIssue astIssue3 = new AstIssue();
        astIssue3.setGavLocation("ks-abc-sdk");
        astIssues.add(astIssue3);
        return astIssues;
    }

    @Test
    public void testFileWalkes() {
        List<String> strings = FileUtil.readUtf8Lines(new File(
                "/Users/<USER>/develop/idea-project/ks-serveree-themis/ks-serveree-themis-api/src/main"
                        + "/resources/logback-spring-old.xml"));
        for (String string : strings) {
            System.out.println(string);
        }

        System.out.println("======");
        List<String> ignoreEmptyFiles = strings.stream().map(String::trim)
                .filter(o -> StringUtils.isNotEmpty(o) && !checkIsDoc(o)).collect(Collectors.toList());
        for (String ignoreEmptyFile : ignoreEmptyFiles) {
            System.out.println(ignoreEmptyFile);
        }
    }

    private boolean checkIsDoc(String line) {
        return line.startsWith("//") || line.startsWith("/*") || line.startsWith("*") || line.startsWith("#")
                || line.startsWith("<!--");
    }

    @Test
    public void testStealingExecutors() {
        Lists.newArrayList(1, 2, 3, 4, 5).parallelStream()
                .forEach(o -> System.out.println(Thread.currentThread().getName()));
        System.out.println("==========");
        ExecutorService FORK_JOIN_POOL_EXECUTOR =
                Executors.newWorkStealingPool(Runtime.getRuntime().availableProcessors());

        FORK_JOIN_POOL_EXECUTOR.execute(() -> {
            Lists.newArrayList(1, 2, 3, 4, 5).parallelStream()
                    .forEach(o -> System.out.println(Thread.currentThread().getName()));
        });

        Uninterruptibles.sleepUninterruptibly(10, TimeUnit.SECONDS);

    }

    @Test
    public void testDate() {
        List<String> strings = Lists.newArrayList("1", "2", "3");
        List<String> sub = ListUtil.sub(strings, 0, 3);
        System.out.println(sub);
    }

    @Test
    public void testShard() {
        List<List<Integer>> totalShardList = ListUtil.splitAvg(
                Lists.newArrayList(170, 640, 1064, 2127), 6
        );
        for (List<Integer> integers : totalShardList) {
            System.out.println(integers);
        }
    }


    @Test
    public void testSplitAA() {
        String sonarBranch = "master||1241414||all";
        String[] split = sonarBranch.split("\\|\\|");
        for (String s : split) {
            System.out.println(s);
        }
    }

    @Test
    public void testSize() {
        long size = FileUtil.size(new File("/Users/<USER>/develop/idea-project/infra-internal-platform"));
        System.out.println(size);
    }

    @Test
    public void testSub() {
        List<Integer> ids = Lists.newArrayList();
        for (int i = 0; i < 1000; i++) {
            ids.add(i);
        }
        System.out.println(ids.size());
        System.out.println("=============");
        ids.clear();
        System.out.println(ids.size());
    }

    @Test
    public void testMod() {
        int i = 111516 % 32;
        System.out.println(i);

        int i1 = 211516 % 32;
        System.out.println(i1);
    }

    @Test
    public void testShard1() {
        String s = "8,5,0,9,6,1,7,2";
        String[] split = s.split(",");
        for (String s1 : split) {
            System.out.println(s1);
        }
    }

    @Test
    public void testSubList() {
        List<Integer> allProjectIds =
                Lists.newArrayList(640, 61698, 51590, 1929, 60553, 40332, 4243, 45588, 61334, 10392, 3992, 65050, 23323,
                        33948,
                        45598, 1824, 20642, 66851, 9380, 20644, 41126, 44583, 1064, 170, 44846, 61489, 26803, 48054,
                        48055,
                        14649, 36282, 27195, 3900, 3901, 2110, 2111, 2112, 35520, 64450, 20162, 63301, 64072, 52424,
                        1738, 1740,
                        1741, 1742, 1743, 2127, 33872, 64210, 64211, 46291, 46292, 51417, 45401, 24155, 24157, 6109,
                        8674,
                        44899, 11113, 8042, 64879, 4079, 21487, 55665, 37874, 51447, 51448, 51450, 37246, 45310, 35199);

        List<Integer> hasTongbuId =
                Lists.newArrayList(24155, 1743, 23323, 4079, 20642, 1824, 20162, 9380, 3900, 35520, 8042, 1738, 170,
                        24157,
                        37874, 8674, 6109, 35199, 37246, 20644, 33872, 4243, 44899, 44583, 1741, 45598, 45588, 45310,
                        10392,
                        40332, 41126, 44846, 45401, 46291, 46292, 48054, 48055, 33948, 51447, 51448, 51450, 51590,
                        51417, 36282,
                        52424, 21487, 55665, 60553, 61334, 61698, 61489, 63301, 64072, 64450, 64210, 64211, 64879,
                        65050);

        boolean b = allProjectIds.removeAll(hasTongbuId);

        System.out.println(allProjectIds.size());

        System.out.println(JSONUtils.serialize(allProjectIds));

    }

    @Test
    public void testAN() {
        String replace = "asdafafafagag".replace("test:", "");
        System.out.println(replace);
        String replace1 = "test:asdafafafagag1111".replace("test:", "");
        System.out.println(replace1);
    }

    @Test
    public void testHb() throws IOException {
        FileInputStream fileInputStream = new FileInputStream("/Users/<USER>/Desktop/工作簿111111.xlsx");
        List<Map<Integer, String>> domainMapList = EasyExcel.read(fileInputStream, null, null).sheet().doReadSync();
        //        System.out.println(JSONUtils.serialize(domainMapList));


        FileInputStream fileInputStream1 = new FileInputStream("/Users/<USER>/Desktop/指标数据.xlsx");

        List<Tj> outputList = Lists.newArrayList();
        List<Map<Integer, String>> domainMapList1 = EasyExcel.read(fileInputStream1, null, null).sheet().doReadSync();

        for (Map<Integer, String> integerStringMap : domainMapList) {
            String bumen = integerStringMap.get(0);
            String gitProjectId = integerStringMap.get(1);
            String gitUrl = integerStringMap.get(2);

            Tj tj = new Tj();
            tj.setName(bumen);
            tj.setGitProjectId(gitProjectId);
            tj.setGitUrl(gitUrl);
            SonarConfigEnum configEnum = SonarConfigEnum.modByProjectId(Long.valueOf(gitProjectId));
            tj.setSharDomain(configEnum.getDomain() + "/dashboard?id=measure:" + gitProjectId);

            for (Map<Integer, String> integerStringMap1 : domainMapList1) {
                String gitProjectId1 = integerStringMap1.get(0);
                String metericKey = integerStringMap1.get(1);
                String metericVal = integerStringMap1.get(2);
                if (!gitProjectId1.equals(gitProjectId)) {
                    continue;
                }
                if ("complexity".equals(metericKey)) {
                    tj.setComplexity(metericVal);
                }
                if ("duplicated_blocks".equals(metericKey)) {
                    tj.setDuplicatedBlocks(metericVal);
                }
                if ("bugs".equals(metericKey)) {
                    tj.setBugs(metericVal);
                }
            }

            outputList.add(tj);
        }

        String name = "/Users/<USER>/Desktop/整合指标1111113332223233.xlsx";

        EasyExcel.write(name, Tj.class).sheet("模板").doWrite(outputList);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Tj {
        @ExcelProperty(value = "姓名", index = 0)
        private String name;

        @ExcelProperty(value = "111", index = 1)
        private String gitProjectId;

        @ExcelProperty(value = "年龄", index = 2)
        private String gitUrl;

        @ExcelProperty(value = "所属shard链接", index = 3)
        private String sharDomain;

        @ExcelProperty(value = "圈复杂度", index = 4)
        private String complexity;

        @ExcelProperty(value = "重复代码块", index = 5)
        private String duplicatedBlocks;

        @ExcelProperty(value = "bug数", index = 6)
        private String bugs;

    }

    @Test
    public void testFileContent() {
        List<String> strings = FileUtil.readLines("/Users/<USER>/Desktop/需要更新的项目列表", "UTF-8");
        List<A> as = Lists.newArrayList();
        for (String string : strings) {
            String[] split = string.split(",");
            A a = A.builder()
                    .gitProjectId(Integer.parseInt(split[0]))
                    .jdkVersion(split[1])
                    .build();
            as.add(a);
        }
        System.out.println(JSONUtils.serialize(as));
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    static class A {
        private Integer gitProjectId;
        private String jdkVersion;
    }

    @Test
    public void testBlame() throws GitAPIException {
        Git gitRepo = MoreFunctions.throwing(() -> Git.open(new File("/Users/<USER>/develop/idea-project/ks"
                + "-serveree-themis")));
        BlameCommand blame = gitRepo.blame();
        BlameResult blameResult = blame.setFilePath("pom.xml")
                .call();
        PersonIdent sourceAuthor = blameResult.getSourceAuthor(26);
        PersonIdent sourceAuthor1 = blameResult.getSourceAuthor(27);
        PersonIdent sourceAuthor2 = blameResult.getSourceAuthor(28);
        String name = sourceAuthor.getName();
        System.out.println(name);
        String name1 = sourceAuthor1.getName();
        System.out.println(name1);
        String name2 = sourceAuthor2.getName();
        System.out.println(name2);
    }


}
