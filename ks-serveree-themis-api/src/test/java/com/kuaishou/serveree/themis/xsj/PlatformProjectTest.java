package com.kuaishou.serveree.themis.xsj;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.SpringBaseTest;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoBranch;
import com.kuaishou.serveree.themis.component.common.entity.CheckRepoLanguage;
import com.kuaishou.serveree.themis.component.common.entity.CheckTrigger;
import com.kuaishou.serveree.themis.component.constant.platform.CheckRepoBranchVersion;
import com.kuaishou.serveree.themis.component.entity.platform.LanguageSetting;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;
import com.kuaishou.serveree.themis.component.service.CheckRepoLanguageService;
import com.kuaishou.serveree.themis.component.service.CheckTriggerService;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRepoService;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.ApiModelProperty;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-02-13 14:57
 **/
public class PlatformProjectTest extends SpringBaseTest {
    @Autowired
    private PlatformRepoService platformRepoService;
    @Autowired
    private CheckRepoLanguageService checkRepoLanguageService;
    @Autowired
    private CheckTriggerService checkTriggerService;
    @Autowired
    private CheckRepoBranchService checkRepoBranchService;

    @Test
    @ApiModelProperty("验证编辑项目:更新语言版本")
    public void testUpdateProject1() {
        String version1 = "11";
        String version2 = "1.8";
        Long repoId = 4403L;
        Long branchId = 4409L;
        Integer gitProjectId = 20554;
        LanguageSetting setting = new LanguageSetting();
        setting.setLanguage("java");

        List<CheckRepoLanguage> before = checkRepoLanguageService.listByCheckRepoId(repoId);
        assert before.size() == 1;
        if (before.get(0).getVersion().equals(version1)) {
            setting.setVersion(version2);
        } else {
            setting.setVersion(version1);
        }

        RepoCreateRequest request = fillRequest(gitProjectId, null, null, null, null,
                setting, null, repoId, branchId);
        platformRepoService.update(request, "xieshijie");
        List<CheckRepoLanguage> after = checkRepoLanguageService.listByCheckRepoId(repoId);

        assert after.size() == 1;
        assert after.get(0).getVersion().equals(setting.getVersion());
    }

    @Test
    @ApiModelProperty("验证编辑项目:更新触发时间")
    public void testUpdateProject2() {
        List<String> list = Arrays.asList(
                "0 0 01 ? * MON,WED,FRI,TUE,THU",
                "0 0 02 ? * MON,WED,FRI,TUE,THU",
                "0 0 03 ? * MON,WED,FRI,TUE,THU",
                "0 0 04 ? * MON,WED,FRI,TUE,THU");
        List<String> triggers = new ArrayList<>(list);
        Long repoId = 4403L;
        Long branchId = 4409L;
        Integer gitProjectId = 20554;

        CheckTrigger before = checkTriggerService.getByCheckRepoBranchId(branchId);
        assert null != before && StringUtils.isNotEmpty(before.getTriggerCron());
        CollUtil.removeAny(triggers, before.getTriggerCron());

        RepoCreateRequest request = fillRequest(gitProjectId, null, null, null, triggers.get(0),
                null, null, repoId, branchId);
        platformRepoService.update(request, "xieshijie");
        CheckTrigger after = checkTriggerService.getByCheckRepoBranchId(branchId);

        assert StringUtils.isNotEmpty(after.getTriggerCron());
        assert after.getTriggerCron().equals(triggers.get(0));
    }

    @Test
    @ApiModelProperty("验证编辑项目:切换长期扫描分支")
    public void testUpdateProject3() {
        String branch1 = "master";
        String branch2 = "release";
        String branch3 = "test";
        Long repoId = 4403L;
        Long branchId = 4409L;
        Integer gitProjectId = 20554;

        List<CheckRepoBranch> checkRepoBranches = checkRepoBranchService.listBranchByRepoId(repoId);
        assert checkRepoBranches.size() > 0;
        CheckRepoBranch checkRepoBranchRaw = checkRepoBranches.stream().filter(v -> v.getVersion() == 1).findAny().orElseGet(() -> null);
        assert null != checkRepoBranchRaw;

        // 切换为一条全新的branch
        String newBranch = checkRepoBranches.get(0).getBranchName() + System.currentTimeMillis();
        RepoCreateRequest request1 = fillRequest(gitProjectId, null, newBranch, null, null,
                null, null, repoId, branchId);
        platformRepoService.update(request1, "xieshijie");
        List<CheckRepoBranch> checkRepoBranches1 = checkRepoBranchService.listBranchByRepoId(repoId);
        assert checkRepoBranches.size() > 0;
        CheckRepoBranch checkRepoBranch1 = checkRepoBranches.stream().filter(v -> v.getVersion() == 1).findAny().orElseGet(() -> null);
        assert null != checkRepoBranch1;
        assert checkRepoBranches.stream().filter(v -> v.getVersion() == CheckRepoBranchVersion.NEW_VERSION.getCode()).count() == 1;
        assert checkRepoBranches.stream().filter(v -> v.getVersion() == CheckRepoBranchVersion.EDIT_VERSION.getCode()).count() == 1;
        assert checkRepoBranch1.getBranchName().equals(newBranch);
        assert !checkRepoBranch1.getBranchName().equals(checkRepoBranchRaw.getBranchName());

        // 切换为一条旧的branch
        CheckRepoBranch checkRepoBranch2 = checkRepoBranches1.stream().filter(v -> v.getVersion() == CheckRepoBranchVersion.EDIT_VERSION.getCode())
                .findAny().orElse(null);
        assert null != checkRepoBranch2;
        RepoCreateRequest request2 = fillRequest(gitProjectId, null, checkRepoBranch2.getBranchName(), null, null,
                null, null, repoId, branchId);
        platformRepoService.update(request2, "xieshijie");
        List<CheckRepoBranch> checkRepoBranches2 = checkRepoBranchService.listBranchByRepoId(repoId);
        assert checkRepoBranches2.stream().filter(v -> v.getVersion() == CheckRepoBranchVersion.NEW_VERSION.getCode()).count() == 1;
        assert checkRepoBranches2.stream().filter(v -> v.getVersion() == CheckRepoBranchVersion.EDIT_VERSION.getCode()).count() == 1;
        assert checkRepoBranches2.size() > 0;
        CheckRepoBranch checkRepoBranch22 = checkRepoBranches2.stream().filter(v -> v.getVersion() == CheckRepoBranchVersion.NEW_VERSION.getCode())
                .findAny().orElseGet(() -> null);
        assert null != checkRepoBranch22;
        assert checkRepoBranch22.getBranchName().equals(checkRepoBranch2.getBranchName());

        // 切换为release分支
        RepoCreateRequest request3 = fillRequest(gitProjectId, null, branch2, null, null,
                null, null, repoId, branchId);
        platformRepoService.update(request3, "xieshijie");
        assert checkRepoBranchService.listBranchByRepoId(repoId).size() == 3;
    }


    private RepoCreateRequest fillRequest(Integer gitProject, String repoUrl, String branch,
            String profileName, String triggerCron, LanguageSetting setting, String scanner, Long repoId, Long branchId) {
        RepoCreateRequest request = new RepoCreateRequest();
        request.setGitProjectId(gitProject);
        request.setRepoUrl(repoUrl);
        request.setBranch(branch);
        request.setProfileName(profileName);
        request.setTriggerCron(triggerCron);
        request.setLanguageSetting(setting);
        request.setScanner(scanner);
        request.setCheckRepoId(repoId);
        request.setCheckRepoBranchId(branchId);
        return request;
    }
}
