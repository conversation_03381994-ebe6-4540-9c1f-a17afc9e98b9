package com.kuaishou.serveree.themis;

import java.util.List;

import javax.annotation.Resource;

import org.junit.Test;

import com.kuaishou.serveree.themis.component.common.entity.CheckProfile;
import com.kuaishou.serveree.themis.component.service.CheckProfileService;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-09-13
 */
public class CheckProfileTest extends SpringBaseTest {

    @Resource
    private CheckProfileService checkProfileService;

    @Test
    public void testFindAllAncestorProfiles() {
        String profileName = "bd336a43fc7f4dc08156f642b9e683ff";
        List<CheckProfile> allAncestorProfiles = checkProfileService.findAllAncestorProfiles(profileName);
        assert allAncestorProfiles.size() == 3;
        System.out.println(allAncestorProfiles);
    }

}
