package com.kuaishou.serveree.themis.ww;

import javax.annotation.Resource;

import org.junit.Test;

import com.kuaishou.serveree.themis.SpringBaseTest;
import com.kuaishou.serveree.themis.component.common.entity.ComplexityFile;
import com.kuaishou.serveree.themis.component.service.id.tables.TablesIdGeneratorManager;

import cn.hutool.core.lang.Assert;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-02-28
 */
public class TableIdGenerateTest extends SpringBaseTest {

    @Resource
    private TablesIdGeneratorManager idGeneratorManager;

    @Test
    public void testComplexityFileIdGenerate() {
        Long id = idGeneratorManager.generateDistributionId(ComplexityFile.class);
        System.out.println(id);
        Assert.isTrue(id > 0, "id should be greater than 0");
    }
}
