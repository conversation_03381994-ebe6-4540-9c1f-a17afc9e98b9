package com.kuaishou.serveree.themis.ww;

import javax.annotation.Resource;

import org.junit.Test;

import com.kuaishou.serveree.themis.SpringBaseTest;
import com.kuaishou.serveree.themis.component.constant.platform.PlatformLanguageEnum;
import com.kuaishou.serveree.themis.component.service.platform.PlatformSpaceManagementService;
import com.kuaishou.serveree.themis.component.vo.request.RepoCreateRequest;

/**
 * <AUTHOR> <<EMAIL>>
 * Created on 2024-10-23
 */
public class PlatformSpaceTest extends SpringBaseTest {

    @Resource
    private PlatformSpaceManagementService platformSpaceManagementService;


    @Test
    public void testPlatformSpaceManagementService() {
        RepoCreateRequest repoCreateRequest =
                platformSpaceManagementService.buildNewRepoCreateRequest(PlatformLanguageEnum.JAVA,
                        "*************************:serveree/ks-serveree-themis.git", 24049);
        assert "c1557854cc5343fab90de324135f68f7".equals(repoCreateRequest.getProfileName());
    }
}
