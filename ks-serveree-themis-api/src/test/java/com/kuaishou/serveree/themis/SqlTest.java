package com.kuaishou.serveree.themis;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.kuaishou.serveree.themis.component.service.CheckProfileRuleRelationService;
import com.kuaishou.serveree.themis.component.service.CheckRepoBranchService;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on 2023-01-06 14:35
 **/
public class SqlTest extends SpringBaseTest{
    @Autowired
    private CheckProfileRuleRelationService checkProfileRuleRelationService;
    @Autowired
    private CheckRepoBranchService checkRepoBranchService;

    @Test
    public void testListProfile() {
        List<String> strings = checkProfileRuleRelationService.listAllDistinctProfile();
        System.out.println(strings);
        assert strings.size() == 20;
    }

    @Test
    public void testBranchSelect() {
        List<Long> longs = checkRepoBranchService.duplicateBranch();
        System.out.println(longs);
    }
}
