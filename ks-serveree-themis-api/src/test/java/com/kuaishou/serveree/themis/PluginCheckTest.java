package com.kuaishou.serveree.themis;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.common.entity.Task;
import com.kuaishou.serveree.themis.component.common.entity.TaskConfig;
import com.kuaishou.serveree.themis.component.entity.plugin.ReportData;
import com.kuaishou.serveree.themis.component.entity.report.IllegalMeta;
import com.kuaishou.serveree.themis.component.service.PluginCheckService;
import com.kuaishou.serveree.themis.component.service.TaskConfigService;
import com.kuaishou.serveree.themis.component.service.TaskService;
import com.kuaishou.serveree.themis.component.vo.response.ProjectLoadConfigVo;
import com.kuaishou.serveree.themis.component.vo.response.ThemisResponse;


/**
 * <AUTHOR>
 * @since 2022/1/20 2:38 下午
 */
public class PluginCheckTest extends SpringBaseTest {

    @Autowired
    private PluginCheckService pluginCheckService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private TaskConfigService taskConfigService;

    @Test
    public void testKconfConfig() {
        ThemisResponse<ProjectLoadConfigVo> response = pluginCheckService.projectAndCommonConfig(1001);
        Assert.notNull(response, "response must not be null");
    }

    @Test
    public void testReport() {
        List<IllegalMeta> illegalMetaList = Lists.newArrayList();
        IllegalMeta illegalMeta = new IllegalMeta();
        illegalMeta.setIllegalFile("avc/asdaf/dqweqr/fadfsd/A.java");
        illegalMeta.setRuleId("banedAnnotation");
        illegalMeta.setIllegalErrorMsg("禁止的依赖");
        illegalMetaList.add(illegalMeta);
        ReportData reportData = ReportData.newBuilder()
                .branch("master")
                .buildId(118284820L)
                .pipelineId(12470L)
                .buildUserName("lixiaoxin")
                .commitId("9bc24ec5cdeab5f4215e18497e7b4b4a5144d09f")
                .gavArgs("kuaishou:aaaa")
                .projectId(10086)
                .repoUrl("*************************:plateco-dev/kuaishou-ad-merchant-api.git")
                .illegalMetaList(illegalMetaList)
                .build();
        pluginCheckService.report(reportData);
    }

    @Test
    public void testSendKimNotice() {
        Task task = taskService.getById(8749);
        TaskConfig taskConfig = taskConfigService.getTaskConfigByTaskId(8749L);
        pluginCheckService.sendDependencyRuleNotice(task, taskConfig);
    }

}
