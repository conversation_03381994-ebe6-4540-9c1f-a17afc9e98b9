package com.kuaishou.serveree.themis;

import java.util.List;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.common.collect.Lists;
import com.kuaishou.serveree.themis.component.service.platform.PlatformRuleService;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;
import com.kuaishou.serveree.themis.component.vo.request.AddRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.DeleteRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.request.UpdateRuleRequestVo;
import com.kuaishou.serveree.themis.component.vo.response.AddRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.SearchRuleResponseVo;
import com.kuaishou.serveree.themis.component.vo.response.SearchRuleResponseVo.CheckRuleVo;

/**
 * <AUTHOR>
 * @since 2021/9/6 5:10 下午
 */
public class PlatformRuleTest extends SpringBaseTest {

    @Autowired
    private PlatformRuleService platformRuleService;

    @Test
    public void testAdd() {
        AddRuleRequestVo addRuleRequestVo = new AddRuleRequestVo();
        List<CheckRuleVo> ruleVos = Lists.newArrayList();
        CheckRuleVo checkRuleVo = new CheckRuleVo();
        checkRuleVo.setType("BUG");
        checkRuleVo.setSeverity("COMMON");
        checkRuleVo.setDescription("这是测试1");
        checkRuleVo.setKey("eslint-1");
        ruleVos.add(checkRuleVo);

        CheckRuleVo checkRuleVo1 = new CheckRuleVo();
        checkRuleVo1.setType("BUG");
        checkRuleVo1.setSeverity("COMMON");
        checkRuleVo1.setDescription("这是测试1");
        checkRuleVo1.setKey("eslint-2");
        ruleVos.add(checkRuleVo1);

        CheckRuleVo checkRuleVo2 = new CheckRuleVo();
        checkRuleVo2.setType("CODE_SMELL");
        checkRuleVo2.setSeverity("MAJOR");
        checkRuleVo2.setDescription("这是测试1");
        checkRuleVo2.setKey("eslint-3");
        ruleVos.add(checkRuleVo2);

        CheckRuleVo checkRuleVo3 = new CheckRuleVo();
        checkRuleVo3.setType("VULNERABILITY");
        checkRuleVo3.setSeverity("MAJOR");
        checkRuleVo3.setDescription("这是测试1");
        checkRuleVo3.setKey("eslint-3");
        ruleVos.add(checkRuleVo3);
        addRuleRequestVo.setAddRules(ruleVos);

        AddRuleResponseVo add = platformRuleService.add(addRuleRequestVo);
        System.out.println(JSONUtils.serialize(add));
    }


    @Test
    public void testSearch(){
        SearchRuleResponseVo search = platformRuleService.search(null);
        System.out.println(JSONUtils.serialize(search));
    }

    @Test
    public void testDel(){
        DeleteRuleRequestVo deleteRuleRequestVo = new DeleteRuleRequestVo();
        List<Long> delRuleIds = Lists.newArrayList();
        delRuleIds.add(1L);
        delRuleIds.add(2L);
        delRuleIds.add(3L);
        deleteRuleRequestVo.setDeleteRuleIds(delRuleIds);
        platformRuleService.delete(deleteRuleRequestVo);
    }

    @Test
    public void testUpdate(){
        UpdateRuleRequestVo updateRuleRequestVo = new UpdateRuleRequestVo();
        List<CheckRuleVo> checkRuleVos = Lists.newArrayList();
        CheckRuleVo checkRuleVo = new CheckRuleVo();
        checkRuleVo.setRuleId(4L);
        checkRuleVo.setDescription("affafafafa");
        checkRuleVo.setKey("esl-22323");
        checkRuleVo.setSeverity("MAJOR");
        checkRuleVos.add(checkRuleVo);
        CheckRuleVo checkRuleVo1 = new CheckRuleVo();
        checkRuleVos.add(checkRuleVo1);
        updateRuleRequestVo.setUpdateRules(checkRuleVos);
        platformRuleService.update(updateRuleRequestVo);
    }

}
