package com.kuaishou.serveree.themis;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import org.apache.kafka.clients.producer.RecordMetadata;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;

import com.google.common.util.concurrent.ListenableFuture;
import com.kuaishou.infra.framework.kafka.KafkaProducers;
import com.kuaishou.serveree.themis.component.entity.kafka.CodeScanResultNotify;
import com.kuaishou.serveree.themis.component.utils.JSONUtils;

import cn.hutool.http.HttpRequest;

/**
 * <AUTHOR>
 * @since 2020/11/4 5:30 下午
 */
public class KafkaTest {

    @Value("${kafka.quality_task_distribute_topic}")
    private String qualityTaskDistributeTopic;

    @Value("${kafka.code_check_result_notify_topic}")
    private String codeCheckResultNotifyTopic;

    @Test
    public void test01() throws ExecutionException, InterruptedException {
        ListenableFuture<RecordMetadata> recordMetadataListenableFuture = KafkaProducers.sendString(
                qualityTaskDistributeTopic,
                "{\"repoUrl\":\"*************************:serveree/ks-serveree-themis.git\",\"branch\":\"master\","
                        + "\"commitId\":\"\",\"params\":\"\",\"checkType\":\"JAVA_KS_PLUGIN_CHECK\",\"taskId\":1,"
                        + "\"priority\":3,\"checkTypeEnum\":null}");

        RecordMetadata recordMetadata = recordMetadataListenableFuture.get();
        System.out.println(JSONUtils.serialize(recordMetadata));
    }

    @Test
    public void test02() {
        String resultResp = HttpRequest
                .get("localhost:2115/api/quality/check//log/read?taskId=5")
                .header("Token", "88888888")
                .timeout(2000)//超时，毫秒
                .execute()
                .body();
        System.out.println(resultResp);
    }

    @Test
    public void testKafkaSend() throws InterruptedException, ExecutionException, TimeoutException {
        CodeScanResultNotify codeScanResultNotify = CodeScanResultNotify.builder()
                .buildId(11111L)
                .projectId(32314)
                .branch("saffafafaf")
                .scanTypeName("新SonarMavenScanner扫描")
                .build();
        ListenableFuture<RecordMetadata> listenableFuture = KafkaProducers.sendString(
                codeCheckResultNotifyTopic, JSONUtils.serialize(codeScanResultNotify));
        RecordMetadata recordMetadata = listenableFuture.get(10, TimeUnit.SECONDS);
        System.out.println(JSONUtils.serialize(recordMetadata));
    }

}
