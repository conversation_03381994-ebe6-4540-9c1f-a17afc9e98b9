<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>kuaishou</groupId>
        <artifactId>ks-serveree-themis</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ks-serveree-themis-api</artifactId>

    <dependencies>
        <!-- 引入 ks-boot -->
        <dependency>
            <groupId>com.kuaishou.infra.boot</groupId>
            <artifactId>ks-boot-starter-web</artifactId>
        </dependency>
        <!-- 引入 ks-boot-actuator，可选，actuator 是 spring-boot 的重磅功能，建议开启 -->
        <dependency>
            <groupId>com.kuaishou.infra.boot</groupId>
            <artifactId>ks-boot-starter-actuator</artifactId>
        </dependency>
        <!-- 引入 kuaishou-framework，可选 -->
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>infra-framework-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-framework-deprecated</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>ks-serveree-themis-component</artifactId>
        </dependency>
        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>ks-serveree-themis-security</artifactId>
        </dependency>
        <!-- Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-logback</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>kuaishou</groupId>-->
<!--            <artifactId>kuaishou-ad-reco-base-ad-base-proto</artifactId>-->
<!--            <version>1.0.9318</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>kuaishou</groupId>
            <artifactId>kuaishou-intown-json-sdk</artifactId>
            <version>1.0.56</version>
        </dependency>

    </dependencies>

    <build>
        <!-- 更改打包之后的 jar name，去掉 version 信息，便于部署 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>